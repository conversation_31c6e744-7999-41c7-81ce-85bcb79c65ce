import React from 'react';
import ErrorIcon from '@mui/icons-material/Error';
import { SxProps, Theme, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';

export interface ValueFieldWithErrorProps {
  /**
   * The value to display. If null/undefined, an error icon will be shown instead
   */
  value: any;

  /**
   * Optional tooltip text to show when hovering over the error icon
   */
  errorMessage?: string;

  /**
   * Color for the error icon
   */
  errorColor?: 'error' | 'warning' | 'info' | 'success';

  /**
   * Size of the error icon
   */
  iconSize?: 'small' | 'medium' | 'large' | 'inherit';

  /**
   * Additional styling for the component
   */
  sx?: SxProps<Theme>;

  children?: React.ReactNode;
}

/**
 * Field component that displays a value or an error icon if the value is missing
 *
 * @example
 * <ErrorField value={record.measureUnit} />
 * <ErrorField value={record.measureUnit} errorMessage="Missing measure unit" />
 */
export const ValueFieldWithError: React.FC<ValueFieldWithErrorProps> = ({
  value,
  errorMessage,
  errorColor = 'error',
  iconSize = 'small',
  sx,
  children,
}) => {
  const { t } = useTranslation('');
  const defaultErrorMessage = t('common.missingValue', 'Missing value');

  // Check if value is null, undefined, or empty string
  if (value === null || value === undefined || value === '') {
    return (
      <Tooltip title={errorMessage || defaultErrorMessage}>
        <ErrorIcon
          role="presentation"
          color={errorColor}
          fontSize={iconSize}
          sx={sx}
        />
      </Tooltip>
    );
  }

  // Return the value itself
  return <>{children || value}</>;
};
