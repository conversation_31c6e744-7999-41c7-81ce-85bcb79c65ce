import {
  Box,
  CardProps,
} from '@mui/material';
import {
  RecordContextProvider,
  useListContext,
} from 'react-admin';

const MobileGrid = ({ children }: CardProps) => {
  const { data } = useListContext();

  return (
    <Box display="flex" flexDirection="column" gap={0}>
      {data?.map(record => (
        <RecordContextProvider key={record.id} value={record}>
          {children}
        </RecordContextProvider>
      ))}
    </Box>
  );
};

export default MobileGrid;
