import { Dispatch, SetStateAction, useEffect } from 'react';
import { Box } from '@mui/material';
import { useGetList } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import PricingSubscriptionsCreateStep1 from './PricingSubscriptionsCreateStep1';
import PricingSubscriptionsCreateStep2 from './PricingSubscriptionsCreateStep2';
import PricingSubscriptionsCreateStep3 from './PricingSubscriptionsCreateStep3';
import PricingSubscriptionsCreateStep4Free from './PricingSubscriptionsCreateStep4Free';
import PricingSubscriptionsCreateStep4Premium from './PricingSubscriptionsCreateStep4Premium';

interface PricingSubscriptionsFormProps {
  step: number;
  setStep: (step: number) => void;
  setIsPremium: Dispatch<SetStateAction<boolean>>;
  isEditing: boolean;
  handleClose: () => void;
}

export default function PricingSubscriptionsForm({
  step,
  setIsPremium,
  setStep,
  handleClose,
}: PricingSubscriptionsFormProps) {
  const { watch } = useFormContext();
  const isPremium = watch('isPremium');

  useEffect(() => {
    setIsPremium(isPremium);
  }, [isPremium]);

  const { data: subscriptions } = useGetList('pricing-subscriptions');

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '800px',
        width: '90%',
        mx: 'auto',
        my: 2,
        gap: 1,
      }}
    >
      {step === 0 && <PricingSubscriptionsCreateStep1 />}
      {step === 1 && (
        <PricingSubscriptionsCreateStep2 handleClose={handleClose} />
      )}
      {step === 2 && (
        <PricingSubscriptionsCreateStep3 handleClose={handleClose} />
      )}
      {step === 3 && subscriptions && subscriptions[0].isPremium && (
        <PricingSubscriptionsCreateStep4Premium setStep={setStep} />
      )}
      {step === 3 && subscriptions && !subscriptions[0].isPremium && (
        <PricingSubscriptionsCreateStep4Free />
      )}
    </Box>
  );
}
