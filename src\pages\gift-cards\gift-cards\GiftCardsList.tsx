import { Fragment } from 'react';
import { Theme, useMediaQuery } from '@mui/material';
import {
  AutocompleteInput,
  BulkDeleteWithConfirmButton,
  Datagrid,
  FilterButton,
  List,
  ReferenceInput,
  TextField,
  TextInput,
  TopToolbar,
} from 'react-admin';

import CustomSearchInput from '../../../components/atoms/inputs/CustomSearchInput';
import { CustomSelect } from '../../../components/atoms/inputs/CustomSelect';

const filters = [
  <CustomSearchInput key="search-input" source="q" alwaysOn />,
  <CustomSelect
    key="disabled-select2"
    source="cardType"
    label="Card type"
    choices={[
      { id: 'Electronic', name: 'Electronic' },
      { id: 'Plastic', name: 'Plastic' },
      { id: 'Third Party', name: 'Third Party' },
    ]}
  />,
  <CustomSelect
    key="disabled-select"
    source="firstLoad"
    label="First load"
    choices={[
      { id: 'Proload', name: 'Proload' },
      { id: 'Online', name: 'Online' },
      { id: 'Developer integration', name: 'Developer integration' },
      { id: 'Refund', name: 'Refund' },
      { id: 'Point of sale', name: 'Point of sale' },
      { id: 'Third Party', name: 'Third Party' },
    ]}
  />,
  <CustomSelect
    key="disabled-select"
    source="otherCardDetails"
    label="Other card details"
    choices={[
      { id: 'Unused since activation', name: 'Unused since activation' },
      { id: 'Purchased with discount', name: 'Purchased with discount' },
      { id: 'Purchased in bulk', name: 'Purchased in bulk' },
      {
        id: 'Migrated from other account',
        name: 'Migrated from other account',
      },
    ]}
  />,
  <ReferenceInput
    key="giftCard"
    label={'Location'}
    source="gift-card"
    reference="location"
    perPage={50}
    sort={{ field: 'name', order: 'ASC' }}
    filterToQuery={(searchText: any) => ({ name: [searchText] })}
  >
    <AutocompleteInput label="Location" optionText={'location'} />
  </ReferenceInput>,
  <TextInput key="name-search" label="Name" source="name" />,
];

const ListActions = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <TopToolbar>
      <FilterButton filters={filters} />
    </TopToolbar>
  );
};

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

export const GiftCardsList = () => {
  return (
    <List
      resource="gift-cards-overview"
      component="div"
      filters={filters}
      actions={<ListActions />}
    >
      <Datagrid
        rowClick={`show`}
        sx={{
          '& .RaBulkActionsToolbar-topToolbar': {
            backgroundColor: 'transparent',
          },
          '& .RaDatagrid-headerCell': {
            backgroundColor: '#F2F2F2',
            borderBottom: '1px solid #EAEAEA',
          },
        }}
      >
        <TextField source="cardEndingIn" />
        <TextField source="type" />
        <TextField source="location" />
        <TextField source="lastUsed" />
        <TextField source="balance" />
      </Datagrid>
    </List>
  );
};
