import { PropsWithChildren } from 'react';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import GrayBgContainer from '../atoms/GrayBgContainer';

interface HelpSuggestionProps extends PropsWithChildren {
  topCaption?: any;
  sx?: any;
}
export default function HelpSuggestion({
  topCaption,
  sx,
  children,
}: HelpSuggestionProps) {
  const { t } = useTranslation();
  return (
    <GrayBgContainer
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        ...sx,
      }}
    >
      {topCaption && (
        <Typography variant="caption" mb={2}>
          {topCaption}
        </Typography>
      )}
      <Typography variant="h5" mb={1}>
        {t('menu.stuck')}
      </Typography>
      <Typography variant="caption" mb={2}>
        {t('menu.stuckDescription')}
      </Typography>

      {children}
    </GrayBgContainer>
  );
}
