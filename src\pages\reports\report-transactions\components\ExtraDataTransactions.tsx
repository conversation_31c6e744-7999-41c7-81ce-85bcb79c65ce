import { useMemo, useState } from 'react';
import { Note } from '@mui/icons-material';
import SubdirectoryArrowRightRoundedIcon from '@mui/icons-material/SubdirectoryArrowRightRounded';
import { Box, Button, capitalize, Divider, Typography } from '@mui/material';
import { Link } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts/ThemeContext';
import useEnv from '~/hooks/useEnv';
import { getDownloadUrl } from '~/providers/utils/getDownloadUrl';
import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatQuantityLabel from '~/utils/formatQuantityLabel';
import getInitials from '~/utils/getInitials';
import EmailModal from './EmailModal';
import ExtraDataCard from './ExtraDataCard';

const BILLBASEURL = '';

export default function ExtraDataTransactions({
  extraData,
}: {
  extraData?: { [key: string]: any };
}) {
  const { NODE_ENV } = useEnv();
  const { theme } = useTheme();
  const [isEmailModalOpen, setEmailModalOpen] = useState(false);
  const { t } = useTranslation('');

  const handleOpenEmailModal = () => setEmailModalOpen(true);
  const handleCloseEmailModal = () => setEmailModalOpen(false);

  const icons = {
    comp: '/assets/transactions/comp.svg',
    void: '/assets/transactions/void.svg',
    card: '/assets/transactions/card.svg',
    cash: '/assets/transactions/cash.svg',
    multiple: '/assets/transactions/transfer.svg',
    '3rdParty': '/assets/transactions/3rdParty.svg',
    giftCard: '/assets/transactions/giftCard.svg',
    mealTicket: '/assets/transactions/mealTicket.svg',
    online: '/assets/transactions/online.svg',
    valueTicket: '/assets/transactions/valueTicket.svg',
    wireTransfer: '/assets/transactions/wireTransfer.svg',
    cashless: '/assets/transactions/cashless.svg',
    voucher: '/assets/transactions/voucher.svg',
  };

  const rowData = useMemo(() => {
    const customerNameValue = extraData?.customerName;

    const customerInitials =
      customerNameValue && customerNameValue.trim() !== ''
        ? getInitials(customerNameValue)
        : 'N/A';

    const payments = extraData?.payments
      ? Object.values(extraData?.payments).map((payment: any) => {
          return {
            addedAt: formatTimestamp(payment.addedAt),
            addedBy: payment.addedBy,
            details: payment.details,
            type: payment.type,
            value: formatAndDivideNumber(payment.value),
          };
        })
      : undefined;

    const coupons = extraData?.coupons
      ? Object.values(extraData?.coupons).map((coupon: any) => {
          return {
            name: coupon.name,
            type: coupon.type,
            value: formatAndDivideNumber(coupon.value),
          };
        })
      : undefined;

    const extraCharges = extraData?.extraCharges
      ? Object.values(extraData?.extraCharges).map((extra: any) => {
          return {
            name: extra.name,
            quantity: extra.quantity / 1000,
            value: formatAndDivideNumber(extra.value),
          };
        })
      : undefined;

    const printedBills = extraData?.printedBills
      ? Object.values(extraData?.printedBills)
          .sort((a: any, b: any) => {
            const aTime = a.addedAt || 0;
            const bTime = b.addedAt || 0;
            return bTime - aTime;
          })
          .map((bill: any) => {
            return {
              addedAt: formatTimestamp(bill.addedAt),
              addedBy: bill.addedBy,
              type: bill.type,
              value: formatAndDivideNumber(bill.value),
              url: BILLBASEURL + bill.url,
            };
          })
      : undefined;

    const giftCards = extraData?.giftCards
      ? Object.values(extraData?.giftCards)?.map((giftCard: any) => {
          return {
            name: giftCard.name,
            type: giftCard.type,
            value: formatAndDivideNumber(giftCard.value),
          };
        })
      : undefined;

    const rowDataFiltered = {
      ...extraData,
      billName: extraData?.billName || '-',
      totalValue: formatAndDivideNumber(extraData?.totalValue),
      closedAt: formatTimestamp(extraData?.closedAt),
      items: Object.values(extraData?.items)?.map((item: any) => {
        const modifiers = item.modifiers
          ? Object.values(item.modifiers)
              ?.sort((a: any, b: any) => {
                const aHasValue = a.value != null && a.value !== 0;
                const bHasValue = b.value != null && b.value !== 0;

                if (aHasValue === bHasValue) return 0;
                return aHasValue ? 1 : -1;
              })
              .map((modifier: any) => {
                return {
                  name: modifier.name,
                  prefix: modifier.prefix,
                  value: formatAndDivideNumber(modifier.value),
                };
              })
          : undefined;

        const selections = item.selections
          ? Object.values(item.selections)?.map((selection: any) => {
              const modifiers = selection.modifiers
                ? Object.values(selection.modifiers)
                    ?.sort((a: any, b: any) => {
                      const aHasValue = a.value != null && a.value !== 0;
                      const bHasValue = b.value != null && b.value !== 0;

                      if (aHasValue === bHasValue) return 0;
                      return aHasValue ? 1 : -1;
                    })
                    .map((modifier: any) => {
                      return {
                        name: modifier.name,
                        prefix: modifier.prefix,
                        value: formatAndDivideNumber(modifier.value),
                      };
                    })
                : undefined;

              return {
                name: selection.name,
                modifiers,
              };
            })
          : undefined;

        return {
          name: item.name,
          source: item.source,
          quantity: item.quantity / 1000,
          type: item.type,
          note: item.note,
          moves: item.moves,
          discount: item.discount,
          discountName: item.discountName,
          discountValue: item.discountValue,
          voidedBy: item.voidedBy,
          voidedAt: item.voidedAt
            ? new Date(item.voidedAt * 1000).toTimeString().slice(0, 5)
            : undefined,
          voidReason: item.voidReason,
          color: item.color,
          value: formatAndDivideNumber(item.value),
          modifiers,
          selections,
        };
      }),
      closedFrom: extraData?.closedFrom || '-',
      source:
        extraData?.source && extraData?.source === '@pos'
          ? 'Direct Selio Pos'
          : extraData?.source || '-',
      owner: extraData?.owner,
      covers: extraData?.covers || '-',
      id: extraData?.id,
      closedWith: extraData?.closedWith,
      diningOption: extraData?.dinningOption || '-',
      voidReason: extraData?.voidReason,
      customerName: extraData?.customerName,
      tipsPercentage: extraData?.tipsPercentage,
      tipsValue: extraData?.tipsValue,
      note: extraData?.note,
      subTotalValue: formatAndDivideNumber(extraData?.subTotalValue),
      section: extraData?.section,
      closedBy: extraData?.closedBy,
      vatNoOnReceipt: extraData?.vatNoOnReceipt,
      fiscalNumber: extraData?.fiscalNumber,
      closedWithCompReason: extraData?.closedWithCompReason,
      openedAt: extraData?.openedAt || undefined,
      orderDiscount: extraData?.orderDiscount
        ? extraData?.orderDiscount / 100
        : undefined,
      customerInitials: customerInitials,
      orderDiscountValue: formatAndDivideNumber(extraData?.orderDiscountValue),
      extraCharges,
      coupons,
      payments,
      printedBills,
      giftCards,
    };

    return rowDataFiltered;
  }, [extraData]);

  function formatTimestamp(timestamp: number) {
    const date = new Date(timestamp * 1000);
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
      .format(date)
      .replace(',', '');
  }

  const customerNameExists = Object.prototype.hasOwnProperty.call(
    extraData,
    'customerName'
  );
  const customerName = extraData?.customerName;

  const isNA = customerName === '@na';

  const customerInitials = customerNameExists
    ? isNA
      ? 'N/A'
      : getInitials(customerName)
    : '';

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '500px',
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        backgroundColor: 'background.paper',
        borderRadius: 2,
      }}
    >
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
        <Button
          // @ts-ignore
          variant="contained-light"
          sx={{ width: '100%', height: '52px' }}
          onClick={handleOpenEmailModal}
        >
          {t('transactionsPage.sendReceipt')}
        </Button>
        <EmailModal open={isEmailModalOpen} onClose={handleCloseEmailModal} />
      </Box>
      <ExtraDataCard>
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            justifyContent: 'space-between',
            mb: 2,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Typography variant="h3" fontWeight="bold">
                {rowData.billName}
              </Typography>
              <Typography variant="h3" fontWeight="bold">
                {rowData.section}
              </Typography>
            </Box>

            <Typography
              sx={{ wordBreak: 'break-all', fontSize: '10px' }}
              variant="body2"
              color="textSecondary"
            >
              #{rowData.id}
            </Typography>
          </Box>

          <Box>
            <Typography
              sx={{ textAlign: 'right' }}
              variant="h3"
              fontWeight="bold"
            >
              {rowData.closedWith == 'comp'
                ? rowData.totalValue
                : rowData.closedWith === 'void'
                  ? ''
                  : rowData.totalValue}
            </Typography>
            <Typography
              sx={{ textAlign: 'right' }}
              variant="h4"
              fontWeight="bold"
            >
              {rowData.closedWith == 'comp'
                ? ` ${t('transactionsPage.comped')}`
                : rowData.closedWith === 'void'
                  ? t('transactionsPage.voidedSale')
                  : ` ${t('transactionsPage.sale')}`}
            </Typography>
            <Typography
              sx={{ textAlign: 'end' }}
              variant="body2"
              color="textSecondary"
            >
              {rowData.closedAt}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ color: '#737373', fontWeight: 300 }}>
          {customerNameExists && (
            <Box
              sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 0.5 }}
            >
              <Box
                sx={{
                  width: '33px',
                  height: '33px',
                  backgroundColor: '#0064F0',
                  borderRadius: '100px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  fontWeight: '500',
                  fontSize: '14px',
                  color: 'white',
                  flexShrink: 0,
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                }}
              >
                {customerInitials}
              </Box>

              {!isNA && (
                <Typography variant="body2" fontWeight={400}>
                  {customerName}
                </Typography>
              )}
            </Box>
          )}

          {rowData.closedWithCompReason && (
            <Typography color="black" variant="h3" fontWeight="bold">
              {t('transactionsPage.reason')}: {rowData.closedWithCompReason}
            </Typography>
          )}
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.order')}: {rowData.billName}{' '}
            {t('transactionsPage.ownedBy')} {rowData?.owner}
          </Typography>
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.openedAt')}:{' '}
            {(() => {
              const openedAt = rowData?.openedAt;
              if (!openedAt) return '-';

              const date = new Date(openedAt * 1000);
              const time = date.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
              });
              const day = date.getDate().toString().padStart(2, '0');
              const month = date.toLocaleString('default', { month: 'short' }); // e.g. 'Mar'
              const year = date.getFullYear();

              return `${time} / ${day} ${month} ${year}`;
            })()}
          </Typography>
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.closedBy')}: {rowData.closedBy}
          </Typography>
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.closedFrom')}: {rowData.closedFrom}
          </Typography>
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.source')}: {rowData.source}
          </Typography>
          <Typography variant="body2" fontWeight={300}>
            {t('transactionsPage.saleAttributedTo')}:{' '}
            <span>{rowData.owner}</span>
          </Typography>
          {rowData.covers && (
            <Typography variant="body2" fontWeight={300}>
              {t('transactionsPage.covers')}: {rowData.covers}
            </Typography>
          )}
          {rowData.fiscalNumber && (
            <Typography
              variant="body2"
              fontWeight={300}
              sx={{ textTransform: 'capitalize' }}
            >
              {t('transactionsPage.fiscalReceipt')}:
              {rowData.fiscalNumber === '-1'
                ? ' DEMO'
                : ' #' + rowData.fiscalNumber}
            </Typography>
          )}
          {rowData.vatNoOnReceipt && (
            <Typography variant="body2" fontWeight={300}>
              CIF Client: {rowData.vatNoOnReceipt}
            </Typography>
          )}
        </Box>
        <Divider sx={{ my: 0.5 }} />
        <Typography fontWeight="bold" sx={{ textTransform: 'uppercase' }}>
          {rowData.diningOption === 'here'
            ? t('transactionsPage.forHere')
            : rowData.diningOption === 'togo'
              ? t('transactionsPage.toGo')
              : camelCaseToNormalWords(rowData.diningOption)}
        </Typography>
        <Divider sx={{ my: 0.5 }} />
        {/* Order Details */}

        {console.log(rowData)}
        {rowData?.items &&
          rowData?.items.map((item: any) => {
            const isVoided = item.voidedBy || item.voidReason;
            const itemQuantityName = formatQuantityLabel(
              item.quantity,
              item.name
            );

            return (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    gap: 2,
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        width: '100%',
                      }}
                    >
                      <Box
                        sx={{
                          width: '50px',
                          height: '50px',
                          backgroundColor: item.color || 'gray',
                          borderRadius: '5px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          textAlign: 'center',
                          fontWeight: 'bold',
                          color: 'white',
                          textShadow: '1px 1px 1px black',
                          flexShrink: 0,
                          boxSizing: 'border-box',
                          overflow: 'hidden',
                        }}
                      >
                        {item.name.slice(0, 2).toUpperCase()}{' '}
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          width: '100%',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            gap: 2,
                            width: '100%',
                            fontSize: '14px',
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <Typography
                              sx={{
                                fontSize: '15px',
                                color:
                                  isVoided || rowData.closedWith == 'comp'
                                    ? 'gray'
                                    : '',
                                fontWeight:
                                  isVoided || rowData.closedWith == 'comp'
                                    ? ''
                                    : 'bold',
                              }}
                            >
                              {itemQuantityName}
                            </Typography>
                            {item.source === 'orderNow' && (
                              <Box
                                sx={{
                                  width: '10px',
                                  height: '10px',
                                  backgroundColor: 'red',
                                  borderRadius: '50%',
                                }}
                              ></Box>
                            )}
                          </Box>

                          <Typography
                            sx={{
                              color:
                                isVoided || rowData.closedWith == 'comp'
                                  ? 'gray'
                                  : '',
                              fontSize: '15px',
                              fontWeight:
                                isVoided || rowData.closedWith == 'comp'
                                  ? ''
                                  : 'bold',
                            }}
                          >
                            {item.value}
                          </Typography>
                        </Box>
                        {item.selections &&
                          item.selections.map((selection: any) => {
                            return (
                              <>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                  }}
                                >
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      gap: 0.5,
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                    }}
                                  >
                                    <SubdirectoryArrowRightRoundedIcon
                                      sx={{
                                        color:
                                          theme.palette.mode === 'light'
                                            ? 'gray'
                                            : 'white ',
                                      }}
                                    />
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color:
                                          theme.palette.mode === 'light'
                                            ? 'gray'
                                            : 'white ',
                                      }}
                                    >
                                      {selection.name}
                                    </Typography>
                                  </Box>
                                </Box>
                                {selection?.modifiers && (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      ml: 0.5,
                                    }}
                                  >
                                    {selection?.modifiers.map(
                                      (modifier: any) => {
                                        return (
                                          <Box
                                            sx={{
                                              display: 'flex',
                                              justifyContent: 'space-between',
                                            }}
                                          >
                                            <Box
                                              sx={{ display: 'flex', gap: 0.5 }}
                                            >
                                              {modifier.prefix && (
                                                <Typography
                                                  variant="body2"
                                                  sx={{
                                                    color:
                                                      theme.palette.mode ===
                                                      'light'
                                                        ? 'gray'
                                                        : 'white ',
                                                    textTransform: 'capitalize',
                                                    fontSize: '11px',
                                                  }}
                                                >
                                                  {modifier.prefix}
                                                </Typography>
                                              )}
                                              <Typography
                                                variant="body2"
                                                sx={{
                                                  fontSize: '11px',
                                                  color:
                                                    theme.palette.mode ===
                                                    'light'
                                                      ? 'gray'
                                                      : 'white ',
                                                }}
                                              >
                                                {modifier.name}
                                              </Typography>
                                            </Box>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: '#737373',
                                                fontSize: '11px',
                                              }}
                                            >
                                              {modifier.value}
                                            </Typography>
                                          </Box>
                                        );
                                      }
                                    )}
                                  </Box>
                                )}
                              </>
                            );
                          })}

                        {item.moves && (
                          <Typography
                            sx={{
                              fontSize: '11px',
                              ml: 2,
                              color:
                                theme.palette.mode === 'light'
                                  ? 'gray'
                                  : 'white ',
                            }}
                          >
                            {item.moves}
                          </Typography>
                        )}
                        {/* moves aici */}
                        {item?.note && (
                          <Typography
                            sx={{
                              fontSize: '11px',
                              ml: 2,
                              color:
                                theme.palette.mode === 'light'
                                  ? 'gray'
                                  : 'white ',
                            }}
                          >
                            Note: {item.note}
                          </Typography>
                        )}
                        {item.modifiers && (
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              ml: 2,
                            }}
                          >
                            {item.modifiers.map((modifier: any) => {
                              return (
                                <Box
                                  sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                  }}
                                >
                                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                                    {modifier.prefix && (
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          color:
                                            theme.palette.mode === 'light'
                                              ? 'gray'
                                              : 'white ',
                                          textTransform: 'capitalize',
                                          fontSize: '11px',
                                        }}
                                      >
                                        {modifier.prefix}
                                      </Typography>
                                    )}
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontSize: '11px',
                                        color:
                                          theme.palette.mode === 'light'
                                            ? 'gray'
                                            : 'white ',
                                      }}
                                    >
                                      {modifier.name}
                                    </Typography>
                                  </Box>
                                  <Typography
                                    variant="body2"
                                    sx={{ color: '#737373', fontSize: '11px' }}
                                  >
                                    {modifier.value}
                                  </Typography>
                                </Box>
                              );
                            })}
                          </Box>
                        )}
                        {item.discountValue && (
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              gap: 1,
                              width: '100%',
                              pl: 1.5,
                              pr: 1,
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'start',
                                alignItems: 'center',
                                gap: 0.5,
                              }}
                            >
                              <img
                                src={'/assets/transactions/discount.svg'}
                                alt={item.discountName}
                                style={{
                                  width: '22px',
                                  filter:
                                    theme.palette.mode !== 'light'
                                      ? 'brightness(0) saturate(100%) invert(100%) sepia(7%) saturate(0%) hue-rotate(39deg) brightness(106%) contrast(109%)'
                                      : 'brightness(0) saturate(100%) invert(77%) sepia(0%) saturate(230%) hue-rotate(85deg) brightness(91%) contrast(83%)',
                                }}
                              />
                              <Typography
                                variant="body2"
                                sx={{
                                  fontSize: '11px',
                                  color:
                                    theme.palette.mode === 'light'
                                      ? 'gray'
                                      : 'white ',
                                }}
                              >
                                Discount
                                {item.discount && <> {item.discount / 100}% </>}
                              </Typography>
                            </Box>
                            <Typography
                              variant="body2"
                              sx={{
                                color:
                                  theme.palette.mode === 'light'
                                    ? 'gray'
                                    : 'white ',
                              }}
                            >
                              - {formatAndDivideNumber(item.discountValue)}
                            </Typography>
                          </Box>
                        )}
                        {item.voidedBy && (
                          <Typography
                            sx={{
                              fontWeight: !isVoided ? '' : 600,
                              fontSize: '11px',
                              pl: 2,
                            }}
                            variant="body2"
                          >
                            Voided By {item.voidedBy} at{' '}
                            {item.voidedAt && item.voidedAt}
                          </Typography>
                        )}
                        {item.voidReason && (
                          <Typography
                            sx={{
                              pl: 2,
                              fontWeight: !isVoided ? '' : 600,
                              fontSize: '11px',
                            }}
                            variant="body2"
                          >
                            Reason: {item.voidReason}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </>
            );
          })}
        {rowData?.giftCards &&
          rowData?.giftCards.map((giftCard: any) => {
            const isVoided = giftCard.voidedBy || giftCard.voidReason;
            const isDigital = giftCard.type === '@digital' ? true : false;

            return (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    gap: 2,
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1,
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        width: '100%',
                      }}
                    >
                      <Box
                        sx={{
                          width: '50px',
                          height: '50px',
                          backgroundColor: giftCard.color || '#FFC008',
                          borderRadius: '5px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          textAlign: 'center',
                          fontWeight: 'bold',
                          color: 'white',
                          textShadow: '1px 1px 1px black',
                          flexShrink: 0,
                          boxSizing: 'border-box',
                          overflow: 'hidden',
                        }}
                      >
                        <img
                          src={'/assets/transactions/giftCard.svg'}
                          alt={'gift-card-icon'}
                          style={{
                            width: '22px',
                            filter:
                              theme.palette.mode === 'light' ? '' : 'invert(1)',
                          }}
                        />
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          width: '100%',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            gap: 2,
                            width: '100%',
                            fontSize: '14px',
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: '15px',
                              color:
                                isVoided || rowData.closedWith == 'comp'
                                  ? 'gray'
                                  : '',
                              fontWeight:
                                isVoided || rowData.closedWith == 'comp'
                                  ? ''
                                  : 'bold',
                            }}
                          >
                            {isDigital ? 'eGift Card' : ' Gift Card'}{' '}
                            {giftCard.name}
                          </Typography>
                          <Typography
                            sx={{
                              color:
                                isVoided || rowData.closedWith == 'comp'
                                  ? 'gray'
                                  : '',
                              fontSize: '15px',
                              fontWeight:
                                isVoided || rowData.closedWith == 'comp'
                                  ? ''
                                  : 'bold',
                            }}
                          >
                            {giftCard.value}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </>
            );
          })}
        {rowData?.voidReason && (
          <Typography sx={{ color: '#737373' }}>
            Void: {rowData?.voidReason}
          </Typography>
        )}
        {/* Pricing */}
        <Divider sx={{ my: 1 }} />
        {rowData.note && (
          <Box
            sx={{
              backgroundColor:
                theme.palette.mode === 'light' ? '#f2f2f2' : '#1F1F26 ',
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              minHeight: '80px',
              py: 1.5,
              px: 2,
              borderRadius: '8px',
              mb: 1,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'start', mb: 0.5 }}>
              <Note
                sx={{
                  fontSize: 20,
                  color: theme.palette.mode === 'light' ? 'black' : 'white ',
                  mr: 1,
                }}
              />
              <Typography
                variant="body2"
                fontWeight={400}
                sx={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  whiteSpace: 'normal',
                  color: theme.palette.mode === 'light' ? 'black' : 'white ',
                }}
              >
                <span style={{ fontWeight: 600 }}>Order Note:</span>{' '}
                {rowData.note}
              </Typography>
            </Box>

            <Typography
              variant="body2"
              fontWeight={400}
              color="#555"
            ></Typography>
          </Box>
        )}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography sx={{ fontSize: '16px' }}>Subtotal</Typography>
            <Typography sx={{ fontSize: '16px' }}>
              {rowData?.subTotalValue}
            </Typography>
          </Box>
          {rowData?.orderDiscount && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography>Discount {rowData?.orderDiscount}%</Typography>
              <Typography>- {rowData?.orderDiscountValue}</Typography>
            </Box>
          )}
          {rowData.coupons &&
            rowData.coupons.map(coupon => {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>Coupon {coupon.name}</Typography>
                  <Typography>- {coupon.value}</Typography>
                </Box>
              );
            })}
          {rowData.extraCharges &&
            rowData.extraCharges.map(charge => {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>
                    {charge.name} (x{charge.quantity})
                  </Typography>
                  <Typography>{charge.value}</Typography>
                </Box>
              );
            })}
          {rowData?.tipsValue && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography>
                Tips{' '}
                {rowData?.tipsPercentage && (
                  <>({rowData?.tipsPercentage / 100}%)</>
                )}
              </Typography>
              <Typography>
                {formatAndDivideNumber(rowData.tipsValue)}
              </Typography>
            </Box>
          )}
        </Box>
        <Divider sx={{ my: 1 }} />
        {/* Total */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            fontWeight: 'bold',
          }}
        >
          <Typography variant="h6">TOTAL</Typography>
          <Typography
            sx={{
              textDecoration:
                rowData.closedWith === 'comp' ? 'line-through' : '',
            }}
            variant="h6"
          >
            {rowData.closedWith === 'void'
              ? '0 ' + extraData?.currency
              : rowData.totalValue}
          </Typography>
        </Box>
      </ExtraDataCard>

      {/* Payments */}
      {rowData?.payments && rowData?.payments.length > 0 && (
        <ExtraDataCard>
          <Typography
            fontWeight="bold"
            color="textSecondary"
            gutterBottom
            sx={{ textTransform: 'uppercase' }}
          >
            {t('shared.payments')}
          </Typography>
          {rowData?.payments.map((payment: any, index: number) => (
            <Box
              sx={{
                borderBottom: '1px solid #eee',
                py: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 0.5,
              }}
            >
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'center',
                  }}
                >
                  <img
                    src={
                      icons[
                        payment.type as
                          | 'comp'
                          | 'void'
                          | 'card'
                          | 'cash'
                          | '3rdParty'
                          | 'giftCard'
                          | 'mealTicket'
                          | 'online'
                          | 'valueTicket'
                          | 'wireTransfer'
                      ]
                    }
                    alt={payment.type}
                    style={{
                      width: '30px',
                      filter:
                        'brightness(0) saturate(100%) invert(77%) sepia(0%) saturate(230%) hue-rotate(85deg) brightness(91%) contrast(83%)',
                    }}
                  />
                  <Typography sx={{ fontWeight: 'bold', ml: 1 }}>
                    {camelCaseToNormalWords(payment.type)}{' '}
                    {payment.details && (
                      <span
                        style={{
                          color: 'gray',
                          fontSize: '14px',
                        }}
                      >
                        payment.details
                      </span>
                    )}
                  </Typography>
                </Box>
                <Typography>{camelCaseToNormalWords(payment.value)}</Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flex: 1,
                  gap: 0.5,
                }}
              >
                <Typography
                  sx={{
                    color: 'text.secondary',
                    fontSize: '14px',
                  }}
                >
                  {payment.addedBy}
                </Typography>
                <Typography sx={{ color: 'text.secondary', fontSize: '14px' }}>
                  {payment.addedAt}
                </Typography>
              </Box>
            </Box>
          ))}
        </ExtraDataCard>
      )}

      {/* Bills Issued */}
      {rowData?.printedBills && rowData?.printedBills.length > 0 && (
        <ExtraDataCard>
          <Typography
            fontWeight="bold"
            color="textSecondary"
            gutterBottom
            sx={{ textTransform: 'uppercase' }}
          >
            {t('transactionsPage.billsIssued')}
          </Typography>
          {rowData?.printedBills.map((bill: any, index: number) => (
            <Link
              onClick={async (e: any) => {
                // TODO! temp implementation
                e.preventDefault();
                const url = await getDownloadUrl(bill.url);
                window.open(url, '_blank');
              }}
              target={'_blank'}
              to={bill.url}
              underline="none"
              key={index}
              sx={{
                borderBottom: '1px solid #eee',
                py: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 0.5,
              }}
            >
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'center',
                  }}
                >
                  <img
                    src={'/assets/transactions/bill.svg'}
                    alt={bill.type}
                    style={{
                      width: '30px',
                      filter: theme.palette.mode === 'light' ? '' : 'invert(1)',
                    }}
                  />
                  <Typography sx={{ fontWeight: 'bold', ml: 1 }}>
                    {bill.addedAt}
                  </Typography>
                </Box>
                <Typography
                  sx={{ fontWeight: 'bold', ml: 1, color: '#0064F0' }}
                >
                  {t('transactionsPage.' + camelCaseToNormalWords(bill.type))}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flex: 1,
                  gap: 0.5,
                }}
              >
                <Typography
                  sx={{
                    ml: 0.5,
                    color: 'text.secondary',
                    fontSize: '14px',
                  }}
                >
                  {t('transactionsPage.issuedBy')} {bill.addedBy}
                </Typography>
              </Box>
            </Link>
          ))}
        </ExtraDataCard>
      )}

      {/* Earnings */}
      {NODE_ENV === 'dev' && (
        <ExtraDataCard>
          <Typography fontWeight="bold">EARNING DETAILS </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography>-</Typography>
            <Typography>N/A</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography>Commission N/A % from your Net Sales</Typography>
            <Typography>N/A</Typography>
          </Box>
        </ExtraDataCard>
      )}
    </Box>
  );
}
