/*
Checks if the given argument is a number.
Use typeof to check if a value is classified as a number primitive.
To safeguard against NaN, check if val === val
(as NaN has a typeof equal to number and is the only value not equal to itself).
*/
export function isNumberPrimitive(value: unknown): value is number {
  return typeof value === 'number' && value === value;
}

// Checks if the given argument can be cast to a number.
export function itCanBeCastToNumber(value: unknown): boolean {
  return !isNaN(Number(value));
}
