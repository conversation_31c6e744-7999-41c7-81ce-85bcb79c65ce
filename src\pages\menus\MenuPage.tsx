import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '../../components/molecules/PageTitle';
import MenuCreate from './MenuCreate';
import MenuEdit from './MenuEdit';
import MenuList from './MenuList';

export default function MenuPage() {
  const { t } = useTranslation('');
  return (
    <Box p={2}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('posMenus.title')}
        description={
          <>
            {t('posMenus.desc')}
            <a href="https://selio.io/support" target="_blank" rel="noreferrer">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <MenuList />
      <MenuEdit />
      <MenuCreate />
    </Box>
  );
}
