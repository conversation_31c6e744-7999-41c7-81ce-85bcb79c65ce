import { Box, Card, Typography } from "@mui/material";
import { TextField, TextInput, TranslatableInputs } from "react-admin";
import { useTranslation } from "react-i18next";

export const PublicNameAndDescriptionSection = (props: any) => {
    const { t } = useTranslation('');
    const { catalogSpecific, getCatalogName, vatsChoices } = props;
  
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h5">{t('itemsLibrary.nameAndDescriptionVariations')}</Typography>
        {Object.entries(catalogSpecific).map(
          ([catalogId, catalogSpecificValues]: [string, any]) => {
            console.log(catalogSpecificValues);
            const catalogName = getCatalogName(catalogId);
            if (!catalogName) return null;
            return (
              <Card key={catalogId} sx={{ borderRadius: 2, p:2, boxShadow: '0px 6px 12px 4px rgb(216, 215, 215)', mb: 2}}>
                <TextField source="" defaultValue={catalogName} />
                <TranslatableInputs locales={['ro', 'en']}>
                  <TextInput
                    // defaultValue={catalogSpecificValues.publicName}
                    source={`catalogSpecific.${catalogId}.publicName`}
                    label={t('itemsLibrary.publicName')}
                  />
                  <TextInput
                    // defaultValue={catalogSpecificValues.description}
                    source={`catalogSpecific.${catalogId}.description`}
                    label={t('itemsLibrary.publicDescription')}
                  />
                  <TextInput
                    // defaultValue={catalogSpecificValues.description}
                    source={`catalogSpecific.${catalogId}.ingredients`}
                    label={t('itemsLibrary.ingredients')}
                  />
                </TranslatableInputs>
              </Card>
            );
          }
        )}
      </Box>
    );
  };