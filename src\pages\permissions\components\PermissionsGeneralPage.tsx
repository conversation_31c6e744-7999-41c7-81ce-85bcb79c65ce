import { Box } from '@mui/material';

import { PermissionsParts } from '.';
import { PermissionPages } from './constants';

interface GeneralPageProps {
  pageLabel: PermissionPages;
  groups: {
    groupLabel: string;
    possiblePermissions: number[];
  }[];
  isPageActive: boolean;
  togglePageActive: (pageLabel: PermissionPages) => void;
  activePermissions: number[];
  handlePermissionChange: (id: number) => void;
}
const PermissionsGeneralPage = ({
  pageLabel,
  groups,
  isPageActive,
  togglePageActive,
  activePermissions,
  handlePermissionChange,
}: GeneralPageProps) => {
  return (
    <Box>
      <PermissionsParts.PageHeader
        pageLabel={pageLabel}
        isPageActive={isPageActive}
        togglePageActive={togglePageActive}
        disableToggle={activePermissions[0] === 1}
      />
      {groups.map(group => (
        <PermissionsParts.Group
          key={group.groupLabel}
          groupLabel={group.groupLabel}
          possiblePermissions={group.possiblePermissions}
          activePermissions={activePermissions}
          handlePermissionChange={handlePermissionChange}
          groupDisabled={!isPageActive || activePermissions[0] === 1}
        />
      ))}
    </Box>
  );
};

export { PermissionsGeneralPage };
