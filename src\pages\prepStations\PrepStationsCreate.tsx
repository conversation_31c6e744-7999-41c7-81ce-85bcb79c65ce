import { Box, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  SimpleForm,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomInput from '~/components/atoms/inputs/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { useAvailableCategories } from '~/hooks';
import { validateName } from '~/utils/validateName';

const PrepStationsCreateInner = () => {
  const resource = useResourceContext();
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const { availableCategories, isLoading } = useAvailableCategories();

  const handleClose = () => {
    redirect('list', resource, undefined, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('prepStations.createPrepStation')}
      >
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <CustomInput
            source="name"
            label={t('shared.name')}
            validate={[required(), validateName]}
          />
          {availableCategories.length > 0 ? (
            <CustomInput
              source="groups"
              choices={availableCategories}
              type="select-array"
              label={t('prepStations.tags')}
              optionText="name"
              optionValue="id"
              placeholder={t('shared.none', { context: 'female' })}
              validate={[required()]}
              disabled={isLoading}
            />
          ) : (
            <>
              <CustomInput
                source="groups"
                choices={[]}
                type="select-array"
                label={t('prepStations.tags')}
                optionText="name"
                optionValue="id"
                placeholder={
                  isLoading
                    ? t('shared.loading')
                    : t('prepStations.noAvailableCategories')
                }
                validate={[required()]}
                disabled={true}
              />
              {!isLoading && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: 'block' }}
                >
                  {t('prepStations.allCategoriesAssigned')}
                </Typography>
              )}
            </>
          )}
        </Box>
      </Box>
    </>
  );
};

export const PrepStationsCreate = () => {
  const transform = (data: any) => {
    return {
      ...data,
      groupIds: data.groups,
    };
  };

  return (
    <CreateDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="optimistic"
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <PrepStationsCreateInner />
      </SimpleForm>
    </CreateDialog>
  );
};
