name: Deploy to Live Channel
on:
  push:
    branches:
      - main

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Use Node.js LTS
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
      - name: Add React Admin Enterprise Edition CI token
        uses: react-admin/ra-enterprise-action@main
        env:
          RA_EE_CI_TOKEN: ${{ secrets.RA_EE_CI_TOKEN }}
      - name: Install dependencies
        run: npm install
      - name: Build App
        run: npm run build
        env:
          VITE_NODE_ENV: 'prod'
          VITE_FIREBASE_API_KEY: '${{ secrets.PROD_FIREBASE_API_KEY }}'
          VITE_FIREBASE_AUTH_DOMAIN: '${{ secrets.PROD_FIREBASE_AUTH_DOMAIN }}'
          VITE_FIREBASE_DATABASE_URL: '${{ secrets.PROD_FIREBASE_DATABASE_URL }}'
          VITE_FIREBASE_PROJECT_ID: '${{ secrets.PROD_FIREBASE_PROJECT_ID }}'
          VITE_FIREBASE_STORAGE_BUCKET: '${{ secrets.PROD_FIREBASE_STORAGE_BUCKET }}'
          VITE_FIREBASE_MESSAGING_SENDER_ID: '${{ secrets.PROD_FIREBASE_MESSAGING_SENDER_ID }}'
          VITE_FIREBASE_APP_ID: '${{ secrets.PROD_FIREBASE_APP_ID }}'
          VITE_FIREBASE_MEASUREMENT_ID: '${{ secrets.PROD_FIREBASE_MEASUREMENT_ID }}'
          VITE_FIREBASE_RTDB_INSTANCES: '${{ secrets.PROD_FIREBASE_RTDB_INSTANCES }}'
          VITE_FIREBASE_APP_CHECK_SITE_KEY: '${{ secrets.PROD_FIREBASE_APP_CHECK_SITE_KEY }}'
          VITE_ORDERNOW_URL_SECRET: '${{ secrets.PROD_ORDERNOW_URL_SECRET }}'
          VITE_FIREBASE_STORAGE_BUCKET_DEFAULT: 'selio-eu.firebasestorage.app'
          VITE_FIREBASE_STORAGE_BUCKET_IMAGES: 'selio-eu-images'
          VITE_FIREBASE_STORAGE_BUCKET_VIDEOS: 'selio-eu-videos'
          VITE_FIREBASE_STORAGE_BUCKET_STORAGE: 'selio-eu-storage'
          VITE_FIREBASE_STORAGE_BUCKET_CLOUD: 'selio-eu-cloud'
          VITE_FIREBASE_STORAGE_BUCKET_TEMP: 'selio-eu-temp'
      - name: Deploy
        uses: FirebaseExtended/action-hosting-deploy@v0
        #uses: FirebaseExtended/action-hosting-deploy@79b1a830a9ae85409cb38056dd7761f6f1cb83fd
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_SELIO_EU }}'
          channelId: live
          projectId: selio-eu
