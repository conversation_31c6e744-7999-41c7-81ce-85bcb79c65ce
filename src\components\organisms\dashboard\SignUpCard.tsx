import { Box, Button, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

export default function SignUpCard() {
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        borderRadius: '6px',
        padding: 3,
        boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor:
          theme.palette.mode == 'light' ? 'transparent' : 'background.tinted',
      }}
    >
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 1,
        }}
      >
        <Box>
          <Typography variant="h1">Your</Typography>{' '}
          <Typography variant="h1">Online Orders</Typography>
          <Typography variant="h1">start with us!</Typography>
          <Typography variant="body1" color="custom.gray600" mt={3}>
            Signup now with ARIVA and start taking online orders in minutes.
          </Typography>
        </Box>
        <img
          src="/assets/app.png"
          alt="app-img"
          width={'100px'}
          style={{ marginRight: '10px' }}
        />
      </Box>
      <Button variant="contained" fullWidth sx={{ marginTop: 3 }}>
        Signup with ARIVA
      </Button>
    </Box>
  );
}
