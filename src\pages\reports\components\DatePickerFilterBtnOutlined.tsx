import { forwardRef } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Button, Typography, useForkRef } from '@mui/material';
import { useTranslation } from 'react-i18next';

import type {
  FieldType,
  SingleInputDateRangeFieldProps,
} from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

interface DatePickerFilterBtnProps
  extends SingleInputDateRangeFieldProps<Dayjs> {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

type DatePickerFilterBtnComponent = ((
  props: DatePickerFilterBtnProps & React.RefAttributes<HTMLDivElement>
) => React.JSX.Element) & { fieldType?: FieldType };

const DatePickerFilterBtn = forwardRef(
  (props: DatePickerFilterBtnProps, ref: React.Ref<HTMLElement>) => {
    const {
      setOpen,
      label,
      id,
      disabled,
      InputProps: { ref: containerRef } = {},
      inputProps: { 'aria-label': ariaLabel } = {},
    } = props;

    const handleRef = useForkRef(ref, containerRef);
    const { t } = useTranslation('');

    return (
      <Button
        //@ts-ignore
        variant="outlined-2"
        id={id}
        disabled={disabled}
        ref={handleRef}
        aria-label={ariaLabel}
        onClick={() => setOpen?.(prev => !prev)}
        sx={{ height: '40px' }}
      >
        <Typography variant="body2" sx={{ mr: 1 }} color="custom.gray600">
          {t('dashboard.date')}
        </Typography>
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={400} color="custom.black">
          {label}
        </Typography>

        {/* <KeyboardArrowDownIcon color="disabled" /> */}
      </Button>
    );
  }
) as DatePickerFilterBtnComponent;

DatePickerFilterBtn.fieldType = 'single-input';

export default DatePickerFilterBtn;
