import {
  getDefaultResourceInfo,
  ResourcesInfo,
  resourcesInfo,
} from '~/providers/resources';

export const getResourcePath = (
  resource: string,
  accountId: string,
  meta?: { [key: string]: any }
): string => {
  console.log(
    `getResourcePath: ${resource}`,
    `accountId: ${accountId}`,
    `meta: ${JSON.stringify(meta)}`
  );
  // Get the specific config or the default one
  const resourceInfo =
    resourcesInfo[resource as keyof ResourcesInfo] ??
    getDefaultResourceInfo(resource);

  let template = resourceInfo.pathTemplate;
  let placeholder = '{accountId}';
  if (template.includes(placeholder)) {
    if (!accountId) {
      // Throw error only if the placeholder exists AND accountId is missing
      throw new Error(
        `Account Id is required for resource '${resource}' path template "${template}" but was not provided.`
      );
    }
    template = template.replace(placeholder, accountId);
  }
  placeholder = '{sellPointId}';
  if (template.includes(placeholder)) {
    if (!meta?.sellPointId) {
      // Throw error only if the placeholder exists AND accountId is missing
      throw new Error(
        `SellPoint Id is required for resource '${resource}' path template "${template}" but was not provided.`
      );
    }
    template = template.replace(placeholder, meta.sellPointId);
  }
  // No placeholder, return template as is (handles global resources like 'accounts')
  return template;
};
