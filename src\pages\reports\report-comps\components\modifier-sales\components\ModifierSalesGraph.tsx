import { useCallback, useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import { useGetList } from 'react-admin';

import ReportMultiLineChart from '~/pages/reports/components/ReportMultiLineChart';
import capitalize from '~/utils/capitalize';
import { CurrencyType } from '~/utils/formatNumber';

export default function ModifierSalesGraph({
  data,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
}) {
  return (
    <>
      {!data.datasets || !data.labels ? (
        <></>
      ) : (
        <>
          <Typography
            sx={{
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
            variant="body2"
            fontWeight="500"
            mb={1.5}
          >
            {data.datasets.length >= 3 &&
              `Top ${data.datasets.length} Modifier Sales: Gross Sales`}
          </Typography>
          <ReportMultiLineChart datasets={data.datasets} labels={data.labels} />
        </>
      )}
    </>
  );
}
