import React, { useEffect, useRef, useState } from 'react';
import LanguageIcon from '@mui/icons-material/Language';
import { Box, IconButton, MenuItem } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useTheme } from '../../contexts';

const LanguageSelector = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const languages = ['en', 'ro'];
  const { theme } = useTheme();
  const menuRef: any = useRef(null);

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
    setSelectedLanguage(language);
    setIsOpen(false);
  };

  useEffect(() => {
    setSelectedLanguage(i18n.language);
  }, [i18n.language]);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (menuRef.current && !menuRef?.current?.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const savedLang = localStorage.getItem('language');
    if (savedLang && savedLang !== selectedLanguage) {
      setSelectedLanguage(savedLang);
    }
  }, []);

  return (
    <Box
      position="relative"
      sx={{ cursor: 'pointer', userSelect: 'none', left: 8 }}
      onClick={() => setIsOpen(!isOpen)}
      ref={menuRef}
    >
      <Box
        sx={{
          position: 'absolute',
          top: '100%',
          left: 0,
          width: '100%',
          zIndex: 1,
          borderRadius: '4px',
          backgroundColor: theme.palette.mode == 'light' ? 'white' : '#13131A',
          overflow: 'hidden',
          border:
            theme.palette.mode === 'light'
              ? '1px solid #ccc'
              : '1px solid #2f2f3f',
          mt: '2px',
          maxHeight: isOpen ? '150px' : '0',
          opacity: isOpen ? 1 : 0,
          transition: 'max-height 0.2s, opacity 0.2s',
        }}
      >
        {languages.map(language => (
          <MenuItem
            key={language}
            onClick={() => changeLanguage(language)}
            sx={{
              fontSize: '14px',
              padding: '10px',
              whiteSpace: 'nowrap',
              textAlign: 'center',
              pl: 1.5,
              color: selectedLanguage === language ? '#0168FF' : 'inherit',
            }}
          >
            {language.toUpperCase()}
          </MenuItem>
        ))}
      </Box>
      <Box
        sx={{
          borderRadius: '4px',
          textAlign: 'center',
          fontSize: '14px',
          minWidth: '50px',
        }}
      >
        <IconButton sx={{ pt: 1 }}>
          <LanguageIcon
            sx={{ color: theme.palette.mode == 'light' ? 'gray' : '#fff' }}
          />
        </IconButton>
      </Box>
    </Box>
  );
};

export default LanguageSelector;
