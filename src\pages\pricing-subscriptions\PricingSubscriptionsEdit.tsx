import { useState } from 'react';
import { <PERSON>, But<PERSON>, Theme, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  SimpleForm,
  useGetList,
  useNotify,
  useRedirect,
  useUpdate,
} from 'react-admin';

import ModalHeader from '../../components/molecules/ModalHeader';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import PricingSubscriptionsForm from './components/PricingSubscriptionsForm';
import PricingTable from './components/PricingTable';
import { useTranslation } from 'react-i18next';

export default function PricingSubscriptionsEdit() {
  const { data: subscriptions } = useGetList('pricing-subscriptions');
  const [isPremium, setIsPremium] = useState(false);
  const [step, setStep] = useState<number>(0);
  const [update] = useUpdate();
  const notify = useNotify();
  const redirect = useRedirect();
  const { t } = useTranslation('')

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const currentSubscription = subscriptions && subscriptions[0];

  const handleSave = async () => {
    if (!subscriptions || subscriptions.length === 0) return;

    try {
      await update('pricing-subscriptions', {
        id: currentSubscription.id,
        data: {
          isPremium: isPremium,
        },
      });
      setStep(0);
      notify(
        currentSubscription.isPremium
          ? 'Successfully downgraded.'
          : 'Successfully upgraded.',
        { type: 'info' }
      );
      redirect('/pricing-subscriptions');
    } catch (error) {
      notify('Error while updating subscription', { type: 'warning' });
    }
  };

  const handleClose = () => {
    setStep(0);
    redirect('/pricing-subscriptions');
  };

  return (
    <EditDialog {...getFullscreenModalProps()}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title={
            step === 0
              ? t('pricingSubscriptions.updateSubscription')
              : step === 1
                ? 'Downgrade to Selio for Restaurants Free'
                : step === 2
                  ? 'Downgrade to Selio for Restaurants Free'
                  : subscriptions && subscriptions[0].isPremium
                    ? t('pricingSubscriptions.updateSubscription')
                    : t('pricingSubscriptions.manageSubscription')
          }
          alignCenter={!isXSmall}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexDirection: { xs: 'column', sm: 'row' },
            }}
          >
            <PricingTable />
            {step !== 3 ? (
              <Button
                disabled={
                  subscriptions && isPremium === subscriptions[0].isPremium
                }
                variant="contained"
                onClick={() => {
                  if (step === 0) {
                    if (subscriptions && subscriptions[0].isPremium) {
                      setStep(step + 1);
                    } else {
                      setStep(3);
                    }
                  } else {
                    setStep(step + 1);
                  }
                }}
              >
                Continue
              </Button>
            ) : (
              <Button onClick={handleSave} variant="contained">
                {subscriptions && subscriptions[0].isPremium
                  ? 'Confirm Downgrade'
                  : 'Subscribe'}
              </Button>
            )}
          </Box>
        </ModalHeader>
        <PricingSubscriptionsForm
          step={step}
          setStep={setStep}
          setIsPremium={setIsPremium}
          handleClose={handleClose}
          isEditing={true}
        />
      </SimpleForm>
    </EditDialog>
  );
}
