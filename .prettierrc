{"semi": true, "tabWidth": 2, "printWidth": 80, "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "plugins": ["@ianvs/prettier-plugin-sort-imports"], "importOrder": ["<BUILTIN_MODULES>", "^react$", "^react-dom/(.*)$", "^@airportlabs/(.*)$", "<THIRD_PARTY_MODULES>", "", "^~/(.*)$", "^[./]", "", "<TYPES>"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"]}