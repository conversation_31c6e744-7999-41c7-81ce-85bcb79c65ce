import { Box, Button } from '@mui/material';
import { EditableDatagrid } from '@react-admin/ra-editable-datagrid';
import { List, TextField } from 'react-admin';

import Subsection from '../../components/molecules/Subsection';
import { useTranslation } from 'react-i18next';
import { useTheme } from '~/contexts';

const BankAccountsListInner = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  return (
    <EditableDatagrid
      editForm={<></>}
      bulkActionButtons={false}
      actions={<CustomActions />}
      sx={{
        '& .MuiTableCell-root:last-of-type': {
          textAlign: 'right',
          '& button': {
            visibility: 'visible',
          },
        },
        '& .MuiFormControl-root': {
          margin: 0,
          height: '30px',
          textAlign: 'right',
        },
        '& .MuiTableCell-root': {
          width: '33%',
        },
        '& .MuiTableHead-root .MuiTableCell-root': {
          backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
          borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
        },
      }}
    >
      <TextField source="id" />
      <TextField source="name" textAlign="right" label={t('shared.name')} />
    </EditableDatagrid>
  );
};

export default function BankAccountsList() {
  const { t } = useTranslation();

  return (
    <List
      resource="bankAccounts"
      component="div"
      exporter={false}
      pagination={false}
      sx={{ mt: 1, p: 2, mx: 'auto', width: '95%' }}
    >
      <Subsection
        title={t('bankAccounts.title')}
        subtitle={
          <>
            {t('bankAccounts.description1')}
            <br /> {t('bankAccounts.description2')}
          </>
        }
      >
        <BankAccountsListInner />
      </Subsection>
    </List>
  );
}

const CustomActions = () => {
  const { t } = useTranslation();
  return (
    <Box sx={{ display: 'flex', flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
      <Button sx={{ whiteSpace: 'nowrap' }}>{t('bankAccounts.linkAccount')}</Button>
    </Box>
  );
};
