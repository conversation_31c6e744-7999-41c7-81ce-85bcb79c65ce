import { useMemo, useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import ModalHeader from '~/components/molecules/ModalHeader';

export interface NutritionalValues {
  portion: {
    energy: string;
    fats: number;
    saturatedFats: number;
    carbs: number;
    sugars: number;
    fiber: number;
    protein: number;
    salt: number;
  };
  per100g: {
    energy: string;
    fats: number;
    saturatedFats: number;
    carbs: number;
    sugars: number;
    fiber: number;
    protein: number;
    salt: number;
  };
}

const defaultValues: NutritionalValues = {
  portion: {
    energy: '',
    fats: 0,
    saturatedFats: 0,
    carbs: 0,
    sugars: 0,
    fiber: 0,
    protein: 0,
    salt: 0,
  },
  per100g: {
    energy: '',
    fats: 0,
    saturatedFats: 0,
    carbs: 0,
    sugars: 0,
    fiber: 0,
    protein: 0,
    salt: 0,
  },
};

interface NutritionalValuesModalProps {
  initialValue?: NutritionalValues;
  onClose: (e?: any) => void;
  onSave: (values: NutritionalValues) => void;
}



export default function NutritionalValuesModal({
  initialValue,
  onClose,
  onSave,
}: NutritionalValuesModalProps) {
  const { t } = useTranslation();
  const [values, setValues] = useState<NutritionalValues>(
    initialValue || defaultValues
  );

  const handleChange = (
    section: 'portion' | 'per100g',
    field: string,
    value: string
  ) => {
    setValues(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]:
          field === 'energy' ? value : value === '' ? 0 : parseFloat(value),
      },
    }));
  };

  const textFieldProps = useMemo(() => ({
    InputProps: {
      sx: {
        height: '55px',
        '& input': {
          textAlign: 'right',
          padding: '4px 8px',
        },
      },
    },
    variant: 'outlined' as const,
    size: 'small' as const,
    placeholder: t('shared.enterValue'),
    sx: {
      width: '100%',
      '& .MuiOutlinedInput-root': {
        height: '55px',
        '& fieldset': {
          borderColor: 'transparent',
        },
        '&:hover fieldset': {
          borderColor: 'rgba(0, 0, 0, 0.23)',
        },
        '&.Mui-focused fieldset': {
          borderColor: '#1976d2',
        },
      },
    },
  }), [t]);

  return (
    <Dialog open onClose={onClose} fullWidth={true} maxWidth={'md'}>
      <ModalHeader handleClose={onClose} title="" noBorder>
        <Button variant="contained" onClick={() => onSave(values)}>
          {t('shared.set')}
        </Button>
      </ModalHeader>

      <Box mb={4} pt={0}>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            maxWidth: {
              md: '70%',
              xs: '95%',
            },
            margin: '0 auto',
          }}
        >
          <Table
            sx={{
              border: '1px solid rgba(224, 224, 224, 1)',
              '& .MuiTableCell-root': {
                borderRight: '1px solid rgba(224, 224, 224, 1)',
                padding: '8px 16px',
              },
              '& .MuiTableCell-root:has(.MuiTextField-root)': {
                padding: 0,
              },
              '& .MuiTableCell-root:last-child': {
                borderRight: 'none',
              },
              '& .MuiTableHead-root .MuiTableRow-root': {
                backgroundColor: '#f5f5f5',
              },
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{ fontWeight: 'bold', width: '40%' }}
                ></TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>{t('nutritionalValues.portion')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>{t('nutritionalValues.per100g')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.energy')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    value={values.portion.energy}
                    onChange={e =>
                      handleChange('portion', 'energy', e.target.value)
                    }
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    value={values.per100g.energy}
                    onChange={e =>
                      handleChange('per100g', 'energy', e.target.value)
                    }
                    fullWidth
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.fat')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.fats || ''}
                    onChange={e =>
                      handleChange('portion', 'fats', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.fats || ''}
                    onChange={e =>
                      handleChange('per100g', 'fats', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.saturatedFat')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.saturatedFats || ''}
                    onChange={e =>
                      handleChange('portion', 'saturatedFats', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.saturatedFats || ''}
                    onChange={e =>
                      handleChange('per100g', 'saturatedFats', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.carbohydrates')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.carbs || ''}
                    onChange={e =>
                      handleChange('portion', 'carbs', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.carbs || ''}
                    onChange={e =>
                      handleChange('per100g', 'carbs', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.sugar')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.sugars || ''}
                    onChange={e =>
                      handleChange('portion', 'sugars', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.sugars || ''}
                    onChange={e =>
                      handleChange('per100g', 'sugars', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.fiber')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.fiber || ''}
                    onChange={e =>
                      handleChange('portion', 'fiber', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.fiber || ''}
                    onChange={e =>
                      handleChange('per100g', 'fiber', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.protein')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.protein || ''}
                    onChange={e =>
                      handleChange('portion', 'protein', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.protein || ''}
                    onChange={e =>
                      handleChange('per100g', 'protein', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell component="th" scope="row">
                  {t('nutritionalValues.salt')}
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.portion.salt || ''}
                    onChange={e =>
                      handleChange('portion', 'salt', e.target.value)
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    {...textFieldProps}
                    type="number"
                    value={values.per100g.salt || ''}
                    onChange={e =>
                      handleChange('per100g', 'salt', e.target.value)
                    }
                  />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Dialog>
  );
}
