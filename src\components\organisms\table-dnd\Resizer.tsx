import { useEffect, useState } from 'react';

import { GRID_SIZE } from '../../../pages/floor-plans/SectionEdit';
import styles from './Resizer.module.css';

export enum Direction {
  Top = 'startY',
  Right = 'endX',
  Bottom = 'endY',
  Left = 'startX',
}

interface ReziserProps {
  onResize: (
    direction: 'startX' | 'startY' | 'endX' | 'endY',
    delta: number
  ) => void;
}

const Resizer = ({ onResize }: ReziserProps) => {
  const [direction, setDirection] = useState<
    'startX' | 'startY' | 'endX' | 'endY'
  >('startX');
  const [mouseDown, setMouseDown] = useState<boolean>(false);
  const [gridSnap, setGridSnap] = useState<{
    direction: 'startX' | 'startY' | 'endX' | 'endY';
    value: number;
  }>({ direction: 'startX', value: 0 });

  useEffect(() => {
    const handleMouseMove = (e: any) => {
      if (!direction) return;

      const delta = direction.includes('X') ? e.movementX : e.movementY;

      if (!delta) return;

      const totalDelta =
        direction === gridSnap.direction ? gridSnap.value + delta : delta;

      setGridSnap({ direction, value: totalDelta % GRID_SIZE });

      // we do this so it works well on negative numbers as well
      onResize(direction, totalDelta - (totalDelta % GRID_SIZE));

      // check in the future if this works on different devices
      // const ratio = window.devicePixelRatio;
      // onResize(direction, e.movementX / ratio, e.movementY / ratio);
    };

    if (mouseDown) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [mouseDown, direction, onResize, gridSnap.value, gridSnap.direction]);

  useEffect(() => {
    const handleMouseUp = (e: any) => {
      setMouseDown(false);
    };

    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  const handleMouseDown = (direction: Direction) => () => {
    setDirection(direction);
    setMouseDown(true);
  };

  return (
    <>
      <div
        className={styles.top}
        onMouseDown={handleMouseDown(Direction.Top)}
      />

      <div
        className={styles.right}
        onMouseDown={handleMouseDown(Direction.Right)}
      />

      <div
        className={styles.bottom}
        onMouseDown={handleMouseDown(Direction.Bottom)}
      />

      <div
        className={styles.left}
        onMouseDown={handleMouseDown(Direction.Left)}
      />
    </>
  );
};

export default Resizer;
