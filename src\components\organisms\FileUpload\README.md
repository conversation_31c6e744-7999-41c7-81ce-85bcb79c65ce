# File Upload System

A modular, configurable file upload system that replaces the monolithic `MuiFileUploadInput` component with a clean, maintainable architecture.

## Overview

The new file upload system is built with the following principles:
- **Modular Architecture**: Separated into focused hooks and sub-components
- **Configuration-Driven**: Single configuration object controls all behavior
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Backward Compatibility**: Drop-in replacement for existing components
- **Testability**: Each piece can be tested independently

## Components

### Main Components

- **`FileUploadComponent`**: Core component that orchestrates all functionality
- **`RaFileUploadComponent`**: React-Admin wrapper for form integration

### Sub-Components

- **`FileDropzone`**: Handles drag-and-drop and file selection
- **`FileList`**: Displays uploaded files with sorting and preview
- **`FileItem`**: Individual file display with actions
- **`ValidationDisplay`**: Shows validation errors and warnings

### Hooks

- **`useFileUpload`**: Manages upload operations and progress
- **`useFileValidation`**: Handles file validation logic
- **`useImageEditor`**: Manages image editing workflow
- **`useFileList`**: Manages file list state and operations
- **`useFileLifecycle`**: Handles file lifecycle (temp to permanent)

## Configuration

The system uses a single configuration object that controls all behavior:

```typescript
interface FileUploadConfig {
  // File constraints
  fileType: 'images' | 'videos' | 'public' | 'private';
  multiple: boolean;
  maxFiles: number;
  maxSize: number;
  acceptedTypes: string[];

  // Image processing
  imageConfig?: {
    enableEditor: boolean;
    targetSizes?: Array<{
      width: number;
      height: number;
      key: string;
      name?: string;
    }>;
    quality?: number;
    autoGenerateThumbnail: boolean;
  };

  // Validation
  validation?: {
    minFileCount?: number;
    minFileSize?: number;
    allowInvalidFiles?: boolean;
    customValidation?: (file: File) => string | null;
  };

  // UI behavior
  ui?: {
    variant?: 'default' | 'compact';
    disabled?: boolean;
    readOnly?: boolean;
    placeholder?: string;
    dragAndDrop?: boolean;
  };

  // Callbacks
  callbacks?: {
    onUploadSuccess?: (files: UploadedFile[]) => void;
    onValidationError?: (errors: ValidationError[]) => void;
    onFileUploaded?: (file: UploadedFile) => void;
    // ... more callbacks
  };
}
```

## Usage Examples

### Basic Usage

```tsx
import { FileUploadComponent, createConfig } from '~/components/organisms/FileUpload';

function MyComponent() {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  
  const config = createConfig.itemImages([
    { width: 400, height: 300, key: 'medium' },
    { width: 800, height: 600, key: 'large' },
  ]);

  return (
    <FileUploadComponent
      value={files}
      onChange={setFiles}
      config={config}
      label="Upload Images"
    />
  );
}
```

### React-Admin Integration

```tsx
import { RaFileUploadComponent, createConfig } from '~/components/organisms/FileUpload';

function MyForm() {
  return (
    <SimpleForm>
      <RaFileUploadComponent
        source="images"
        config={createConfig.profileImage()}
        label="Profile Picture"
      />
    </SimpleForm>
  );
}
```

### Custom Configuration

```tsx
const customConfig: FileUploadConfig = {
  fileType: 'images',
  multiple: true,
  maxFiles: 5,
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: ['image/jpeg', 'image/png'],
  
  imageConfig: {
    enableEditor: true,
    targetSizes: [
      { width: 200, height: 200, key: 'thumbnail' },
      { width: 800, height: 600, key: 'display' },
    ],
    quality: 0.9,
  },
  
  validation: {
    minFileCount: 1,
    customValidation: (file) => {
      if (file.name.includes('temp')) {
        return 'Temporary files are not allowed';
      }
      return null;
    },
  },
  
  ui: {
    variant: 'default',
    placeholder: 'Drop your images here',
  },
  
  callbacks: {
    onUploadSuccess: (files) => {
      console.log('Uploaded:', files);
    },
  },
};
```

## Migration Guide

### From MuiFileUploadInput

Replace:
```tsx
<MuiFileUploadInput
  value={files}
  onChange={setFiles}
  fileType="images"
  multiple={true}
  maxFiles={10}
  enableImageEditor={true}
  imageEditorConfig={editorConfig}
/>
```

With:
```tsx
<FileUploadComponent
  value={files}
  onChange={setFiles}
  config={{
    fileType: 'images',
    multiple: true,
    maxFiles: 10,
    imageConfig: {
      enableEditor: true,
      editorConfig: editorConfig,
    },
  }}
/>
```

### From FileUploadInput (React-Admin)

Replace:
```tsx
<FileUploadInput
  source="images"
  fileType="images"
  multiple={true}
  enableImageEditor={true}
/>
```

With:
```tsx
<RaFileUploadComponent
  source="images"
  config={{
    fileType: 'images',
    multiple: true,
    imageConfig: {
      enableEditor: true,
    },
  }}
/>
```

## Features Preserved

All existing functionality is preserved:
- ✅ Bucket system (images, videos, public, private, temp)
- ✅ Image editor with cropping and variants
- ✅ Drag and drop file selection
- ✅ File validation and error handling
- ✅ Progress indicators
- ✅ File preview and thumbnails
- ✅ Temporary file lifecycle management
- ✅ React-Admin integration
- ✅ Private file context support

## Benefits

1. **Maintainability**: Code is organized into focused, single-responsibility modules
2. **Testability**: Each hook and component can be tested independently
3. **Reusability**: Sub-components and hooks can be used in other contexts
4. **Performance**: Better optimization opportunities with smaller components
5. **Developer Experience**: Much easier to understand, modify, and extend
6. **Type Safety**: Comprehensive TypeScript support with better IntelliSense
7. **Configuration**: Single source of truth for all component behavior
