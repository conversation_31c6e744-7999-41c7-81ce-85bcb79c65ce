/**
 * Utility functions for handling file names and paths
 */

/**
 * Sanitizes a filename by replacing invalid characters and limiting length
 * @param filename - The raw filename to sanitize
 * @param maxLength - Maximum length for the filename (default: 200)
 * @returns A clean, filesystem-safe filename
 */
export const sanitizeFilename = (filename: string, maxLength: number = 200): string => {
    return filename
        .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid chars with underscore
        .replace(/\s+/g, '_') // Replace spaces with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
        .substring(0, maxLength); // Limit length
};

/**
 * Generates a clean filename for QR code files using location, floor plan, and table info
 * @param locationName - Name of the location/restaurant
 * @param floorPlanName - Name of the floor plan
 * @param tableIdentifier - Table number or tag
 * @param extension - File extension (without dot)
 * @returns A sanitized filename in format: LocationName-FloorPlanName-TableIdentifier.extension
 */
export const generateQRCodeFilename = (
    locationName: string,
    floorPlanName: string,
    tableIdentifier: string,
    extension: string
): string => {
    const rawFilename = `${locationName}-${floorPlanName}-${tableIdentifier}`;
    const cleanFilename = sanitizeFilename(rawFilename);
    return `${cleanFilename}.${extension}`;
};

/**
 * Generates a filename from a QR code title and subtitle (used in modals)
 * @param title - QR code title (format: "LocationName - FloorPlanName")
 * @param subtitle - QR code subtitle (table identifier)
 * @param extension - File extension (without dot)
 * @returns A sanitized filename
 */
export const generateQRCodeFilenameFromModal = (
    title: string,
    subtitle: string,
    extension: string
): string => {
    const titleParts = title.split(' - ');
    const locationName = titleParts[0] || '';
    const floorPlanName = titleParts[1] || '';

    return generateQRCodeFilename(locationName, floorPlanName, subtitle, extension);
};
