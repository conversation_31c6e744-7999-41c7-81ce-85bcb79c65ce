import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function DiscountsGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency?: CurrencyType;
}) {
  const { t } = useTranslation();
  if (!data.datasets || !data.labels) {
    return <></>;
  }

  return (
    <>
      <Typography
        sx={{
          display: { xs: 'none', md: 'block' },
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        fontWeight="500"
        mb={1.5}
      >
        {data.datasets.length >= 3 &&
          `Top ${data.datasets.length} ${t('discounts.topDiscounts')}`}
      </Typography>
      <ReportMultiLineChart
        datasets={data.datasets}
        labels={data.labels}
        formatData={data => formatNumber(data, currency)}
      />
    </>
  );
}
