import React, {
  createContext,
  Dispatch,
  useContext,
  useMemo,
  useState,
} from 'react';

import { darkTheme } from '../styles/darkTheme';
import { lightTheme } from '../styles/lightTheme';

const lightColorsArray = [
  ['#006aFF', '#cce1ff', '#f2f2f2', '#B0E5FF', '#87B0F5', '#96C2FF', '#A2D3FF'],
  ['#00AA07', '#00F946', '#5CDFA8', '#B1E2CE'],
  ['#006AFF', '#737373'],
  ['#49A4D5', '#E52817', '#FFCC00', '#4AB300', '#88519F', '#6366f1'],
];

const darkColorsArray = [
  ['#3399FF', '#66CCFF', '#A6A6A6', '#6699FF', '#578BFF', '#6699FF', '#80BFFF'],
  ['#008005', '#00C737', '#4DB28B', '#8FB69E'],
  ['#006AFF', '#737373'],
  ['#49A4D5', '#E52817', '#FFCC00', '#4AB300', '#88519F', '#6366f1'],
];

interface ContextProviderProps {
  children: React.ReactNode;
  initialValue: any;
}

interface ThemeContextI {
  theme: any;
  toggleTheme: Dispatch<any>;
  getChartColours: (n: number, colorSet?: number) => string[];
}

const defaultState: ThemeContextI = {
  theme: lightTheme,
  toggleTheme: () => {},
  getChartColours: () => [],
};

const ThemeContext = createContext<ThemeContextI>(defaultState);

const ThemeProvider = (props: ContextProviderProps) => {
  const { children, initialValue } = props;
  const [theme, setTheme] = useState<any>(initialValue);

  const getChartColours: ThemeContextI['getChartColours'] = (
    number,
    colorSet = 0
  ) => {
    const colorsArray =
      theme.palette.mode === 'light'
        ? lightColorsArray[colorSet]
        : darkColorsArray[colorSet];

    if (number <= colorsArray.length) return colorsArray.slice(0, number);

    const multiplier = Math.ceil(number / colorsArray.length);
    return [].concat(...Array(multiplier).fill(colorsArray)).slice(0, number);
  };

  const passValue: ThemeContextI = useMemo(
    () => ({
      theme,
      toggleTheme: () =>
        setTheme((prevValue: any) =>
          prevValue.palette.mode == 'dark' ? lightTheme : darkTheme
        ),
      getChartColours,
    }),
    [theme]
  );

  return (
    <ThemeContext.Provider value={passValue}>{children}</ThemeContext.Provider>
  );
};

const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export { ThemeProvider, useTheme };
