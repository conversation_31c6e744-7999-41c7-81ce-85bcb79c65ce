import { Box, Button, Typography } from '@mui/material';

import SlimBanner from '~/components/molecules/SlimBanner';

const NotifyUpdate: React.FC = () => {
  return (
    <SlimBanner permanent={true} type="warning">
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          gap: 2,
          alignItems: 'center',
          width: '100%',
        }}
      >
        {/* @ts-ignore */}
        <Typography variant="label">
          A new version of the application is available. Please update.
        </Typography>
        <Button
          color="inherit"
          size="small"
          sx={{ p: 0 }}
          onClick={() => window.location.reload()}
        >
          Update
        </Button>
      </Box>
    </SlimBanner>
  );
};

export default NotifyUpdate;
