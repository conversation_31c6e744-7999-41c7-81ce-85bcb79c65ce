import { ValidationError, FileUploadConfig } from '../types';

/**
 * Validate a single file against the configuration
 */
export const validateFile = (
  file: File,
  config: FileUploadConfig,
  existingFiles: File[] = []
): ValidationError[] => {
  const errors: ValidationError[] = [];
  const fileId = `${file.name}-${file.size}-${file.lastModified}`;

  // File size validation
  if (file.size > config.maxSize) {
    errors.push({
      id: `${fileId}-size`,
      message: `File "${file.name}" is too large. Maximum size is ${formatFileSize(config.maxSize)}.`,
      severity: 'error',
      fileName: file.name,
      file,
    });
  }

  // Minimum file size validation
  if (config.validation?.minFileSize && file.size < config.validation.minFileSize) {
    errors.push({
      id: `${fileId}-min-size`,
      message: `File "${file.name}" is too small. Minimum size is ${formatFileSize(config.validation.minFileSize)}.`,
      severity: 'error',
      fileName: file.name,
      file,
    });
  }

  // File type validation
  if (!config.acceptedTypes.includes(file.type)) {
    errors.push({
      id: `${fileId}-type`,
      message: `File "${file.name}" has an unsupported type. Accepted types: ${config.acceptedTypes.join(', ')}.`,
      severity: 'error',
      fileName: file.name,
      file,
    });
  }

  // Custom validation
  if (config.validation?.customValidation) {
    const customError = config.validation.customValidation(file);
    if (customError) {
      errors.push({
        id: `${fileId}-custom`,
        message: customError,
        severity: 'error',
        fileName: file.name,
        file,
      });
    }
  }

  return errors;
};

/**
 * Validate multiple files against the configuration
 */
export const validateFiles = (
  files: File[],
  config: FileUploadConfig,
  existingFiles: File[] = []
): ValidationError[] => {
  const errors: ValidationError[] = [];
  const allFiles = [...existingFiles, ...files];

  // File count validation
  if (!config.multiple && files.length > 1) {
    errors.push({
      id: 'multiple-not-allowed',
      message: 'Only one file is allowed.',
      severity: 'error',
    });
  }

  if (allFiles.length > config.maxFiles) {
    errors.push({
      id: 'max-files-exceeded',
      message: `Too many files. Maximum allowed: ${config.maxFiles}.`,
      severity: 'error',
    });
  }

  // Minimum file count validation
  if (config.validation?.minFileCount && allFiles.length < config.validation.minFileCount) {
    errors.push({
      id: 'min-files-not-met',
      message: `Not enough files. Minimum required: ${config.validation.minFileCount}.`,
      severity: 'warning',
    });
  }

  // Validate each file individually
  files.forEach(file => {
    const fileErrors = validateFile(file, config, existingFiles);
    errors.push(...fileErrors);
  });

  // Check for duplicate files
  const fileMap = new Map<string, File[]>();
  allFiles.forEach(file => {
    const key = `${file.name}-${file.size}`;
    if (!fileMap.has(key)) {
      fileMap.set(key, []);
    }
    fileMap.get(key)!.push(file);
  });

  fileMap.forEach((duplicateFiles, key) => {
    if (duplicateFiles.length > 1) {
      const fileName = duplicateFiles[0].name;
      errors.push({
        id: `duplicate-${key}`,
        message: `Duplicate file detected: "${fileName}".`,
        severity: 'warning',
        fileName,
      });
    }
  });

  return errors;
};

/**
 * Filter files based on validation results
 */
export const filterValidFiles = (
  files: File[],
  validationErrors: ValidationError[],
  allowInvalidFiles: boolean = false
): File[] => {
  if (allowInvalidFiles) {
    return files;
  }

  const errorFiles = new Set(
    validationErrors
      .filter(error => error.severity === 'error' && error.file)
      .map(error => error.file!)
  );

  return files.filter(file => !errorFiles.has(file));
};

/**
 * Check if validation errors contain any errors (not just warnings)
 */
export const hasValidationErrors = (errors: ValidationError[]): boolean => {
  return errors.some(error => error.severity === 'error');
};

/**
 * Group validation errors by severity
 */
export const groupValidationErrors = (errors: ValidationError[]) => {
  const grouped = {
    errors: errors.filter(error => error.severity === 'error'),
    warnings: errors.filter(error => error.severity === 'warning'),
  };

  return {
    ...grouped,
    hasErrors: grouped.errors.length > 0,
    hasWarnings: grouped.warnings.length > 0,
    total: errors.length,
  };
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get validation summary message
 */
export const getValidationSummary = (errors: ValidationError[]): string => {
  const grouped = groupValidationErrors(errors);

  if (!grouped.hasErrors && !grouped.hasWarnings) {
    return 'All files are valid.';
  }

  const parts: string[] = [];

  if (grouped.hasErrors) {
    parts.push(`${grouped.errors.length} error${grouped.errors.length > 1 ? 's' : ''}`);
  }

  if (grouped.hasWarnings) {
    parts.push(`${grouped.warnings.length} warning${grouped.warnings.length > 1 ? 's' : ''}`);
  }

  return `Found ${parts.join(' and ')}.`;
};

/**
 * Validate file type configuration
 */
export const validateFileTypeConfiguration = (config: FileUploadConfig): string[] => {
  const errors: string[] = [];

  // Check if file type matches accepted types
  const fileTypeMap = {
    images: ['image/'],
    videos: ['video/'],
    public: ['application/', 'text/', 'image/'],
    private: ['application/', 'text/', 'image/'],
  };

  const expectedPrefixes = fileTypeMap[config.fileType] || [];
  const hasMatchingTypes = config.acceptedTypes.some(type =>
    expectedPrefixes.some(prefix => type.startsWith(prefix))
  );

  if (!hasMatchingTypes) {
    errors.push(`Accepted types don't match file type "${config.fileType}"`);
  }

  // Validate image-specific configuration
  if (config.fileType === 'images') {
    if (!config.acceptedTypes.some(type => type.startsWith('image/'))) {
      errors.push('Image file type must include image MIME types');
    }

    if (config.imageConfig?.enableEditor && !config.imageConfig.targetSizes) {
      // This is actually valid - editor can be enabled without target sizes for manual cropping
    }
  } else {
    // Non-image file types shouldn't have image config
    if (config.imageConfig?.enableEditor) {
      errors.push(`Image editor is not supported for file type "${config.fileType}"`);
    }
  }

  return errors;
};
