import {
  ReportCommonFields,
  ReportType,
  ReportTypeCalculatedSummableFields,
  ReportTypeSpecificFields,
  ReportTypeSummableFields,
} from './types';

export const reportCommonFields: Array<
  keyof ReportCommonFields<keyof ReportType>
> = [
  'reportType',
  'date',
  'dayOfWeek@number',
  'year@number',
  'quarter@number',
  'month@number',
  'week@number',
  'weekYear@number',
  'day@number',
  'currency',
  'hourOfDay@number',
  'whatDay',
  'member',
  'memberId',
  'section',
  'serviceType',
  'source',
];

export const reportSpecificFields: {
  [K in keyof ReportType]: Array<keyof ReportTypeSpecificFields[K]>;
} = {
  sales: [],
  payments: ['type', 'field1', 'field2'],
  groups: ['vat@number', 'id'],
  items: [
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  modifiers: [
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  giftCards: [
    'vat@number',
    'type',
    'price@number',
    'discountName',
    'promotionName',
  ],
  extraCharges: ['vat@number', 'name', 'id', 'price@number', 'promotionName'],
  discounts: ['vat@number', 'name'],
  coupons: ['vat@number', 'name'],
  promotions: ['vat@number', 'name'],
  tips: ['vat@number', 'price@number'],
  compedItems: [
    'reason',
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  compedModifiers: [
    'reason',
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  compedGiftCards: [
    'reason',
    'vat@number',
    'type',
    'price@number',
    'discountName',
    'promotionName',
  ],
  compedExtraCharges: [
    'reason',
    'vat@number',
    'name',
    'id',
    'price@number',
    'promotionName',
  ],
  compedTips: ['reason', 'vat@number', 'price@number'],
  voids: [
    'type',
    'reason',
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
  ],
  teamRevenue: [],
  vat: ['vat@number'],
  topGroups: ['id'],
  topItems: ['id'],
  topModifiers: ['id'],
  transactions: ['id'],
  pmsItems: [
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  pmsModifiers: [
    'vat@number',
    'groupId',
    'prepStation',
    'prepStationId',
    'id',
    'measureUnit',
    'variant',
    'price@number',
    'discountName',
    'promotionName',
  ],
  pmsGiftCards: [
    'vat@number',
    'type',
    'price@number',
    'discountName',
    'promotionName',
  ],
  pmsExtraCharges: [
    'vat@number',
    'name',
    'id',
    'price@number',
    'promotionName',
  ],
  pmsTips: ['vat@number', 'price@number'],
  orderNowOrders: ['id'],
};

export const reportSummableFields: {
  [K in keyof ReportType]: Array<keyof ReportTypeSummableFields[K]>;
} = {
  sales: [
    'itemsQty',
    'itemsValue',
    'modifiersQty',
    'modifiersValue',
    'giftCardsQty',
    'giftCardsValue',
    'extraChargesQty',
    'extraChargesValue',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'tipsCashValue',
    'tipsNonCashValue',
    'bills',
    'payments',
    'prepStations',
    'covers',
    'voidValue',
    'compValue',
    'compedBills',
    'pmsBills',
  ],
  payments: ['quantity', 'value'],
  groups: [
    'itemsQty',
    'itemsValue',
    'modifiersQty',
    'modifiersValue',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
  ],
  items: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  modifiers: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  giftCards: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  extraCharges: ['promotionsValue', 'quantity', 'value'],
  discounts: [
    'itemsQty',
    'itemsValue',
    'modifiersQty',
    'modifiersValue',
    'giftCardsQty',
    'giftCardsValue',
  ],
  coupons: [
    'itemsQty',
    'itemsValue',
    'modifiersQty',
    'modifiersValue',
    'giftCardsQty',
    'giftCardsValue',
  ],
  promotions: [
    'itemsQty',
    'itemsValue',
    'modifiersQty',
    'modifiersValue',
    'giftCardsQty',
    'giftCardsValue',
    'extraChargesQty',
    'extraChargesValue',
  ],
  tips: ['quantity', 'value', 'tipsCashValue', 'tipsNonCashValue'],
  compedItems: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  compedModifiers: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  compedGiftCards: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  compedExtraCharges: ['promotionsValue', 'quantity', 'value'],
  compedTips: ['quantity', 'value', 'tipsCashValue', 'tipsNonCashValue'],
  voids: ['quantity', 'value'],
  teamRevenue: ['payments', 'comps', 'netSalesValue'],
  vat: [
    'itemsValue',
    'modifiersValue',
    'giftCardsValue',
    'extraChargesValue',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'tipsValue',
    'baseValue',
    'finalValue',
    'nettoValue',
    'vatValue',
  ],
  topGroups: [
    'bills',
    'billPercentage',
    'quantity',
    'value',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
  ],
  topItems: [
    'bills',
    'billPercentage',
    'quantity',
    'value',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
  ],
  topModifiers: [
    'bills',
    'billPercentage',
    'quantity',
    'value',
    'discountsValue',
    'couponsValue',
    'promotionsValue',
  ],
  transactions: [],
  pmsItems: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  pmsModifiers: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  pmsGiftCards: [
    'discountsValue',
    'couponsValue',
    'promotionsValue',
    'quantity',
    'value',
  ],
  pmsExtraCharges: ['promotionsValue', 'quantity', 'value'],
  pmsTips: ['quantity', 'value', 'tipsCashValue', 'tipsNonCashValue'],
  orderNowOrders: [],
};

export const reportCalculatedSummableFields: {
  [K in keyof ReportType]: Array<keyof ReportTypeCalculatedSummableFields[K]>;
} = {
  sales: [
    'value@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue',
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)',
    'tipsValue@eval:$tipsCashValue+$tipsNonCashValue',
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+($tipsCashValue+$tipsNonCashValue)',
  ],
  payments: [],
  groups: [
    'value@eval:$itemsValue+$modifiersValue',
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:($itemsValue+$modifiersValue)-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  items: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  modifiers: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  giftCards: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  extraCharges: [
    'reductionValue@eval:$promotionsValue',
    'netValue@eval:$value-$promotionsValue',
  ],
  discounts: ['totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue'],
  coupons: ['totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue'],
  promotions: [
    'totalValue@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue',
  ],
  tips: [],
  compedItems: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  compedModifiers: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  compedGiftCards: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  compedExtraCharges: [
    'reductionValue@eval:$promotionsValue',
    'netValue@eval:$value-$promotionsValue',
  ],
  compedTips: [],
  voids: [],
  teamRevenue: [],
  vat: [
    'value@eval:$itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue',
    'netValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)',
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue',
  ],
  topGroups: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
    'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)',
  ],
  topItems: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
    'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)',
  ],
  topModifiers: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
    'scoreValue@eval:(((($bills-$min(bills))/($max(bills)-$min(bills)))+0.01)**0.25)*(((($billPercentage-$min(billPercentage))/($max(billPercentage)-$min(billPercentage)))+0.01)**0.20)*(((($quantity-$min(quantity))/($max(quantity)-$min(quantity)))+0.01)**0.20)*(((($value-$min(value))/($max(value)-$min(value)))+0.01)**0.15)*(((($netValue-$min(netValue))/($max(netValue)-$min(netValue)))+0.01)**0.20)',
  ],
  transactions: [
    'subTotalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue)-($discountsValue-$orderDiscountValue)',
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue',
  ],
  pmsItems: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  pmsModifiers: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  pmsGiftCards: [
    'reductionValue@eval:$discountsValue+$couponsValue+$promotionsValue',
    'netValue@eval:$value-($discountsValue+$couponsValue+$promotionsValue)',
  ],
  pmsExtraCharges: [
    'reductionValue@eval:$promotionsValue',
    'netValue@eval:$value-$promotionsValue',
  ],
  pmsTips: [],
  orderNowOrders: [
    'subTotalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue)-($discountsValue-$orderDiscountValue)',
    'totalValue@eval:($itemsValue+$modifiersValue+$giftCardsValue+$extraChargesValue)-($discountsValue+$couponsValue+$promotionsValue)+$tipsValue',
  ],
};
