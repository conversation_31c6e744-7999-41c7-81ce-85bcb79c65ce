import React from 'react';
import { Box, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';
import { Table } from '../table-dnd/types';

interface FloorPlanTableProps {
  table: Table;
  floorPlanLabel: string;
  children?: React.ReactNode;
  selected?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  showLabel?: boolean;
  labelColor?: string;
  labelSize?: number;
  mode?: 'selection' | 'qr' | 'basic';
}

const FloorPlanTable = React.forwardRef<
  HTMLDivElement,
  FloorPlanTableProps & React.HTMLAttributes<HTMLDivElement>
>(
  (
    {
      table,
      floorPlanLabel,
      children,
      selected = false,
      disabled = false,
      onClick,
      showLabel = true,
      labelColor,
      labelSize = 14,
      mode = 'basic',
      ...rest
    },
    ref
  ) => {
    const { theme } = useTheme();
    const { startX, startY, endX, endY } = table.position;

    const getTableStyles = () => {
      const baseStyles = {
        position: 'absolute' as const,
        left: startX,
        top: startY,
        width: endX - startX,
        height: endY - startY,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column' as const,
        borderRadius: table.shape === 'circle' ? '50%' : '6px',
        cursor: disabled ? 'not-allowed' : onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease',
        userSelect: 'none' as const,
        zIndex: selected ? 10 : disabled ? 0 : 1,
        boxSizing: 'border-box' as const,
      };

      // Safe access to theme properties with fallbacks
      const primaryMain = theme?.palette?.primary?.main || '#1976d2';
      const primaryDark = theme?.palette?.primary?.dark || '#1565c0';
      const customDraggableTable =
        theme?.palette?.custom?.draggableTable || '#ffffff';

      if (disabled) {
        return {
          ...baseStyles,
          backgroundColor: '#e0e0e0', // Medium/light gray for disabled
          border: '2px dashed #bdbdbd',
          opacity: 1,
          '&:hover': {
            backgroundColor: '#e0e0e0',
            border: '2px dashed #bdbdbd',
          },
        };
      } else if (selected && mode === 'selection') {
        return {
          ...baseStyles,
          backgroundColor: primaryMain,
          boxShadow: `inset 0 0 0 2px ${primaryDark}, 0 2px 8px ${primaryMain}40`,
          border: `2px solid ${primaryDark}`,
        };
      } else {
        return {
          ...baseStyles,
          backgroundColor: customDraggableTable,
          border: '1px solid #ddd',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            backgroundColor: '#9c9c9c',
            border: '1px solid rgba(0, 0, 0, 0.15)',
            boxShadow:
              'inset 0 0 0 2px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.15)',
          },
        };
      }
    };

    const getTextColor = () => {
      if (labelColor) return labelColor;
      if (disabled) return '#757575'; // Medium gray text for disabled
      if (selected && mode === 'selection') return 'white'; // White text for selected
      return 'white'; // White text for all modes (consistent styling)
    };

    const getTableLabel = () => {
      return table.tag ? table.tag : `${floorPlanLabel}${table.number}`;
    };

    return (
      <Box
        ref={ref}
        sx={getTableStyles()}
        onClick={disabled ? undefined : onClick}
        {...rest}
      >
        {showLabel && (
          <Typography
            variant="caption"
            fontSize={labelSize}
            sx={{
              display: 'block',
              textAlign: 'center',
              fontWeight: 'bold',
              color: getTextColor(),
              mb: children ? 0.5 : 0,
            }}
          >
            {getTableLabel()}
          </Typography>
        )}
        {children}
      </Box>
    );
  }
);

FloorPlanTable.displayName = 'FloorPlanTable';

export default FloorPlanTable;

// Export the helper function for use in child components
export const getTableLabelHelper = (table: Table, floorPlanLabel: string) => {
  return table.tag ? table.tag : `${floorPlanLabel}${table.number}`;
};
