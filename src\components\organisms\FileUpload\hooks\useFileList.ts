import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { UploadedFile } from '~/types/fileUpload';
import { FileListState } from '../types';
import { isValidUploadedFile } from '../utils/fileUploadHelpers';

/**
 * Hook for managing file list state and operations
 */
export const useFileList = (
    initialFiles: UploadedFile[] = [],
    onChange?: (files: UploadedFile[]) => void
) => {
    const [state, setState] = useState<FileListState>({
        files: initialFiles.filter(isValidUploadedFile),
        draggedIndex: null,
        hoveredIndex: null,
    });

    const isInitialRender = useRef(true);

    // Call onChange when files change (but not on initial render)
    useEffect(() => {
        if (isInitialRender.current) {
            isInitialRender.current = false;
            return;
        }
        onChange?.(state.files);
    }, [state.files, onChange]);

    // Update files when external value changes
    const updateFiles = useCallback((newFiles: UploadedFile[]) => {
        const validFiles = newFiles.filter(isValidUploadedFile);
        setState(prev => ({ ...prev, files: validFiles }));
    }, []);

    // Add files to the list
    const addFiles = useCallback((newFiles: UploadedFile[]) => {
        const validFiles = newFiles.filter(isValidUploadedFile);
        setState(prev => {
            const updatedFiles = [...prev.files, ...validFiles];
            return { ...prev, files: updatedFiles };
        });
    }, []);

    // Add a single file
    const addFile = useCallback((file: UploadedFile) => {
        if (isValidUploadedFile(file)) {
            addFiles([file]);
        }
    }, [addFiles]);

    // Remove file by index
    const removeFile = useCallback((index: number) => {
        setState(prev => {
            const updatedFiles = prev.files.filter((_, i) => i !== index);
            return { ...prev, files: updatedFiles };
        });
    }, []);

    // Remove file by filename
    const removeFileByName = useCallback((filename: string) => {
        setState(prev => {
            const updatedFiles = prev.files.filter(file => file.fn !== filename);
            return { ...prev, files: updatedFiles };
        });
    }, []);

    // Replace file at index
    const replaceFile = useCallback((index: number, newFile: UploadedFile) => {
        if (!isValidUploadedFile(newFile)) return;

        setState(prev => {
            const updatedFiles = [...prev.files];
            updatedFiles[index] = newFile;
            return { ...prev, files: updatedFiles };
        });
    }, []);

    // Reorder files (for drag and drop)
    const reorderFiles = useCallback((fromIndex: number, toIndex: number) => {
        setState(prev => {
            const updatedFiles = [...prev.files];
            const [movedFile] = updatedFiles.splice(fromIndex, 1);
            updatedFiles.splice(toIndex, 0, movedFile);
            return { ...prev, files: updatedFiles };
        });
    }, []);

    // Clear all files
    const clearFiles = useCallback(() => {
        setState(prev => {
            return { ...prev, files: [] };
        });
    }, []);

    // Set dragged index for drag and drop
    const setDraggedIndex = useCallback((index: number | null) => {
        setState(prev => ({ ...prev, draggedIndex: index }));
    }, []);

    // Set hovered index for drag and drop
    const setHoveredIndex = useCallback((index: number | null) => {
        setState(prev => ({ ...prev, hoveredIndex: index }));
    }, []);

    // Get file by index
    const getFile = useCallback((index: number): UploadedFile | undefined => {
        return state.files[index];
    }, [state.files]);

    // Get file by filename
    const getFileByName = useCallback((filename: string): UploadedFile | undefined => {
        return state.files.find(file => file.fn === filename);
    }, [state.files]);

    // Check if file exists
    const hasFile = useCallback((filename: string): boolean => {
        return state.files.some(file => file.fn === filename);
    }, [state.files]);

    // Get files by type
    const getFilesByType = useCallback((type: 'i' | 'v' | 's' | 'p'): UploadedFile[] => {
        return state.files.filter(file => file.t === type);
    }, [state.files]);

    // Get temporary files
    const getTempFiles = useCallback((): UploadedFile[] => {
        return state.files.filter(file => file.x === true);
    }, [state.files]);

    // Get permanent files
    const getPermanentFiles = useCallback((): UploadedFile[] => {
        return state.files.filter(file => !file.x);
    }, [state.files]);

    // Computed values
    const fileCount = useMemo(() => state.files.length, [state.files]);
    const isEmpty = useMemo(() => fileCount === 0, [fileCount]);
    const hasFiles = useMemo(() => fileCount > 0, [fileCount]);
    const tempFileCount = useMemo(() => getTempFiles().length, [getTempFiles]);
    const permanentFileCount = useMemo(() => getPermanentFiles().length, [getPermanentFiles]);

    // Get total size of all files (estimated for uploaded files)
    const totalSize = useMemo(() => {
        // Note: UploadedFile doesn't have size property, so this is an estimation
        // In a real implementation, you might want to fetch sizes asynchronously
        return state.files.length * 1024 * 1024; // Rough estimate
    }, [state.files]);

    return {
        // State
        files: state.files,
        draggedIndex: state.draggedIndex,
        hoveredIndex: state.hoveredIndex,

        // Actions
        updateFiles,
        addFiles,
        addFile,
        removeFile,
        removeFileByName,
        replaceFile,
        reorderFiles,
        clearFiles,
        setDraggedIndex,
        setHoveredIndex,

        // Getters
        getFile,
        getFileByName,
        hasFile,
        getFilesByType,
        getTempFiles,
        getPermanentFiles,

        // Computed values
        fileCount,
        isEmpty,
        hasFiles,
        tempFileCount,
        permanentFileCount,
        totalSize,
    };
};
