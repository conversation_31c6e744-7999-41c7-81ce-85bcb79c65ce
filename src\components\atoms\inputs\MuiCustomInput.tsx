import { useMemo, useState } from 'react';
import {
  Autocomplete,
  AutocompleteRenderInputParams,
  Box,
  TextField,
  TextFieldProps,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';

import { useTheme } from '../../../contexts';

interface MuiCustomInputI extends Partial<TextFieldProps<'outlined'>> {
  inputStyle?: any;
  type?: 'text' | 'number' | 'autocomplete-select';
  optional?: boolean;
  renderInput?: (params: AutocompleteRenderInputParams) => React.ReactNode;
  options?: any[];
  getOptionLabel?: (option: any) => string;
}

export default function MuiCustomInput({
  label,
  disabled,
  placeholder = '',
  type = 'text',
  inputStyle = {},
  error,
  required,
  optional,
  ...props
}: MuiCustomInputI) {
  const [focused, setFocused] = useState<boolean>(false);
  const { theme } = useTheme();
  const custom = theme.palette.custom;
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const disabledBg =
    theme.palette.mode == 'light'
      ? custom.fieldBg
      : theme.palette.background.tinted;

  const InputComponent = useMemo(() => {
    switch (type) {
      case 'text':
        return TextField;
      case 'number':
        return TextField;
      case 'autocomplete-select':
        return Autocomplete;
      default:
        return <></>;
    }
  }, [type]);

  return (
    <Box
      sx={{
        width: '100%',
        position: 'relative',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        border: `1px solid ${custom.gray400}`,
        borderBottom: error
          ? `1px solid ${theme.palette.error.main}`
          : focused
            ? `1px solid ${theme.palette.primary.main}`
            : `1px solid ${custom.gray400}`,
        zIndex: focused ? 2 : 0,
        marginTop: '-1px',
        bgcolor:
          disabled && theme.palette.mode === 'light'
            ? 'custom.gray400'
            : 'custom.fieldBg',
        backgroundImage: disabled
          ? `repeating-linear-gradient(135deg,transparent,transparent 1px,${disabledBg} 1px,${disabledBg} 5px,transparent 5px,transparent 6px,${disabledBg} 6px,${disabledBg} 10px)`
          : 'none',
        backgroundSize: '7px 7px',
      }}
    >
      <Box
        sx={{
          width: isXSmall ? '100%' : '200px',
          height: isXSmall ? '35px' : 'unset',
          bgcolor: error
            ? 'error.light'
            : focused
              ? 'primary.light'
              : 'background.tinted',
          display: 'flex',
          alignItems: 'center',
          px: 2,
        }}
      >
        <Typography
          // @ts-ignore
          variant="label"
          color={error ? 'error.main' : 'inherit'}
        >
          {label} {required ? ' *' : ''}{' '}
          {optional ? (
            <Typography variant="caption" fontSize={10} color="textDisabled">
              (optional)
            </Typography>
          ) : (
            ''
          )}
        </Typography>
      </Box>
      {/* @ts-ignore */}
      <InputComponent
        variant="outlined"
        disabled={disabled}
        type={type}
        sx={{
          flex: 1,
          '.MuiOutlinedInput-notchedOutline': {
            border: 0,
            borderColor: 'transparent !important',
          },
          '.MuiFormControl-root': {
            m: 0,
          },
          ...inputStyle,
          ...(type === 'autocomplete-select' && autocompleteStyle),
        }}
        className="custom-outlined"
        label=""
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        {...props}
      />
    </Box>
  );
}

const autocompleteStyle = {
  m: 0,
  '> div, :hover > div': {
    background: 'transparent !important',
  },
  '& p, label': {
    display: 'none',
  },
  '& .MuiInputBase-input': {
    padding: '11px',
  },
  '> div::before': {
    borderBottom: 'none !important',
  },
};
