import { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '~/pages/reports/components/ReportFilters';
import { useGetListHospitalityCategoriesLive, useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import CompsCards from './components/CompsCards';
import ExtraCharges from './components/extra-charges/ExtraCharges';
import ItemSales from './components/item-sales/ItemSales';
import ModifierSales from './components/modifier-sales/ModifierSales';
import ReportTips from './components/report-tips/ReportTips';
import { downloadCSV } from 'react-admin';
import { useGetListLive } from '@react-admin/ra-realtime';

const REPORT_TYPE = 'compedItems';

export default function Comps() {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const contentRef = useRef<HTMLDivElement>(null);
  const [rawData, setRawData] = useState<any>();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [compsData, setCompsData] = useState<{
    [key: string]: any;
  }>([]);
  const { data: sellpoints } = useGetListLocationsLive();
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const updateCompsData = (key: string, value: number) => {
    setCompsData(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  //TODO: asta nu mai e ok la dining si la source
  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }
      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);
  

  return (
    <Box sx={{ width: '100%' }} p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        description={
          <>
            {t('comps.description')}
            <a href="https://selio.io/support-center" target="_blank">
              {t('support.support-link')}
            </a>
          </>
        }
        title={t('menu.comps')}
        doNotPrint
        hideBorder
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
        contentRef={contentRef}
        printOnly={true}
      />
      <ReportDateTitle />
      <CompsCards compsData={compsData} />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
        <ItemSales
          rawData={rawData}
          filters={filters}
          commonFields={commonFields}
          updateCompsData={updateCompsData}
        />
        <ModifierSales
          commonFields={commonFields}
          filters={filters}
          updateCompsData={updateCompsData}
        />
        <ExtraCharges filters={filters} updateCompsData={updateCompsData} />
        <ReportTips filters={filters} updateCompsData={updateCompsData} />
      </Box>
    </Box>
  );
}
