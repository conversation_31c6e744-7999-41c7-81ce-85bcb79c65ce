import { useMemo, useState } from 'react';
import { Box, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import CustomTable from '~/components/organisms/CustomTable';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';
import FormattedFilters from '../../report-item-sales/components/FormattedFilters';

type TableRow = {
  type: string;
  quantity: string;
  value: string;
  field1: string;
  field2: string;
  items?: TableRow[];
  subItems?: TableRow[];
};

export default function ExtraDataPaymentModal({
  extraData,
}: {
  extraData?: { [key: string]: any };
}) {
  const { t } = useTranslation();
  const { data: sellpoints } = useGetListLocationsLive();
  const { sellPointId } = useGlobalResourceFilters();

  const paymentMethodConfig: ColumnConfig<TableRow>[] = useMemo(
    () => [
      {
        id: 'type',
        textAlign: 'start',
        label: t('reportsPage.paymentMethod'),
        render: (row: TableRow, rowIndex) => {
          return (
            <Typography
              fontWeight={rowIndex === 0 ? 500 : 300}
              sx={{
                fontSize: { xs: '11px', sm: '16px' },
              }}
              py={rowIndex === 0 ? 0 : 0.5}
            >
              {rowIndex === 0 ? 'Total' : row.type}
            </Typography>
          );
        },
      },
      {
        id: 'field1',
        label: t('reportsPage.details'),
        textAlign: 'end',
        render: (row: TableRow, rowIndex) => {
          return (
            <Typography
              sx={{ fontSize: { xs: '11px', sm: '16px' } }}
              fontWeight={rowIndex === 0 ? 500 : 300}
            >
              {rowIndex === 0 ? '' : capitalize(cleanStringArond(row.field1))}
            </Typography>
          );
        },
      },
      {
        id: 'field2',
        label: t('reportsPage.extraDetails'),
        textAlign: 'end',
        render: (row: TableRow, rowIndex) => {
          return (
            <Typography
              sx={{ fontSize: { xs: '11px', sm: '16px' } }}
              fontWeight={rowIndex === 0 ? 500 : 300}
            >
              {rowIndex === 0 ? '' : capitalize(cleanStringArond(row.field2))}
            </Typography>
          );
        },
      },
      {
        id: 'quantity',
        textAlign: 'end',
        label: t('reportsPage.count'),
        render: (row: TableRow, rowIndex) => {
          return (
            <Typography
              sx={{ fontSize: { xs: '11px', sm: '16px' } }}
              fontWeight={rowIndex === 0 ? 500 : 300}
            >
              {formatNumberIntl(Number(row?.quantity), true)}
            </Typography>
          );
        },
      },
      {
        id: 'value',
        textAlign: 'end',
        label: t('reportsPage.value'),
        render: (row: TableRow, rowIndex) => {
          return (
            <Typography
              sx={{ fontSize: { xs: '11px', sm: '16px' } }}
              fontWeight={rowIndex === 0 ? 500 : 300}
            >
              {formatAndDivideNumber(Number(row.value))}
            </Typography>
          );
        },
      },
    ],
    [t]
  );

  const formatDate = (date: Date | dayjs.Dayjs) =>
    dayjs(date).format('MMM D, YYYY');
  const formatTime = (time: Date | dayjs.Dayjs) => dayjs(time).format('H:mm');

  const [fields, setFields] = useState<FieldOption[]>(
    paymentMethodConfig.map(col => ({
      value: col.id as string,
      isChecked: true,
    }))
  );

  const { itemsData, totalItemsData } = useMemo(() => {
    if (!extraData?.rawData || !extraData?.filters) return { itemsData: [] };

    const rawDataFiltered = filterReport(
      extraData.reportType,
      extraData.rawData,
      extraData.composedFilters,
      [
        {
          field: 'type',
          operator: '==',
          value: extraData.rowData.type,
        },
      ]
    );

    const groupedTableData = groupReport(
      extraData?.reportType,
      rawDataFiltered,
      [],
      ['type', 'field1', 'field2']
    );

    let itemsData = groupedTableData[0]?.report;
    const totalItemsData = mergeAndSumObjects(itemsData);
    itemsData = [totalItemsData, ...itemsData];

    if (!itemsData) return { itemsData: [], totalItemsData: {} };

    return { itemsData, totalItemsData };
  }, [extraData]);

  const from = formatDate(extraData?.filters.dateRange[0]);
  const to = formatDate(extraData?.filters.dateRange[1]);
  const between = formatTime(
    extraData?.filters?.timeRange?.start ?? dayjs().hour(9).minute(0)
  );
  const and = formatTime(
    extraData?.filters?.timeRange?.end ?? dayjs().hour(23).minute(0)
  );
  const at = sellpoints?.find(el => el.id === sellPointId).name;

  return (
    <Box sx={{ width: '100%', maxWidth: '1500px', mx: 'auto' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          maxWidth: '1000px',
          width: '100%',
          mx: 'auto',
          mb: 8,
          gap: 1,
        }}
      >
        <Typography sx={{ fontSize: 18, fontWeight: 300, textAlign: 'center' }}>
          <span style={{ fontWeight: 600 }}>
            {capitalize(totalItemsData.type)}
          </span>{' '}
          {t('reportsPage.paymentsFrom')}
        </Typography>
        <FormattedFilters formattedFilters={extraData?.formattedFilters} />
      </Box>
      <CustomTable
        greyLastRow={false}
        fields={fields}
        extraDataFirstRow={true}
        setFields={setFields}
        filter={false}
        searchBar={false}
        fixLastRow={true}
        maxWidthFirstColumn="180px"
        fixedFirstColumn={true}
        config={paymentMethodConfig}
        data={itemsData || []}
        alignLastColumnRight={false}
      />
    </Box>
  );
}
