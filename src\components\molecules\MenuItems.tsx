import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import Switch from '@mui/material/Switch';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';

import { useTheme } from '../../contexts';
import menuConfig from '../../data/menu-items';

const AntSwitch = styled(Switch)(({ theme }) => ({
  width: 32,
  height: 20,
  padding: 0,
  display: 'flex',

  '&:active': {
    '& .MuiSwitch-thumb': {
      width: 15,
    },
    '& .MuiSwitch-switchBase.Mui-checked': {
      transform: 'translateX(9px)',
    },
  },

  '& .MuiSwitch-switchBase': {
    padding: 2,

    '&.Mui-checked': {
      transform: 'translateX(12px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        backgroundColor: theme.palette.mode === 'dark' ? '#177ddc' : '#1990FF',
      },
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: '0 2px 4px 0 rgb(0 35 11 / 20%)',
    width: 16,
    height: 16,
    borderRadius: 10,

    transition: theme.transitions.create(['width'], {
      duration: 200,
    }),
  },
  '& .MuiSwitch-track': {
    borderRadius: 13,
    opacity: 1,
    backgroundColor:
      theme.palette.mode === 'dark'
        ? 'rgba(255,255,255,.35)'
        : 'rgba(0,0,0,.25)',
    boxSizing: 'border-box',
  },
}));

export default function MenuItems({ selectedItems, setSelectedItems }: any) {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const handleSwitchChange = (item: any) => {
    setSelectedItems((prev: any) => {
      if (prev.some((selectedItem: any) => selectedItem.label === item.label)) {
        return prev.filter(
          (selectedItem: any) => selectedItem.label !== item.label
        );
      } else {
        return [...prev, item];
      }
    });
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        mt: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
      }}
    >
      {menuConfig.map((section, index) => (
        <Box
          sx={{
            width: '100%',
          }}
          key={index}
        >
          <Typography sx={{ fontSize: 15, fontWeight: 600, p: 1 }}>
            {t(`menu.${section.label}`)}
          </Typography>
          <Box
            sx={{
              width: '100%',
              height: '1px',
              backgroundColor: '#D9D9D9',
            }}
          />
          {section?.items?.map((item, index) => {
            const isChecked = selectedItems.some(
              (selectedItem: any) => selectedItem.label === item.label
            );
            return (
              <Box
                sx={{ cursor: 'pointer' }}
                onClick={() => handleSwitchChange(item)}
                key={index}
              >
                <Box
                  sx={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    sx={{ fontSize: 14, py: 2, px: 1.5, fontWeight: 300 }}
                  >
                    {t(`menu.${item.label}`)}
                  </Typography>
                  <AntSwitch
                    checked={isChecked}
                    inputProps={{ 'aria-label': 'ant design' }}
                  />
                </Box>

                <Box
                  sx={{
                    width: '100%',
                    height: '1px',
                    backgroundColor:
                      theme.palette.mode === 'light' ? '#F2F2F2' : '#4A4A4E',
                  }}
                />
              </Box>
            );
          })}
        </Box>
      ))}
    </Box>
  );
}
