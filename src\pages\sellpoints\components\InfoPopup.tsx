import { Box, Button, Dialog, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
export default function InfoPopup({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const { t } = useTranslation();
  return (
    <Dialog open={open} onClose={onClose} fullWidth={true} maxWidth={'xs'}>
      <Box p={4}>
        <Typography variant="h3">{t('sellPointsPage.locationBusinessDetails')}</Typography>
        <Typography pt={3} variant="body2">
          {t('sellPointsPage.locationBusinessNameDescription2')}
        </Typography>
        <Box display="flex" justifyContent="space-between" mt={3}>
          {/* @ts-ignore */}
          <Button variant="contained-light">{t('sellPointsPage.learnMore')}</Button>
          <Button variant="contained" onClick={onClose}>
            {t('sellPointsPage.done')}
          </Button>
        </Box>
      </Box>
    </Dialog>
  );
}
