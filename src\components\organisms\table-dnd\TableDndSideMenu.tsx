import { useMemo } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Box,
  Button,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import {
  GRID_HEIGHT,
  GRID_SIZE,
  GRID_WIDTH,
} from '../../../pages/floor-plans/SectionEdit';
import { Coordinates, shapeForms, Table } from './types';

interface DndSideMenuProps {
  table: Table | null;
  updateTable: (table: Partial<Table>) => void;
  deleteTable: () => void;
}

const MIN_TABLE_SIZE = 5;

export default function TableDndSideMenu({
  table,
  updateTable,
  deleteTable,
}: DndSideMenuProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  return (
    <Box
      sx={{
        position: 'fixed',
        left: {
          xs: '5vw',
          sm: 'max(calc((100vw - 1100px) /2 - 80px), 10px)',
          xl: 'max(calc((100vw - 1100px) /2 - 140px), 10px)',
        },
        top: { xs: 80, sm: 100 },
        transform: !table ? 'translateX(-100vw)' : 'none',
        transition: 'all 0.2s ease-in-out',
        bgcolor: 'background.paper',
        display: 'flex',
        flexDirection: { xs: 'row', sm: 'column' },
        justifyContent: 'center',
        alignItems: 'center',
        p: 2,
        border: 'solid 1px',
        borderColor: 'primary.main',
        borderRadius: '4px',
        zIndex: 11,
        gap: { xs: 1, sm: 2 },
        width: { xs: '90vw', sm: 'fit-content' },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 1,
        }}
      >
        {/* @ts-ignore */}
        <Typography variant="label">{t('floorPlansPage.shape')}</Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: 1,
          }}
        >
          {shapeForms.map(shape => (
            <ShapeButton
              key={shape}
              onClick={shape => updateTable({ shape })}
              shape={shape}
              isSelected={table?.shape === shape}
            />
          ))}
        </Box>
      </Box>
      {['Width', 'Height'].map(direction => (
        <SizeInput
          key={direction}
          direction={direction as any}
          position={table ? table.position : null}
          updateTable={updateTable}
        />
      ))}
      <Button
        // @ts-ignore
        variant="contained"
        color="error"
        sx={{
          mt: { xs: '23px', sm: 1 },
          width: { xs: 'fit-content', sm: '100%' },
        }}
        onClick={deleteTable}
        
      >
        {isXSmall ? <DeleteIcon fontSize="small" /> : t("shared.delete")}
      </Button>
    </Box>
  );
}

const ShapeButton = ({
  shape,
  isSelected,
  onClick,
}: {
  shape: Table['shape'];
  isSelected: boolean;
  onClick: (shape: Table['shape']) => void;
}) => (
  <Button
    // @ts-ignore
    variant="contained-light"
    onClick={() => onClick(shape)}
    sx={{
      bgcolor: isSelected ? 'primary.light' : 'unset',
    }}
  >
    <Box
      sx={{
        width: '20px',
        height: '20px',
        borderRadius: shape === 'circle' ? '10px' : '2px',
        border: 'solid 1px',
        borderColor: 'custom.text',
      }}
    />
  </Button>
);

const SizeInput = ({
  direction,
  position,
  updateTable,
}: {
  direction: 'Width' | 'Height';
  position: Coordinates | null;
  updateTable: (table: Partial<Table>) => void;
}) => {
  const min = 5;
  const max = direction === 'Width' ? GRID_WIDTH : GRID_HEIGHT;

  const updateValue = (value: number) => {
    if (!position) return;

    const { startX, startY, endX, endY } = position;
    const updatedPosition = {
      startX: +startX,
      startY: +startY,
      endX: direction === 'Width' ? +startX + value * GRID_SIZE : +endX,
      endY: direction === 'Height' ? +startY + value * GRID_SIZE : +endY,
    };

    updateTable({ position: updatedPosition });
  };

  const value = useMemo(() => {
    return !!position
      ? direction === 'Height'
        ? (+position.endY - +position.startY) / GRID_SIZE
        : (+position.endX - +position.startX) / GRID_SIZE
      : 0;
  }, [position]);

  const buttonStyles = {
    py: 1,
    px: 1.5,
    position: 'absolute',
    top: 0,
    cursor: 'pointer',
    zIndex: 2,
    userSelect: 'none',
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
        width: '100%',
        gap: 1,
        zIndex: 30,
      }}
    >
      {/* @ts-ignore */}
      <Typography variant="label">{direction}</Typography>
      <Box
        sx={{
          width: '100%',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            left: 0,
            ...buttonStyles,
          }}
          onClick={() => {
            if (value < MIN_TABLE_SIZE + 1) return;
            let newValue = value - 1;
            updateValue(newValue);
          }}
        >
          -
        </Box>
        <TextField
          type="number"
          variant="outlined"
          sx={{
            width: '100%',
            minWidth: '80px',
            height: '35px',
            m: 0,
            '& input': {
              textAlign: 'center',
              px: '30px',
            },
          }}
          InputProps={{
            inputProps: { min, max },
          }}
          value={value}
        />
        <Box
          sx={{
            right: 0,
            ...buttonStyles,
          }}
          onClick={() => {
            let newValue = value + 1;
            updateValue(newValue);
          }}
        >
          +
        </Box>
      </Box>
    </Box>
  );
};
