import { useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  Button,
  Dialog,
  IconButton,
  Link,
  Paper,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';

import ModalHeader from '~/components/molecules/ModalHeader';

export interface SEOValues {
  title: string;
  description: string;
  permalink: string;
}

interface SearchEngineModalProps {
  initialValue?: SEOValues;
  onClose: () => void;
  onSave: (values: SEOValues) => void;
}

export default function SearchEngineModal({
  initialValue,
  onClose,
  onSave,
}: SearchEngineModalProps) {
  const [values, setValues] = useState<SEOValues>(
    initialValue || {
      title: '<TITLE>',
      description: '<DESCRIPTION>',
      permalink: '',
    }
  );

  const handleChange = (field: keyof SEOValues, value: string) => {
    setValues(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog
      open
      onClose={(e: any, reason: any) => {
        if (reason === 'backdropClick') {
          e.stopPropagation();
          return;
        }
        onClose();
      }}
      fullWidth
      maxWidth="sm"
    >
      <ModalHeader handleClose={onClose} title="" noBorder>
        <Button variant="contained" onClick={() => onSave(values)}>
          Done
        </Button>
      </ModalHeader>

      <Box p={3}>
        <Typography variant="h3" mb={2}>
          SEO (Search Engine Optimization)
        </Typography>

        <Typography variant="body2" color="textPrimary" mb={3}>
          Customize this item's title and description for Google and other
          search engines.{' '}
          <Link href="#" underline="always" color="primary">
            Learn more
          </Link>
        </Typography>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 1,
          }}
        >
          <Typography variant="subtitle2" color="textSecondary">
            PREVIEW
          </Typography>
          <Tooltip title="How this appears may differ across devices and search engines.">
            <IconButton size="small">
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        </Box>

        <Paper variant="outlined" sx={{ p: 2, mb: 4 }}>
          <Typography variant="subtitle1" color="primary" sx={{ mb: 0.5 }}>
            {values.title} | Let's burger
          </Typography>
          <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
            example.com
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {values.description}
          </Typography>
        </Paper>

        <TextField
          label="SEO title"
          fullWidth
          value={values.title}
          onChange={e => handleChange('title', e.target.value)}
          sx={{ mb: 2 }}
        />

        <TextField
          label="SEO description"
          multiline
          rows={4}
          fullWidth
          value={values.description}
          onChange={e => handleChange('description', e.target.value)}
          sx={{ mb: 2 }}
        />

        <TextField
          label="Permalink"
          fullWidth
          value={values.permalink}
          onChange={e => handleChange('permalink', e.target.value)}
          InputProps={{
            startAdornment: <Typography color="text.secondary">/ </Typography>,
            endAdornment: (
              <Tooltip title="This is the unique, customizable part of the item's URL.Use hyphens, slashes, dots, or underscores instead of spaces. Note: Changing the permalink of a published item may temporarily affect its visibility in search results.">
                <IconButton size="small">
                  <InfoIcon fontSize="small" color="action" />
                </IconButton>
              </Tooltip>
            ),
          }}
        />

        <Typography variant="caption" color="text.secondary">
          Using item name by default.
        </Typography>
      </Box>
    </Dialog>
  );
}
