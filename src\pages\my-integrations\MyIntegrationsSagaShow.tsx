import { useEffect, useMemo, useState } from 'react';
import { Box, Button, CircularProgress } from '@mui/material';
import dayjs from 'dayjs';
import JSZip from 'jszip';
import {
  useDataProvider,
  useNotify,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import ModalHeader from '~/components/molecules/ModalHeader';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { useFirebase } from '~/contexts/FirebaseContext';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { RESOURCES } from '~/providers/resources';
import { getSagaExport } from './getSagaExport';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

export const MyIntegrationsSagaShow = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const notify = useNotify();
  const { t } = useTranslation('');

  // Local state for selections
  const [localSellPointId, setLocalSellPointId] = useState('');
  const [localDateRange, setLocalDateRange] = useState<DateRange<Dayjs>>([
    dayjs().startOf('day'),
    dayjs().endOf('day'),
  ]);
  // Add loading state
  const [isExporting, setIsExporting] = useState(false);

  const dataProvider = useDataProvider();

  const { details: fbDetails, loading: fbLoading } = useFirebase();

  // Use useGlobalResourceFilters directly
  const {
    sellPoints,
    isLoading,
    sellPointId: globalSellPointId,
    dateRange: globalDateRange,
  } = useGlobalResourceFilters();

  const localSellPointName = useMemo(() => {
    if (!sellPoints || !localSellPointId) return '';
    const point = sellPoints.find((p: any) => p.id === localSellPointId);
    return point?.name || localSellPointId;
  }, [sellPoints, localSellPointId]);

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  const handleExport = async () => {
    console.log('Exporting SAGA data with:', {
      sellPointId: localSellPointId,
      dateRange: localDateRange,
    });

    if (
      !fbDetails.rtdb ||
      !fbDetails.selectedAccount ||
      !localSellPointId ||
      !localDateRange[0] ||
      !localDateRange[1]
    )
      return;

    setIsExporting(true);

    try {
      const exportData = await getSagaExport(
        dataProvider,
        fbDetails.rtdb,
        fbDetails.selectedAccount,
        localSellPointId,
        localDateRange
      );

      // get account data
      const accountData = await dataProvider
        .getOne(RESOURCES.ACCOUNTS, {
          id: fbDetails.selectedAccount,
        })
        .then(response => response.data)
        .catch(error => {
          null;
        });

      const accountCIF =
        accountData?.company?.identification || localSellPointName;

      if (exportData === undefined || exportData.length === 0) {
        notify('No data found for the selected date range', {
          type: 'warning',
        });
        setIsExporting(false);
        return;
      }

      const startDate = localDateRange[0].format('YYYY-MM-DD');
      const endDate = localDateRange[1].format('YYYY-MM-DD');

      // Create ZIP archive
      const zip = new JSZip();

      if (exportData && exportData.length > 0) {
        zip.file(`F_${accountCIF}_${startDate}_${endDate}.xml`, exportData);
      }

      // Generate ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      const fileName = `${localSellPointName}_saga_export_${startDate}_${endDate}.zip`;

      // Create download link and trigger click
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }, 100);

      // Show success notification
      notify(t('Export completed successfully'), { type: 'success' });
    } catch (error) {
      notify(error instanceof Error ? error.message : 'An error occurred', {
        type: 'error',
      });
    } finally {
      setIsExporting(false);
    }
  };

  useEffect(() => {
    // Initialize local state with global values when modal opens
    setLocalSellPointId(globalSellPointId);
    setLocalDateRange(globalDateRange);
  }, []);

  return (
    <>
      <ModalHeader handleClose={handleClose} title={record?.name}>
        <Button
          onClick={handleExport}
          variant="contained"
          color="primary"
          disabled={isExporting}
          sx={{ minWidth: 100 }}
        >
          {isExporting ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            'Export'
          )}
        </Button>
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box sx={{ width: '100%' }}>
          <LocationAndDateSelectors
            externalSellPoints={sellPoints}
            externalSellPointId={localSellPointId}
            onSellPointChange={setLocalSellPointId}
            externalDateRange={localDateRange}
            onDateRangeChange={setLocalDateRange}
            externalIsLoading={isLoading}
            fullWidthButtons={true}
          />
        </Box>
      </Box>
    </>
  );
};
