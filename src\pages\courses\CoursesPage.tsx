import { useEffect, useState } from 'react';
import { Autocomplete, Box, Button, TextField, Typography } from '@mui/material';
import { t } from 'i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import {
  useGetListHospitalityCategoriesLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import { Category } from './components/CategoriesEditModal';
import CoursesList from './CoursesList';

export default function CoursesPage() {
  const { data: sellPoints } = useGetListLocationsLive();
  const { data: allCategories } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });
  const { sellPointId, setSellPointId } = useGlobalResourceFilters();

  const [courses, setCourses] = useState<string[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    if (sellPointId && sellPoints?.length) {
      setCourses(sellPoints?.find(({ id }) => id === sellPointId).courses);
    }
    if (allCategories) {
      setCategories(allCategories);
    }
  }, [sellPointId, sellPoints, allCategories]);

  const handleChange = (_: any, newValue: any) => {
    setSellPointId(newValue ? newValue.id : null);
  };

  const [openCategoriesModal, setOpenCategoriesModal] = useState(false);

  return (
    <Box p={2}>
      <PageTitle
        title={t('devices.courses.title')}
        description={
          <>
            Courses streamline your kitchen workflow, ensuring that dishes are
            prepared and delivered to your guests at the perfect moment.
            <br /> For detailed instructions, please click{' '}
            <a href="https://selio.io/" target="_blank">
              learn more.
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <Box display="flex" alignItems="center" justifyContent="space-between" gap={2}>
        <Autocomplete
          sx={{ maxWidth: 300 }}
          options={sellPoints ?? []}
          getOptionLabel={option => option.name}
          value={sellPoints?.find(({ id }) => id === sellPointId) ?? null}
          onChange={handleChange}
          renderInput={params => <TextField {...params} label={t('shared.location')} />}
          disableClearable
        />
        <Button onClick={() => setOpenCategoriesModal(true)} variant="contained" color="primary" sx={{ textTransform: 'capitalize' }}>
          {t('devices.courses.categoriesAssigned')}
        </Button>
      </Box>

      <CoursesList
        sellpointId={sellPointId}
        courses={courses}
        categories={categories}
        openCategoriesModal={openCategoriesModal}
        setOpenCategoriesModal={setOpenCategoriesModal}
      />
    </Box>
  );
}
