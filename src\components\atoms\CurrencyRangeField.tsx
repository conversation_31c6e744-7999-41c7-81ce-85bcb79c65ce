import React from 'react';
import { Box } from '@mui/material';

import { CurrencyField, CurrencyFieldProps } from './CurrencyField';

export interface CurrencyRangeFieldProps
  extends Omit<CurrencyFieldProps, 'defaultValue'> {
  /**
   * Minimum value in the range
   */
  min: number | undefined;

  /**
   * Maximum value in the range
   */
  max: number | undefined;

  /**
   * Separator between min and max values (default: ' - ')
   */
  separator?: string;

  /**
   * Whether to hide the component if values are undefined
   */
  hideIfUndefined?: boolean;
}

/**
 * Displays a price range with proper currency formatting
 * Shows a single value when min equals max
 *
 * @example
 * <CurrencyRangeField min={1000} max={2000} currency="RON" isStoredInCents />
 * <CurrencyRangeField min={1000} max={1000} currency="EUR" /> // Shows single value
 */
export const CurrencyRangeField: React.FC<CurrencyRangeFieldProps> = ({
  min,
  max,
  separator = ' ~ ',
  hideIfUndefined = true,
  ...currencyFieldProps
}) => {
  // Handle undefined values
  if (min === undefined && max === undefined && hideIfUndefined) {
    return null;
  }

  // If values are equal or only one value provided, show a single currency field
  if (min === max || max === undefined) {
    return <CurrencyField defaultValue={min} {...currencyFieldProps} />;
  }

  // If only max provided
  if (min === undefined) {
    return <CurrencyField defaultValue={max} {...currencyFieldProps} />;
  }

  // Show range with separator
  return (
    <Box component="span" sx={{ whiteSpace: 'nowrap' }}>
      <CurrencyField
        defaultValue={min}
        {...currencyFieldProps}
        additionalOptions={{
          style: 'decimal',
          minimumFractionDigits: 2,
        }}
      />
      {separator}
      <CurrencyField defaultValue={max} {...currencyFieldProps} />
    </Box>
  );
};
