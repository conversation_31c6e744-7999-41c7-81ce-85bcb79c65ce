import { Box, Divider, Grid, Typography } from '@mui/material';
import { Pie } from 'react-chartjs-2';

import { useTheme } from '~/contexts';
import { formatNumber } from '~/utils/formatNumber';

interface PieChartProps {
  chartData: Array<number>;
  labels: Array<string>;
}

export const options = {
  //   responsive: true,
  maintainAspectRatio: false,
  animations: {
    animateScale: true,
    animateRotate: true,
  },
  plugins: {
    legend: {
      display: false,
    },
    title: {
      display: false,
    },
    tooltip: {
      displayColors: false,
      padding: 8,
      bodyFont: {
        size: 14,
        weight: 600,
      },
      footerFont: {
        size: 14,
        weight: 400,
      },
      footerColor: '#eeeeee',
      yAlign: 'bottom',
      callbacks: {
        label: function (context: any) {
          let label = context.dataset.label || '';

          if (label) {
            label += ': ';
          }
          if (context.parsed !== null) {
            label += formatNumber(context.parsed, 'currency');
          }
          return label;
        },
        title: function () {
          return '';
        },
        footer: function (context: any) {
          return context[0]?.label;
        },
      },
    },
  },
};

export default function PieChart({ chartData, labels }: PieChartProps) {
  const { getChartColours } = useTheme();
  const chartColours = getChartColours(chartData.length);

  const data = {
    labels: labels,
    datasets: [
      {
        data: chartData,
        backgroundColor: chartColours,
        borderColor: chartColours,
      },
    ],
  };

  return (
    <Grid
      container
      sx={{
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Grid item xs={12} md={7}>
        {chartData.map((data, index) => {
          return (
            <Box key={`${index}-${data}`}>
              <Divider sx={{ my: 2 }} />
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  cursor: 'pointer',
                }}
              >
                <Box
                  sx={{
                    p: '4px',
                    borderRadius: '50%',
                    backgroundColor: chartColours[index],
                    mr: 2,
                  }}
                />
                {/* @ts-ignore */}
                <Typography variant="label" fontWeight={400}>
                  {labels[index]}
                </Typography>
                <Box sx={{ flex: 1 }} />
                {/* @ts-ignore */}
                <Typography variant="label" fontWeight={400}>
                  {formatNumber(data, 'currency')}
                </Typography>
              </Box>
            </Box>
          );
        })}
        <Divider sx={{ my: 2 }} />
      </Grid>
      <Grid item xs={12} md={4} sx={{ height: '250px' }}>
        {/* @ts-ignore */}
        <Pie data={data} options={options} />
      </Grid>
    </Grid>
  );
}
