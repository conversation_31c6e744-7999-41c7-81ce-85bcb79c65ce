export const sellpoints = {
  sellPoints: [
    {
      bankAccountId: '2',
      accountId: 'oqoj2x0HZmb0GaFVJPZjaXF5Ca12',
      address: {
        city: 'Bucharest',
        county: 'Bucharest',
        zipCode: '900344',
        country: 'RO',
        line1: 'Calea Victoriei 101',
        line2: '',
        state: 'Sector 2',
      },
      askForCovers: true,
      billColorTimings: {
        '#DB4437': 60,
        '#F4B400': 10,
      },
      businessHoursExceptions: {
        '3': {
          endAt: '22:59',
          startAt: '13:00',
        },
        '5': {
          endAt: '',
          startAt: '',
        },
        '6': {
          endAt: '',
          startAt: '',
        },
      },
      businessHoursSchedule: {
        endAt: '21:59',
        startAt: '09:00',
      },
      businessHours: [
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '01:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: null,
          startAt: null,
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: null,
          startAt: null,
        },
      ],
      businessType: 'hospitality',
      catalogs: {
        Ra00cVaNSsY1upK7tpkL: {
          active: true,
          name: 'BREAKFAST',
          schedule: [
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '17:00',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
          ],
        },
        hWMxw1updlPLuhrKOSQX: {
          active: true,
          name: 'LAUNCH',
          schedule: [
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
          ],
        },
      },
      communication: {
        financial: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
        marketing: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
        trouble: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
      },
      compReasons: ['Masa angajati', 'Din partea casei', 'Protocol'],
      contactInfo: {
        email: '<EMAIL>',
        phoneNumber: '0372823718',
        website: '',
        facebook: '',
        instagram: '',
        youtube: '',
      },
      country: 'RO',
      courses: ['Course 1', 'Course 2', 'Desert', 'Apperitiv'],
      categories: [
        {
          id: '5TdXIenoYao8QEq7IYa6',
          name: 'Bere',
          straightFire: true,
        },
        {
          id: '7pg7q36z84LmjCkb7wUG',
          name: 'Frappe',
          straightFire: true,
        },
        {
          id: 'C6m2fdcW2N0pdgbx6cvo',
          name: 'Ceai',
          straightFire: true,
        },
        {
          id: 'Fa9XzZdZphWYlNbvS78v',
          name: 'Limonada',
          straightFire: true,
        },
        {
          id: 'Ne4tvYWCbaMTIXIYH8lB',
          name: 'Antipaste',
          straightFire: false,
        },
        {
          id: 'OetLvqQtm7PGYvbZd008',
          name: 'Pizza',
          straightFire: false,
        },
        {
          id: 'YcPO7fINyVIyjji0CY8x',
          name: 'Salate',
          straightFire: false,
        },
        {
          id: 'ZlwptBsuqMQGjsXh3eSL',
          name: 'Paste',
          straightFire: false,
        },
        {
          id: 'o3Kef6w1UfSudZGafquR',
          name: 'Cafea',
          straightFire: true,
        },
        {
          id: 'qwlGXr4Mcy2cwIDUMIVa',
          name: 'Desert',
          straightFire: false,
        },
        {
          id: 'uweslytLYgHOlz1LI6g5',
          name: 'Combos',
          straightFire: false,
        },
        {
          id: 'w5urPSPkj9M1qlNk05hG',
          name: 'Vinuri',
          straightFire: true,
        },
        {
          id: 'zgJiHwi5HIaMVQphMlvq',
          name: 'Orez',
          straightFire: false,
        },
      ],
      coursesFireHold: false,
      currency: 'RON',
      defaultMeasureUnit: 'BUC',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam nec arcu vehicula magna faucibus vehicula. Curabitur varius vitae metus ut hendrerit. Quisque non urna nec felis posuere pretium sit amet dapibus est. Nulla porttitor dignissim odio, ac dapibus ligula eleifend sed. Vestibulum bibendum, magna ut mollis egestas, urna felis',
      discounts: {
        'Discount 1': 40,
        'Discount 2': 1.5,
        'Happy Hour': 10,
      },
      errorEmailRecipients: [
        {
          email: '<EMAIL>',
          localization: 'ro-RO',
        },
      ],
      excludedFromRewards: false,
      id: 'W1HXNP7cHSCDfhskGxUd',
      localization: 'ro-RO',
      logoURL:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQdi3f1ONFoVR8R8f9eJnsZQkV4MOtA_A6oiA&usqp=CAU',
      menuBehaviourReturnToHome: false,
      name: "Let's Burger",
      nextInvoiceNumber: 898,
      paymentTypes: {
        '3rdParty': {
          active: true,
          askForConfirmation: true,
          order: 8,
          values: ['Fan Courier', 'TAZZ MANUAL', 'GLOVO MANUAL'],
        },
        card: {
          active: true,
          askForConfirmation: true,
          implemntation: 'manual',
          order: 2,
        },
        cash: {
          active: true,
          askForConfirmation: true,
          order: 1,
        },
        cashless: {
          active: true,
          askForConfirmation: true,
          order: 10,
        },
        giftCard: {
          active: true,
          askForConfirmation: true,
          order: 3,
        },
        mealTicket: {
          active: true,
          askForConfirmation: true,
          order: 5,
        },
        online: {
          active: true,
          askForConfirmation: true,
          askForDetails: true,
          implementation: 'manual',
          order: 7,
        },
        tapToPay: {
          active: true,
          askForConfirmation: false,
          implementation: 'cec',
          order: 0,
        },
        valueTicket: {
          active: true,
          askForConfirmation: true,
          order: 9,
        },
        voucher: {
          active: true,
          askForConfirmation: true,
          askForNumber: true,
          askForPartner: true,
          order: 4,
          partnerValues: ['MALL', 'RADIO'],
          values: ['MALL', 'RADIO'],
        },
        wireTransfer: {
          active: true,
          askForConfirmation: true,
          order: 6,
        },
      },
      sellingType: 'fullService',
      specificSellingType: 'bakery',
      timezone: 'Europe/Bucharest',
      tips: [500, 1000, 1500],
      type: 'physical',
      voidReasons: [
        'Produs alterat',
        'Produs neconform',
        'Clientul s-a razgandit',
        'Comanda eronata',
      ],
    },
    {
      bankAccountId: '2',
      accountId: 'oqoj2x0HZmb0GaFVJPZjaXF5Ca12',
      address: {
        city: 'Bucharest',
        county: 'Bucharest',
        zipCode: '900344',
        country: 'RO',
        line1: 'Calea Victoriei 101',
        line2: '',
        state: 'Sector 2',
      },
      askForCovers: true,
      billColorTimings: {
        '#DB4437': 60,
        '#F4B400': 10,
      },
      businessHoursExceptions: {
        '3': {
          endAt: '22:59',
          startAt: '13:00',
        },
        '5': {
          endAt: '',
          startAt: '',
        },
        '6': {
          endAt: '',
          startAt: '',
        },
      },
      businessHoursSchedule: {
        endAt: '20:00',
        startAt: '09:00',
      },
      businessHours: [
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '01:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
        {
          endAt: '22:00',
          startAt: '09:00',
        },
      ],
      businessType: 'hospitality',
      catalogs: {
        Ra00cVaNSsY1upK7tpkL: {
          active: true,
          name: 'BREAKFAST',
          schedule: [
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '17:00',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
          ],
        },
        hWMxw1updlPLuhrKOSQX: {
          active: true,
          name: 'LAUNCH',
          schedule: [
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
            {
              endAt: '23:59',
              startAt: '00:00',
            },
          ],
        },
      },
      communication: {
        financial: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
        marketing: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
        trouble: {
          email: [
            {
              email: '<EMAIL>',
              localization: 'ro-RO',
            },
          ],
          text: [
            {
              country: 'RO',
              localization: 'ro-RO',
              phone: '+40755017755',
            },
          ],
        },
      },
      compReasons: ['Masa angajati2', 'Din partea casei2', 'Protocol2'],
      contactInfo: {
        email: '<EMAIL>',
        phoneNumber: '0238128382',
        website: '',
        facebook: '',
        instagram: '',
        youtube: '',
      },
      country: 'RO',
      courses: ['Course 1', 'Desert', 'Course 2'],
      categories: [
        {
          id: '5TdXIenoYao8QEq7IYa6',
          name: 'Bere',
          straightFire: true,
        },
        {
          id: '7pg7q36z84LmjCkb7wUG',
          name: 'Frappe',
          straightFire: true,
        },
        {
          id: 'C6m2fdcW2N0pdgbx6cvo',
          name: 'Ceai',
          straightFire: true,
        },
        {
          id: 'Fa9XzZdZphWYlNbvS78v',
          name: 'Limonada',
          straightFire: true,
        },
        {
          id: 'Ne4tvYWCbaMTIXIYH8lB',
          name: 'Antipaste',
          straightFire: false,
        },
        {
          id: 'OetLvqQtm7PGYvbZd008',
          name: 'Pizza',
          straightFire: false,
        },
        {
          id: 'YcPO7fINyVIyjji0CY8x',
          name: 'Salate',
          straightFire: false,
        },
        {
          id: 'ZlwptBsuqMQGjsXh3eSL',
          name: 'Paste',
          straightFire: false,
        },
        {
          id: 'o3Kef6w1UfSudZGafquR',
          name: 'Cafea',
          straightFire: true,
        },
        {
          id: 'qwlGXr4Mcy2cwIDUMIVa',
          name: 'Desert',
          straightFire: false,
        },
        {
          id: 'uweslytLYgHOlz1LI6g5',
          name: 'Combos',
          straightFire: false,
        },
        {
          id: 'w5urPSPkj9M1qlNk05hG',
          name: 'Vinuri',
          straightFire: true,
        },
        {
          id: 'zgJiHwi5HIaMVQphMlvq',
          name: 'Orez',
          straightFire: false,
        },
      ],
      coursesFireHold: false,
      currency: 'RON',
      defaultMeasureUnit: 'BUC',
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam nec arcu vehicula magna faucibus vehicula. Curabitur varius vitae metus ut hendrerit. Quisque non urna nec felis posuere pretium sit amet dapibus est. Nulla porttitor dignissim odio, ac dapibus ligula eleifend sed. Vestibulum bibendum, magna ut mollis egestas, urna felis',
      discounts: {
        'Test 2': 20,
        'Discount 3112': 15,
        'Happyyyyy hour!': 50,
      },
      errorEmailRecipients: [
        {
          email: '<EMAIL>',
          localization: 'ro-RO',
        },
      ],
      excludedFromRewards: false,
      id: 'W1HXNP7cHSCDfhskGxUd2',
      localization: 'ro-RO',
      logoURL:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQdi3f1ONFoVR8R8f9eJnsZQkV4MOtA_A6oiA&usqp=CAU',
      menuBehaviourReturnToHome: true,
      name: 'Restaurant Doi',
      nextInvoiceNumber: 898,
      paymentTypes: {
        '3rdParty': {
          active: true,
          askForConfirmation: true,
          order: 8,
          values: ['Fan Courier', 'TAZZ MANUAL', 'GLOVO MANUAL'],
        },
        card: {
          active: true,
          askForConfirmation: true,
          implemntation: 'manual',
          order: 2,
        },
        cash: {
          active: true,
          askForConfirmation: true,
          order: 1,
        },
        cashless: {
          active: true,
          askForConfirmation: true,
          order: 10,
        },
        giftCard: {
          active: true,
          askForConfirmation: true,
          order: 3,
        },
        mealTicket: {
          active: true,
          askForConfirmation: true,
          order: 5,
        },
        online: {
          active: true,
          askForConfirmation: true,
          askForDetails: true,
          implementation: 'manual',
          order: 7,
        },
        tapToPay: {
          active: true,
          askForConfirmation: false,
          implementation: 'cec',
          order: 0,
        },
        valueTicket: {
          active: true,
          askForConfirmation: true,
          order: 9,
        },
        voucher: {
          active: true,
          askForConfirmation: true,
          askForNumber: true,
          askForPartner: true,
          order: 4,
          partnerValues: ['MALL', 'RADIO'],
          values: ['MALL', 'RADIO'],
        },
        wireTransfer: {
          active: true,
          askForConfirmation: true,
          order: 6,
        },
      },
      sellingType: 'fullService',
      specificSellingType: 'bakery',
      timezone: 'Europe/Bucharest',
      tips: [500, 1000, 1500],
      type: 'physical',
      voidReasons: [
        '1Produs alterat',
        '2Produs neconform',
        '3Clientul s-a razgandit',
        '1Comanda eronata',
      ],
    },
  ],
};
