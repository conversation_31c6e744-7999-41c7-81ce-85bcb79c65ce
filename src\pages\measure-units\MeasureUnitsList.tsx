import { theme } from '@react-admin/ra-navigation';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import { DataTable, List, useLocaleState } from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomSearchInput from '~/components/atoms/inputs/CustomSearchInput';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import { useTheme } from '../../contexts';

export const MeasureUnitsList = () => {
  const [locale] = useLocaleState();
  const { theme } = useTheme();
  const { t } = useTranslation();

  const filters = [
    <CustomSearchInput
      placeholder={t('dashboard.headerSearchPlaceholder')}
      key="search-input"
      source="q"
      alwaysOn
    />,
  ];

  return (
    <List
      exporter={false}
      pagination={false}
      perPage={Number.MAX_SAFE_INTEGER}
      resource={RESOURCES.MEASURE_UNITS}
      filters={filters}
      sort={resourcesInfo[RESOURCES.MEASURE_UNITS].defaultSort}
      component="div"
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
      }}
    >
      <DataTable
        bulkActionButtons={false}
        sx={{
          marginTop: '10px',
          '& .RaDataTable-headerCell': {
            backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
            borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
          },
        }}
      >
        <DataTable.Col
          source={`name.${locale}`}
          label={t('shared.name')}
        />
        <DataTable.Col source={`symbol.${locale}`} label={t('shared.symbol')} />
        <DataTable.Col
          source={`description.${locale}`}
          label={t('shared.description')}
          sx={{ textAlign: { xs: 'center', md: 'left' } }}
        />
      </DataTable>
    </List>
  );
};
