import { useEffect, useState } from 'react';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CircleCheckedFilled from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import CircleUnchecked from '@mui/icons-material/RadioButtonUnchecked';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { SmallBorderLinearProgress } from '../../atoms/charts/BorderLinearProgress';

export default function SetupProgressBar() {
  const [show, setShow] = useState<boolean>(true);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [percentage, setPercentage] = useState<number>(0);
  const { data: setupProgress } = useGetList('setupProgress');
  const { t } = useTranslation();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  useEffect(() => {
    if (setupProgress?.length) {
      const completedItems = setupProgress.filter(
        el => el.completed === true
      ).length;
      setPercentage((completedItems * 100) / setupProgress.length);
    }
  }, [setupProgress]);

  return (
    <Box sx={{ my: 2 }}>
      <Divider />
      <Box
        onClick={() => setOpenModal(true)}
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          my: 1,
          px: 1,
          py: 2,
          cursor: 'pointer',
          borderRadius: '6px',
          '&:hover': {
            bgcolor: 'background.tinted',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <Typography variant="h4" component={'p'}>
            {isXSmall
              ? `${percentage}% set up`
              : ` ${t('dashboard.youAre')} ${percentage}% ${t('dashboard.setUp')}`}
          </Typography>
          <Box sx={{ width: isXSmall ? 60 : 120 }}>
            <SmallBorderLinearProgress
              variant="determinate"
              value={percentage}
            />
          </Box>
        </Box>

        <ArrowForwardIcon sx={{ color: 'primary.main', ml: 2 }} />
      </Box>
      <Divider />
      <Dialog
        open={openModal}
        onClose={() => setOpenModal(false)}
        fullWidth={true}
        maxWidth={'sm'}
      >
        <DialogTitle sx={{ px: 5, py: 3 }}>
          <Button
            // @ts-ignore
            variant="close-btn"
            onClick={() => setOpenModal(false)}
            sx={{
              '& span': { mr: 0 },
            }}
          >
            <CloseIcon fontSize="small" />
          </Button>
          <Typography variant="h2" mt={4} mb={3}>
            {t('dashboard.setupGuide')}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <SmallBorderLinearProgress
              variant="determinate"
              value={percentage}
            />
            <Typography variant="body2" fontWeight={500} color="primary.main">
              {percentage}%
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {setupProgress?.map(el => {
            return (
              <Box
                key={el.id}
                onClick={() => window.open(el.href)}
                sx={{
                  borderRadius: 3,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  ':hover': {
                    bgcolor: 'primary.veryLight',
                  },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    py: 3,
                    px: 2,
                  }}
                >
                  <Checkbox
                    checked={el.completed}
                    icon={<CircleUnchecked sx={{ transform: 'scale(1.15)' }} />}
                    checkedIcon={
                      <CircleCheckedFilled sx={{ transform: 'scale(1.15)' }} />
                    }
                  />
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="subtitle1">{el.title}</Typography>
                    <Typography variant="subtitle2">
                      {el.description}
                    </Typography>
                  </Box>
                </Box>
                <Divider variant="middle" />
              </Box>
            );
          })}
        </DialogContent>
      </Dialog>
    </Box>
  );
}
