import React, { ChangeEvent, useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

type EmailModalProps = {
  open: boolean;
  onClose: () => void;
};

const EmailModal: React.FC<EmailModalProps> = ({ open, onClose }) => {
  const [email, setEmail] = useState<string>('');
  const [touched, setTouched] = useState(false);
  const { t } = useTranslation('');
  const isValidEmail = (value: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleSend = () => {
    setEmail('');
    setTouched(false);
    onClose();
  };

  const handleBlur = () => {
    setTouched(true);
  };

  const isEmailValid = isValidEmail(email);
  const shouldShowError = touched && !isEmailValid;

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>{t('transactionsPage.sendReceiptByEmail')}</DialogTitle>
      <DialogContent>
        <TextField
          label="Email"
          value={email}
          onChange={handleChange}
          onBlur={handleBlur}
          fullWidth
          margin="normal"
          variant="outlined"
          error={shouldShowError}
          helperText={
            shouldShowError ? t('transactionsPage.enterValidEmailAddress') : ''
          }
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('shared.cancel')}</Button>
        <Button
          variant="contained"
          onClick={handleSend}
          disabled={!isEmailValid}
        >
          {t('transactionsPage.send')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmailModal;
