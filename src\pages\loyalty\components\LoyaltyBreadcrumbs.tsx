import { Box, Theme, Typography, useMediaQuery } from '@mui/material';

interface LoyaltysBreadcrumbsProps {
  step: number;
  setStep: (step: number) => void;
}
export default function LoyaltysBreadcrumbs({
  step,
}: LoyaltysBreadcrumbsProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        margin: isXSmall ? 0 : 'auto',
        display: 'flex',
        gap: 1,
        width: '100%',
      }}
    >
      <Typography
        // @ts-ignore
        variant="label"
        color="gray"
        style={{
          cursor: 'pointer',
          textTransform: 'uppercase',
        }}
      >
        Step
      </Typography>
      <Typography
        // @ts-ignore
        variant="label"
        color="gray"
        style={{
          cursor: 'pointer',
          textTransform: 'uppercase',
        }}
      >
        {step + 1}
      </Typography>
      <Typography
        // @ts-ignore
        variant="label"
        color="gray"
        style={{
          cursor: 'pointer',
          textTransform: 'uppercase',
        }}
      >
        of
      </Typography>
      <Typography
        // @ts-ignore
        variant="label"
        color="gray"
        style={{
          cursor: 'pointer',
          textTransform: 'uppercase',
        }}
      >
        4
      </Typography>
    </Box>
  );
}
