import CheckIcon from '@mui/icons-material/Check';
import { Box, Button, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

const bulletpoints = [
  'Standard processing fee - 0,90%',
  'Table management',
  'Open checks',
  'Reports',
  'Fast order entry',
  'Menu management',
];
export default function UpgradePlanCard() {
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        borderRadius: '6px',
        padding: 3,
        boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor:
          theme.palette.mode == 'light' ? 'transparent' : 'background.tinted',
      }}
    >
      <Box
        sx={{
          flex: 1,
        }}
      >
        <Typography variant="body1" fontWeight={600}>
          You are now on the FREE PLAN
        </Typography>
        <Typography variant="body2" color="custom.gray600" mt={1} mb={3}>
          Continue using Selio for Restaurants without interruption and upgrade
          at any time for new features.
        </Typography>
        {bulletpoints.map((el, idx) => (
          <Box
            key={idx}
            mb={1}
            sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}
          >
            <CheckIcon />
            <Typography variant="body2" fontWeight={600}>
              {el}
            </Typography>
          </Box>
        ))}
      </Box>
      <Button variant="contained" fullWidth sx={{ marginTop: 3 }}>
        Upgrade to Premium
      </Button>
    </Box>
  );
}
