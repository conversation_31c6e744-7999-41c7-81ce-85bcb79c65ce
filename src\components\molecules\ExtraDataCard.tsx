import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, Button, Tooltip, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function ExtraDataCard({
  item,
}: {
  item: { title: string; value: string | number; info?: string };
}) {
  const { t } = useTranslation();
  if (item?.info) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: {
            xs: 'row',
            md: 'column',
          },
          gap: 1,
          height: '100%',
          whiteSpace: 'nowrap',
          alignItems: { xs: 'center', md: 'start' },
          justifyContent: { xs: 'center', md: 'start' },
          textAlign: { xs: 'center', md: 'start' },
        }}
      >
        <Typography
          sx={{
            '@media print': {
              color: 'black !important',
            },
            fontSize: { xs: '13px', sm: '18px' },
            fontWeight: 700,
          }}
        >
          {item?.value}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
          }}
        >
          <Typography
            sx={{
              fontSize: '13px',
              '@media print': {
                color: 'black !important',
              },
            }}
          >
            {item?.title}
          </Typography>
          {item?.info && (
            <Tooltip arrow={true} title={item.info}>
              <InfoOutlinedIcon sx={{ fontSize: '18px', color: 'gray' }} />
            </Tooltip>
          )}
        </Box>
      </Box>
    );
  } else {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          whiteSpace: 'nowrap',
          alignItems: { xs: 'center', md: 'start' },
          justifyContent: { xs: 'center', md: 'start' },
          textAlign: { xs: 'center', md: 'start' },
        }}
      >
        <Typography
          sx={{
            mb: 1,
            '@media print': {
              color: 'black !important',
            },
          }}
          variant="h2"
        >
          {item?.value}
        </Typography>

        <Typography
          sx={{
            fontSize: '13px',
            '@media print': {
              color: 'black !important',
            },
          }}
        >
          {t('giftCards.' + item?.title)}
        </Typography>
      </Box>
    );
  }
}
