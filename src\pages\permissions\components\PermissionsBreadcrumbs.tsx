import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { Breadcrumbs, Theme, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface PermissionsBreadcrumbsProps {
  step: number;
  setStep: (step: number) => void;
}
function PermissionsBreadcrumbs({
  step,
  setStep,
}: PermissionsBreadcrumbsProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const handleBreadcrumbClick = (newStep: number) => {
    if (newStep < step) setStep(newStep);
  };

  const { t } = useTranslation();

  return (
    <Breadcrumbs
      separator={<NavigateNextIcon fontSize="small" />}
      aria-label="breadcrumb"
      sx={{ margin: isXSmall ? 0 : 'auto' }}
    >
      <Typography
        // @ts-ignore
        variant="label"
        color="primary"
        onClick={() => handleBreadcrumbClick(0)}
        style={{
          cursor: 'pointer',
        }}
      >
        {t('menu.permissions')}
      </Typography>
      <Typography
        // @ts-ignore
        variant="label"
        color={step === 1 ? 'primary' : 'inherit'}
        onClick={() => handleBreadcrumbClick(1)}
        style={{
          cursor: 'pointer',
        }}
      >
        {t('createPermissions.accessPoints')}
      </Typography>
    </Breadcrumbs>
  );
}

export { PermissionsBreadcrumbs };
