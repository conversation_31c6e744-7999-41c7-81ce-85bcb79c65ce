import { FIREBASE_RTDB_EUROPE_WEST1_DOMAIN } from './constants';
import { jumpConsistentHash } from './jumpConsistentHash';

const projectId = import.meta.env.VITE_FIREBASE_PROJECT_ID;
const rtdbInstances = import.meta.env.VITE_FIREBASE_RTDB_INSTANCES;

export async function getDatabaseUrlFromAccountId(
  accountId: string | null
): Promise<string | null> {
  if (!accountId) {
    return null;
  }
  // Hash the account ID using SubtleCrypto
  const encoder = new TextEncoder();
  const data = encoder.encode(accountId);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = new Uint8Array(hashBuffer);

  // Convert the first 8 bytes (16 hex characters) to a BigInt
  let hashLong = BigInt(0);
  for (let i = 0; i < 8; i++) {
    hashLong = (hashLong << BigInt(8)) + BigInt(hashArray[i]);
  }

  // Create a buffer for jump consistent hash
  const buffer = new Uint8Array(new ArrayBuffer(8));
  new DataView(buffer.buffer).setBigUint64(0, hashLong, false);

  const instanceIndex = jumpConsistentHash(buffer, parseInt(rtdbInstances, 10));
  const instanceId = instanceIndex + 1;

  return `https://${projectId}-${instanceId}.${FIREBASE_RTDB_EUROPE_WEST1_DOMAIN}/`;
}
