import { useState } from 'react';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import {
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useSidebarState } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useTheme } from '../../contexts';

export default function MainMenuItem({ menuItem }: any) {
  const [openSidebar, setOpenSidebar] = useSidebarState();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { label, icon, items } = menuItem;
  const open = Boolean(anchorEl);
  const navigate = useNavigate();
  const { theme } = useTheme();
  const id = Math.random();
  const { t } = useTranslation();

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const handleHeaderClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (menuItem.items?.length) {
      setAnchorEl(event.currentTarget);
    } else if (menuItem.href) {
      navigate(menuItem.href);
      if (isXSmall) {
        setOpenSidebar(false);
        window.scrollTo({ top: 0, left: 0 });
      }
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleItemClick = (href: string) => {
    navigate(href);
    handleClose();
    setOpenSidebar(false);
    if (isXSmall) {
      window.scrollTo({ top: 0, left: 0 });
    }
  };

  return (
    <>
      <ListItemButton
        aria-controls={open ? `pop-menu-${id}` : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleHeaderClick}
        disableGutters
        sx={{
          px: 1.5,
          color: open ? 'primary.main' : 'inherit',
          backgroundColor: open ? 'primary.veryLight' : 'inherit',
          borderRadius: '9px',
          ':hover': {
            bgcolor: 'primary.veryLight',
          },
        }}
      >
        {icon && (
          <img
            style={{
              marginRight: 10,
              filter: theme.palette.mode === 'light' ? '' : 'invert(1)',
            }}
            src={icon.props.src}
            alt={icon.props.alt}
          />
        )}
        <ListItemText
          // @ts-ignore
          primary={
            <Typography variant="body1" fontWeight={500}>
              {t(`menu.${label}`)}
            </Typography>
          }
        />
        {items?.length && <KeyboardArrowRightIcon color="disabled" />}
      </ListItemButton>
      {openSidebar && items?.length && (
        <Menu
          id={`pop-menu-${id}`}
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          sx={{
            marginLeft: '25px',
            '& .MuiPopover-paper': {
              borderRadius: '6px',
              boxShadow:
                'rgba(0, 0, 0, 0.08) 0px 2px 16px, rgba(0, 0, 0, 0.08) 0px 4px 8px',
              px: 1,
              minWidth: '250px',
              bgcolor: 'background.default',
            },
          }}
        >
          {/* @ts-ignore */}
          {items?.map((el, index: number) => {
            return (
              <MenuItem
                sx={{ my: 1 }}
                key={`${el.href}-${index}`}
                onClick={() => handleItemClick(el.href || '')}
              >
                {el.icon && (
                  <ListItemIcon
                    sx={{
                      filter:
                        theme.palette.mode === 'light'
                          ? ''
                          : 'invert(1) !important',
                    }}
                  >
                    {el.icon}
                  </ListItemIcon>
                )}
                <Typography
                  variant="body1"
                  fontSize={'0.9rem'}
                  fontWeight={500}
                >
                  {t(`menu.${el.label}`)}
                </Typography>
              </MenuItem>
            );
          })}
        </Menu>
      )}
    </>
  );
}
