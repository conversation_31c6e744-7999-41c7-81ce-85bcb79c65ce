import { use<PERSON><PERSON>back, useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Grid, Typography } from '@mui/material';
import { ShowDialog } from '@react-admin/ra-form-layout';
import {
  SimpleForm,
  SimpleShowLayout,
  TextField,
  useGetOne,
  useGetRecordId,
  useRedirect,
} from 'react-admin';

import GrayBgContainer from '../../../components/atoms/GrayBgContainer';
import CustomInput from '../../../components/atoms/inputs/CustomInput';
import ListCard from '../../../components/atoms/ListCard';
import CustomModal from '../../../components/molecules/CustomModal';
import getSideModalProps from '../../../utils/getSideModalProps';

const Label = ({ value }: any) => (
  <Typography
    // @ts-ignore
    variant="label"
    fontWeight={500}
    sx={{ opacity: 0.5, display: 'block' }}
  >
    {value}
  </Typography>
);

const GiftCardsShowInner = () => {
  const redirect = useRedirect();
  const recordId = useGetRecordId();
  const [eGiftModal, setEGiftModal] = useState(false);
  const [eGiftState, seteGiftState] = useState('');

  // Fetch the gift card details using the recordId
  const { data: giftCard } = useGetOne('gift-cards-overview', { id: recordId });

  useEffect(() => {
    if (giftCard) {
      seteGiftState(giftCard.recipient);
    }
  }, [giftCard]);

  const handleClose = useCallback(() => {
    redirect('/gift-cards-overview');
  }, [redirect]);

  const handleCloseEGift = () => {
    setEGiftModal(false);
  };

  const openEGiftModal = () => {
    setEGiftModal(true);
  };

  return (
    <>
      {/* Header */}
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 1.5,
          position: 'sticky',
          top: 0,
          bgcolor: 'background.paper',
          zIndex: 3,
        }}
      >
        <Button
          // @ts-ignore
          variant="close-btn"
          aria-label="close"
          onClick={handleClose}
          sx={{ '& span': { mr: 0 } }}
        >
          <CloseIcon fontSize="small" />
        </Button>
        <Typography variant="h2">
          <TextField
            sx={{ fontSize: 24, fontWeight: '700' }}
            source="displayName"
          />
        </Typography>
      </Box>
      {/* Body */}
      <GrayBgContainer
        sx={{
          p: 1.5,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          minHeight: 'calc(100vh - 70px)',
        }}
      >
        <Typography sx={{ px: 2, fontWeight: 600, fontSize: 23 }}>
          eGift card {recordId}
        </Typography>
        {/* Card details Section */}
        <ListCard
          sx={{
            width: '100%',
            alignItems: 'flex-start',
            py: 2.5,
            px: 3,
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h4">Card details</Typography>
            </Grid>
            <Grid item xs={6}>
              <Label value={'Balance'} />
              <>
                $ <TextField source="balance" label="Balance" />
              </>
            </Grid>
            <Grid item xs={6}>
              <Label value={'Type'} />
              <TextField source="type" label="Type" />
            </Grid>
            <Grid item xs={6}>
              <Label value={'Last used'} />
              <TextField source="lastUsed" label="Last used" />
            </Grid>
            <Grid item xs={6}>
              <Label value={'Status'} />
              <TextField source="status" label="Status" />
            </Grid>
            <Grid item xs={6}>
              <Label value={'Date sent'} />
              <TextField source="dateSent" label="Date sent" />
            </Grid>
            <Grid item xs={6}>
              <Label value={'First load'} />
              <TextField source="firstLoad" label="First load" />
            </Grid>
            <Grid item xs={12}>
              <Label value={'Recipient'} />
              <TextField source="recipient" label="Recipient" />
            </Grid>
            <Grid item xs={12}>
              <Label value={'Tags'} />
              <TextField source="tags" label="Tags" />
            </Grid>
          </Grid>
        </ListCard>
        <Box
          sx={{
            width: '100%',
            alignItems: 'center',
            display: 'flex',
            gap: 4,
            justifyContent: 'flex-start',
            py: 1.5,
          }}
        >
          <Button variant="contained" onClick={openEGiftModal}>
            Resend eGift card
          </Button>

          <Button
            sx={{
              color: '#BF0120',
              p: 0,
              '&.MuiButtonBase-root:hover': {
                bgcolor: 'transparent',
                opacity: 0.7,
              },
            }}
          >
            Clear balance
          </Button>
        </Box>
        <CustomModal
          modalTextButton="Resend"
          onSubmit={() => console.log('test')}
          modalHeaderTile={'Resend eGift card'}
          open={eGiftModal}
          handleClose={handleCloseEGift}
          confirmDisabled={false}
          fullWidth
          maxWidth="sm"
        >
          <Box sx={{ p: 2 }}>
            <Typography sx={{ fontWeight: 300, lineHeight: 2, ml: 0.5, mb: 2 }}>
              Enter an email address you want to resend the eGift card.
            </Typography>
            <SimpleForm toolbar={false} sx={{ p: 0 }}>
              <CustomInput
                type="text"
                label="Email address"
                source={'gift-cards-overview'}
                onChange={event => {
                  seteGiftState(event.target.value);
                }}
                defaultValue={eGiftState}
              />
            </SimpleForm>
          </Box>
        </CustomModal>
      </GrayBgContainer>
    </>
  );
};

export default function GiftCardsShow() {
  return (
    <ShowDialog {...getSideModalProps({ hideBackdrop: true })}>
      <SimpleShowLayout
        sx={{
          p: 0,
          '& .RaSimpleShowLayout-row': {
            m: 0,
          },
        }}
      >
        <GiftCardsShowInner />
      </SimpleShowLayout>
    </ShowDialog>
  );
}
