import { forwardRef } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Button, Typography, useForkRef } from '@mui/material';

import type {
  FieldType,
  SingleInputDateRangeFieldProps,
} from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

interface DatePickerFilterBtnProps
  extends SingleInputDateRangeFieldProps<Dayjs> {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

type DatePickerFilterBtnComponent = ((
  props: DatePickerFilterBtnProps & React.RefAttributes<HTMLDivElement>
) => React.JSX.Element) & { fieldType?: FieldType };

const DatePickerFilterBtn = forwardRef(
  (props: DatePickerFilterBtnProps, ref: React.Ref<HTMLElement>) => {
    const {
      setOpen,
      label,
      id,
      disabled,
      InputProps: { ref: containerRef } = {},
      inputProps: { 'aria-label': ariaLabel } = {},
    } = props;

    const handleRef = useForkRef(ref, containerRef);

    return (
      <Button
        //@ts-ignore
        variant="contained-light"
        id={id}
        disabled={disabled}
        ref={handleRef}
        aria-label={ariaLabel}
        onClick={() => setOpen?.(prev => !prev)}
      >
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={500} color="custom.gray800">
          {label}
        </Typography>

        <KeyboardArrowDownIcon color="disabled" />
      </Button>
    );
  }
) as DatePickerFilterBtnComponent;

DatePickerFilterBtn.fieldType = 'single-input';

export default DatePickerFilterBtn;
