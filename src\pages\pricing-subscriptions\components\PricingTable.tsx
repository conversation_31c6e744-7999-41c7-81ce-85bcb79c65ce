import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Dialog,
  IconButton,
  Link,
  Slide,
  Typography,
} from '@mui/material';
import { TransitionProps } from '@mui/material/transitions';
import { Helmet } from 'react-helmet';

import InfoTooltip from '../../../components/atoms/InfoTooltip';
import { useTheme } from '../../../contexts';
import camelCaseToNormalWords from '../../../utils/camelCaseToNormalWords';
import styles from './style.module.css';

export default function PricingTable() {
  const { theme } = useTheme();
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Link href="https://selio.io/pricing/" target="_blank">
        <Button
          // @ts-ignore
          variant="contained-light"
          // onClick={handleOpen}
      >
        Compare Plans
      </Button>
      </Link>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
        PaperProps={{
          sx: {
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            height: 'auto',
            maxHeight: '100%',
            display: 'flex',
            justifyContent: 'ceter',
            alignItems: 'center',
            overflowY: 'auto',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
            padding: '16px',
            borderBottom: '1px solid #ddd',
            maxWidth: '1800px',
            width: '100%',
            backgroundColor:
              theme.palette.mode === 'light' ? '#f7f7f7' : '#282828',
          }}
        >
          <IconButton
            onClick={handleClose}
            sx={{
              position: 'absolute',
              left: '16px',
            }}
          >
            <CloseIcon />
          </IconButton>
          <Typography
            sx={{
              flexGrow: 1,
              textAlign: 'center',
            }}
            variant="h3"
          >
            Pricing Plans
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            maxWidth: '1800px',
            width: '100%',
          }}
        >
          <ModalContent />
        </Box>
      </Dialog>
    </>
  );
}

const ModalContent = () => {
  const { theme } = useTheme();
  useEffect(() => {
    const metaTag = document.createElement('meta');
    metaTag.name = 'viewport';
    metaTag.content = 'width=1200, initial-scale=0';
    document.head.appendChild(metaTag);

    return () => {
      document.head.removeChild(metaTag);
      const defaultMetaTag = document.createElement('meta');
      defaultMetaTag.name = 'viewport';
      defaultMetaTag.content = 'width=device-width, initial-scale=1';
      document.head.appendChild(defaultMetaTag);
    };
  }, []);

  const pricingPlans: any = [
    {
      name: 'Free',
      description: 'Everything you need to manage your business with ease',
      perLocation: 'For 1 location',
      price: 0,
      processingFees: {
        info: 'When you process a credit card payment through SoftPOS CEC and Selio for Restaurants App, a processing fee is deducted to cover costs from the card issuer and ensure your data security.',
        'Online Payments': {
          value: '1.2% per transaction',
          info: '',
          extraText: '',
        },
        'Credit & DebitCards - Contactless Payments': {
          value: '1.2% per transaction',
          info: '',
          extraText: '',
        },
      },
      'SOFTWARE / DEVICES ': {
        info: '',
        'Selio for Restaurants App': {
          value: '',
          info: (
            <>
              <h2>Selio for Restaurants POS</h2>

              <p>
                An online POS system designed to perform orders and flawless
                payments.
              </p>

              <h3>Features:</h3>
              <ul>
                <li>For any restaurant concept</li>
                <li>Easy to set up and use</li>
              </ul>
            </>
          ),
          extraText: '1 Device included!',
        },
        'Selio KDS App': {
          value: false,
          info: (
            <>
              <h2>Selio KDS</h2>
              <p>
                Eliminate kitchen chaos and ensure order accuracy with Selio's
                Kitchen Display System (KDS).
              </p>
              <ul>
                <li>Intelligent Ticket Routing</li>
                <li>Flexible Ticket Layouts</li>
                <li>Real-Time Order Updates</li>
                <li>Ticket Timers</li>
                <li>Multi-Device Sync</li>
                <li>Performance Reports</li>
              </ul>
            </>
          ),
          extraText: '',
        },
      },
      'ORDERS AND BILLS': {
        'Open bills': {
          value: true,
          info: '',
          extraText: '',
        },
        'Item availability': {
          value: true,
          info: '',
          extraText: '',
        },
        'Cash management': {
          value: true,
          info: '',
          extraText: '',
        },
        'Split bills': {
          value: true,
          info: '',
          extraText: '',
        },
        Discounts: {
          value: true,
          info: '',
          extraText: '',
        },
        'Service charges': {
          value: true,
          info: '',
          extraText: '',
        },
        'KDS order Ready -> Progress': {
          value: false,
          info: '',
          extraText: '',
        },
        'Multiple locations': {
          value: false,
          info: '',
          extraText: '',
        },
        'Course management': {
          value: false,
          info: '',
          extraText: '',
        },
        'Floor plan customization': {
          value: false,
          info: '',
          extraText: '',
        },
        'Menu management': {
          value: false,
          info: '',
          extraText: '',
        },
      },
      'REPORTING & ANALITICS': {
        Dashboard: {
          value: true,
          info: '',
          extraText: '',
        },
        'Service-charge Reporting': {
          value: true,
          info: '',
          extraText: '',
        },
        'Closing Procedures': {
          value: true,
          info: '',
          extraText: '',
        },
        'Close-of-day Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Live Sales Reports': {
          value: false,
          info: '',
          extraText: '',
        },
        'Advanced  Reports': {
          value: false,
          info: '',
          extraText: '',
        },
        'Item Sales Reports': {
          value: false,
          info: '',
          extraText: '',
        },
      },
      Operations: {
        'Dark Mode': {
          value: true,
          info: '',
          extraText: '',
        },
        'Team Sales Report': {
          value: false,
          info: '',
          extraText: '',
        },
        'Team Permissions': {
          value: false,
          info: '',
          extraText: '',
        },
        'Time tracking': {
          value: false,
          info: '',
          extraText: '',
        },
        'Remote Device Management': {
          value: false,
          info: '',
          extraText: '',
        },
        'Email Support M-F  9 a.m. to 5 p.m.': {
          value: true,
          info: '',
          extraText: '',
        },
        'Premium Support 24/7': {
          value: false,
          info: '',
          extraText: 'available only for Premium Plan',
        },
      },
      'CUSTOMERs management': {
        'Customer groups': {
          value: false,
          info: '',
          extraText: '',
        },
        'Customer notes and reminders': {
          value: false,
          info: '',
          extraText: '',
        },
        'Import existing customers': {
          value: false,
          info: '',
          extraText: '',
        },
        'Customers sales history': {
          value: false,
          info: '',
          extraText: '',
        },
      },
      Payments: {
        'Multiple Types of Payment (Cash, Card, etc)': {
          value: true,
          info: '',
          extraText: '',
        },
        'Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Gift Card Payments': {
          value: false,
          info: '',
          extraText: '',
        },
        Coupons: {
          value: false,
          info: '',
          extraText: '',
        },
        Discounts: {
          value: true,
          info: '',
          extraText: '',
        },
        'Data security (PCI) compliance': {
          value: true,
          info: '',
          extraText: '',
        },
        'Send Professional Invoices': {
          value: false,
          info: '',
          extraText: '• available only for Premium Plan',
        },
        'eGift cards': {
          value: false,
          info: '',
          extraText: '',
        },
        'Physical gift cards': {
          value: false,
          info: '',
          extraText: '• available only for Premium Plan',
        },
      },
      'Hardware support': {
        'Selio POS Terminals for Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Selio Mobile POS for Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Epson Printers for Orders, Bills, Receipts, and Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Datecs AMEF for fiscal receipts': {
          value: false,
          info: '',
          extraText:
            '• only on accredited units in your city!. Click here for more details! ',
        },
      },
    },

    {
      name: 'Premium',
      perLocation: 'per location',
      price: 85,
      description:
        'A complete plan that meets complex booking and staff management needs.',
      processingFees: {
        info: 'When you process a credit card payment through SoftPOS CEC and Selio for Restaurants App, a processing fee is deducted to cover costs from the card issuer and ensure your data security.',
        'Online Payments': {
          value: '0.9% per transaction',
          info: '',
          extraText: '',
        },
        'Credit & DebitCards - Contactless Payments': {
          value: '0.9% per transaction',
          info: '',
          extraText: '',
        },
      },
      'SOFTWARE / DEVICES ': {
        info: '',
        'Selio for Restaurants App': {
          value: React.createElement(
            'p',
            {
              style: {
                color: '#0064F0',
                fontWeight: 500,
              },
            },
            'First included!'
          ),
          info: ``,
          extraText: `
           +25 €/mo. for each additional`,
        },
        'Selio KDS App': {
          value: '',
          info: ``,
          extraText: '+15 €/mo. per device',
        },
      },
      'ORDERS AND BILLS': {
        'Open bills': {
          value: true,
          info: '',
          extraText: '',
        },
        'Item availability': {
          value: true,
          info: '',
          extraText: '',
        },
        'Cash management': {
          value: true,
          info: '',
          extraText: '',
        },
        'Split bills': {
          value: true,
          info: '',
          extraText: '',
        },
        Discounts: {
          value: true,
          info: '',
          extraText: '',
        },
        'Service charges': {
          value: true,
          info: '',
          extraText: '',
        },
        'KDS order Ready -> Progress': {
          value: true,
          info: '',
          extraText: '',
        },
        'Multiple locations': {
          value: true,
          info: '',
          extraText: '',
        },
        'Course management': {
          value: true,
          info: '',
          extraText: '',
        },
        'Floor plan customization': {
          value: true,
          info: '',
          extraText: '',
        },
        'Menu management': {
          value: true,
          info: '',
          extraText: '',
        },
      },
      'REPORTING & ANALITICS': {
        Dashboard: {
          value: true,
          info: '',
          extraText: '',
        },
        'Service-charge Reporting': {
          value: true,
          info: '',
          extraText: '',
        },
        'Closing Procedures': {
          value: true,
          info: '',
          extraText: '',
        },
        'Close-of-day Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Live Sales Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Advanced  Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Item Sales Reports': {
          value: true,
          info: '',
          extraText: '',
        },
      },
      Operations: {
        'Dark Mode': {
          value: true,
          info: '',
          extraText: '',
        },
        'Team Sales Report': {
          value: true,
          info: '',
          extraText: '',
        },
        'Team Permissions': {
          value: true,
          info: '',
          extraText: '',
        },
        'Time tracking': {
          value: true,
          info: '',
          extraText: '',
        },
        'Remote Device Management': {
          value: true,
          info: '',
          extraText: '',
        },
        'Email Support M-F  9 a.m. to 5 p.m.': {
          value: true,
          info: '',
          extraText: '',
        },
        'Premium Support 24/7': {
          value: true,
          info: '',
          extraText: '',
        },
      },
      'CUSTOMERs management': {
        'Customer groups': {
          value: true,
          info: '',
          extraText: '',
        },
        'Customer notes and reminders': {
          value: true,
          info: '',
          extraText: '',
        },
        'Import existing customers': {
          value: true,
          info: '',
          extraText: '',
        },
        'Customers sales history': {
          value: true,
          info: '',
          extraText: '',
        },
      },
      Payments: {
        'Multiple Types of Payment (Cash, Card, etc)': {
          value: true,
          info: '',
          extraText: '',
        },
        'Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Gift Card Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        Coupons: {
          value: true,
          info: '',
          extraText: '',
        },
        Discounts: {
          value: true,
          info: '',
          extraText: '',
        },
        'Data security (PCI) compliance': {
          value: true,
          info: '',
          extraText: '',
        },
        'Send Professional Invoices': {
          value: true,
          info: '',
          extraText: '',
        },
        'eGift cards': {
          value: true,
          info: '',
          extraText: '',
        },
        'Physical gift cards': {
          value: true,
          info: '',
          extraText: 'Starts at 40 Euro for pack of 20 gift cards',
        },
      },
      'Hardware support': {
        'Selio POS Terminals for Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Selio Mobile POS for Contactless Payments': {
          value: true,
          info: '',
          extraText: '',
        },
        'Epson Printers for Orders, Bills, Receipts, and Reports': {
          value: true,
          info: '',
          extraText: '',
        },
        'Datecs AMEF for fiscal receipts': {
          value: false,
          info: '',
          extraText: '',
        },
      },
    },
  ];

  return (
    <>
      <Helmet>
        <meta name="viewport" content="width=device-width, initial-scale=0" />
      </Helmet>

      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            p: 3,
            backgroundColor:
              theme.palette.mode !== 'light' ? 'transparent' : '#F7F7F7',
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography
              variant={'h2'}
              sx={{ fontSize: '23px', fontWeight: 600 }}
            >
              Choose the right Appointments plan for your business.
            </Typography>
            <Typography sx={{ fontSize: '16px', fontWeight: 300 }}>
              Custom pricing, discounts, and a dedicated Account Manager are
              available for businesses that process over $250,000 per year.
            </Typography>
          </Box>

          <Button
            href="https://selio.io/contact-form/"
            target="_blank"
            variant="contained"
          >
            Contact sales
          </Button>
        </Box>
        <Box
          style={{
            overflowX: 'auto',
            minHeight: '100vh',
            overflowY: 'auto',
          }}
        >
          <table className={styles.table}>
            <thead>
              <tr>
                <th
                  className={
                    theme.palette.mode === 'light'
                      ? styles.th
                      : styles['th-dark']
                  }
                ></th>
                {pricingPlans.map((plan: any) => (
                  <th
                    key={plan.name}
                    className={
                      theme.palette.mode === 'light'
                        ? styles.th
                        : styles['th-dark']
                    }
                  >
                    <Box
                      sx={{
                        pt: 4,
                        pb: 1,
                      }}
                    >
                      <Typography variant="h2">{plan.name}</Typography>
                      <Typography
                        sx={{ fontSize: '14px', fontWeight: 300, mt: 2 }}
                      >
                        {plan.description}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '40px',
                          fontWeight: 500,
                          color: 'primary.main',
                        }}
                      >
                        {plan.price} €
                        <span style={{ fontSize: '30px' }}> /mo</span>
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          fontSize: '13px',
                          textTransform: 'capitalize',
                          color: '#969697',
                        }}
                      >
                        {plan.perLocation}
                      </Typography>
                    </Box>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Object.keys(pricingPlans[0]).map(category => {
                if (
                  category === 'name' ||
                  category === 'description' ||
                  category === 'perLocation' ||
                  category === 'price' ||
                  category === 'info'
                ) {
                  return null;
                }
                return (
                  <React.Fragment key={category}>
                    <tr
                      className={
                        theme.palette.mode === 'light'
                          ? styles.category
                          : styles['category-dark']
                      }
                    >
                      <td className={`${styles.td} ${styles.categoryName}`}>
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 500,
                              textAlign: 'start',
                              fontSize: '14px',
                              textTransform: 'uppercase',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {camelCaseToNormalWords(category)}
                          </Typography>
                          {pricingPlans[0][category].info && (
                            <InfoTooltip
                              text={pricingPlans[0][category].info}
                            />
                          )}
                        </Box>
                      </td>
                      {pricingPlans.map(
                        (plan: { name: string; items: any }) => (
                          <td
                            key={`${plan.name}-category`}
                            className={styles.td}
                          ></td>
                        )
                      )}
                    </tr>
                    {Object.keys(pricingPlans[0][category]).map(
                      (feature: any) => {
                        if (feature === 'info') {
                          return;
                        }
                        return (
                          <tr key={`${category}-${feature}`}>
                            <td className={styles.td}>
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  py: 0.6,
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontWeight: 300,
                                    fontSize: '13px',
                                    textAlign: 'start',
                                    whiteSpace: 'nowrap',
                                  }}
                                >
                                  {camelCaseToNormalWords(feature)}
                                </Typography>
                                {pricingPlans[0][category][feature].info && (
                                  <InfoTooltip
                                    text={
                                      pricingPlans[0][category][feature].info
                                    }
                                  />
                                )}
                              </Box>
                            </td>
                            {pricingPlans.map((plan: any) => {
                              return (
                                <td
                                  key={`${plan.name}-${feature}`}
                                  className={styles.td}
                                >
                                  {typeof plan[category][feature].value ===
                                  'boolean' ? (
                                    plan[category][feature].value === true ? (
                                      <svg
                                        width="20"
                                        height="16"
                                        viewBox="0 0 20 16"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          d="M6.68998 15.7099L0.47998 9.49992L3.30998 6.66992L6.68998 10.0599L16.57 0.169922L19.4 2.99992L6.68998 15.7099Z"
                                          fill="#0064F0"
                                        />
                                      </svg>
                                    ) : (
                                      <svg
                                        width="20"
                                        height="21"
                                        viewBox="0 0 20 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          d="M10 0.5C15.5 0.5 20 5 20 10.5C20 16 15.5 20.5 10 20.5C4.5 20.5 0 16 0 10.5C0 5 4.5 0.5 10 0.5ZM10 2.5C8.1 2.5 6.4 3.1 5.1 4.2L16.3 15.4C17.3 14 18 12.3 18 10.5C18 6.1 14.4 2.5 10 2.5ZM14.9 16.8L3.7 5.6C2.6 6.9 2 8.6 2 10.5C2 14.9 5.6 18.5 10 18.5C11.9 18.5 13.6 17.9 14.9 16.8Z"
                                          fill="#AAAAAA"
                                        />
                                      </svg>
                                    )
                                  ) : (
                                    <Typography
                                      sx={{
                                        fontSize: '14px',
                                        fontWeight: 300,
                                        my: 2,
                                      }}
                                    >
                                      {plan[category][feature].value}
                                    </Typography>
                                  )}
                                  {plan[category][feature]?.extraText && (
                                    <Typography
                                      sx={{
                                        fontSize: '14px',
                                        fontWeight: 500,
                                        color: 'primary.main',
                                      }}
                                    >
                                      {plan[category][feature]?.extraText}
                                    </Typography>
                                  )}
                                </td>
                              );
                            })}
                          </tr>
                        );
                      }
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </Box>
      </Box>
    </>
  );
};

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});
