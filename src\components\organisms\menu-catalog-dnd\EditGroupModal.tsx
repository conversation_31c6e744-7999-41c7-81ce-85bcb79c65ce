import { useEffect, useState } from 'react';
import { Button, Dialog, Grid, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { findNextAvailablePosition, getValueByPath } from '~/pages/menus/utils';
import { menuColors } from '../../../data/menu-colors';
import CustomInput from '../../atoms/inputs/CustomInput';
import ColorSelectInputGroup from '../../molecules/input-groups/ColorSelectInputGroup';
import ModalHeader from '../../molecules/ModalHeader';

interface EditTileModalProps {
  path: string;
  initialColorValue: string;
  onClose: () => void;
}

const EditTileModalInner = ({
  path,
  initialColorValue,
  onClose,
}: EditTileModalProps) => {
  const { setValue, getValues } = useFormContext();
  const [colorValue, setColorValue] = useState<string>(initialColorValue || '');
  const [selectedPage, setSelectedPage] = useState(0);
  const [initialPage, setInitialPage] = useState(0);
  const [numberOfPages, setNumberOfPages] = useState<number>();
  const [showError, setShowError] = useState(false);
  const { t } = useTranslation('');

  useEffect(() => {
    const match = path.match(/^pages\.(\d+)\[/);
    const number = parseInt(match![1], 10);
    const values = getValues();

    setInitialPage(number);
    setSelectedPage(number);
    setNumberOfPages(values?.pages?.length ?? 1);
  }, [path]);

  const handleSave = () => {
    if (selectedPage !== initialPage) {
      const values = getValues();
      const position = findNextAvailablePosition(values?.pages?.[selectedPage]);

      const currentGroup = getValueByPath(values, path);
      currentGroup.position = position;

      const pages = [...values.pages];
      pages[selectedPage] = [...pages[selectedPage], currentGroup];
      pages[initialPage] = pages[initialPage].filter(
        (group: any) => group.id !== currentGroup.id
      );

      if (!position) {
        setShowError(true);
        return;
      }

      setValue('pages', pages, {
        shouldDirty: true,
        shouldTouch: true,
      });
    }

    onClose();
  };

  return (
    <>
      <ModalHeader
        handleClose={onClose}
        title={t('shared.edit') + ' ' + `${t('menu.menuGroup')}`}
      >
        {/* @ts-ignore */}
        <Button variant="contained-light" onClick={handleSave}>
          {t('shared.save')}
        </Button>
      </ModalHeader>
      <Grid p={3} container spacing={3}>
        <Grid item xs={12} sm={7}>
          <CustomInput
            source={`${path}.displayName`}
            label={t('menu.itemName')}
          />
          <Typography variant="body2" fontWeight={600} mt={3}>
            {selectedPage !== initialPage
              ? t('menu.moveToPage')
              : t('menu.currentPage')}
            :
          </Typography>
          <Grid container spacing={1} mt={1}>
            {[...Array(numberOfPages)].map((_, pageNum) => (
              <Grid item key={pageNum}>
                <Button
                  variant={selectedPage === pageNum ? 'contained' : 'outlined'}
                  color={selectedPage === pageNum ? 'primary' : 'inherit'}
                  onClick={() => {
                    setSelectedPage(pageNum);
                    setShowError(false);
                  }}
                  sx={{
                    minWidth: 36,
                    minHeight: 36,
                    borderRadius: '50%',
                    fontWeight: selectedPage === pageNum ? 700 : 400,
                  }}
                >
                  {pageNum + 1}
                </Button>
              </Grid>
            ))}
            {showError && (
              <Grid item xs={12}>
                <Typography variant="body2" color="error">
                  {t('menu.pageFullError')}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Grid>
        <Grid item xs={12} sm={5}>
          <ColorSelectInputGroup
            onChange={value => {
              setColorValue(value);
              setValue(`${path}.color`, value, {
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
            value={colorValue}
            choices={menuColors}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default function EditGroupModal({
  onClose,
  ...props
}: EditTileModalProps) {
  return (
    <Dialog open onClose={onClose} fullWidth={true} maxWidth={'md'}>
      <EditTileModalInner {...props} onClose={onClose} />
    </Dialog>
  );
}
