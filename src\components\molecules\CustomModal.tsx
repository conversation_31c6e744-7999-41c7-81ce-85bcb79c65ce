import React from 'react';
import { Box, Dialog, DialogProps } from '@mui/material';
import { But<PERSON> } from 'react-admin';

import ModalHeader from './ModalHeader';

interface CustomModalProps extends DialogProps {
  children: React.ReactNode;
  open: boolean;
  handleClose: () => void;
  onSubmit: () => void;
  confirmDisabled: boolean;
  modalTextButton: any;
  modalHeaderTile: string;
}

const CustomModal: React.FC<CustomModalProps> = ({
  children,
  open,
  handleClose,
  onSubmit,
  confirmDisabled,
  modalTextButton,
  modalHeaderTile,
  ...props
}) => {
  return (
    <Dialog {...props} open={open} onClose={handleClose}>
      <ModalHeader handleClose={handleClose} title={modalHeaderTile}>
        <Button
          sx={{
            fontSize: 16,
            backgroundColor: '#0169FF',
            color: 'white',
            fontWeight: 600,
            borderRadius: '10px',
            textAlign: 'center',
          }}
          variant="contained"
          onClick={onSubmit}
          disabled={confirmDisabled}
        >
          {modalTextButton}
        </Button>
      </ModalHeader>
      <Box sx={{ p: 2 }}>{children}</Box>
    </Dialog>
  );
};

export default CustomModal;
