import { generatePushId } from '../providers/utils/generatePushId';

/**
 * Generates a unique filename with the following properties:
 * 1. Based on timestamp so filenames sort chronologically
 * 2. Contains random data to prevent collisions
 * 3. Exactly 20 characters long
 * 4. Removes the starting "-" from generatePushId and adds a random alphanumeric character at the end
 */
export const generateUniqueId = (): string => {
  // Generate the base ID using generatePushId
  let baseId = generatePushId();

  // Remove the starting "-" character (generatePushId starts with "-")
  if (baseId.startsWith('-')) {
    baseId = baseId.substring(1);
  }

  // Generate a random alphanumeric character (a-z, A-Z, 0-9)
  const chars =
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const randomChar = chars.charAt(Math.floor(Math.random() * chars.length));

  // Append the random character to maintain 20 characters total
  const filename = baseId + randomChar;

  // Ensure the length is exactly 20 characters
  if (filename.length !== 20) {
    throw new Error(
      `Generated filename length should be 20, but got ${filename.length}`
    );
  }

  return filename;
};
