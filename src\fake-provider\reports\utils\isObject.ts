/* eslint-disable @typescript-eslint/no-explicit-any */
export function isObject(value: any): value is Record<string, any> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Checks if a value is a non-empty object.
 * An object is considered non-empty if it:
 * 1. Is an object (not null, not an array)
 * 2. Has at least one own property
 *
 * @param {any} value - The value to check
 * @return {boolean} true if value is a non-empty object, false otherwise
 *
 * @example
 * isObjectAndNotEmpty({}) // false
 * isObjectAndNotEmpty(null) // false
 * isObjectAndNotEmpty([]) // false
 * isObjectAndNotEmpty({a: 1}) // true
 */
export function isObjectAndNotEmpty(value: any): value is Record<string, any> {
  return isObject(value) && Object.keys(value).length > 0;
}
