import { Tooltip } from '@mui/material';

import { Table } from '../table-dnd/types';
import FloorPlanTable from './FloorPlanTable';

interface SelectableTableProps {
  table: Table;
  floorPlanLabel: string;
  selected: boolean;
  disabled?: boolean;
  assignedMemberName?: string; // New prop for assigned member name
  onClick: () => void;
}

export default function SelectableTable({
  table,
  floorPlanLabel,
  selected,
  disabled = false,
  assignedMemberName,
  onClick,
}: SelectableTableProps) {
  const tableContent = (
    <FloorPlanTable
      table={table}
      floorPlanLabel={floorPlanLabel}
      selected={selected}
      disabled={disabled}
      onClick={onClick}
      mode="selection"
      showLabel={true}
      labelSize={14}
    />
  );

  // Show tooltip only for disabled (assigned) tables
  if (disabled && assignedMemberName) {
    return (
      <Tooltip title={assignedMemberName} placement="top" arrow>
        {tableContent}
      </Tooltip>
    );
  }

  return tableContent;
}
