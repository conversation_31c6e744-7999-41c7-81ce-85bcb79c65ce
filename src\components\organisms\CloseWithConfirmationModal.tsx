import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';

interface CloseWithConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onDiscard: () => void;
  saveButton: React.ReactNode;
  title?: string;
  message?: string;
  btnDiscardText?: string;
}

export default function CloseWithConfirmationModal({
  open,
  onClose,
  onDiscard,
  saveButton,
  title = 'There are unsaved changes',
  message = 'Are you sure you want to abandon progress?',
  btnDiscardText = 'Discard',
}: CloseWithConfirmationModalProps) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs">
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <Typography color="text.secondary">{message}</Typography>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'space-between', p: 3, py: 2 }}>
        <Button variant="outlined" onClick={onDiscard}>
          {btnDiscardText}
        </Button>
        {saveButton}
      </DialogActions>
    </Dialog>
  );
}
