import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { PrepStationsCreate } from './PrepStationsCreate';
import { PrepStationEdit } from './PrepStationsEdit';
import { PrepStationsList } from './PrepStationsList';

export default function PrepStationsPage() {
  const { t } = useTranslation('');
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('menu.prepStations')}
        description={
          <>
            {t('menu.prepStationsDescription')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <PrepStationsList />
      <PrepStationsCreate />
      <PrepStationEdit />
    </Box>
  );
}
