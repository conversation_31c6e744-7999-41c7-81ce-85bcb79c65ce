import React, { useEffect, useState } from 'react';
import RefreshIcon from '@mui/icons-material/Refresh';
import TabIcon from '@mui/icons-material/Tab';
import { Box, Button, CircularProgress, Typography } from '@mui/material';

import { useTheme } from '~/contexts';
import { tabManager } from '~/providers/utils/tabManager';

interface TabMonitorProps {
  children: React.ReactNode;
}

const TabMonitor: React.FC<TabMonitorProps> = ({ children }) => {
  const { theme } = useTheme();
  const [isActiveTab, setIsActiveTab] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [randomReasons, setRandomReasons] = useState<string[]>([]);

  const allReasons = [
    'Protects your work from being lost or overwritten',
    'Prevents duplicate actions that could mess up your data',
    'Keeps your information safe and consistent across all your devices',
    'Makes the app run faster and smoother',
    'Avoids confusion when managing multiple location resources at once',
    'Prevents accidentally making the same changes twice',
    "Saves your device's battery and memory",
    'Ensures your latest changes are always saved properly',
    'Reduces the chance of errors and mistakes',
    'Keeps your workflow organized and clutter-free',
  ];

  const getRandomReasons = () => {
    const shuffled = [...allReasons].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3);
  };

  useEffect(() => {
    // Set random reasons on component mount
    setRandomReasons(getRandomReasons());

    // Detect mobile
    const userAgent = navigator.userAgent || navigator.vendor;
    const mobile =
      /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
        userAgent.toLowerCase()
      );
    setIsMobile(mobile);

    const initializeTab = async () => {
      try {
        const isActive = await tabManager.initialize();
        setIsActiveTab(isActive);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize tab manager:', error);
        // Fallback to active tab if initialization fails
        setIsActiveTab(true);
        setIsInitialized(true);
      }
    };

    initializeTab();

    return () => {
      tabManager.cleanup();
    };
  }, []);

  const handleRefresh = () => {
    setIsChecking(true);
    // Small delay to show the checking state, then reload
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.default',
        }}
      >
        <Typography variant="h6" color="text.secondary">
          Initializing...
        </Typography>
      </Box>
    );
  }

  // Show inactive tab UI with refresh button
  if (!isActiveTab) {
    return (
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          bgcolor: 'background.default',
          px: 3,
        }}
      >
        <TabIcon
          sx={{
            fontSize: { xs: '120px', md: '160px' },
            color: 'warning.main',
            mb: 3,
          }}
        />

        <Typography
          variant="h4"
          fontWeight={600}
          sx={{
            textAlign: 'center',
            mb: 2,
            fontSize: { xs: '1.5rem', md: '2rem' },
          }}
        >
          Application Already Running
        </Typography>

        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            textAlign: 'center',
            mb: 4,
            maxWidth: '500px',
            lineHeight: 1.6,
          }}
        >
          SELIO Manager is already running in another browser tab. Please switch
          to the existing tab or close it before continuing.
        </Typography>

        {isMobile && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 3,
              p: 2,
              borderRadius: '8px',
              bgcolor: 'info.light',
              color: 'info.contrastText',
              maxWidth: '500px',
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>
              <strong>Tip:</strong> If you just closed the other tab, wait a
              couple of seconds before checking again. This gives your device
              time to properly release the connection and prevent conflicts.
            </Typography>
          </Box>
        )}

        <Button
          variant="contained"
          onClick={handleRefresh}
          disabled={isChecking}
          startIcon={
            isChecking ? (
              <CircularProgress size={16} color="inherit" />
            ) : (
              <RefreshIcon />
            )
          }
          sx={{
            mb: 4,
            minWidth: 120,
            backgroundColor: '#0064F0',
            color: 'white',
            borderRadius: '6px',
            padding: '12px 20px',
            fontWeight: '500',
            fontSize: '15px',
            textTransform: 'none',
            boxShadow: 'none',
            '&:hover': {
              backgroundColor: isChecking ? '#0064F0' : '#0049b2',
              boxShadow: 'none',
            },
            '&:disabled': {
              backgroundColor: '#0064F0',
              color: 'white',
              opacity: 0.7,
            },
            '@media (max-width:600px)': {
              padding: '8px 15px',
              fontSize: '14px',
            },
          }}
        >
          {isChecking ? 'Checking...' : 'Check Again'}
        </Button>

        <Box
          sx={{
            mt: 2,
            p: 2,
            borderRadius: '6px',
            bgcolor: 'background.paper',
            border: '1px solid',
            borderColor: theme.palette.mode === 'light' ? '#d9d9d9' : '#272727',
            maxWidth: '500px',
          }}
        >
          <Typography variant="body2" color="text.secondary">
            <strong>Why only one tab?</strong>
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            component="ul"
            sx={{ mt: 1, pl: 2 }}
          >
            {randomReasons.map((reason, index) => (
              <li key={index}>{reason}</li>
            ))}
          </Typography>
        </Box>
      </Box>
    );
  }

  return <>{children}</>;
};

export default TabMonitor;
