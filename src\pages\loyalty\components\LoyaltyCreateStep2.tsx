import { useEffect, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, Tooltip } from '@mui/material';
import { ReferenceInput, required, SelectInput } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';

export default function LoyaltyCreateStep2() {
  const { getValues } = useFormContext();
  const selectedUnit = getValues('units');

  return (
    <>
      <Subsection
        title="Program type"
        titleSx={{
          fontSize: '32px',
          textShadow: '1px 5px 2px rgba(0, 0, 0, 0.17)',
        }}
        subtitle="Your program type determines how your customers will earn Stars and rewards."
      >
        <Box
          sx={{
            p: 3,
            py: 6,
            boxShadow: 'rgba(99, 99, 99, 0.3) 0px 2px 8px 0px',
            borderRadius: 1.5,
          }}
        >
          <Subsection
            title="Amount spent"
            titleSx={{
              fontWeight: 500,
              fontSize: 17,
            }}
            subtitle="Reward customers for the amount they spend in a single transaction. The more they spend, the more they earn. Great for creating a multi-tiered rewards program."
          >
            <Box sx={{ p: 2, pr: 4, position: 'relative' }}>
              <Subsection
                title="Every time customers spend"
                titleSx={{
                  fontWeight: 500,
                  fontSize: 16,
                }}
                subtitle="1,00 RON"
              />
              <Box
                sx={{
                  bottom: 0,
                  right: 2,
                  position: 'absolute',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  cursor: 'pointer',
                }}
              >
                <Tooltip
                  title="Points will be awarded based on the amount spent in a single transaction. Amount spent does not accumulate across transactions."
                  arrow
                  placement="top"
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'flip',
                        options: {
                          fallbackPlacements: ['bottom'],
                        },
                      },
                      {
                        name: 'preventOverflow',
                        options: {
                          mainAxis: true,
                          altAxis: true,
                          padding: 8,
                        },
                      },
                    ],
                  }}
                  sx={{
                    fontSize: '14px',
                    maxWidth: {
                      xs: '95vw',
                      sm: '400px',
                    },
                    whiteSpace: 'normal',
                    wordWrap: 'break-word',
                  }}
                >
                  <InfoOutlinedIcon
                    sx={{
                      color: 'gray',
                    }}
                  />
                </Tooltip>
              </Box>
            </Box>
            {/* Customers Earn Input */}
            <ReferenceInput source="customersEarn" reference="customersEarn">
              <SelectInput
                defaultValue={0}
                type="select"
                sx={{ mb: 2 }}
                label="Customers earn"
                optionText={record => {
                  return <Record record={record} selectedUnit={selectedUnit} />;
                }}
                size="medium"
                optionValue="id"
                validate={required()}
              />
            </ReferenceInput>
            <ReferenceInput
              source="rewardExpiration"
              reference="rewardExpiration"
            >
              <SelectInput
                defaultValue={0}
                type="select"
                label="Expiration"
                optionText={record => {
                  return (
                    <>
                      {`${record.value !== '0' ? record?.value : ''} ${
                        record.value === '1'
                          ? 'Day'
                          : record.value === '0'
                            ? 'Never'
                            : 'Days'
                      }`}
                    </>
                  );
                }}
                optionValue="value"
                size="medium"
                validate={required()}
              />
            </ReferenceInput>
          </Subsection>
        </Box>
      </Subsection>
    </>
  );
}

function Record({ record, selectedUnit }: any) {
  const [unitLabel, setUnitLabel] = useState(
    record?.id === 0
      ? selectedUnit?.values?.singular
      : selectedUnit?.values?.plural
  );

  useEffect(() => {
    setUnitLabel(
      record?.id === 0
        ? selectedUnit?.values?.singular
        : selectedUnit?.values?.plural
    );
  }, [selectedUnit]);
  return <>{`${record.value} ${unitLabel || 'Unit'}`}</>;
}
