import { Box, Typography } from '@mui/material';

import { formatNumber } from '../../../utils/formatNumber';

interface CardI {
  title: string;
  value: number;
}

export default function GiftCardsHeaderCards({
  cards,
}: {
  cards: Array<CardI>;
}) {
  return (
    <Box sx={{ display: 'flex', width: '100%', gap: { xs: 2, sm: 3 } }}>
      {cards.map((card: CardI, index: number) => {
        return (
          <Box
            key={index}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5,
              width: { xs: '70%', sm: '100%' },
              maxWidth: '500px',
            }}
          >
            <Typography
              sx={{ textAlign: { xs: 'center', sm: 'start' } }}
              variant="subtitle2"
            >
              {card.title}
            </Typography>
            <Typography
              sx={{ textAlign: { xs: 'center', sm: 'start' } }}
              variant="h2"
            >
              {formatNumber(card.value)} €
            </Typography>
          </Box>
        );
      })}
    </Box>
  );
}
