import { useCallback, useState } from 'react';
import { Box, Button, Dialog, Divider, Typography } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { QRCodeSVG } from 'qrcode.react';
import {
  email,
  RaRecord,
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useGetIdentity,
  useGetOne,
  useNotify,
  useRedirect,
  useUnique,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import CustomInput from '../../components/atoms/inputs/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';

const boxStyle = {
  width: '60px',
  height: '60px',
  borderRadius: '6px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const DeviceAddedConfirmation = ({
  isOpen,
  handleClose,
  deviceCode,
}: {
  isOpen: boolean;
  handleClose: () => void;
  deviceCode?: string;
}) => {
  const { t } = useTranslation('');
  const notify = useNotify();
  const copyCode = () => {
    navigator.clipboard.writeText(deviceCode!);
    notify('Device code copied to clipboard!', { type: 'success' });
  };

  if (!deviceCode) {
    return null;
  }

  return (
    <Dialog open={isOpen} fullScreen onClose={handleClose}>
      <Box sx={{ width: '100%', p: 2 }}>
        <Box display="flex" justifyContent="flex-end">
          <Button
            // @ts-ignore
            variant="contained"
            aria-label="close"
            onClick={handleClose}
            sx={{ '& span': { mr: 0 } }}
          >
            Done
          </Button>
        </Box>
        <Box
          sx={{
            p: 5,
            border: 'solid 1px',
            borderColor: 'custom.gray400',
            borderRadius: '6px',
            maxWidth: '1000px',
            mx: 'auto',
            my: 5,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box>
              <Typography
                variant="h2"
                sx={{ textShadow: '0px 4px 5px #00000052' }}
              >
                Device created.
              </Typography>
              <Typography
                variant="h5"
                color="custom.gray600"
                fontWeight={'400'}
                mt={3}
                sx={{ maxWidth: '280px' }}
              >
                Follow the steps bellow to use Selio on your device.
              </Typography>
            </Box>
            <img
              src="/assets/device-created.png"
              style={{ maxWidth: '100%' }}
            />
          </Box>

          <Divider />
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-end',
              flexWrap: 'wrap',
              gap: 2,
              py: 4,
            }}
          >
            {/* steps box */}
            <Box sx={{ display: 'flex', gap: 5, alignItems: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                <Box
                  sx={{
                    bgcolor: 'black',
                    ...boxStyle,
                  }}
                >
                  <img
                    src="/assets/logo/SELIO_LOGO_WHITE.svg"
                    width="90%"
                    style={{
                      marginTop: '4px',
                    }}
                  />
                </Box>
                <img src="/assets/icons/line.svg" />
                <Box
                  sx={{
                    border: '1px solid',
                    borderColor: 'custom.gray600',
                    ...boxStyle,
                  }}
                >
                  <img src="/assets/icons/lock.svg" width="24px" />
                </Box>
              </Box>
              <Box>
                <Typography variant="h4">1. GET THE SELIO APP</Typography>
                <Typography
                  color="custom.gray600"
                  fontWeight={'400'}
                  sx={{ maxWidth: '280px' }}
                >
                  Download Selio from Google Play on your device.
                </Typography>
                <Typography variant="h4" mt={4}>
                  2. SIGN IN
                </Typography>
                <Typography
                  color="custom.gray600"
                  fontWeight={'400'}
                  sx={{ maxWidth: '280px' }}
                >
                  Use this device code to sign in to Selio app.
                </Typography>
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <QRCodeSVG
                value={deviceCode!}
                imageSettings={{
                  src: '/assets/logo/logo_cropped_2.svg',
                  height: 15,
                  width: 90,
                  excavate: true,
                }}
              />
              <Box
                sx={{
                  display: 'flex',
                  bgcolor: 'custom.gray400',
                  borderRadius: '4px',
                  gap: 2.5,
                }}
              >
                <Typography fontWeight={'800'} sx={{ py: 1, pl: 2.5 }}>
                  {deviceCode!.slice(0, 4)}
                </Typography>
                <Typography fontWeight={'800'} sx={{ py: 1 }}>
                  {deviceCode!.slice(5, 9)}
                </Typography>
                <Typography fontWeight={'800'} sx={{ py: 1 }}>
                  {deviceCode!.slice(-4)}
                </Typography>
                <Box
                  sx={{
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    minWidth: '50px',
                    pr: 0.5,
                  }}
                  onClick={copyCode}
                >
                  <Typography variant="caption" color="primary">
                    Copy
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Dialog>
  );
};

export const DeviceCreate = () => {
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const unique = useUnique();

  const { data: identity, isLoading: identityIsLoading } = useGetIdentity();
  const { data: member, isLoading: memberIsLoading } = useGetOne(
    RESOURCES.TEAM_MEMBERS,
    { id: identity?.id },
    { enabled: !identityIsLoading }
  );

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.DEVICES);
    //
    setTimeout(() => {
      setAddedDeviceCode(undefined);
    }, 100);
  }, [redirect]);

  const [addedDeviceCode, setAddedDeviceCode] = useState<string>();

  const DeviceSaveButton = () => {
    const notify = useNotify();
    const onSuccess = (data: RaRecord) => {
      notify(`Device "${data.name}" created!`);
      setAddedDeviceCode(data.id as string);
    };
    return (
      <SaveButton
        type="button"
        icon={<></>}
        alwaysEnable
        label={t('shared.save')}
        mutationOptions={{ onSuccess }}
      />
    );
  };

  if (memberIsLoading) {
    return null;
  }

  return (
    <CreateDialog
      fullWidth
      maxWidth="sm"
      mutationOptions={{
        meta: {
          memberId: identity?.id,
          memberName: member?.displayName ?? identity?.fullName,
        },
      }}
    >
      <SimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={{
          pairedAt: 0,
        }}
      >
        <DeviceAddedConfirmation
          isOpen={addedDeviceCode !== undefined}
          handleClose={handleClose}
          deviceCode={addedDeviceCode}
        />
        <ModalHeader
          handleClose={handleClose}
          title={t('devicesPage.newDevice')}
        >
          <DeviceSaveButton />
        </ModalHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            maxWidth: '800px',
            width: '90%',
            mx: 'auto',
            gap: 6,
          }}
        >
          <Subsection title="">
            <CustomInput
              source="name"
              label={t('devicesPage.deviceName')}
              validate={[required(), unique()]}
            />

            <ReferenceInput
              source="sellPointId"
              reference={RESOURCES.LOCATIONS}
            >
              <CustomInput
                type="select"
                label={t('shared.location')}
                optionText="name"
                optionValue="id"
                validate={[required()]}
              />
            </ReferenceInput>
          </Subsection>
        </Box>
      </SimpleForm>
    </CreateDialog>
  );
};
