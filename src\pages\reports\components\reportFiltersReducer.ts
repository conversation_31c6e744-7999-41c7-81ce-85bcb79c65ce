import {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from './ReportFilters';

export enum ReportFiltersActionType {
  SET_DATE_RANGE,
  SET_SELLPOINT_ID,
  SET_TIME_RANGE,
  SET_MEMBER,
  SET_FLOOR,
  SET_DINING_OPTION,
  SET_SOURCE,
  RESET_FILTERS,
}

export type ReportFiltersAction =
  | {
      type: ReportFiltersActionType.SET_DATE_RANGE;
      payload: ReportFiltersState['dateRange'];
    }
  | { type: ReportFiltersActionType.SET_SELLPOINT_ID; payload: string }
  | {
      type: ReportFiltersActionType.SET_TIME_RANGE;
      payload: ReportFiltersState['timeRange'];
    }
  | { type: ReportFiltersActionType.SET_MEMBER; payload: any }
  | { type: ReportFiltersActionType.SET_FLOOR; payload: any }
  | { type: ReportFiltersActionType.SET_DINING_OPTION; payload: DiningOption }
  | { type: ReportFiltersActionType.SET_SOURCE; payload: SourceOption }
  | {
      type: ReportFiltersActionType.RESET_FILTERS;
      payload: ReportFiltersState;
    };

export function reportFiltersReducer(
  state: ReportFiltersState,
  action: ReportFiltersAction
): ReportFiltersState {
  switch (action.type) {
    case ReportFiltersActionType.SET_DATE_RANGE:
      return { ...state, dateRange: action.payload };

    case ReportFiltersActionType.SET_SELLPOINT_ID:
      return { ...state, sellpointId: action.payload };

    case ReportFiltersActionType.SET_TIME_RANGE:
      return { ...state, timeRange: action.payload };

    case ReportFiltersActionType.SET_MEMBER:
      return { ...state, member: action.payload };

    case ReportFiltersActionType.SET_FLOOR:
      return { ...state, floor: action.payload };

    case ReportFiltersActionType.SET_DINING_OPTION:
      return { ...state, diningOption: action.payload };

    case ReportFiltersActionType.SET_SOURCE:
      return { ...state, source: action.payload };

    case ReportFiltersActionType.RESET_FILTERS:
      return action.payload;

    default:
      return state;
  }
}
