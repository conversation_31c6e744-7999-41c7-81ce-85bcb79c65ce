import { useEffect } from 'react';

import { cleanupTempFiles } from '~/utils/bucketManager';

/**
 * Custom hook for cleaning up temp file blob URLs on component unmount
 * Use this in components that handle temp files to prevent memory leaks
 *
 * @example
 * ```tsx
 * function FileUploadComponent() {
 *   useTempFileCleanup(); // Clean up on unmount
 *
 *   return <MuiFileUploadInput fileType="private" />;
 * }
 * ```
 */
export function useTempFileCleanup() {
    useEffect(() => {
        // Cleanup function runs on component unmount
        return () => {
            cleanupTempFiles();
        };
    }, []);
}
