import { getDownloadURL, getBlob, getMetadata, ref } from 'firebase/storage';

import { getStorageInstance } from '~/configs/firebaseConfig';
import { UploadedFile, UrlGenerationOptions } from '~/types/fileUpload';
import { generateTempFilePlaceholder, shouldShowPlaceholder } from './placeholderGenerator';

/**
 * Environment variable names for different buckets
 */
const BUCKET_ENV_VARS = {
    temp: 'VITE_FIREBASE_STORAGE_BUCKET_TEMP',
    images: 'VITE_FIREBASE_STORAGE_BUCKET_IMAGES',
    videos: 'VITE_FIREBASE_STORAGE_BUCKET_VIDEOS',
    public: 'VITE_FIREBASE_STORAGE_BUCKET_STORAGE',
    private: 'VITE_FIREBASE_STORAGE_BUCKET_CLOUD',
} as const;

/**
 * Gets the appropriate bucket name based on file type and temporary status
 */
export const getBucketForFile = (
    fileType: 'i' | 'v' | 's' | 'p',
    isTemporary: boolean
): string => {
    if (isTemporary) {
        const tempBucket = import.meta.env[BUCKET_ENV_VARS.temp];
        if (!tempBucket) {
            throw new Error(
                `Environment variable ${BUCKET_ENV_VARS.temp} is not defined`
            );
        }
        return tempBucket;
    }

    const bucketMap = {
        i: BUCKET_ENV_VARS.images,
        v: BUCKET_ENV_VARS.videos,
        s: BUCKET_ENV_VARS.public,
        p: BUCKET_ENV_VARS.private,
    };

    const envVar = bucketMap[fileType];
    const bucket = import.meta.env[envVar];

    if (!bucket) {
        throw new Error(
            `Environment variable ${envVar} is not defined for file type '${fileType}'`
        );
    }

    return bucket;
};

/**
 * Gets Firebase Storage instance for the appropriate bucket
 */
export const getStorageInstanceForFile = (
    fileType: 'i' | 'v' | 's' | 'p',
    isTemporary: boolean
) => {
    const bucketName = getBucketForFile(fileType, isTemporary);
    return getStorageInstance(bucketName);
};

/**
 * Constructs the full filename with extension
 */
export const constructFullFilename = (file: UploadedFile): string => {
    return `${file.fn}.${file.e}`;
};

/**
 * Synchronous URL generator for immediate use (fallback for emergency cases)
 * Note: This may not work due to CORS/ORB restrictions
 */
export const generateFileUrlSync = (file: UploadedFile): string => {
    const bucket = getBucketForFile(file.t, file.x || false);
    const fullFilename = constructFullFilename(file);

    return `https://firebasestorage.googleapis.com/v0/b/${bucket}/o/${encodeURIComponent(fullFilename)}?alt=media`;
};

/**
 * Validates that all required bucket environment variables are set
 */
export const validateBucketConfiguration = (): void => {
    const missingVars: string[] = [];

    Object.values(BUCKET_ENV_VARS).forEach(envVar => {
        if (!import.meta.env[envVar]) {
            missingVars.push(envVar);
        }
    });

    if (missingVars.length > 0) {
        throw new Error(
            `Missing required environment variables: ${missingVars.join(', ')}`
        );
    }
};

/**
 * Cache entry for private files
 */
interface CacheEntry {
    blob: Blob;
    blobUrl: string;
    generation: string;
    metageneration: string;
    cached: number;
    fileSize: number;
}

/**
 * Configuration for cache behavior by bucket type
 */
const CACHE_CONFIG = {
    privateFiles: {
        memoryLimit: 100 * 1024 * 1024,    // 100MB in memory
        memoryTTL: 2 * 60 * 60 * 1000,     // 2 hours
    },
    tempFiles: {
        maxConcurrentUrls: 100,             // Track for cleanup
        // No caching - direct access only
    }
};

/**
 * Temp file manager - no caching, direct blob access with cleanup tracking
 * Handles temp bucket files that are large, short-lived, and change frequently
 * Temp files should not be cached and original uploads show no preview until moved to permanent
 */
class TempFileManager {
    private activeBlobUrls = new Set<string>();
    private readonly MAX_CONCURRENT_URLS = CACHE_CONFIG.tempFiles.maxConcurrentUrls;

    async getUrl(file: UploadedFile, variant?: string): Promise<string> {
        // For temp image files with 'original' variant or no variant, return placeholder until moved to permanent
        if (file.t === 'i' && (!variant || variant === 'original')) {
            return generateTempFilePlaceholder(file, 'original');
        }

        // For temp files showing placeholder (using the shouldShowPlaceholder logic with variant)
        if (shouldShowPlaceholder(file, variant)) {
            return generateTempFilePlaceholder(file, variant === 'original' ? 'original' : 'preview');
        }

        // Direct getBlob for temp files - no caching
        // Always fresh access with full auth validation
        const storage = getStorageInstanceForFile(file.t, true);
        let filePath: string;

        if (file.t === 'i' && variant && variant !== 'original') {
            // Image variants use folder structure - only non-original variants allowed for temp files
            const variantFormat = 'webp'; // All variants except original use webp
            filePath = `i/${file.fn}/${variant}.${variantFormat}`;
        } else {
            // Non-image files or no variant specified
            filePath = constructFullFilename(file);
        }

        const fileRef = ref(storage, filePath);

        try {
            const blob = await getBlob(fileRef); // Respects security rules
            const blobUrl = URL.createObjectURL(blob);

            // Track for cleanup and enforce limits
            this.activeBlobUrls.add(blobUrl);
            this.enforceUrlLimits();

            return blobUrl;
        } catch (error: any) {
            // If the specific variant doesn't exist, return a "processing" placeholder
            if (error?.code === 'storage/object-not-found') {
                return generateTempFilePlaceholder(file, 'preview');
            }
            throw error;
        }
    }

    private enforceUrlLimits(): void {
        // If too many URLs, revoke oldest ones
        // This prevents memory leaks from large temp files
        while (this.activeBlobUrls.size > this.MAX_CONCURRENT_URLS) {
            const firstUrl = this.activeBlobUrls.values().next().value;
            if (firstUrl) {
                this.revokeUrl(firstUrl);
            }
        }
    }

    cleanup(): void {
        // Revoke all temp blob URLs
        this.activeBlobUrls.forEach(url => URL.revokeObjectURL(url));
        this.activeBlobUrls.clear();
    }

    revokeUrl(url: string): void {
        if (this.activeBlobUrls.has(url)) {
            URL.revokeObjectURL(url);
            this.activeBlobUrls.delete(url);
        }
    }
}

/**
 * Private file cache with aggressive memory caching for immutable files
 * Handles default bucket (p) and cloud bucket (c) - files that never change
 * Uses in-memory caching only with 2-hour TTL and 100MB limit
 */
class PrivateFileCache {
    private memoryCache = new Map<string, CacheEntry>();
    private readonly MAX_MEMORY_SIZE = CACHE_CONFIG.privateFiles.memoryLimit;
    private readonly MEMORY_TTL = CACHE_CONFIG.privateFiles.memoryTTL;
    private currentMemoryUsage = 0;

    private getCacheKey(file: UploadedFile): string {
        return `${file.t}:${file.fn}.${file.e}`;
    }

    private getFileRef(file: UploadedFile, context?: any) {
        const storage = getStorageInstanceForFile(file.t, false);

        if (file.t === 'p') {
            // Private files need the full path with account context
            // For now, we'll use a simple path structure since context isn't always available
            // In a full implementation, this would need proper context handling
            const filename = constructFullFilename(file);
            return ref(storage, filename);
        }

        const filename = constructFullFilename(file);
        return ref(storage, filename);
    }

    private evictLRU(): void {
        if (this.memoryCache.size === 0) return;

        // Find oldest entry
        let oldestKey = '';
        let oldestTime = Date.now();

        for (const [key, entry] of this.memoryCache.entries()) {
            if (entry.cached < oldestTime) {
                oldestTime = entry.cached;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            const entry = this.memoryCache.get(oldestKey);
            if (entry) {
                URL.revokeObjectURL(entry.blobUrl);
                this.currentMemoryUsage -= entry.fileSize;
                this.memoryCache.delete(oldestKey);
            }
        }
    }

    private async validateCache(file: UploadedFile, cached: CacheEntry): Promise<boolean> {
        try {
            // Check TTL first (fast check for immutable files)
            const age = Date.now() - cached.cached;
            if (age > this.MEMORY_TTL) {
                return false;
            }

            // For immutable files in default/cloud buckets, generation-based validation is sufficient
            // Since these files never change once created, we can rely on generation matching
            const metadata = await getMetadata(this.getFileRef(file));

            // Check generation (primary validation for immutable files)
            if (metadata.generation !== cached.generation) {
                return false;
            }

            return true;
        } catch (error) {
            // If metadata check fails, invalidate cache
            return false;
        }
    }

    async getUrl(file: UploadedFile): Promise<string> {
        const cacheKey = this.getCacheKey(file);
        const cached = this.memoryCache.get(cacheKey);

        if (cached) {
            const isValid = await this.validateCache(file, cached);
            if (isValid) {
                return cached.blobUrl;
            }

            // Cache is stale, remove it
            URL.revokeObjectURL(cached.blobUrl);
            this.currentMemoryUsage -= cached.fileSize;
            this.memoryCache.delete(cacheKey);
        }

        // Fetch fresh file and cache
        return this.fetchAndCache(file, cacheKey);
    }

    private async fetchAndCache(file: UploadedFile, cacheKey: string): Promise<string> {
        const fileRef = this.getFileRef(file);

        // Get both blob and metadata in parallel
        const [blob, metadata] = await Promise.all([
            getBlob(fileRef),  // Respects security rules
            getMetadata(fileRef)
        ]);

        const blobUrl = URL.createObjectURL(blob);

        // Check if we need to evict entries to make room
        while (this.currentMemoryUsage + blob.size > this.MAX_MEMORY_SIZE && this.memoryCache.size > 0) {
            this.evictLRU();
        }

        // Cache the entry if it fits
        if (blob.size <= this.MAX_MEMORY_SIZE) {
            const entry: CacheEntry = {
                blob,
                blobUrl,
                generation: metadata.generation,
                metageneration: metadata.metageneration,
                cached: Date.now(),
                fileSize: blob.size
            };

            this.memoryCache.set(cacheKey, entry);
            this.currentMemoryUsage += blob.size;
        }

        return blobUrl;
    }

    clear(): void {
        for (const entry of this.memoryCache.values()) {
            URL.revokeObjectURL(entry.blobUrl);
        }
        this.memoryCache.clear();
        this.currentMemoryUsage = 0;
    }
}

/**
 * Smart secure file manager implementing bucket-specific strategies
 * Follows the revised bucket-specific approach:
 * - Default & Cloud buckets (p): Aggressive caching (immutable files)
 * - Temp bucket (x): Direct access, no caching (large, short-lived)
 * - Public buckets (i,v,s): CDN/Firebase direct (existing logic)
 */
class SecureFileManager {
    private privateCache = new PrivateFileCache();
    private tempManager = new TempFileManager();

    async getFileUrl(file: UploadedFile, options?: UrlGenerationOptions): Promise<string> {
        // PUBLIC FILES: Use existing CDN/Firebase URL generation for caching
        if ((file.t === 'i' || file.t === 'v' || file.t === 's') && !file.x) {
            // Import and use existing URL generation for public files
            const { generateFileUrl } = await import('./urlGeneration');
            return generateFileUrl(file, options);
        }

        // TEMP FILES: Use blob URLs, no caching, with variant support for images
        if (file.x) {
            return this.tempManager.getUrl(file, options?.imageVariant);
        }

        // PRIVATE FILES: Use blob URLs with aggressive caching
        // These are immutable files like reports, bills, invoices - perfect for caching
        if (file.t === 'p') {
            return this.privateCache.getUrl(file);
        }

        throw new Error(`Unknown file type: ${file.t} with temporary flag: ${file.x}`);
    }

    /**
     * Clear all private file caches (call on user logout)
     */
    clearPrivateCache(): void {
        this.privateCache.clear();
    }

    /**
     * Clean up temp file blob URLs (call on component unmount)
     */
    cleanupTempFiles(): void {
        this.tempManager.cleanup();
    }

    /**
     * Revoke specific temp file URL (call when file moved from temp to permanent)
     */
    revokeTempUrl(url: string): void {
        this.tempManager.revokeUrl(url);
    }
}

// Singleton instance
const secureFileManager = new SecureFileManager();

/**
 * Get secure file URL with proper caching and security
 */
export const getSecureFileUrl = async (
    file: UploadedFile,
    options?: UrlGenerationOptions
): Promise<string> => {
    return secureFileManager.getFileUrl(file, options);
};

/**
 * Clear private file cache (call on user logout)
 */
export const clearPrivateFileCache = (): void => {
    secureFileManager.clearPrivateCache();
};

/**
 * Clean up temp file blob URLs (call on component unmount)
 */
export const cleanupTempFiles = (): void => {
    secureFileManager.cleanupTempFiles();
};

/**
 * Revoke specific temp file URL (call when file moved from temp to permanent)
 */
export const revokeTempFileUrl = (url: string): void => {
    secureFileManager.revokeTempUrl(url);
};

/**
 * Check if a file should use secure access (private buckets or temp files)
 */
export const shouldUseSecureAccess = (file: UploadedFile): boolean => {
    // Private files (p maps to both default and cloud buckets) and temp files (x) use secure access
    return file.t === 'p' || file.x === true;
};

/**
 * Check if a file is a temporary file
 */
export const isTempFile = (file: UploadedFile): boolean => {
    return file.x === true;
};

/**
 * Check if a file is public and not temporary (should show Copy URL button)
 */
export const isPublicFile = (file: UploadedFile): boolean => {
    // Public files have type 'i', 'v', or 's' and are not temporary
    return (file.t === 'i' || file.t === 'v' || file.t === 's') && !file.x;
};
