import { useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import {
  Box,
  Button,
  Modal,
  Slide,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { SimpleForm, useGetList, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomCreateInDialogButton } from '~/components/molecules/CustomCreateInDialogButton';
import ModalHeader from '~/components/molecules/ModalHeader';
import { useTheme } from '~/contexts';
import { MyIntegrationsWinMentorCreateEdit } from '~/pages/my-integrations/MyIntegrationsWinMentorCreateEdit';
import { RESOURCES } from '~/providers/resources';
import { MyIntegrationsOrderNowCreateEdit } from '../my-integrations/MyIntegrationsOrderNowCreateEdit';
import { MyIntegrationsSagaCreateEdit } from '../my-integrations/MyIntegrationsSagaCreateEdit';

const CreateIntegrationForm = ({
  partnerExtraData,
}: {
  partnerExtraData: any;
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <CustomCreateInDialogButton
      open={open}
      setOpen={setOpen}
      ButtonProps={{
        sx: {
          backgroundColor: 'primary.main',
          color: 'white',
        },
      }}
      resource={RESOURCES.MY_INTEGRATIONS}
      fullScreen={true}
    >
      {partnerExtraData.id === 'winMentor' && (
        <SimpleForm
          toolbar={false}
          sx={{ p: 0 }}
          defaultValues={{
            id: 'winMentor',
            name: partnerExtraData.name,
            active: true,
            type: partnerExtraData.type,
          }}
        >
          <MyIntegrationsWinMentorCreateEdit
            mode="create"
            setOpen={setOpen}
            partnerExtraData={partnerExtraData}
          />
        </SimpleForm>
      )}
      {partnerExtraData.id === 'orderNow' && (
        <SimpleForm
          toolbar={false}
          sx={{ p: 0 }}
          defaultValues={{
            id: 'orderNow',
            name: partnerExtraData.name,
            active: true,
            type: partnerExtraData.type,
          }}
        >
          <MyIntegrationsOrderNowCreateEdit
            mode="create"
            setOpen={setOpen}
            partnerExtraData={partnerExtraData}
          />
        </SimpleForm>
      )}
      {partnerExtraData.id === 'saga' && (
        <SimpleForm
          toolbar={false}
          sx={{ p: 0 }}
          defaultValues={{
            id: 'saga',
            name: partnerExtraData.name,
            active: true,
            type: partnerExtraData.type,
          }}
        >
          <MyIntegrationsSagaCreateEdit
            mode="create"
            setOpen={setOpen}
            partnerExtraData={partnerExtraData}
          />
        </SimpleForm>
      )}
    </CustomCreateInDialogButton>
  );
};

export default function PartnerShow({
  onClose,
  open,
  partnerExtraData,
}: {
  onClose: () => void;
  open: boolean;
  partnerExtraData: any;
}) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { data: myIntegrations } = useGetList(RESOURCES.MY_INTEGRATIONS);
  const myIntegration = myIntegrations?.find(
    integration => integration.id === partnerExtraData.id
  );
  const redirect = useRedirect();

  return (
    <Modal open={open} onClose={onClose} closeAfterTransition>
      <Slide direction="up" in={open} mountOnEnter unmountOnExit>
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            width: '100%',
            height: '100%',
            bgcolor: 'background.paper',
            boxShadow: 24,
            overflowY: 'auto',
          }}
        >
          <ModalHeader title={partnerExtraData.name} handleClose={onClose}>
            {myIntegration?.active ? (
              <Button
                variant="contained"
                color="primary"
                onClick={() =>
                  redirect('show', RESOURCES.MY_INTEGRATIONS, myIntegration.id)
                }
              >
                {t('myIntegrations.viewIntegration')}
              </Button>
            ) : (
              <CreateIntegrationForm partnerExtraData={partnerExtraData} />
            )}
          </ModalHeader>

          <Box
            display="flex"
            flexDirection={'column'}
            gap={2}
            marginY={4}
            paddingX={2}
          >
            {partnerExtraData.price === 'free' && (
              <Box bgcolor={'#4AB300'} p={2} borderRadius={1}>
                <Typography variant="h6" color={'white'}>
                  {t('partnerIntegrations.free')}
                </Typography>
              </Box>
            )}

            <Box
              display="flex"
              flexDirection={isXSmall ? 'column' : 'row'}
              justifyContent={'center'}
              gap={isXSmall ? 0 : 4}
            >
              <img
                src={partnerExtraData.img1}
                alt={partnerExtraData.name}
                height={300}
                style={{ maxWidth: '550px', objectFit: 'contain' }}
              />
              <img
                src={partnerExtraData.img2}
                alt={partnerExtraData.name}
                height={300}
                style={{ maxWidth: '550px', objectFit: 'contain' }}
              />
            </Box>
            <Box
              display="flex"
              flexDirection={'column'}
              gap={4}
              paddingX={isXSmall ? 0 : 4}
            >
              <Box display="flex" flexDirection={'column'} gap={1}>
                <Typography
                  variant="h2"
                  fontSize={'24px'}
                  sx={{ textShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)' }}
                >
                  {t('partnerIntegrations.about')}
                </Typography>
                <Typography variant="subtitle2">
                  {t(partnerExtraData.about)}
                </Typography>
                <Typography variant="subtitle2">
                  {t(partnerExtraData.about2)}
                </Typography>
              </Box>

              <Box display="flex" flexDirection={'column'} gap={1}>
                <Typography
                  variant="h2"
                  fontSize={'24px'}
                  sx={{ textShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)' }}
                >
                  {t('partnerIntegrations.features')}
                </Typography>
                {partnerExtraData?.features?.map((feature: any) => (
                  <Box
                    display="flex"
                    flexDirection={'column'}
                    gap={1}
                    paddingY={2}
                    key={feature.title}
                  >
                    <Typography
                      variant="body1"
                      fontSize={isXSmall ? '16px' : '18px'}
                    >
                      {t(feature.title)}
                    </Typography>
                    {Array.isArray(feature.subtitle) ? (
                      feature.subtitle.map((subtitle: string) => (
                        <Typography variant="subtitle2">
                          {t(subtitle)}
                        </Typography>
                      ))
                    ) : (
                      <Typography variant="subtitle2">
                        {t(feature.subtitle)}
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>

              <Typography
                variant="h2"
                fontSize={'24px'}
                sx={{ textShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)' }}
              >
                {t('partnerIntegrations.information')}
              </Typography>

              <Box
                display="flex"
                flexDirection={isXSmall ? 'column' : 'row'}
                gap={isXSmall ? 2 : 16}
              >
                <Box display="flex" flexDirection={'column'} gap={1}>
                  <Typography variant="body1" fontSize={'18px'}>
                    {t('partnerIntegrations.support')}
                  </Typography>
                  <ul
                    style={{
                      listStyleType: 'none',
                      paddingLeft: 0,
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                    }}
                  >
                    <li
                      style={{
                        display: 'flex',
                        gap: '8px',
                        alignItems: 'center',
                      }}
                    >
                      <img
                        src="/assets/menu-icons/home.svg"
                        alt="email"
                        style={{
                          filter:
                            theme.palette.mode === 'dark'
                              ? 'invert(1)'
                              : 'invert(0)',
                        }}
                      />
                      <a
                        style={{ color: '#0064F0' }}
                        target="_blank"
                        href={partnerExtraData?.support?.website}
                      >
                        {t('partnerIntegrations.developerWebsite')}
                      </a>
                    </li>
                    <li
                      style={{
                        display: 'flex',
                        gap: '8px',
                        alignItems: 'center',
                      }}
                    >
                      <InfoOutlinedIcon />
                      <a
                        style={{ color: '#0064F0' }}
                        target="_blank"
                        href={partnerExtraData?.support?.support}
                      >
                        {t('partnerIntegrations.supportWebsite')}
                      </a>
                    </li>
                    <li
                      style={{
                        display: 'flex',
                        gap: '8px',
                        alignItems: 'center',
                      }}
                    >
                      <MailOutlineIcon />
                      <a
                        style={{ color: '#0064F0' }}
                        href={`mailto:${partnerExtraData?.support?.email}`}
                      >
                        {t('partnerIntegrations.supportEmail')}
                      </a>
                    </li>
                  </ul>
                </Box>
                <Box>
                  <Typography variant="body1" fontSize={'18px'}>
                    {t('partnerIntegrations.requirements')}
                  </Typography>
                  <Typography variant="subtitle2" paddingTop={1}>
                    {partnerExtraData?.requirementsDescription
                      ? t(partnerExtraData?.requirementsDescription)
                      : t('partnerIntegrations.requirementsDescription')}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body1" fontSize={'18px'}>
                    {t('partnerIntegrations.supportedCountries')}
                  </Typography>
                  <Typography variant="subtitle2" paddingTop={1}>
                    {partnerExtraData?.supportedCountries}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Slide>
    </Modal>
  );
}
