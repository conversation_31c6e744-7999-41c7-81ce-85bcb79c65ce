import { useMemo, useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  FormControlLabel,
  Typography,
} from '@mui/material';
import { useGetList } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import ModalHeader from '../../molecules/ModalHeader';
import { ROWS_NO } from './CatalogContainer';
import { Coordinates } from './types';
import { useTranslation } from 'react-i18next';


interface AddFunctionModalProps {
  path: string;
  data: any;
  columns: number;
  position?: Coordinates;
  onClose: () => void;
}

const functions = [
  {
    displayName: 'Courses',
    id: 'coursesOnOff',
  },
  {
    displayName: 'Dinning Options',
    id: 'dinningOptions',
  },
  {
    displayName: 'Discounts',
    id: 'discounts',
  },
  {
    displayName: 'Cancel Order',
    id: 'void',
  },
  {
    displayName: 'Tips',
    id: 'tips',
  },
  {
    displayName: 'Rewards',
    id: 'rewards',
  },
  {
    displayName: 'Void',
    id: 'void',
  },
  {
    displayName: 'Comp',
    id: 'comp',
  },
  {
    displayName: 'Assign',
    id: 'assign',
  },
  {
    displayName: 'Order Notes',
    id: 'notes',
  },
  {
    displayName: 'Move',
    id: 'move',
  },
  {
    displayName: 'Split',
    id: 'split',
  },
  {
    displayName: 'Covers',
    id: 'covers',
  },
  {
    displayName: 'Gift Cards',
    id: 'giftCard',
  },
  {
    displayName: 'Customers',
    id: 'customers',
  },
  {
    displayName: 'Calculator',
    id: 'calculator',
  },
  {
    displayName: '3rd Party',
    id: '3rdParty',
  },
  {
    displayName: 'Card',
    id: 'card',
  },
  {
    displayName: 'Cash',
    id: 'cash',
  },
  {
    displayName: 'Cashless',
    id: 'cashless',
  },
  {
    displayName: 'Gift Card',
    id: 'giftCard',
  },
  {
    displayName: 'Meal Ticket',
    id: 'mealTicket',
  },
  {
    displayName: 'Online',
    id: 'online',
  },
  {
    displayName: 'Tap To Pay',
    id: 'tapToPay',
  },
  {
    displayName: 'Value Ticket',
    id: 'valueTicket',
  },
  {
    displayName: 'Voucher',
    id: 'voucher',
  },
  {
    displayName: 'Wire Transfer',
    id: 'wireTransfer',
  },
];

export default function AddFunctionModal({
  path,
  data,
  columns,
  position,
  onClose,
}: AddFunctionModalProps) {
  const { t } = useTranslation();
  const { setValue } = useFormContext();
  const [selectedItems, setSelectedItems] = useState<any>([]);

  const itemsLength = useMemo(() => {
    return selectedItems.length;
  }, [selectedItems]);

  const checkAvailablePosition = (position: Coordinates): boolean => {
    for (let i = 0; i < data.length; i++) {
      if (
        (data[i].position.startX === position.startX &&
          data[i].position.startY === position.startY) ||
        (data[i].position.endX === position.endX &&
          data[i].position.endY === position.endY)
      ) {
        return false;
      }
    }
    return true;
  };

  const handleClose = () => {
    setSelectedItems([]);
    onClose();
  };

  const addItems = () => {
    if (!position) return;

    const items = [...data];
    const startIndex = position?.startY * columns + position?.startX;
    let selectedItemIndex = 0;

    for (
      let i = startIndex;
      i < ROWS_NO * columns && selectedItemIndex < selectedItems.length;
      i++
    ) {
      const x = i % columns;
      const y = Math.floor(i / columns);
      const position = { startX: x, startY: y, endX: x + 1, endY: y + 1 };

      if (checkAvailablePosition(position)) {
        let itemToAdd = {
          ...selectedItems[selectedItemIndex++],
          type: 'function',
          position,
        };
        items.push(itemToAdd);
      }
    }

    setValue(path, items);
    handleClose();
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: any
  ) => {
    if (event.target.checked) {
      setSelectedItems((prev: any) => [...prev, item]);
      return;
    }

    const tmp = [...selectedItems];
    const index = tmp.findIndex(
      (el: any) => el.displayName === item.displayName
    );
    tmp.splice(index, 1);
    setSelectedItems(tmp);
  };

  return (
    <Dialog open onClose={handleClose} fullWidth={true} maxWidth={'md'}>
      <ModalHeader handleClose={handleClose} title={t('menu.addFunctions')}>
        <Button
          variant="contained"
          onClick={addItems}
          disabled={itemsLength === 0}
        >
          {t('menu.add')} {t('menu.function', { count: itemsLength })}
        </Button>
      </ModalHeader>
      <Box
        sx={{
          p: 4,
          pt: 2,
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(80vh - 80px)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: 'solid 1px',
            borderColor: 'custom.gray600',
          }}
        >
          <Typography sx={{ textTransform: 'capitalize' }}>{t('menu.functions')}</Typography>
        </Box>
        {functions?.map((item: any) => {
          return (
            <Box
              key={item.displayName}
              sx={{
                display: 'flex',
                alignItems: 'center',
                borderBottom: 'solid 1px',
                borderColor: 'custom.gray400',
                p: 1,
                ':hover': {
                  bgcolor: 'background.tinted',
                },
              }}
            >
              <FormControlLabel
                sx={{
                  flex: 1,
                  m: 0,
                }}
                control={
                  <Checkbox
                    checked={
                      !!selectedItems.find(
                        (el: any) => el.displayName === item.displayName
                      )
                    }
                    onChange={e => handleChange(e, item)}
                    inputProps={{ 'aria-label': 'controlled' }}
                  />
                }
                label={
                  <Typography variant="body2" fontWeight={200}>
                    {item.displayName}
                  </Typography>
                }
              />
            </Box>
          );
        })}
      </Box>
    </Dialog>
  );
}
