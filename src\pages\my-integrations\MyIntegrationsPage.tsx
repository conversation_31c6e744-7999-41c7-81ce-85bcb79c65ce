import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { MyIntegrationsEdit } from './MyIntegrationsEdit';
import { MyIntegrationsList } from './MyIntegrationsList';
import { MyIntegrationsShow } from './MyIntegrationsShow';

export default function MyIntegrationsPage() {
  const { t } = useTranslation('');
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title="My Integrations"
        description={
          <>
            {t('categoryLibrary.desc')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <MyIntegrationsList />
      <MyIntegrationsShow />
      <MyIntegrationsEdit />
    </Box>
  );
}
