import { useMemo, useState } from 'react';
import { Theme, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  BooleanField,
  Datagrid,
  List,
  TextField,
  WrapperField,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomEmpty } from '~/components/organisms/CustomEmpty';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { useTheme } from '../../contexts';
import { MyIntegrationsShow } from './MyIntegrationsShow';

export const MyIntegrationsList = () => {
  const { theme } = useTheme();
  const { t } = useTranslation('');
  const [showIntegration, setShowIntegration] = useState<any>('');

  const filters = useMemo(
    () => [
      <CustomSearchInput
        placeholder="Search Integrations"
        key="search-input"
        source="q"
        alwaysOn
      />,
    ],
    [t]
  );
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <List
      resource={RESOURCES.MY_INTEGRATIONS}
      component="div"
      sort={resourcesInfo[RESOURCES.MY_INTEGRATIONS].defaultSort}
      filters={filters}
      exporter={false}
      empty={<></>}
      perPage={Number.MAX_SAFE_INTEGER}
      pagination={false}
    >
      <Datagrid
        rowClick="show"
        sx={{
          '& .RaDatagrid-headerCell': {
            backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
            borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
          },
          '& .RaDatagrid-tableWrapper': {
            marginTop: '10px',
          },
        }}
        bulkActionButtons={false}
      >
        <TextField source="name" label={t('members.displayName')} />
        <BooleanField source="active" textAlign="right" />
        <TextField source="type" textAlign="right" />
        <WrapperField label={t('prepStations.actions')} textAlign="right">
          <ActionsField
            textAlign="right"
            hasEdit={true}
            hasDelete={true}
            deleteMutationMode="optimistic"
          />
        </WrapperField>
      </Datagrid>
      <ListLiveUpdate />
    </List>
  );
};
