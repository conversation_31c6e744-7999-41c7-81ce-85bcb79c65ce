import { useEffect, useMemo, useState } from 'react';
import { Box, Checkbox, Typography } from '@mui/material';
import { useRecordContext } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export default function AccessPointsSelect() {
  const record = useRecordContext();
  const { setValue } = useFormContext();
  const { t } = useTranslation();

  const accessPoints = useMemo(
    () => [
      {
        id: 'all',
        title: t('createPermissions.step2.all'),
        subtitle: t('createPermissions.step2.allDescription'),
      },
      {
        id: 'pos',
        title: t('createPermissions.step2.sharedPointsOfSale'),
        subtitle: t('createPermissions.step2.sharedPointsOfSaleDescription'),
      },
      {
        id: 'manager',
        title: t('createPermissions.step2.dashboard'),
        subtitle: t('createPermissions.step2.dashboardDescription'),
      },
    ],
    [t]
  );

  const [selectedAccessPoints, setSelectedAccessPoints] = useState<string[]>(
    []
  );

  useEffect(() => {
    if (record && record.access) {
      // If access is undefined or empty array, select "all"
      if (!record.access || record.access.length === 0) {
        setSelectedAccessPoints(['all']);
      } else {
        setSelectedAccessPoints(record.access);
      }
    } else {
      // Default to "all" if no record
      setSelectedAccessPoints(['all']);
    }
  }, [record]);

  useEffect(() => {
    // If "all" is selected, set form value to empty array
    // Otherwise, filter out "all" and set the specific access points
    const formValue = selectedAccessPoints.includes('all')
      ? []
      : selectedAccessPoints.filter(point => point !== 'all');

    setValue('access', formValue, {
      shouldDirty: true,
      shouldTouch: true,
    });
  }, [selectedAccessPoints, setValue]);

  const toggleOption = (id: string) => {
    setSelectedAccessPoints(prev => {
      if (id === 'all') {
        // If "all" is clicked, deselect everything else and select only "all"
        return ['all'];
      } else {
        // If a specific option is clicked
        if (prev.includes(id)) {
          // Remove the specific access point
          const newSelection = prev.filter(p => p !== id);
          // If no specific options are left, select "all"
          return newSelection.length === 0 ||
            newSelection.every(p => p === 'all')
            ? ['all']
            : newSelection;
        } else {
          // Add the specific access point and remove "all" if present
          return [...prev.filter(p => p !== 'all'), id];
        }
      }
    });
  };

  return (
    <>
      {accessPoints.map(accessPoint => (
        <Box
          key={accessPoint.id}
          onClick={() => toggleOption(accessPoint.id)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '25px',
            border: '1px solid',
            borderColor: selectedAccessPoints?.includes(accessPoint.id)
              ? 'primary.main'
              : 'custom.gray400',
            borderRadius: '6px',
            px: 3,
            py: 2,
            cursor: 'pointer',
            ':hover': {
              bgcolor: 'primary.veryLight',
            },
          }}
        >
          <Box>
            <Typography
              variant="h6"
              color={
                selectedAccessPoints?.includes(accessPoint.id)
                  ? 'inherit'
                  : 'custom.gray600'
              }
            >
              {accessPoint.title}
            </Typography>
            <Typography variant="subtitle2">{accessPoint.subtitle}</Typography>
          </Box>
          <Checkbox
            checked={selectedAccessPoints?.includes(accessPoint.id)}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </Box>
      ))}
    </>
  );
}
