import { useMemo } from 'react';
import { Box } from '@mui/material';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { groupReport } from '~/fake-provider/reports/groupReport';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../../../types/globals';
import { useTranslation } from 'react-i18next';
import { downloadCSV } from 'react-admin';
import { useGetListLocationsLive } from '~/providers/resources';
import { useGetListLive } from '@react-admin/ra-realtime';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';

type TableRow = {
  vat: string | undefined;
  promotionsValue: number;
  price: number;
  quantity: number;
  couponsValue: number;
  discountsValue: number;
  netValue: number;
  name: string;
  reason: string;
  value: number;
  subItems: [];
};

export default function ExtraChargesTable({
  tableData,
  filters,
  groupingItems,
  fields,
  onChangeGrouping,
  updateCompsData,
  setFields,
}: {
  groupingItems: string[];
  fields: FieldOption[];
  onChangeGrouping?: (items: any[]) => void;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
  updateCompsData?: any;
  filters: any;
}) {
  const { t } = useTranslation();
  const extraChargesData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.name = 'Total';
      totalItemsData.vat = undefined;
      totalItemsData.reason = '';
      totalItemsData.subItems = [];
      mappedTableData = [...mappedTableData, totalItemsData];
      updateCompsData('totalExtraCharges', totalItemsData);
      return mappedTableData;
    }
    updateCompsData('totalExtraCharges', {});
    return [];
  }, [tableData]);

  const extraChargesConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'name',
      label: t('shared.name'),
      textAlign: 'start',
      render: (row: TableRow) => {
        return <>{row.name}</>;
      },
    },
    {
      id: 'reason',
      label: t('reportsPage.reason'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.reason === '@na' ? 'N/A' : row.reason}</>;
      },
    },
    {
      id: 'vat',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'end',
    },
    {
      id: 'price',
      label: t('shared.price'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.price)}</>;
      },
    },
    {
      id: 'quantity',
      label: t('reportsPage.quantity'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row?.quantity, true)}</>;
      },
    },
    {
      id: 'value',
      label: t('reportsPage.grossComps'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },

    {
      id: 'promotionsValue',
      label: t('reportsPage.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'netValue',
      label: t('reportsPage.netComps'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.netValue)}</>;
      },
    },
  ];

  const columnsToFilter = useMemo(() => {
    const columns = [
      'vat',
      'quantity',
      'price',
      'value',
      'promotionsValue',
      'promotionName',
      'reason',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'vat', label: t('shared.tva') },
    { value: 'reason', label: t('reportsPage.reason') },
  ];

  const { dateRange, timeRange } = useGlobalResourceFilters();
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();
  
  const handleExport = () => {
    const title = 'Report comped extra charges';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Name',
        'Reason',
        'VAT',
        'Price',
        'Quantity',
        'Gross Comps',
        'Promotions',
        'Net Comps',
      ].join(','),
      ...extraChargesData?.map((el: any) => el.subItems?.map((report: any) =>
             [
              report.name,
              el.name,
              report.vat,
              report.price / 10000 || 0,
              report.quantity / 1000 || 0,
              report.value / 10000 || 0,
              report.promotionsValue / 10000 || 0,
              report.netValue / 10000 || 0,
            ].join(',')
          )
        ).flat(),
        [
          'Total',
          '',
          '',
          '',
          Number(extraChargesData[extraChargesData.length - 1].quantity ) / 1000 || 0,
          Number(extraChargesData[extraChargesData.length - 1].value) / 10000 || 0,
          Number(extraChargesData[extraChargesData.length - 1].promotionsValue) / 10000 || 0,
          Number(extraChargesData[extraChargesData.length - 1].netValue) / 10000 || 0,
        ].join(','),
    ].join('\n');
   
    downloadCSV(csvContent, 'comped-extra-charges');
  };

  return (
    <>
      <Box sx={{ py: 2 }}>
        <GroupingTable
          config={extraChargesConfig}
          data={extraChargesData}
          fields={fields}
          separateFirstColumn={true}
          exportCSV={true}
          handleExport={handleExport}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          fixedFirstColumn={true}
        />
      </Box>
    </>
  );
}
