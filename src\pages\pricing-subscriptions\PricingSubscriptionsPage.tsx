import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PageTitle from '~/components/molecules/PageTitle';
import PricingSubscriptionsEdit from './PricingSubscriptionsEdit';
import PricingSubscriptionsList from './PricingSubscriptionsList';

export default function PricingSubscriptionsPage() {
  const { t } = useTranslation();
  return (
    <>
      <Box sx={{ height: '100%' }} p={2}>
        <PageTitle
          title={t('pricingSubscriptions.title')}
          description={
            <>
              {t('pricingSubscriptions.description')}
              <a
                href="https://selio.io/support-center"
                target="_blank"
                rel="noreferrer"
              >
                {t('support.support-link')}
              </a>
            </>
          }
        />
        <PricingSubscriptionsList />
        <PricingSubscriptionsEdit />
      </Box>
    </>
  );
}
