/**
 * File type placeholder system for displaying thumbnails
 * when actual thumbnails are not available
 */

export interface PlaceholderInfo {
    icon: string;
    color: string;
    bgColor: string;
    label: string;
}

/**
 * Get placeholder information based on file type and extension
 */
export const getFilePlaceholder = (fileType: string, extension: string): PlaceholderInfo => {
    const ext = extension.toLowerCase();

    // Image files
    if (fileType.startsWith('image/')) {
        return {
            icon: '🖼️',
            color: '#1976d2',
            bgColor: '#e3f2fd',
            label: 'Image',
        };
    }

    // Video files
    if (fileType.startsWith('video/')) {
        return {
            icon: '🎥',
            color: '#7b1fa2',
            bgColor: '#f3e5f5',
            label: 'Video',
        };
    }

    // Audio files
    if (fileType.startsWith('audio/')) {
        return {
            icon: '🎵',
            color: '#388e3c',
            bgColor: '#e8f5e8',
            label: 'Audio',
        };
    }

    // PDF files
    if (fileType === 'application/pdf' || ext === 'pdf') {
        return {
            icon: '📄',
            color: '#d32f2f',
            bgColor: '#ffebee',
            label: 'PDF',
        };
    }

    // Word documents
    if (fileType.includes('word') || ['doc', 'docx'].includes(ext)) {
        return {
            icon: '📝',
            color: '#1976d2',
            bgColor: '#e3f2fd',
            label: 'Document',
        };
    }

    // Excel spreadsheets
    if (fileType.includes('excel') || fileType.includes('spreadsheet') || ['xls', 'xlsx', 'csv'].includes(ext)) {
        return {
            icon: '📊',
            color: '#388e3c',
            bgColor: '#e8f5e8',
            label: 'Spreadsheet',
        };
    }

    // PowerPoint presentations
    if (fileType.includes('powerpoint') || fileType.includes('presentation') || ['ppt', 'pptx'].includes(ext)) {
        return {
            icon: '📽️',
            color: '#f57c00',
            bgColor: '#fff3e0',
            label: 'Presentation',
        };
    }

    // Text files
    if (fileType.startsWith('text/') || ['txt', 'md', 'rtf'].includes(ext)) {
        return {
            icon: '📄',
            color: '#616161',
            bgColor: '#f5f5f5',
            label: 'Text',
        };
    }

    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
        return {
            icon: '🗜️',
            color: '#795548',
            bgColor: '#efebe9',
            label: 'Archive',
        };
    }

    // Code files
    if (['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'json', 'xml', 'py', 'java', 'cpp', 'c', 'php'].includes(ext)) {
        return {
            icon: '💻',
            color: '#424242',
            bgColor: '#fafafa',
            label: 'Code',
        };
    }

    // Default for unknown file types
    return {
        icon: '📎',
        color: '#757575',
        bgColor: '#f5f5f5',
        label: 'File',
    };
};

/**
 * Check if a file should use placeholder thumbnail
 */
export const shouldUsePlaceholder = (file: any): boolean => {
    // For public images (t = 'i'), thumbnails are always generated
    if (file.t === 'i') {
        return false; // Use real thumbnail
    }

    // For public files (t = 's'), check if it's an image type
    if (file.t === 's') {
        // Check if it's an image file by extension or type
        const isImageFile =
            (file.e && ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp', 'tiff'].includes(file.e.toLowerCase())) ||
            (file.type && file.type.startsWith('image/'));

        if (isImageFile) {
            return false; // Use real thumbnail for private image files
        }
    }

    // For all other cases (videos, documents, etc.), use placeholder
    return true;
};

/**
 * Get display name for file type
 */
export const getFileTypeDisplayName = (fileType: string, extension: string): string => {
    const placeholder = getFilePlaceholder(fileType, extension);
    return placeholder.label;
};
