import { useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import SimpleTable from '~/components/organisms/CustomTable/otherTables/SimpleTable';
import { MainFilterSelect } from '~/components/organisms/MainFilterSelect';
import { useFilters } from '~/contexts/FilterContext';
import { useTheme } from '~/contexts/ThemeContext';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import { ColumnConfig } from '../../../../../types/globals';
import ExtraDataTransactions from './ExtraDataTransactions';
import SubFiltersDisplay from './SubFiltersTransactions';

interface TableRow {
  id: string;
  billName: string;
  closedAt: number;
  closedBy: string;
  closedFrom: string;
  closedWith: string;
  covers: number;
  dinningOption: string;
  extraCharges?: any;
  extraChargesValue: number;
  items: any;
  itemsValue: number;
  openedAt: number;
  openedBy: string;
  fiscalNumber: string;
  discountsValue: number;
  owner: string;
  payments: any;
  paymentsValue: number;
  printedBills?: any;
  section: string;
  source: string;
  transfers: any[];
  subTotalValue: number;
  totalValue: number;
}

const icons = {
  comp: '/assets/transactions/comp.svg',
  void: '/assets/transactions/void.svg',
  card: '/assets/transactions/card.svg',
  cash: '/assets/transactions/cash.svg',
  multiple: '/assets/transactions/transfer.svg',
  '3rdParty': '/assets/transactions/3rdParty.svg',
  giftCard: '/assets/transactions/giftCard.svg',
  mealTicket: '/assets/transactions/mealTicket.svg',
  online: '/assets/transactions/online.svg',
  valueTicket: '/assets/transactions/valueTicket.svg',
  wireTransfer: '/assets/transactions/wireTransfer.svg',
  cashless: '/assets/transactions/cashless.svg',
  voucher: '/assets/transactions/voucher.svg',
  tapToPay: '/assets/transactions/taptopay.svg',
};

const OPTIONS = [
  'closedWith',
  'hasDiscounts',
  'hasTransfers',
  'hasBillsIssued',
];

type FilterCriteria = {
  closedWith?: 'receipt' | 'comp' | 'void' | 'any';
  hasDiscounts?: 'yes' | 'no' | 'any';
  hasTransfers?: 'yes' | 'no' | 'any';
  hasBillsIssued?: 'yes' | 'no' | 'any';
};

function filterTableData(
  data: TableRow[],
  filters: FilterCriteria
): TableRow[] {
  return data.filter(row => {
    if (filters.closedWith && filters.closedWith !== 'any') {
      switch (filters.closedWith) {
        case 'receipt':
          if (row.closedWith === 'comp' || row.closedWith === 'void')
            return false;
          break;
        case 'comp':
          if (row.closedWith !== 'comp') return false;
          break;
        case 'void':
          if (row.closedWith !== 'void') return false;
          break;
      }
    }

    if (filters.hasDiscounts && filters.hasDiscounts !== 'any') {
      const hasDiscounts = row.discountsValue && row.discountsValue > 0;
      if (
        (filters.hasDiscounts === 'yes' && !hasDiscounts) ||
        (filters.hasDiscounts === 'no' && hasDiscounts)
      ) {
        return false;
      }
    }

    if (filters.hasTransfers && filters.hasTransfers !== 'any') {
      const hasTransfers = row.transfers !== undefined;
      if (
        (filters.hasTransfers === 'yes' && !hasTransfers) ||
        (filters.hasTransfers === 'no' && hasTransfers)
      ) {
        return false;
      }
    }

    if (filters.hasBillsIssued && filters.hasBillsIssued !== 'any') {
      const hasBillsIssued = row.printedBills !== undefined;
      if (
        (filters.hasBillsIssued === 'yes' && !hasBillsIssued) ||
        (filters.hasBillsIssued === 'no' && hasBillsIssued)
      ) {
        return false;
      }
    }

    return true;
  });
}

export default function TransactionsTable({
  tableData,
  currency,
}: {
  tableData: any;
  currency?: string;
}) {
  const { theme } = useTheme();
  const { filterValues } = useFilters();
  const { t } = useTranslation('');

  const transactionsTablConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'closedWith',
      textAlign: 'start',
      label: 'Item',
      render: (row: TableRow, _, __, isSelected) => {
        return (
          <img
            src={
              icons[
                row.closedWith as
                  | 'comp'
                  | 'void'
                  | 'card'
                  | 'cash'
                  | '3rdParty'
                  | 'giftCard'
                  | 'mealTicket'
                  | 'online'
                  | 'valueTicket'
                  | 'wireTransfer'
                  | 'cashless'
              ]
            }
            alt={row.closedWith}
            style={{
              width: '30px',
              filter: isSelected
                ? 'brightness(0) saturate(100%) invert(100%) sepia(7%) saturate(0%) hue-rotate(39deg) brightness(106%) contrast(109%)'
                : 'brightness(0) saturate(100%) invert(77%) sepia(0%) saturate(230%) hue-rotate(85deg) brightness(91%) contrast(83%)',
            }}
          />
        );
      },
    },
    {
      id: 'closedAt',
      textAlign: 'start',
      label: 'Closed At',
      render: (row: TableRow, _, __, isSelected) => {
        return (
          <Typography
            sx={{
              color: isSelected ? '#FFFFFF' : '#AAAAAA',
              fontSize: '14px',
            }}
          >
            {(() => {
              if (!row?.closedAt) return '-';
              const openedDate = new Date(row.closedAt * 1000);
              const now = new Date();
              const timeString = openedDate.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
              });
              const isToday = openedDate.toDateString() === now.toDateString();
              if (isToday) {
                return timeString;
              }
              const day = openedDate.getDate().toString().padStart(2, '0');
              const month = (openedDate.getMonth() + 1)
                .toString()
                .padStart(2, '0');
              if (openedDate.getFullYear() === now.getFullYear()) {
                return `${day}/${month}, ${timeString}`;
              }
              return `${day}/${month}/${openedDate.getFullYear()}, ${timeString}`;
            })()}
          </Typography>
        );
      },
    },
    {
      id: 'billName',
      textAlign: 'start',
      label: 'Bill Name',
      render: (row: TableRow, _, __, isSelected) => {
        return (
          <Typography
            sx={{
              fontWeight: 500,
              color: isSelected ? '#FFFFFF' : 'inherit',
            }}
          >
            {row.billName}
          </Typography>
        );
      },
    },
    {
      id: 'owner',
      textAlign: 'start',
      label: 'Owner',
      render: (row: TableRow, _, __, isSelected) => {
        return (
          <Typography
            sx={{
              color: isSelected ? '#FFFFFF' : '#AAAAAA',
              fontWeight: 300,
              fontSize: '14px',
            }}
          >
            {t('transactionsPage.ownedBy')}
            <span
              style={{
                fontWeight: 500,
                color: isSelected
                  ? '#FFFFFF'
                  : theme.palette.mode == 'light'
                    ? 'black '
                    : '#fff ',
              }}
            >
              {' '}
              {row.owner}
            </span>
          </Typography>
        );
      },
    },
    {
      id: 'fiscalNumber',
      textAlign: 'start',
      label: 'Fiscal Receipt',
      render: (row: TableRow, _, __, isSelected) => {
        if (!row.fiscalNumber) return;
        return (
          <Typography
            sx={{
              color: isSelected ? '#FFFFFF' : '#AAAAAA',
              fontWeight: 300,
              fontSize: '14px',
            }}
          >
            {t('transactionsPage.fiscalReceipt')}
            <span
              style={{
                fontWeight: 500,
                color: isSelected
                  ? '#FFFFFF'
                  : theme.palette.mode == 'light'
                    ? 'black '
                    : '#fff ',
              }}
            >
              #{row.fiscalNumber === '-1' ? 'Demo' : row.fiscalNumber}
            </span>
          </Typography>
        );
      },
    },
    {
      id: 'totalValue',
      textAlign: 'start',
      label: 'Total Value',
      render: (row: TableRow, _, __, isSelected) => {
        return (
          <Typography
            sx={{
              color: isSelected
                ? '#FFFFFF'
                : theme.palette.mode == 'light'
                  ? 'black'
                  : '#fff ',
              fontSize: '14px',
              textDecoration: row.closedWith === 'comp' ? 'line-through' : '',
            }}
          >
            {row.closedWith === 'void'
              ? currency && 0 + ' ' + currency
              : formatAndDivideNumber(row.totalValue)}
          </Typography>
        );
      },
    },
  ];

  const filteredData = useMemo(() => {
    return filterTableData(tableData, filterValues);
  }, [tableData, filterValues]);

  return (
    <>
      <Box sx={{ py: 7, width: '100%' }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            flexDirection: { xs: 'column-reverse', md: 'row' },
            alignItems: { xs: 'end', lg: 'center' },
            mb: 2,
          }}
        >
          <SubFiltersDisplay />
        </Box>
        <SimpleTable
          config={transactionsTablConfig}
          data={filteredData}
          searchMaxWidth={500}
          separateFirstColumn={true}
          searchPlaceHolder={t('transactionsPage.searchTransactions')}
          enableSidePanel={true}
          renderSidePanel={rowData => (
            <ExtraDataTransactions extraData={{ ...rowData, currency }} />
          )}
        />
      </Box>
    </>
  );
}
