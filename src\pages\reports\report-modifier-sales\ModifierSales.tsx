import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { downloadCSV, useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListHospitalityCategoriesLive, useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import { CurrencyType } from '~/utils/formatNumber';
import { FieldOption } from '../../../../types/globals';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import ModifierSalesGraph from './components/ModifierSalesGraph';
import ModifierSalesTable from './components/ModifierSalesTable';
import { useGetListLive } from '@react-admin/ra-realtime';

const REPORT_TYPE = 'modifiers';
const DEFAULT_FIELDS = [
  { isChecked: true, value: 'groupId' },
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'measureUnit' },
  { isChecked: false, value: 'variant' },
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'prepStation' },
  { isChecked: true, value: 'netValue' },
  { isChecked: true, value: 'value' },
];

export default function ModifierSales() {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<CurrencyType>();
  const [tableFields, setTableFields] = useState<FieldOption[]>(DEFAULT_FIELDS);
  const [groupingItems, setGroupingItems] = useState<string[]>([]);
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();
  // TODO! change to useGetListLive after implementation
  const { data: modifiersLibrary } = useGetList('modifiers');
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});
  const updateCommonField = (key: string, value: OptionType[]) =>
    setCommonFields(prev => ({ ...prev, [key]: value }));
  const contentRef = useRef<HTMLDivElement>(null);
  const defaultFilterValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }
      if (!dateRange?.[0] || !dateRange?.[1]) return;
      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonValues = getReportCommonFieldsValues(REPORT_TYPE, data);
        const memberOptions: OptionType[] = commonValues.member.map(
          (member, idx) => ({
            label: member,
            value: commonValues.memberId[idx],
          })
        );
        updateCommonField('member', memberOptions);
        const floorOptions: OptionType[] = commonValues.section.map(
          section => ({
            label: section,
            value: section,
          })
        );
        updateCommonField('floor', floorOptions);
        const serviceTypeOptions: OptionType[] = commonValues.serviceType.map(
          service => ({
            label: capitalize(service),
            value: service,
          })
        );
        updateCommonField('serviceType', serviceTypeOptions);
        const sourceOptions: OptionType[] = commonValues.source.map(source => ({
          label: SourceTypes[source as keyof typeof SourceTypes] || source,
          value: source,
        }));
        updateCommonField('sources', sourceOptions);
      } catch (error) {
        console.error(error);
      }
    }
    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { graphData, composedFilters, baseGroupedData } = useMemo(() => {
    if (!rawData || !filters)
      return { graphData: {}, composedFilters: [], baseGroupedData: [] };
    if (rawData.length) setCurrency(rawData[0].currency);

    const appliedFilters = composeFilters(filters, REPORT_TYPE);

    const filteredData = filterReport(REPORT_TYPE, rawData, appliedFilters, [
      { field: 'variant', operator: 'notIn', value: ['no', 'allergy'] },
      { field: 'value', operator: '!=', value: 0 },
    ]);

    const topProductIds = groupReport(REPORT_TYPE, filteredData, [], ['id'])[0]
      ?.report.sort((a, b) => b.value - a.value)
      ?.slice(0, 5)
      ?.map(el => el.id);

    const filteredByTopIds = filterReport(
      REPORT_TYPE,
      rawData,
      appliedFilters,
      [
        { field: 'id', operator: 'in', value: topProductIds },
        { field: 'variant', operator: 'notIn', value: ['no', 'allergy'] },
        { field: 'value', operator: '!=', value: 0 },
      ]
    );
    const groupedByHour = groupReport(
      REPORT_TYPE,
      filteredByTopIds,
      ['hourOfDay'],
      ['id']
    );
    const labels = groupedByHour?.map(group => group.hourOfDay.toString());

    const datasets: { label: string; data: number[] }[] = topProductIds?.map(
      id => ({
        label: id,
        data: [],
      })
    );

    groupedByHour.forEach(({ report: items }) => {
      datasets.forEach(ds => {
        const item = items.find(i => i.id == ds.label);
        const value = item?.value || 0;
        const formattedValue = value / 10000;
        ds.data.push(formattedValue);
      });
    });
    const computedGraphData = {
      datasets: datasets?.map(ds => ({ ...ds, label: ds.label })),
      labels,
    };
    const availableFields = tableFields.filter((field: any) => {
      return (
        field.isChecked &&
        reportSpecificFields.modifiers.some(
          discountField =>
            cleanStringArond(field.value) === cleanStringArond(discountField)
        )
      );
    });

    const rawGroupedData = groupReport(
      REPORT_TYPE,
      filteredData,
      [],
      availableFields.map((item: FieldOption) => item.value)
    );
    const hierarchicalGroupedData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        rawGroupedData,
        groupingItems as []
      )[0]?.report || [];

    return {
      graphData: computedGraphData,
      composedFilters: appliedFilters,
      baseGroupedData: hierarchicalGroupedData,
    };
  }, [filters, rawData, groupingItems, tableFields]);

  const [enrichedModifierSalesData, setEnrichedModifierSalesData] = useState<
    any[]
  >([]);
  const fetchEnrichedData = useCallback(async () => {
    if (!baseGroupedData) return;

    const abortController = new AbortController();

    const collectLeafAndGroupIds = (
      items: any[]
    ): { leafIds: string[]; groupIds: Set<string> } => {
      let leafIds: string[] = [];
      let groupIds: Set<string> = new Set();

      for (const item of items) {
        if (!item.subReport || item.subReport.length === 0) {
          leafIds.push(item.id);
          groupIds.add(item.groupId);
        } else {
          groupIds.add(item.groupId);
          const { leafIds: childLeafIds, groupIds: childGroupIds } =
            collectLeafAndGroupIds(item.subReport);
          leafIds = [...leafIds, ...childLeafIds];
          childGroupIds.forEach(id => groupIds.add(id));
        }
      }

      return { leafIds, groupIds };
    };
    try {
      const { leafIds, groupIds } = collectLeafAndGroupIds(baseGroupedData);
      const itemNameMap: Record<string, string> = {};

      leafIds.map(id => {
        try {
          const modifierFound = modifiersLibrary?.find(item => item.id === id);
          itemNameMap[id] = modifierFound?.name;
        } catch (error) {
          console.error(`Failed to fetch item with id: ${id}`, error);
          itemNameMap[id] = id;
        }
      });

      const categoryNameMap: Record<string, string> = {};
      Array.from(groupIds).map(groupId => {
        try {
          const categoryFound = categories?.find(
            category => category.id === groupId
          );
          categoryNameMap[groupId] = categoryFound?.name;
        } catch (error) {
          console.error(`Failed to fetch category with id: ${groupId}`, error);
          categoryNameMap[groupId] = groupId;
        }
      });

      const enrichItems = (items: any[]): any[] => {
        return items.map(item => {
          if (item.groupedBy) {
            const groupField = item.groupedBy.field;
            const groupValue = item.groupedBy.value;

            return {
              ...item,
              name:
                groupField === 'groupId'
                  ? categoryNameMap[groupValue] || groupValue
                  : `${capitalize(groupField)}: ${groupValue}`,
              groupName:
                groupField === 'groupId' && item.groupedBy
                  ? categoryNameMap[groupValue] || groupValue
                  : '',
              subReport: item.subReport
                ? enrichItems(item.subReport)
                : undefined,
            };
          } else {
            const enrichedItem = {
              ...item,
              name: itemNameMap[item.id] || item.id,
              groupName: categoryNameMap[item.groupId] || item.groupId,
            };
            if (item.subReport && item.subReport.length > 0) {
              enrichedItem.subReport = enrichItems(item.subReport);
            }
            return enrichedItem;
          }
        });
      };

      const enriched = enrichItems(baseGroupedData);
      setEnrichedModifierSalesData(enriched);
    } catch (error: any) {
      if (error.name !== 'AbortError')
        console.error('Error fetching enriched data:', error);
    }
    return () => abortController.abort();
  }, [baseGroupedData, modifiersLibrary, categories]);

  useEffect(() => {
    fetchEnrichedData();
  }, [fetchEnrichedData]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedModifierSalesData);
    return remapReports(sortedData, 'id');
  }, [enrichedModifierSalesData]);

  const onChangeGrouping = (items: string[]) => setGroupingItems(items);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const formattedFilters = useMemo(() => {
    if (!filters) return {};

    const getLabel = (field: string, value: string) => {
      const options = commonFields[field];
      const found = options?.find(opt => opt.value === value);
      return found?.label || value;
    };

    return {
      sellpoint: filters.sellpointId,
      dateRange: filters.dateRange.map(d => d?.format('YYYY-MM-DD')),
      timeRange: filters.timeRange,
      member: getLabel('member', filters.member ?? ''),
      floor: getLabel('floor', filters.floor ?? ''),
      serviceType: getLabel('serviceType', filters.diningOption),
      source: getLabel('sources', filters.source),
    };
  }, [filters, commonFields]);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report modifier sales';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Modifier',
        'VAT',
        'Prep Station',
        'Category',
        'Unit',
        'Items Sold',
        'Gross Sales',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Sales',
      ].join(','),
      ...finalTableData.map(el =>
        [
          el.name,
          el.vat,
          el.prepStation,
          categories?.find(cat => cat.id === el.groupId)?.name || el.groupId,
          el.measureUnit,
          el.quantity / 1000 || 0,
          el.value / 10000 || 0,
          -el.discountsValue / 10000 || 0,
          -el.couponsValue / 10000 || 0,
          el.promotionsValue / 10000 || 0,
          el.netValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        '',
        '',
        '',
        finalTableData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        finalTableData.reduce((acc, el) => acc + el.value, 0) / 10000,
        -finalTableData.reduce((acc, el) => acc + (el.discountsValue || 0) / 10000, 0),
        -finalTableData.reduce((acc, el) => acc + (el.couponsValue || 0) / 10000, 0),
        finalTableData.reduce((acc, el) => acc + (el.promotionsValue || 0) / 10000, 0),
        finalTableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'modifierSales');
  };


  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('menu.modifierSales')}
        description={
          <>
            {t('menu.modifierSalesDescription')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <ReportFilters
        defaultValues={defaultFilterValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
        contentRef={contentRef}
        handleExport={handleExport}
      />
      <ReportDateTitle />
      <ModifierSalesGraph data={graphData || {}} currency={currency} />
      <ModifierSalesTable
        formattedFilters={formattedFilters}
        tableData={finalTableData || []}
        fields={tableFields}
        setFields={setTableFields}
        groupingItems={groupingItems}
        reportType={REPORT_TYPE}
        rawData={rawData}
        composedFilters={composedFilters}
        filters={filters}
        onChangeGrouping={onChangeGrouping}
      />
    </Box>
  );
}
