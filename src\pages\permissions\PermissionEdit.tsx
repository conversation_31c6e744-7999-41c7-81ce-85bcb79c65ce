import { useState } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import { Button, Theme, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm, TextField, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import CustomDeleteWithConfirmButton from '../../components/molecules/CustomDeleteWithConfirmButton';
import ModalHeader from '../../components/molecules/ModalHeader';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { PermissionsParts } from './components';

export default function PermissionEdit() {
  const redirect = useRedirect();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const [step, setStep] = useState<number>(0);

  const handleClose = () => {
    setStep(0);
    redirect('list', RESOURCES.PERMISSIONS);
  };

  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="optimistic">
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title={
            <TextField
              sx={{ fontSize: isXSmall ? '18px' : '24px', fontWeight: 'bold' }}
              source="name"
            />
          }
          alignCenter={!isXSmall}
        >
          <>
            <CustomDeleteWithConfirmButton
              icon={isXSmall ? <DeleteIcon /> : <></>}
              sx={{ mr: 2, background: 'rgba(0,0,0,.05)' }}
              field="name"
              label={t('shared.delete')}
            />
            {step === 0 ? (
              <Button variant="contained" onClick={() => setStep(1)}>
                {t('shared.continue')}
              </Button>
            ) : (
              <SaveButton
                type="button"
                label={t('shared.save')}
                icon={<></>}
                alwaysEnable
                mutationOptions={{ onSuccess: handleClose }}
              />
            )}
          </>
        </ModalHeader>

        <PermissionsParts.Form step={step} setStep={setStep} edit />
      </SimpleForm>
    </EditDialog>
  );
}
