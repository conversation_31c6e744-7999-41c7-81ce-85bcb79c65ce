import { Box } from '@mui/material';
import PageTitle from '~/components/molecules/PageTitle';
import { MemberCreate } from './MemberCreate';
import { MemberEdit } from './MemberEdit';
import { MemberList } from './MemberList';
import MemberShow from './MemberShow';
import { useTranslation } from 'react-i18next';


export default function MemberPage() {
  const { t } = useTranslation('');
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
          mt: 2,
        }}
        title={t('members.title')}
        description={
          <>
            {t('members.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <MemberList />
      <MemberShow />
      <MemberCreate />
      <MemberEdit />
    </Box>
  );
}
