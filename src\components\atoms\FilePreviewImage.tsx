import { useMemo } from 'react';
import RefreshIcon from '@mui/icons-material/Refresh';
import { Box, CircularProgress } from '@mui/material';

import { useFileUrl } from '~/hooks/useFileUrl';
import { UploadedFile } from '~/types/fileUpload';

interface FilePreviewImageProps {
  file: UploadedFile;
  size?: number;
  onClick?: () => void;
  disabled?: boolean;
  theme: any;
}

/**
 * Component for displaying file preview images with automatic URL management
 * and expiration handling via the useFileUrl hook
 */
export function FilePreviewImage({
  file,
  size = 40,
  onClick,
  disabled = false,
  theme,
}: FilePreviewImageProps) {
  // Memoize options to prevent unnecessary re-renders - static options
  const options = useMemo(
    () => ({
      imageVariant: 'thumbnail' as const,
    }),
    [file.x] // Include x flag to detect bucket transitions
  );

  const { url, loading, error, refresh } = useFileUrl(file, options);

  if (loading) {
    return (
      <Box
        sx={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          backgroundColor: theme.palette.action.hover,
        }}
      >
        <CircularProgress size={size * 0.6} />
      </Box>
    );
  }

  if (error || !url) {
    return (
      <Box
        sx={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${theme.palette.error.main}`,
          borderRadius: 1,
          cursor: 'pointer',
          backgroundColor: theme.palette.error.light,
          opacity: 0.7,
          '&:hover': {
            opacity: 1,
          },
        }}
        onClick={refresh}
        title="Click to retry loading image"
      >
        <RefreshIcon fontSize="small" color="error" />
      </Box>
    );
  }

  return (
    <Box
      component="img"
      src={url}
      alt={file.fn}
      onClick={onClick}
      sx={{
        width: size,
        height: size,
        borderRadius: 1,
        objectFit: 'cover',
        border: `1px solid ${theme.palette.divider}`,
        cursor: onClick && !disabled ? 'pointer' : 'default',
        opacity: disabled ? 0.6 : 1,
        transition: 'filter 0.2s ease-in-out',
        '&:hover':
          onClick && !disabled
            ? {
                filter: 'brightness(0.8)',
              }
            : {},
      }}
    />
  );
}
