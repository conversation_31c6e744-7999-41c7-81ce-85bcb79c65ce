import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import PageTitle from '../../components/molecules/PageTitle';
import SectionCreate from './SectionCreate';
import SectionEdit from './SectionEdit';
import SectionList from './SectionList';

export default function SectionPage() {
  const { t } = useTranslation();
  const { sellPointId, isLoading } = useGlobalResourceFilters();

  if (isLoading || !sellPointId) {
    return null;
  }

  return (
    <Box p={2}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('floorPlansPage.title')}
        description={
          <>
            {t('floorPlansPage.description')}
            <br />
            {t('floorPlansPage.description2')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />

      <SectionList sellPointId={sellPointId} />
      <SectionEdit sellPointId={sellPointId} />
      <SectionCreate sellPointId={sellPointId} />
    </Box>
  );
}
