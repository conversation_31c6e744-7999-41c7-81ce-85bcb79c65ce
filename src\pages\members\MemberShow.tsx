import { useCallback, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Grid, Typography } from '@mui/material';
import { ShowDialog } from '@react-admin/ra-form-layout';
import {
  EmailField,
  Link,
  ReferenceField,
  SimpleShowLayout,
  TextField,
  useGetRecordId,
  useRecordContext,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import GrayBgContainer from '../../components/atoms/GrayBgContainer';
import ListCard from '../../components/atoms/ListCard';
import getSideModalProps from '../../utils/getSideModalProps';
import { PhoneField } from '~/components/atoms/PhoneField';

const Label = ({ value }: { value: string }) => (
  <Typography
    // @ts-ignore
    variant="label"
    fontWeight={500}
    sx={{ opacity: 0.5, display: 'block', textTransform: 'uppercase' }}
  >
    {value}
  </Typography>
);

const MemberShowInner = () => {
  const redirect = useRedirect();
  const recordId = useGetRecordId();
  const { t } = useTranslation('');
  const [showPasscode, setShowPasscode] = useState(false);
  const record = useRecordContext();

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.TEAM_MEMBERS);
  }, [redirect]);

  return (
    <>
      {/* Header */}
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 1.5,
          position: 'sticky',
          top: 0,
          bgcolor: 'background.paper',
          zIndex: 3,
        }}
      >
        <Button
          // @ts-ignore
          variant="close-btn"
          aria-label="close"
          onClick={handleClose}
          sx={{ '& span': { mr: 0 } }}
        >
          <CloseIcon fontSize="small" />
        </Button>
        <Typography variant="h2">
          <TextField
            sx={{ fontSize: 24, fontWeight: '700' }}
            source="displayName"
          />
        </Typography>
        <Box
          onClick={() => {
            redirect('edit', RESOURCES.TEAM_MEMBERS, recordId);
          }}
          sx={{ p: 1, cursor: 'pointer' }}
        >
          {/* @ts-ignore */}
          <Typography variant="label" color="primary.main" fontSize={{xs: 12, md: 15}}>
            {t('shared.edit')}
          </Typography>
        </Box>
      </Box>

      {/* Body */}
      <GrayBgContainer
        sx={{
          p: 1.5,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          minHeight: 'calc(100vh - 70px)',
        }}
      >
        {/* Personal Information Section */}
        <ListCard
          sx={{
            width: '100%',
            alignItems: 'flex-start',
            py: 2.5,
            px: 3,
            cursor: 'default',
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h4">
                {t('members.personalInformation')}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Label value={t('members.firstName')} />
              <TextField source="firstName" label={t('members.firstName')} />
            </Grid>
            <Grid item xs={6}>
              <Label value={t('members.lastName')} />
              <TextField source="lastName" label={t('members.lastName')} />
            </Grid>
            <Grid item xs={6}>
              <Label value={t('members.displayName')} />
              <TextField
                source="displayName"
                label={t('members.displayName')}
              />
            </Grid>
            <Grid item xs={6}>
              <Label value={t('members.phone')} />
              <PhoneField source="phone" />
            </Grid>
            <Grid item xs={12}>
              <Label value={'EMAIL'} />
              <EmailField source="email" defaultValue="--" label="Email" />
            </Grid>
          </Grid>
        </ListCard>

        <ListCard
          sx={{
            width: '100%',
            alignItems: 'flex-start',
            py: 2.5,
            px: 3,
            cursor: 'default',
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h4">{t('menu.permissions')}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Label value={t('members.role')} />
              <ReferenceField
                label={t('members.role')}
                source="roleId"
                reference={RESOURCES.PERMISSIONS}
              >
                <TextField source="name" />
              </ReferenceField>
            </Grid>
            <Grid item xs={12}>
              <Label value={t('members.passcode')} />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {showPasscode && (
                  <TextField source="pin" sx={{ letterSpacing: '2px' }} />
                )}
                <Typography
                  // @ts-ignore
                  variant="label"
                  fontWeight={400}
                  color="primary"
                  onClick={() => setShowPasscode(prev => !prev)}
                >
                  {showPasscode ? 'Hide' : 'Show'} passcode
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </ListCard>
      </GrayBgContainer>
    </>
  );
};

export default function MemberShow() {
  return (
    <ShowDialog {...getSideModalProps({ hideBackdrop: true })}>
      <SimpleShowLayout
        sx={{
          p: 0,
          '& .RaSimpleShowLayout-row': {
            m: 0,
          },
        }}
      >
        <MemberShowInner />
      </SimpleShowLayout>
    </ShowDialog>
  );
}
