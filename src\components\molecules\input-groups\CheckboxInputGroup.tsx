import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import {
  CheckboxGroupInput,
  CheckboxGroupInputProps,
  useChoicesContext,
  useInput,
  useRecordContext,
} from 'react-admin';

const OptionField = () => {
  const record = useRecordContext();

  return (
    // @ts-ignore
    <Typography variant="label" fontWeight={200} sx={{ ml: '5px' }}>
      {record?.name}
    </Typography>
  );
};

export default function CheckboxInputGroup({
  source: sourceProp,
  choices,
  disabled,
  title,
  ...props
}: CheckboxGroupInputProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const { source } = useChoicesContext({
    source: sourceProp,
  });

  if (source === undefined) {
    throw new Error(
      `If you're not wrapping the RadioGroup inside a ReferenceInput, you must provide the source prop`
    );
  }

  const { fieldState } = useInput({ source });

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        border: `1px solid`,
        borderColor: 'custom.gray400',
        marginTop: '-1px',
      }}
      onClick={event => {
        if (disabled) {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
    >
      {title && (
        <Box
          sx={{
            width: isXSmall ? '100%' : '200px',
            bgcolor: !!fieldState.error ? 'error.light' : 'background.tinted',
            display: 'flex',
            p: 2,
          }}
        >
          {/* @ts-ignore */}
          <Typography variant="label">{title}</Typography>
        </Box>
      )}
      <Box
        sx={{
          px: 3.5,
          pt: 2,
        }}
      >
        <CheckboxGroupInput
          disabled={disabled}
          row={false}
          source="roles"
          choices={choices}
          label=""
          optionText={<OptionField />}
          {...props}
        />
      </Box>
    </Box>
  );
}
