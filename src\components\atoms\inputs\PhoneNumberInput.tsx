import { useEffect, useState } from 'react';
import { Box, TextFieldProps } from '@mui/material';
import { matchIsValidTel, MuiTelInput, MuiTelInputInfo } from 'mui-tel-input';
import { InputProps, useInput, useLocaleState } from 'react-admin';

// European countries - with Romania (RO) as the default
const EUROPEAN_COUNTRIES = [
  'RO',
  'GB',
  'FR',
  'ES',
  'DE',
  'IT',
  'NL',
  'BE',
  'PT',
  'GR',
  'AT',
  'BG',
  'HR',
  'CY',
  'CZ',
  'DK',
  'EE',
  'FI',
  'HU',
  'IE',
  'LV',
  'LT',
  'LU',
  'MT',
  'PL',
  'SK',
  'SI',
  'SE',
];

// Custom validator using matchIsValidTel
export const validatePhone = (value: string) => {
  if (!value) return undefined;
  return matchIsValidTel(value) ? undefined : 'Invalid phone number';
};

export interface PhoneNumberInputProps
  extends Omit<InputProps, 'children' | 'defaultValue'> {
  source: string;
  defaultCountry?: string;
  sx?: TextFieldProps['sx'];
  placeholder?: string;
}

export default function PhoneNumberInput({
  source,
  defaultCountry = 'RO',
  validate,
  sx,
  placeholder,
  ...inputProps
}: PhoneNumberInputProps) {
  const [locale] = useLocaleState();

  // Use react-admin's useInput hook for form integration
  const {
    field: { onChange: formOnChange, value: formValue, onBlur },
    fieldState: { error, invalid, isTouched },
    formState: { isSubmitted },
  } = useInput({
    source,
    validate,
  });

  // Initialize with form value or empty string
  const [phoneValue, setPhoneValue] = useState(formValue || '');
  const [info, setInfo] = useState<MuiTelInputInfo | null>(null);

  // Update form value when phone changes
  const handleChange = (newValue: string, info: MuiTelInputInfo) => {
    setPhoneValue(newValue);
    setInfo(info);
    // Remove spaces before updating the form value
    const unformattedValue = newValue.replace(/\s+/g, '');
    formOnChange(unformattedValue);
  };

  // Update local state if form value changes externally
  useEffect(() => {
    if (formValue !== undefined && formValue !== phoneValue) {
      setPhoneValue(formValue);
    }
  }, [formValue]);

  return (
    <Box sx={{ display: 'flex', flex: 1 }}>
      <MuiTelInput
        value={phoneValue}
        onChange={handleChange}
        onBlur={onBlur}
        defaultCountry={defaultCountry as any}
        continents={['EU']}
        langOfCountryName={locale}
        forceCallingCode
        focusOnSelectCountry
        preferredCountries={EUROPEAN_COUNTRIES as any}
        placeholder={placeholder}
        sx={{
          flex: 1,
          '.MuiOutlinedInput-notchedOutline': {
            border: 0,
            borderColor: 'transparent !important',
          },
          '.MuiFormControl-root': {
            m: 0,
          },
          '.MuiIconButton-root': {
            pl: 0,
          },
          ...sx,
        }}
        error={invalid && (isTouched || isSubmitted)}
        {...inputProps}
      />
    </Box>
  );
}
