import { Box } from '@mui/material';

import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  vat: number | string | undefined;
  extraChargesValue: number;
  discountsValue: number;
  couponsValue: number;
  tipsValue: number;
  totalValue: number;
  finalValue: number;
  netValue: number;
  vatValue: number;
  promotionsValue: number;
};

export default function VatCards({ tableData }: { tableData: any }) {
  const calculateCardValues = (itemsData: TableRow[]) => {
    const vatSales = mergeAndSumObjects(
      itemsData.filter(item => item.vat !== 0)
    );
    const nonVatSales = mergeAndSumObjects(
      itemsData.filter(item => item.vat === 0)
    );

    const totalItems = mergeAndSumObjects(tableData);

    return {
      vatSales,
      nonVatSales,
      totalItems,
    };
  };

  const cardValues = calculateCardValues(tableData);
  const cardsConfig: { title: string; value: string | number }[] = [
    {
      title: 'Net Sales',
      value: cardValues.totalItems.netValue
        ? formatAndDivideNumber(cardValues.totalItems.netValue)
        : '-',
    },
    {
      title: 'Taxable Sales',
      value: cardValues.vatSales.netValue
        ? formatAndDivideNumber(cardValues.vatSales.netValue)
        : '-',
    },
    {
      title: 'Non Taxable Sales',
      value: cardValues.nonVatSales.netValue
        ? formatAndDivideNumber(cardValues.nonVatSales.netValue)
        : '-',
    },
    {
      title: 'VAT Amount',
      value: cardValues.totalItems.vatValue
        ? formatAndDivideNumber(cardValues.totalItems.vatValue)
        : '-',
    },
  ];

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: { xs: 'grid', sm: 'flex' },
          gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'unset' },
          gap: 8,
          mt: 4,
          alignItems: 'center',
          justifyContent: 'space-around',
          pb: 2,
          borderBottom: '2px solid #F2F2F2',
          '@media print': {
            borderBottom: '2px solid black',
          },
        }}
      >
        {cardsConfig.map(
          (item: { title: string; value: string | number }, index: number) => {
            return (
              <Box
                key={index}
                sx={{
                  gridColumn: 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ExtraDataCard item={item} />
              </Box>
            );
          }
        )}
      </Box>
    </>
  );
}
