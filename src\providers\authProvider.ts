/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
} from 'firebase/auth';
// Removed onAuthStateChanged
import { AuthProvider, UserIdentity } from 'react-admin';

import { auth } from '../configs/firebaseConfig';
import { accountStorage } from './utils/accountStorage';
import cleanupActions from './utils/cleanupActions';

interface LoginParams {
  email?: string;
  password?: string;
  method: 'email' | 'google';
}

const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({ prompt: 'select_account' });

// Once the login() method returns, the login form redirects to the previous page, or to the admin index if the user just arrived.
// If the login() method throws an Error, react-admin displays the error message to the user in a notification.
// If the login() method returns an object with a redirectTo path, react-admin will redirect the user to that path after login.
// You can use this feature to redirect the user to a specific page, or to disable redirection by returning false.
const login = async ({
  email,
  password,
  method,
}: LoginParams): Promise<{ redirectTo?: string | boolean } | void> => {
  console.log('login called with params:', { email, password, method });
  try {
    if (method === 'google') {
      await signInWithPopup(auth, googleProvider);
    } else if (method === 'email' && email && password) {
      await signInWithEmailAndPassword(auth, email, password);
    } else {
      throw new Error('Invalid login method or missing credentials');
    }
    return Promise.resolve();
  } catch (error: any) {
    throw new Error('Login failed: ' + error.message);
  }
};
// After logout, react-admin redirects the user to the string returned by authProvider.logout() or to the /login url if the method returns nothing.
// You can customize the redirection url by returning a route string, or false to disable redirection after logout.
const logout = async (params: any): Promise<string | void | false> => {
  console.log('logout called');
  cleanupActions.runAll();
  if (auth.currentUser) await signOut(auth);
  return Promise.resolve();
};

// Fortunately, each time the user navigates to a list, edit, create or show page, react-admin calls the authProvider.checkAuth() method.
// If this method throws an error, react-admin calls authProvider.logout() and redirects the user to the login page.
// So it’s the ideal place to make sure the credentials are still valid.
// When checkAuth() throws an error, react-admin redirects to the /login page by default.
// You can override this path by throwing an error with a redirectTo property
// If both authProvider.checkAuth() and authProvider.logout() return a redirect URL, the one from authProvider.checkAuth() takes precedence.
// When checkAuth() throws an error, react-admin displays a notification to the end user. You can customize this message by throwing an error with a particular message
// You can also disable this notification completely by rejecting an error with a false message.
const checkAuth = async (): Promise<void> => {
  console.log('checkAuth called');
  if (!auth.currentUser) {
    return Promise.reject(new Error('User not authenticated'));
  }
  return Promise.resolve();
};

// Fortunately, each time the dataProvider returns an error, react-admin calls authProvider.checkError() to check if the error is an authentication error.
// If this method throws an error itself, react-admin calls the authProvider.logout() method immediately, and redirects the user to the login page.
// When checkError() throws an error, react-admin redirects to the /login page, or to the error.redirectTo url. That means you can override the default redirection
// It’s possible to not log the user out, and to instead redirect them. You can do this by passing error.logoutUser = false along with an error.redirectTo url.
// When checkError() throws an error, react-admin displays a notification to the end user, unless the error.message is false. That means you can disable or customize the notification on error.
const checkError = async (error: {
  message?: string;
  status?: number;
}): Promise<void> => {
  console.log('checkError called with error:', error);
};

// React-admin delegates the storage of the connected user identity to the authProvider.
// If it exposes a getIdentity() method, react-admin will call it to read the user details.
// getIdentity should return an object with at least an id field.
// You can also return a fullName and an avatar field, or any other field you need in your app
const getIdentity = async (): Promise<UserIdentity> => {
  console.log('getIdentity called');
  const user = auth.currentUser;
  if (!user) {
    return Promise.reject(new Error('User not authenticated'));
  }
  return Promise.resolve({
    id: user.uid,
    fullName: user.displayName || user.email || 'N/A',
    avatar: user.photoURL || undefined,
    email: user.email || undefined,
  });
};

// getPermissions() lets you return an arbitrary permissions object.
// This object can be used by React components to enable or disable UI elements based on the user’s role.
// The permissions can be in any format: a simple string (e.g. 'editor'),
// an array of strings (e.g. ['editor', 'admin']),
// or a complex object (e.g. { posts: 'editor', comments: 'moderator', users: 'admin' }).
// If getPermissions() throws an error, the error will be passed to checkError
const getPermissions = async (): Promise<any> => {
  console.log('getPermissions called');
  const user = auth.currentUser;
  if (!user) {
    return Promise.reject(new Error('User not authenticated'));
  }
  const cachedSelectedAccount = accountStorage.getSelectedAccount();
  if (cachedSelectedAccount === null) {
    return Promise.resolve({});
  }
  const idToken = await user.getIdTokenResult(false);
  const claims = idToken.claims as { a?: { [key: string]: any } };
  const accountPermissions = claims.a?.[cachedSelectedAccount] ?? {};
  return Promise.resolve(accountPermissions);
};

export const authProvider: AuthProvider = {
  login,
  logout,
  checkAuth,
  checkError,
  getIdentity,
  getPermissions,
};
