import { Box, Dialog } from '@mui/material';
import { required, SaveButton, SimpleForm, useRecordContext, useResourceContext, useUpdate } from 'react-admin';
import { useTranslation } from 'react-i18next';
import CustomInput from '~/components/atoms/inputs/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { validateName } from '~/utils/validateName';

export const SectionEditDialog = ({
  open,
  onClose,
  id,
}: {
  open: boolean;
  onClose: () => void;
  id: number;
}) => {
  const [update] = useUpdate();
  const { sellPointId } = useGlobalResourceFilters();
  const { t } = useTranslation();
  const record = useRecordContext();
  const resource = useResourceContext();

  const handleSubmit = (values: any) => {
    update(resource, { meta: { sellPointId }, id: record?.id, data: values, previousData: record }, {
        onSuccess: () => {
          onClose();
        },
      }
    );
  };
  return (
    <Dialog open={open} onClose={onClose}>
      <SimpleForm toolbar={false} onSubmit={handleSubmit} sx={{ p: 0 }}>
        <ModalHeader title={t('floorPlansPage.editFloorPlan')} handleClose={onClose}>
          <SaveButton type="submit" label={t('shared.save')} icon={<></>} alwaysEnable />
        </ModalHeader>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            marginTop: 2,
            p: 2,
          }}
        >
          <Box>
            <CustomInput source="name" label={t('shared.name')} validate={[required(), validateName]} />
            <CustomInput source="active" type="switch" label={t('shared.active')} />
          </Box>
        </Box>
      </SimpleForm>
    </Dialog>
  );
};
