import { useMemo, useState } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  Popover,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { DesktopTimePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

import type { ReportFiltersState } from './ReportFilters';
import type { DateRange } from '@mui/x-date-pickers-pro';
import type { MouseEvent } from 'react';

interface TimeRangePickerBtnProps {
  defaultValues: ReportFiltersState['timeRange'];
  setTimeRange: (timeRange: DateRange<Dayjs> | null) => void;
  disabled?: boolean;
}

enum TimeRangeValues {
  ALL_DAY,
  CUSTOM,
}

const defaultStart = dayjs().startOf('day').hour(9).set('minute', 0);
const defaultEnd = dayjs().startOf('day').hour(23).set('minute', 0);
// TODO: validation (start < end)
export default function TimeRangePickerBtn({
  defaultValues = {},
  setTimeRange,
  disabled,
}: TimeRangePickerBtnProps) {
  const [radioGroupValue, setRadioGroupValue] = useState<TimeRangeValues>(
    defaultValues.allDay ? TimeRangeValues.ALL_DAY : TimeRangeValues.CUSTOM
  );
  const [localTimeRange, setLocalTimeRange] = useState<DateRange<Dayjs> | null>(
    defaultValues.start && defaultValues.end
      ? [defaultValues.start, defaultValues.end]
      : null
  );
  const [startOpen, setStartOpen] = useState(false);
  const [endOpen, setEndOpen] = useState(false);

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);
  const id = open ? 'time-range-popover' : undefined;
  const { t } = useTranslation();

  const label = useMemo(() => {
    if (radioGroupValue == TimeRangeValues.ALL_DAY) {
      return t('reportFilters.allDay');
    } else {
      return `${localTimeRange?.[0]?.format('HH:mm') ?? ''} - ${localTimeRange?.[1]?.format('HH:mm') ?? ''}`;
    }
  }, [localTimeRange, radioGroupValue]);

  const openDropdown = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const closeDropdown = () => {
    if (localTimeRange !== null && (!localTimeRange[0] || !localTimeRange[1])) {
      const newTimeRange: [Dayjs, Dayjs] = [
        localTimeRange?.[0] ?? defaultStart,
        localTimeRange?.[1] ?? defaultEnd,
      ];
      setLocalTimeRange([
        localTimeRange?.[0] ?? defaultStart,
        localTimeRange?.[1] ?? defaultEnd,
      ]);
      setTimeRange(newTimeRange);
    } else {
      setTimeRange(localTimeRange);
    }
    setAnchorEl(null);
  };

  const handleRadioGroupChange = (event: any) => {
    setRadioGroupValue(event.target.value);
    if (event.target.value == TimeRangeValues.ALL_DAY) {
      setLocalTimeRange(null);
      setTimeRange(null);
      setAnchorEl(null);
    } else {
      setLocalTimeRange([defaultStart, defaultEnd]);
    }
  };

  return (
    <>
      <Button
        //@ts-ignore
        variant="contained-light"
        disabled={disabled}
        aria-describedby={id}
        onClick={openDropdown}
      >
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={500} color="custom.gray800">
          {label}
        </Typography>

        {!disabled && <KeyboardArrowDownIcon color="disabled" />}
      </Button>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeDropdown}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box p={2} pt={0} width="250px">
          <FormControl sx={{ width: '100%' }}>
            <RadioGroup
              name="radio-btns-time-range"
              value={radioGroupValue}
              onChange={handleRadioGroupChange}
            >
              <FormControlLabel
                value={TimeRangeValues.ALL_DAY}
                label={
                  <Box mt="20px" display="flex" flexDirection="column">
                    {/* @ts-ignore */}
                    <Typography variant="label" fontWeight={300}>
                      {t('reportFilters.allDay')}
                    </Typography>
                    <Typography variant="caption" color="custom.gray600">
                      {t('reportFilters.untilNextDay')}
                    </Typography>
                  </Box>
                }
                control={<Radio />}
              />
              <Divider sx={{ my: 1.5 }} />
              <FormControlLabel
                value={TimeRangeValues.CUSTOM}
                label={
                  // @ts-ignore
                  <Typography variant="label" fontWeight={300}>
                    {t('reportFilters.custom')}
                  </Typography>
                }
                control={<Radio />}
              />
            </RadioGroup>
          </FormControl>

          {radioGroupValue == TimeRangeValues.CUSTOM && (
            <Box
              width="80%"
              margin="auto"
              display="flex"
              flexDirection="column"
              gap={2}
            >
              <div onClick={() => setStartOpen(true)}>
                <DesktopTimePicker
                  ampm={false}
                  label={t('reportFilters.start')}
                  views={['hours']}
                  format="HH"
                  open={startOpen}
                  onOpen={() => setStartOpen(true)}
                  onClose={() => setStartOpen(false)}
                  value={localTimeRange?.[0]}
                  onChange={time => {
                    setLocalTimeRange([time, localTimeRange?.[1] ?? null]);
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </div>

              <div onClick={() => setEndOpen(true)}>
                <DesktopTimePicker
                  ampm={false}
                  label={t('reportFilters.end')}
                  views={['hours']}
                  format="HH"
                  open={endOpen}
                  onOpen={() => setEndOpen(true)}
                  onClose={() => setEndOpen(false)}
                  value={localTimeRange?.[1]}
                  onChange={time => {
                    setLocalTimeRange([localTimeRange?.[0] ?? null, time]);
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                    },
                  }}
                />
              </div>
            </Box>
          )}
        </Box>
      </Popover>
    </>
  );
}
