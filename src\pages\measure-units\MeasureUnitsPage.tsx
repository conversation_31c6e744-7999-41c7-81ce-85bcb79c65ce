import { useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import CustomTable from '~/components/organisms/CustomTable';
import { ColumnConfig, FieldOption } from '../../../types/globals';
import { MeasureUnitsList } from './MeasureUnitsList';

type TableRow = {
  name: string;
  code: string;
  description: string;
};

export default function MeasureUnitsPage() {
  const { t } = useTranslation('');

  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('measure-units.title')}
        description={
          <>
            {t('measure-units.description')}{' '}
            <a href="https://selio.io/support" target="_blank" rel="noreferrer">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <MeasureUnitsList />
    </Box>
  );
}
