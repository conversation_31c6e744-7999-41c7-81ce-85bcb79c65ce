.draggable {
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.draggable > div {
  width: calc(100% - 1px);
  height: 100%;
  margin: 1px 0px 0px 1px;
  display: flex;
  justify-content: flex-start;
  padding: 15px;
  appearance: none;
  outline: none;
  background-color: var(--bg-color);
  transform: translate3d(var(--translate-x, 0), var(--translate-y, 0), 0)
    scale(var(--scale, 1));
  z-index: var(--z-index);
  cursor: grab;
  border-radius: 6px;
}

.draggable > div > div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.draggable:hover > div > div {
  background: rgba(0, 106, 255, 0.3);
  border: 1px solid #006aff;
}

.selectedTile {
  background: rgba(0, 106, 255, 0.45);
  border: 1px solid #006aff;
}

.draggable label {
  text-align: left;
  font-size: 14px;
  font-weight: 300;
  color: white;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: grab;
  pointer-events: none;
}

.dragging > div {
  cursor: grabbing;
}

.draggable .openGroupHandle {
  display: none;
  position: absolute;
  top: calc(50% - 15px);
  right: 15px;
  width: 30px;
  height: 30px;
  background: white;
  border: solid 1px rgb(185, 185, 185);
  border-radius: 50%;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.63);
}

.draggable:hover .openGroupHandle {
  display: flex;
}
