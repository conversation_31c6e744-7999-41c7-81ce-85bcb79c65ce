import styled from '@emotion/styled';
import { Select, Theme, useMediaQuery } from '@mui/material';
import { SelectInput } from 'react-admin';

// @ts-ignore
export const CustomSelect = styled(SelectInput)(({ theme }: any) => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return {
    width: isXSmall ? '100%' : 'unset',
    boxShadow: 'none',
    borderRadius: 4,
    '.MuiOutlinedInput-notchedOutline': {
      border:
        theme.palette.mode == 'light'
          ? '1px solid rgba(0, 0, 0, 0.23)'
          : '1px solid rgba(255, 255, 255, 0.23)',
    },
    'label + &': {
      marginTop: '20px',
    },
    '& .MuiInputBase-input': {
      borderRadius: 4,
      position: 'relative',
      '&:focus, &:active, &:focus-visible, &[expanded="true"] ': {
        backgroundColor: 'primary.veryLight',
      },
    },
  };
});

export const CustomSelectMui = styled(Select)(({ theme }) => {
  return {
    width: '100%',
    boxShadow: 'none',
    height: '45px',
    marginTop: '8px',
    fontSize: '14px',
    borderRadius: 4,
    '.MuiOutlinedInput-notchedOutline': {
      border:
        (theme as any).palette.mode == 'light'
          ? '1px solid rgba(0, 0, 0, 0.23)'
          : '1px solid rgba(255, 255, 255, 0.23)',
    },
    'label + &': {
      marginTop: '4px',
    },
    '& .MuiInputBase-input': {
      borderRadius: 4,
      position: 'relative',
      '&:focus, &:active, &:focus-visible, &[expanded="true"] ': {
        backgroundColor: 'primary.veryLight',
      },
    },
  };
});
