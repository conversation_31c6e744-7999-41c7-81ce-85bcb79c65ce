import { useState } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Button, Theme, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm, useRecordContext } from 'react-admin';
import { useNavigate } from 'react-router-dom';

import CustomDeleteWithConfirmButton from '../../components/molecules/CustomDeleteWithConfirmButton';
import ModalHeader from '../../components/molecules/ModalHeader';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import LoyaltyForm from './components/LoyaltyForm';

export default function LoyaltyEdit() {
  const navigate = useNavigate();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const [isContinueDisabled, setIsContinueDisabled] = useState(true);
  const [step, setStep] = useState<number>(0);

  const handleClose = () => {
    setStep(0);
    navigate('/loyalty');
  };

  return (
    <EditDialog {...getFullscreenModalProps()}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title="Rewards"
          alignCenter={!isXSmall}
        >
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              // @ts-ignore
              variant="contained-light"
              sx={{ px: 3 }}
              onClick={() => {
                if (step === 0) {
                  handleClose();
                } else {
                  setStep(step - 1);
                }
              }}
            >
              Back
            </Button>
            <CustomDeleteWithConfirmButton
              onClick={() => {
                setStep(0);
              }}
              redirect="/loyalty"
              icon={isXSmall ? <DeleteIcon /> : <></>}
              sx={{ background: 'rgba(0,0,0,.05)' }}
              field="name"
            />
            {step !== 3 ? (
              <Button
                disabled={isContinueDisabled}
                variant="contained"
                onClick={() => step !== 3 && setStep(step + 1)}
              >
                Continue
              </Button>
            ) : (
              <SaveButton
                disabled={false}
                type="submit"
                label="Save"
                icon={<></>}
              />
            )}
          </Box>
        </ModalHeader>

        <LoyaltyForm
          step={step}
          setStep={setStep}
          edit
          isEditing={true}
          setIsContinueDisabled={setIsContinueDisabled}
        />
      </SimpleForm>
    </EditDialog>
  );
}
