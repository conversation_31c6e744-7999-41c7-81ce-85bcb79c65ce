import { useInput, useNotify, useRecordContext } from 'react-admin';

import { useFirebase } from '~/contexts/FirebaseContext';
import {
  DEFAULT_FILE_UPLOAD_CONFIG,
  ImageEditorConfig,
  PrivateFileContext,
  UploadedFile,
} from '~/types/fileUpload';
import { fileUploadManager } from '~/utils/FileUploadManager';
import {
  resolveImageEditorConfig,
  StandardImageEditorConfig,
} from '~/utils/standardImageEditor';
import MuiFileUploadInput from './MuiFileUploadInput';

interface FileUploadInputProps {
  source: string;
  label?: string;
  multiple?: boolean;
  accept?: string[];
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  maxSize?: number;
  maxFiles?: number;
  helperText?: string;
  fileType?: 'images' | 'videos' | 'public' | 'private';
  validate?: any;

  // UI props
  variant?: 'default' | 'compact';
  placeholder?: string;
  infoText?: string; // New prop for info icon tooltip/content

  // Private file context (required for private files)
  privateFileContext?: PrivateFileContext;

  // Enhanced validation props
  showValidationErrors?: boolean; // Default: true - deprecated, validation errors now always shown via notifications
  validationDisplay?: 'inline' | 'toast' | 'both'; // Default: 'both' - deprecated, now always uses notifications
  allowInvalidFiles?: boolean; // Default: false

  // Simple validation rules (optional)
  minFileCount?: number;
  minFileSize?: number; // in bytes
  requiredFileTypes?: string[]; // MIME types that are required (stricter than acceptedTypes)

  // Custom validation function
  customValidation?: (file: File) => string | null; // return error message or null if valid

  // Image editor props
  enableImageEditor?: boolean;
  imageEditorConfig?: ImageEditorConfig;
  standardImageConfig?: StandardImageEditorConfig; // New: Use standard sizes
  onImageEdit?: (originalFile: File, editedFiles: File[]) => void; // Updated to handle multiple files from multi-aspect-ratio editing
  onImageEditCancel?: (file: File) => void;
  onImageEditStart?: (file: File) => void;

  // Existing callbacks (maintain backward compatibility)
  onValidationError?: (message: string) => void;
  onUploadSuccess?: (count: number) => void;
  onUploadError?: (error: any) => void;
  onFileUploaded?: (file: UploadedFile) => void;
  onFileMoved?: (file: UploadedFile) => void;
  onFileDeleted?: (file: UploadedFile) => void;
  /**
   * Callback to handle when files are updated (useful for refreshing form data
   * after files are moved from temporary to permanent storage)
   */
  onFilesUpdated?: (files: UploadedFile[]) => void;

  [key: string]: any;
}

export default function FileUploadInput({
  source,
  multiple = DEFAULT_FILE_UPLOAD_CONFIG.multiple,
  maxFiles = DEFAULT_FILE_UPLOAD_CONFIG.maxFiles,
  maxSize = DEFAULT_FILE_UPLOAD_CONFIG.maxSize,
  accept = DEFAULT_FILE_UPLOAD_CONFIG.acceptedTypes,
  helperText,
  validate,
  label,
  disabled = false,
  readOnly = false,
  fileType = 'images',
  required = false,
  privateFileContext,

  // UI props
  variant = 'default',
  placeholder,
  infoText,

  // Enhanced validation props
  showValidationErrors = true, // deprecated
  validationDisplay = 'both', // deprecated
  allowInvalidFiles = false,

  // Simple validation rules (optional)
  minFileCount,
  minFileSize,
  requiredFileTypes,

  // Custom validation function
  customValidation,

  // Image editor props
  enableImageEditor,
  imageEditorConfig,
  standardImageConfig,
  onImageEdit,
  onImageEditCancel,
  onImageEditStart,

  // Callback props
  onValidationError,
  onUploadSuccess,
  onUploadError,
  onFileUploaded,
  onFileMoved,
  onFileDeleted,
  onFilesUpdated,

  ...props
}: FileUploadInputProps) {
  const notify = useNotify();
  const record = useRecordContext();
  const { details } = useFirebase();

  const {
    field: { onChange, value },
    fieldState: { error, invalid },
    formState: { isSubmitting },
  } = useInput({
    source,
    validate,
    ...props,
  });

  // Get current files from form value (not record)
  const currentFiles: UploadedFile[] = Array.isArray(value)
    ? value
    : value
      ? [value]
      : [];

  const handleFilesChange = (files: UploadedFile[]) => {
    onChange(files);
  };

  const handleFileUploaded = (file: UploadedFile) => {
    // Add newly uploaded file to current files
    const updatedFiles = multiple ? [...currentFiles, file] : [file];
    handleFilesChange(updatedFiles);
    onFileUploaded?.(file);
  };

  const handleUploadError = (error: any) => {
    let errorMessage = 'Failed to upload file. ';

    if (!details.user) {
      errorMessage += 'Please log in first.';
    } else if (!details.selectedAccount) {
      errorMessage += 'Please select an account first.';
    } else if (!managerState.isInitialized) {
      errorMessage += 'File upload system is not initialized.';
    } else {
      errorMessage +=
        'Please try again or contact support if the problem persists.';
    }

    notify(errorMessage, { type: 'error' });
    onUploadError?.(error);
  };

  // Check if context is ready for file uploads
  const isContextReady = details.user && details.selectedAccount;
  const managerState = fileUploadManager.getState();
  const isManagerReady =
    managerState.isInitialized &&
    managerState.accountId &&
    managerState.uploadedBy;

  // Create context for private files (use provided context or create from Firebase details)
  const effectivePrivateFileContext: PrivateFileContext | undefined =
    fileType === 'private'
      ? privateFileContext ||
        (details.selectedAccount
          ? { accountId: details.selectedAccount }
          : undefined)
      : undefined;

  // Resolve image editor configuration (standard config takes precedence)
  const resolvedImageEditorConfig = standardImageConfig
    ? resolveImageEditorConfig(standardImageConfig)
    : imageEditorConfig;

  // Check if we should use client-side processing for public images
  const shouldUseClientSideProcessing =
    fileType === 'images' && resolvedImageEditorConfig?.targetSizes?.length;

  return (
    <MuiFileUploadInput
      label={label}
      value={currentFiles}
      onChange={handleFilesChange}
      variant={variant}
      placeholder={placeholder}
      multiple={multiple}
      acceptedTypes={accept}
      disabled={disabled || isSubmitting}
      readOnly={readOnly}
      maxSize={maxSize}
      maxFiles={maxFiles}
      helperText={helperText}
      infoText={infoText}
      fileType={fileType}
      privateFileContext={effectivePrivateFileContext}
      error={invalid}
      errorText={error?.message}
      // Enhanced validation props
      showValidationErrors={showValidationErrors}
      validationDisplay={validationDisplay}
      allowInvalidFiles={allowInvalidFiles}
      // Simple validation rules
      minFileCount={minFileCount}
      minFileSize={minFileSize}
      requiredFileTypes={requiredFileTypes}
      // Custom validation function
      customValidation={customValidation}
      // Image editor props
      enableImageEditor={enableImageEditor}
      imageEditorConfig={resolvedImageEditorConfig}
      onImageEdit={onImageEdit}
      onImageEditCancel={onImageEditCancel}
      onImageEditStart={onImageEditStart}
      // Callback props
      onValidationError={onValidationError}
      onUploadSuccess={onUploadSuccess}
      onUploadError={handleUploadError}
      onFileUploaded={handleFileUploaded}
      onFileMoved={onFileMoved}
      onFileDeleted={onFileDeleted}
      onFilesUpdated={onFilesUpdated}
      {...props}
    />
  );
}
