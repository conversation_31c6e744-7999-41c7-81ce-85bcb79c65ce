import {
  eachDayOfInterval,
  eachMonthOfInterval,
  eachQuarterOfInterval,
  eachWeekOfInterval,
  eachYearOfInterval,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  format,
  isSameDay,
} from 'date-fns-v4';

import { getDateFnsLocale } from './getDateFnsLocale';

// Generate a breakdown of the date interval into years, quarters, months, weeks and days
// this is useful for getting reports from storage in a more efficient way
// instead of having to get 7 days of reports we can get the weekly report
// this means 6 less requests to the storage
export async function getDateIntervalBreakdown(
  start: Date,
  end: Date,
  locale: string
): Promise<string[]> {
  const dfLocale = await getDateFnsLocale(locale);

  let dates = eachDayOfInterval({ start, end });

  const result: string[] = [];

  // Generate all possible years within the interval
  const possibleYears = eachYearOfInterval({ start, end });
  for (const yearStart of possibleYears) {
    const yearEnd = endOfYear(yearStart);
    const yearDates = eachDayOfInterval({ start: yearStart, end: yearEnd });

    if (yearDates.every(date => dates.some(d => isSameDay(d, date)))) {
      result.push(format(yearStart, 'yyyy'));
      dates = dates.filter(date => !yearDates.some(d => isSameDay(d, date)));
    }
  }

  // Generate all possible quarters within the interval
  const possibleQuarters = eachQuarterOfInterval({ start, end });
  for (const quarterStart of possibleQuarters) {
    const quarterEnd = endOfQuarter(quarterStart);
    const quarterDates = eachDayOfInterval({
      start: quarterStart,
      end: quarterEnd,
    });

    if (quarterDates.every(date => dates.some(d => isSameDay(d, date)))) {
      result.push(format(quarterStart, "yyyy'Q'Q"));
      dates = dates.filter(date => !quarterDates.some(d => isSameDay(d, date)));
    }
  }

  // Generate all possible months within the interval
  const possibleMonths = eachMonthOfInterval({ start, end });
  for (const monthStart of possibleMonths) {
    const monthEnd = endOfMonth(monthStart);
    const monthDates = eachDayOfInterval({ start: monthStart, end: monthEnd });

    if (monthDates.every(date => dates.some(d => isSameDay(d, date)))) {
      result.push(format(monthStart, "yyyy'M'M"));
      dates = dates.filter(date => !monthDates.some(d => isSameDay(d, date)));
    }
  }

  // Generate all possible weeks within the interval
  const possibleWeeks = eachWeekOfInterval({ start, end }, dfLocale.options);
  for (const weekStart of possibleWeeks) {
    const weekEnd = endOfWeek(weekStart, dfLocale.options);
    const weekDates = eachDayOfInterval({ start: weekStart, end: weekEnd });

    if (weekDates.every(date => dates.some(d => isSameDay(d, date)))) {
      result.push(format(weekStart, "yyyy'W'I"));
      dates = dates.filter(date => !weekDates.some(d => isSameDay(d, date)));
    }
  }

  // Remaining days
  dates.forEach(date => {
    result.push(format(date, 'yyyy-MM-dd'));
  });

  console.log(result);

  return result;
}
