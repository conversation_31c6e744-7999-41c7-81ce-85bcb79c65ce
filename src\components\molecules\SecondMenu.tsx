import { useState } from 'react';
import { List } from '@mui/material';

import DropdownMenuItem, { MenuItemI } from './DropdownMenuItem';

export const DROPDOWN_MENU_WIDTH = 260;

export interface SecondMenuProps {
  items: Array<MenuItemI>;
}

export default function SecondMenu({ items }: SecondMenuProps) {
  const [initialOpen, setInitalOpen] = useState<number | null>(null);

  return (
    <List
      sx={{
        ...{
          position: 'fixed',
          width: DROPDOWN_MENU_WIDTH - 20,
          height: '100%',
          bgcolor: 'background.default',
          py: 3,
          mr: 4,
          overflowY: 'auto',
          '@media print': {
            display: 'none',
          },
        },
      }}
      component="nav"
      aria-labelledby="nested-list-subheader"
    >
      {items.map((el: MenuItemI, index) => {
        return (
          <DropdownMenuItem
            key={el.label}
            menuItem={el}
            open={initialOpen === index}
            setOpen={() => setInitalOpen(index)}
          />
        );
      })}
    </List>
  );
}
