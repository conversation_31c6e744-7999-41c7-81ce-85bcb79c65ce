import { useCallback, useEffect, useState } from 'react';
import { UploadedFile, PrivateFileContext } from '~/types/fileUpload';
import { getFileSize, getBatchFileSizes, formatFileSize as formatFileSizeUtil, getVariantFileSize } from '~/utils/fileSizeUtils';
import { hasImageVariants } from '~/utils/imageVariants';

interface UseFileSizeOptions {
    /** Context for private files */
    context?: PrivateFileContext;
    /** Format the size for display */
    formatted?: boolean;
    /** Precision for formatted size (default: 2) */
    precision?: number;
    /** Whether to load size immediately (default: true) */
    autoLoad?: boolean;
    /** Specific variant to get size for (only for images with variants) */
    variant?: string;
}

interface UseFileSizeReturn {
    /** File size in bytes (null if not loaded or failed) */
    size: number | null;
    /** Formatted file size string (if formatted option is true) */
    formattedSize: string | null;
    /** Loading state */
    loading: boolean;
    /** Error state */
    error: Error | null;
    /** Manually trigger size loading */
    loadSize: () => Promise<void>;
    /** Reset state */
    reset: () => void;
}

/**
 * Hook for getting file size at runtime
 * Handles caching, loading states, and error handling
 */
export const useFileSize = (
    file: UploadedFile | null,
    options: UseFileSizeOptions = {}
): UseFileSizeReturn => {
    const {
        context,
        formatted = false,
        precision = 2,
        autoLoad = true,
        variant
    } = options;

    const [size, setSize] = useState<number | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);

    // Generate formatted size
    const formattedSize = size !== null && formatted
        ? formatFileSizeUtil(size, precision)
        : null;

    const loadSize = useCallback(async () => {
        if (!file) {
            setSize(null);
            setError(null);
            return;
        }

        setLoading(true);
        setError(null);

        try {
            let fileSize: number;

            // If variant is specified and file has image variants, get size from variant metadata
            if (variant && variant !== 'original' && hasImageVariants(file)) {
                try {
                    fileSize = await getVariantFileSize(file, variant, context);
                } catch (variantError) {
                    // Fallback to original file size
                    fileSize = await getFileSize(file, context);
                }
            } else {
                // Standard file size lookup (includes 'original' variant case)
                fileSize = await getFileSize(file, context);
            }

            setSize(fileSize);
        } catch (err) {
            setError(err as Error);
            setSize(null);
        } finally {
            setLoading(false);
        }
    }, [file, context, variant]);

    const reset = useCallback(() => {
        setSize(null);
        setError(null);
        setLoading(false);
    }, []);

    // Auto-load size when file, context, or variant changes
    useEffect(() => {
        if (autoLoad && file) {
            loadSize();
        } else if (!file) {
            reset();
        }
    }, [file, context, variant, autoLoad, loadSize, reset]);

    return {
        size,
        formattedSize,
        loading,
        error,
        loadSize,
        reset
    };
};

/**
 * Hook for getting multiple file sizes
 */
export const useFileSizes = (
    files: UploadedFile[],
    options: UseFileSizeOptions = {}
): {
    sizes: Map<string, number>;
    formattedSizes: Map<string, string>;
    loading: boolean;
    errors: Map<string, Error>;
    loadSizes: () => Promise<void>;
    reset: () => void;
} => {
    const [sizes, setSizes] = useState<Map<string, number>>(new Map());
    const [errors, setErrors] = useState<Map<string, Error>>(new Map());
    const [loading, setLoading] = useState<boolean>(false);

    const { context, formatted = false, precision = 2, autoLoad = true } = options;

    // Generate file keys
    const getFileKey = useCallback((file: UploadedFile) => {
        return `${file.fn}.${file.e}`;
    }, []);

    // Generate formatted sizes
    const formattedSizes = new Map<string, string>();
    if (formatted) {
        for (const [key, size] of sizes) {
            formattedSizes.set(key, formatFileSizeUtil(size, precision));
        }
    }

    const loadSizes = useCallback(async () => {
        if (files.length === 0) {
            setSizes(new Map());
            setErrors(new Map());
            return;
        }

        setLoading(true);

        try {
            // Use batch loading for better performance
            const batchResults = await getBatchFileSizes(files, context);

            // Separate successful loads from errors
            const newSizes = new Map<string, number>();
            const newErrors = new Map<string, Error>();

            files.forEach((file) => {
                const key = getFileKey(file);
                const size = batchResults.get(key);

                if (size !== undefined) {
                    newSizes.set(key, size);
                } else {
                    newErrors.set(key, new Error('Failed to load file size'));
                }
            });

            setSizes(newSizes);
            setErrors(newErrors);

        } catch (error) {
            const newErrors = new Map<string, Error>();
            files.forEach((file) => {
                const key = getFileKey(file);
                newErrors.set(key, error as Error);
            });
            setErrors(newErrors);
            setSizes(new Map());
        } finally {
            setLoading(false);
        }
    }, [files, context, getFileKey]);

    const reset = useCallback(() => {
        setSizes(new Map());
        setErrors(new Map());
        setLoading(false);
    }, []);

    // Auto-load sizes when files change
    useEffect(() => {
        if (autoLoad) {
            loadSizes();
        }
    }, [files, autoLoad, loadSizes]);

    return {
        sizes,
        formattedSizes,
        loading,
        errors,
        loadSizes,
        reset
    };
};
