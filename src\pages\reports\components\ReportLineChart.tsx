import { useMemo, useRef, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { TooltipItem } from 'chart.js';
import { Line } from 'react-chartjs-2';

import { useTheme } from '~/contexts';
import { verticalLinePlugin } from '../utils/verticalLinePlugin';

const arePositionsDifferent = (d1: TooltipData, d2: TooltipData) =>
  d1.left !== d2.left;

type TooltipData = {
  dataPoints: TooltipItem<'line'>[];
  left: number;
  formatData?: (el: string | number) => string | number;
};

export default function ReportLineChart({
  datasets,
  labels,
  fill = false,
  width = 'calc(100vw - 310px)',
  hideLegend = false,
  hidePoints = false,
  formatData = el => el,
}: {
  datasets: { label: string; data: any }[];
  labels: string[];
  fill?: boolean;
  width?: string;
  hideLegend?: boolean;
  hidePoints?: boolean;
  formatData?: (el: string | number) => string | number;
}) {
  const chartRef = useRef(null);
  const { getChartColours } = useTheme();
  const colors = getChartColours(datasets.length, 2);

  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  const [tooltipData, setTooltipData] = useState<TooltipData>({
    dataPoints: [],
    left: -1,
  });

  const resetData = () => {
    setTooltipData({ dataPoints: [], left: -1 });
  };
  const closeTooltip = () => {
    setIsTooltipOpen(false);
  };
  const openTooltip = () => {
    setIsTooltipOpen(true);
  };

  const data: any = useMemo(
    () => ({
      labels,
      datasets: datasets.map((dataset: any, index: number) => ({
        label: dataset.label,
        data: dataset.data,
        borderColor: colors[index],
        backgroundColor: colors[index] + '33',
        borderWidth: 2,
        fill,
      })),
    }),
    [datasets]
  );

  const options: any = {
    responsive: true,
    elements: {
      point: {
        radius: hidePoints ? 0 : 3,
      },
    },
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: !(hideLegend || datasets.find(el => !el.label)),
        align: 'end',
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 8,
          boxHeight: 8,
        },
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        enabled: false,
        external: ({ tooltip }: any) => {
          if (tooltip.opacity === 0 && isTooltipOpen) {
            resetData();
            closeTooltip();
            return;
          }

          const newData = {
            dataPoints: tooltip.dataPoints,
            left: tooltip.caretX,
          };

          if (arePositionsDifferent(tooltipData, newData)) {
            setTooltipData(newData);
            openTooltip();
          }
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#9CA3AF',
        },
      },
      y: {
        grid: {
          color: 'rgba(203, 213, 225, 0.3)',
        },
        ticks: {
          color: '#9CA3AF',
          beginAtZero: true,
        },
      },
    },
  };

  return (
    <Box
      sx={{
        width,
        margin: '0 auto',
        height: '200px',
        position: 'relative',
        '@media print': {
          display: 'block !important',
          margin: '0 !important',
        },
      }}
    >
      <Line
        ref={chartRef}
        data={data}
        options={options}
        plugins={[verticalLinePlugin]}
      />
      {isTooltipOpen && (
        <CustomTooltip {...tooltipData} formatData={formatData} />
      )}
    </Box>
  );
}

const CustomTooltip = (data: TooltipData) => (
  <Tooltip
    open={true}
    arrow
    title={
      <Box width="180px" textAlign={'center'}>
        <Typography variant="caption" color="custom.gray600">
          {data.dataPoints?.[0].label.toLocaleUpperCase()}
        </Typography>

        {data.dataPoints?.map(point => (
          <Box
            key={`${point.datasetIndex}-${point.dataIndex}`}
            width="100%"
            pb={1.5}
          >
            <Box
              sx={{
                height: '0.5px',
                width: '100%',
                bgcolor: 'custom.gray800',
                marginBottom: 1.5,
              }}
            />
            <Typography variant="h2" fontWeight="300">
              {data.formatData
                ? data.formatData(point.raw as number)
                : point.formattedValue}
            </Typography>
            <Typography variant="caption" color="custom.gray600">
              {point.dataset.label?.split('-')[0]}
            </Typography>
          </Box>
        ))}
      </Box>
    }
    componentsProps={{
      tooltip: {
        sx: {
          bgcolor: 'common.black',
          '& .MuiTooltip-arrow': {
            color: 'common.black',
          },
        },
      },
    }}
    placement="top"
  >
    <Box sx={{ top: '45px', left: data.left, position: 'absolute' }} />
  </Tooltip>
);
