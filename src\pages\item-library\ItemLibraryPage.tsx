import { useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import SlimBanner from '../../components/molecules/SlimBanner';
import { ItemLibraryEdit } from './ItemLibraryEdit';
import { ItemLibraryList } from './ItemLibraryList';
import { ItemLibraryShow } from './ItemLibraryShow';

export default function ItemLibraryPage() {
  const { t } = useTranslation('');
  const contentRef = useRef(null);
  return (
    <Box sx={{ p: 2 }} ref={contentRef}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('itemsLibrary.title')}
        description={
          <>
            {t('itemsLibrary.desc')}
            <a href="https://selio.io/support" target="_blank" rel="noreferrer">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <ItemLibraryList contentRef={contentRef} />
      <ItemLibraryShow />
      <ItemLibraryEdit />
    </Box>
  );
}
