import { Typography } from '@mui/material';
import {
  DeleteWithConfirmButton,
  DeleteWithConfirmButtonProps,
  useRecordContext,
} from 'react-admin';

interface CustomDeleteWithConfirmButtonProps
  extends DeleteWithConfirmButtonProps {
  field?: string;
}

// Custom title component
const DeleteTitle = ({
  fieldName,
  recordName,
}: {
  fieldName?: string;
  recordName?: string;
}) => {
  if (fieldName && recordName) {
    return <>Delete {recordName}?</>;
  }
  return <>Delete this item?</>;
};

export default function CustomDeleteWithConfirmButton({
  field,
  confirmTitle,
  ...rest
}: CustomDeleteWithConfirmButtonProps) {
  const record = useRecordContext();

  // If a custom confirmTitle is provided, use it directly
  // Otherwise, use our custom component with the appropriate props
  const finalConfirmTitle = confirmTitle || (
    <DeleteTitle
      fieldName={field}
      recordName={record && field ? record[field] : undefined}
    />
  );

  return (
    <DeleteWithConfirmButton
      confirmTitle={finalConfirmTitle}
      confirmContent={<>Are you sure you want to delete this item?</>}
      {...rest}
    />
  );
}
