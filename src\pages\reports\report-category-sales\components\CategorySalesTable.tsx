import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { groupReport } from '~/fake-provider/reports/groupReport';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';

type TableRow = {
  vat: string;
  promotionsValue: number;
  billPercentage: number;
  couponsValue: number;
  discountsValue: number;
  id: string;
  netValue: number;
  itemsQty: number;
  name: string;
  itemsValue: number;
  modifiersQty: number;
  modifiersValue: number;
  value: number;
  subItems: TableRow[] | undefined;
};

export default function CategorySalesTable({
  tableData,
  onChangeGrouping,
  groupingItems,
  fields,
  setFields,
}: {
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
  groupingItems: string[];
  fields: FieldOption[];
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  onChangeGrouping?: (items: ('vat' | 'id')[]) => void;
}) {
  const { t } = useTranslation();

  const categoryTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      
      // Sort the data by itemsValue
      mappedTableData = [...mappedTableData].sort((a, b) => {
        return (a.itemsValue - b.itemsValue) * -1;
      });

      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.id = 'Total';
      totalItemsData.name = 'Total';
      totalItemsData.vat = '';
      totalItemsData.subItems = [];

      mappedTableData = [...mappedTableData, totalItemsData];

      return mappedTableData;
    }

    return [];
  }, [tableData]);

  const categorySalesConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'id',
      label: t('shared.category'),
      textAlign: 'start',
      render: (row: TableRow) => {
        if (row.subItems && row.subItems.length > 0) {
          return (
            <span
              style={{
                fontSize: '14px',
                whiteSpace: 'nowrap',
              }}
            >
              {row.id.includes('@none')
                ? 'Regular'
                : row?.id.includes('Vat')
                  ? capitalize(row?.id?.toLowerCase())
                  : row.id}
            </span>
          );
        } else {
          return (
            <div
              style={{
                fontSize: '14px',
                minWidth: '130px',
                whiteSpace: 'normal',
              }}
            >
              {row.name === '@none'
                ? 'Regular'
                : capitalize(row?.name?.toLowerCase()) || row.id}{' '}
            </div>
          );
        }
      },
    },
    {
      id: 'vat',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'end',
    },
    {
      id: 'itemsQty',
      label: t('categorySales.itemsSold'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row?.itemsQty, true)}</>;
      },
    },
    {
      id: 'itemsValue',
      label: t('categorySales.itemsSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.itemsValue)}</>;
      },
    },
    {
      id: 'modifiersQty',
      label: t('categorySales.modifiersSold'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row?.modifiersQty, true)}</>;
      },
    },
    {
      id: 'modifiersValue',
      label: t('categorySales.modifiersSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.modifiersValue)}</>;
      },
    },
    {
      id: 'value',
      label: t('categorySales.grossSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },
    {
      id: 'discountsValue',
      label: t('categorySales.discounts'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.discountsValue
              ? '- ' + formatAndDivideNumber(row?.discountsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'couponsValue',
      label: t('categorySales.coupons'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.couponsValue
              ? '- ' + formatAndDivideNumber(row?.couponsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'promotionsValue',
      label: t('categorySales.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'netValue',
      label: t('shared.netSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.netValue)}</>;
      },
    },
  ];

  const columnsToFilter = useMemo(() => {
    const columns = [
      'vat',
      'promotionsValue',
      'couponsValue',
      'discountsValue',
      'itemsQty',
      'itemsValue',
      'modifiersQty',
      'value',
      'modifiersValue',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [{ value: 'vat', label: 'VAT' }];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          separateFirstColumn={true}
          config={categorySalesConfig}
          data={categoryTableData}
          fields={fields}
          fixedFirstColumn={true}
          groupingItems={groupingItems}
          groupingOptions={groupingOptions}
          setFields={setFields}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
        />
      </Box>
    </>
  );
}
