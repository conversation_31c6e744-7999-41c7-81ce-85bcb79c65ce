import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { Box, Typography } from '@mui/material';
import { Button } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';

export default function AccountItem({ item, type }: any) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  return (
    <Box sx={{ width: '100%' }}>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          mb: 1,
        }}
      >
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ position: 'relative', width: 'fit-content' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', border: `${type === 'code' ? `1px solid ${theme.palette.custom.gray400}` : 'none'}` }}>
              {type === 'code' ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography sx={{ fontSize: 16, borderRight: `1px solid ${theme.palette.custom.gray400}`, padding: '6px' }}>
                    Personal POS Passcode
                  </Typography>
                  {item.title
                    ?.toString()
                    .split('')
                    .map((letter: string, idx: number) => (
                    <Box
                      key={idx}
                      sx={{
                        height: '35px',
                        width: '45px',
                        borderRight: `1px solid ${theme.palette.custom.gray400}`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography>{letter}</Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography sx={{ fontSize: 16, fontWeight: 500, mb: 0.5 }}>
                  {item.title}
                </Typography>
              )}
            </Box>
            {item.title === 'Email' && item.value !== '-' && (
              <Box
                sx={{
                  px: '8px',
                  py: '3px',
                  width: 'fit-content',
                  display: 'flex',
                  backgroundColor: '#E5FFEE',
                  borderRadius: 30,
                  color: '#027D2A !important',
                  position: 'absolute',
                  top: -2,
                  right: -90,
                  gap: 0.5,
                  alignItems: 'center',
                }}
              >
                <CheckCircleOutlineIcon sx={{ fontSize: 16 }} />
                <Typography sx={{ fontSize: 13, color: '#027D2A !important' }}>
                  {t('signInSecurity.verified')}
                </Typography>
              </Box>
            )}
          </Box>

          <Typography sx={{ fontSize: 14, fontWeight: 300, color: 'gray' }}>
            {item.value}
          </Typography>
        </Box>
        {item.buttonText && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Button
              onClick={() => {
                item.buttonAction();
              }}
              sx={{ fontSize: 14, fontWeight: 600 }}
            >
              {item.buttonText}
            </Button>
          </Box>
        )}
      </Box>
      <Box
        sx={{
          width: '100%',
          height: '1px',
          backgroundColor: 'gray',
          opacity: 0.3,
        }}
      ></Box>
    </Box>
  );
}
