import { ROWS_NO } from '~/components/organisms/menu-catalog-dnd/CatalogContainer';
import {
  Coordinates,
  MenuItem,
} from '~/components/organisms/menu-catalog-dnd/types';

export const checkAvailablePosition = (
  position: Coordinates,
  items: MenuItem[]
): boolean => {
  for (const item of items) {
    if (
      (+item.position.startX === position.startX &&
        +item.position.startY === position.startY) ||
      (+item.position.endX === position.endX &&
        +item.position.endY === position.endY)
    ) {
      return false;
    }
  }
  return true;
};

export const findNextAvailablePosition = (items: MenuItem[]) => {
  if (items.find(item => !item.position)) {
    return { startX: 0, startY: 0, endX: 1, endY: 1 };
  }

  const startIndex = 0;
  for (let i = startIndex; i < ROWS_NO * 4; i++) {
    const x = i % 4;
    const y = Math.floor(i / 4);
    const position = { startX: x, startY: y, endX: x + 1, endY: y + 1 };
    if (checkAvailablePosition(position, items)) {
      return position;
    }
  }
};

export function getValueByPath(obj: any, path: string) {
  const parts = path.replace(/\[(\d+)\]/g, '.$1').split('.');
  return parts.reduce((acc, key) => acc?.[key], obj);
}
