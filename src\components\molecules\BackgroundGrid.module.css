.grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: repeating-linear-gradient(
      0deg,
      transparent,
      transparent calc(var(--grid-size-height) - 1px),
      var(--grid-color) calc(var(--grid-size-height) - 1px),
      var(--grid-color) var(--grid-size-height)
    ),
    repeating-linear-gradient(
      -90deg,
      transparent,
      transparent calc(var(--grid-size-width) - 1px),
      var(--grid-color) calc(var(--grid-size-width) - 1px),
      var(--grid-color) var(--grid-size-width)
    );
  background-size: var(--grid-size-width) var(--grid-size-height);
  z-index: 0;
  display: grid;
  border-radius: 6px;
}
