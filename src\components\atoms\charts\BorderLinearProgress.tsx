import styled from '@emotion/styled';
import { LinearProgress, linearProgressClasses } from '@mui/material';

export const BorderLinearProgress = styled(LinearProgress)(
  ({ theme }: any) => ({
    height: '25px',
    flex: 1,
    borderRadius: 3,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor:
        theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 3,
      bgcolor: 'primary.main',
    },
  })
);

export const SmallBorderLinearProgress = styled(LinearProgress)(
  ({ theme }: any) => ({
    height: '12px',
    flex: 1,
    borderRadius: 3,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: 'primary.light',
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 3,
      bgcolor: 'primary.main',
    },
  })
);
