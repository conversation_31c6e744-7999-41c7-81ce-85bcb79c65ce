import { useMemo } from 'react';
import {
  Box,
  Chip,
  Divider,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';

interface AreaItemsChipsProps {
  areaItems: any[];
  onDelete?: (itemId: string) => void;
  readOnly?: boolean;
}

export default function AreaItemsChips({
  areaItems = [],
  onDelete,
  readOnly = false,
}: AreaItemsChipsProps) {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));

  const getChipLabel = (item: any) => {
    // If table has a tag, show the tag
    if (item.itemTag) {
      return item.itemTag;
    }
    // Otherwise show itemNumber only (since we'll group by floorplan)
    return item.floorPlanLabel + item.itemNumber;
  };

  const handleDelete = (itemId: string) => {
    if (onDelete && !readOnly) {
      onDelete(itemId);
    }
  };

  // Group items by floor plan name (not label)
  const groupedItems = areaItems.reduce(
    (acc, item) => {
      const floorPlanKey =
        item.floorPlanName || item.floorPlanLabel || 'Unknown';
      if (!acc[floorPlanKey]) {
        acc[floorPlanKey] = [];
      }
      acc[floorPlanKey].push(item);
      return acc;
    },
    {} as Record<string, any[]>
  );

  // Calculate the width needed for the longest floor plan name + count
  const maxLabelWidth = useMemo(() => {
    const labels = Object.entries(groupedItems).map(
      ([name, items]) => `${name} (${(items as any[]).length})`
    );
    const longestLabel = labels.reduce(
      (a, b) => (a.length > b.length ? a : b),
      ''
    );
    // Approximate width: 8px per character + padding
    return Math.max(80, longestLabel.length * 6 + 16);
  }, [groupedItems]);

  if (areaItems.length === 0) {
    return null;
  }

  // On xs screens, show count chip
  if (isXs) {
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        <Chip
          label={`${areaItems.length} tables assigned`}
          variant="outlined"
          size="small"
          sx={{
            backgroundColor: 'rgba(25, 118, 210, 0.08)',
            borderColor: 'primary.main',
            '& .MuiChip-label': {
              color: 'primary.main',
              fontWeight: 500,
            },
          }}
        />
      </Box>
    );
  }

  // On larger screens, show two-column layout with divider
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
      {Object.entries(groupedItems).map(([floorPlanName, items]) => (
        <Box
          key={floorPlanName}
          sx={{
            display: 'grid',
            gridTemplateColumns: `${maxLabelWidth}px auto 1fr`,
            gap: 2,
            alignItems: 'center',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              color: 'text.primary',
              fontWeight: 600,
              whiteSpace: 'nowrap',
            }}
          >
            {floorPlanName} ({(items as any[]).length})
          </Typography>

          <Divider
            orientation="vertical"
            sx={{
              borderColor: 'divider',
            }}
          />

          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {(items as any[]).map(item => (
              <Chip
                key={item.id}
                label={getChipLabel(item)}
                variant="outlined"
                size="small"
                onDelete={readOnly ? undefined : () => handleDelete(item.id)}
                sx={{
                  backgroundColor: 'rgba(25, 118, 210, 0.08)',
                  borderColor: 'primary.main',
                  '& .MuiChip-label': {
                    color: 'primary.main',
                    fontWeight: 500,
                  },
                }}
              />
            ))}
          </Box>
        </Box>
      ))}
    </Box>
  );
}
