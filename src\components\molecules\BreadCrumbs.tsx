import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Box, Typography } from '@mui/material';

import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';
import capitalize from '~/utils/capitalize';

type BreadcrumbsProps = {
  parentFields?: string[];
};

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ parentFields }) => {
  if (!parentFields || parentFields.length === 0) return null;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      {parentFields.map((field, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            alignItems: 'center',
            color: 'gray',
            mt: 1,
          }}
        >
          <div style={{ fontSize: 12, fontWeight: 500 }}>
            {camelCaseToNormalWords(field)}
          </div>
          {index < parentFields.length - 1 && (
            <KeyboardArrowRightIcon fontSize="small" sx={{ p: 0 }} />
          )}
        </Box>
      ))}
    </Box>
  );
};

export default Breadcrumbs;
