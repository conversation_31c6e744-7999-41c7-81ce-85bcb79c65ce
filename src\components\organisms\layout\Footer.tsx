import { Box, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

export default function Footer() {
  const { theme } = useTheme();
  const currentYear = new Date().getFullYear();

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        py: 2,
        mt: 'auto',
      }}
    >
      {theme.palette.mode !== 'light' ? (
        <img
          src="/assets/logo/SELIO_LOGO_WHITE.svg"
          width="80px"
          style={{
            marginTop: '4px',
          }}
        />
      ) : (
        <img
          src="/assets/logo/SELIO_LOGO_BLACK.svg"
          width="80px"
          style={{
            marginTop: '4px',
          }}
        />
      )}
      <Typography fontSize={12} fontWeight={100} mt={0.5}>
        v. {__APP_VERSION__.split('+')[0]} @ {currentYear} SELIO SOFTWARE
      </Typography>
    </Box>
  );
}
