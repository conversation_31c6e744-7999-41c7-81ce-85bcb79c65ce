import SearchIcon from '@mui/icons-material/Search';
import { InputAdornment, Theme, useMediaQuery } from '@mui/material';
import { TextInput, TextInputProps } from 'react-admin';
import { useTranslation } from 'react-i18next';
export default function CustomSearchInput(props: TextInputProps) {
  const { t } = useTranslation('');
  const { placeholder = t('dashboard.headerSearchPlaceholder'), size = 'small', label = '' } = props;
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <TextInput
      sx={{ width: isXSmall ? '100%' : '250px' }}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
      }}
      size={size}
      label={label}
      placeholder={placeholder}
      alwaysOn
      {...props}
    />
  );
}
