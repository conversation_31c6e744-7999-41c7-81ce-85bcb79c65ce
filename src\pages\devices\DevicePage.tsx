import { useState } from 'react';
import InfoOutlineIcon from '@mui/icons-material/InfoOutlined';
import {
  Box,
  Button,
  Dialog,
  Divider,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';
import { useGetList, useNotify } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useGetListDevicesLive } from '~/providers/resources';
import PageTitle from '../../components/molecules/PageTitle';
import { DeviceCreate } from './DeviceCreate';
import DeviceEdit from './DeviceEdit';
import DeviceList from './DeviceList';

export const DevicePage = () => {
  const { t } = useTranslation('');
  const { data } = useGetListDevicesLive();

  return (
    <Box p={2}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
        }}
      >
        <Box sx={{ maxWidth: { xs: '100%', md: '60%' } }}>
          {' '}
          <PageTitle
            sx={{
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
            title={t('devicesPage.title')}
            description={
              <>
                {t('devicesPage.description')}
                <a
                  href="https://selio.io/support-center"
                  target="_blank"
                  rel="noreferrer"
                >
                  {t('support.support-link')}
                </a>
              </>
            }
            hideBorder
            doNotPrint
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
            p: 1,
            // paddingX: 2,
            borderRadius: '6px',
            marginRight: { xs: 0, md: '4px' },
            marginLeft: 'auto',
            width: { xs: '100%', md: 'auto' },
          }}
        >
          <Typography
            sx={{ textShadow: '1px 1px 5px rgba(7, 7, 7, .6)' }}
            variant="h1"
            color="primary"
            fontSize={{ xs: 48, md: 52 }}
          >
            {100 - (data?.length || 0)}
          </Typography>
          <Box>
            <Typography
              sx={{ textShadow: '1px 1px 4px rgba(7, 7, 7, .4)' }}
              variant="h5"
              fontWeight={'300'}
              fontSize={{ xs: 20, md: 24 }}
            >
              {t('devicesPage.devicesAvailable')}
            </Typography>
            <Typography
              sx={{ textShadow: '1px 1px 4px rgba(7, 7, 7, .4)' }}
              variant="h5"
            >
              {' '}
              {t('devicesPage.of')} 100
            </Typography>
          </Box>
          <Tooltip title={t('devicesPage.devicesAvailableDescription')}>
            <IconButton color="info" sx={{ padding: '0', paddingLeft: '5px' }}>
              <InfoOutlineIcon sx={{ fontSize: 20 }} />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <DeviceList />
      <DeviceCreate />
      <DeviceEdit />
    </Box>
  );
};
