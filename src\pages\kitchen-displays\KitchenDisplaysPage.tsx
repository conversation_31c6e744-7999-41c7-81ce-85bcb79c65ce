import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Subsection from '../../components/molecules/Subsection';

export default function KitchenDisplaysPage() {
  const { t } = useTranslation();
  return (
    <>
      <Box sx={{ display: { xs: 'none', lg: 'block' } }}>
        <img
          src="/assets/kitchen-displays/cover.png"
          style={{
            width: '100%',
            height: '500px',
            objectFit: 'cover',
            objectPosition: 'top',
          }}
        />
      </Box>
      <Box sx={{ display: { xs: 'block', lg: 'none' } }}>
        <img
          src="/assets/kitchen-displays/cover_phone.png"
          style={{
            width: '100%',
            objectFit: 'cover',
            objectPosition: 'center',
          }}
        />
      </Box>
      <Box
        my={{ xs: 5, lg: 10 }}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: { xs: 5, lg: 10 },
        }}
      >
        <Subsection
          title={t('kitchenDisplaysPage.title')}
          subtitle={t('kitchenDisplaysPage.description')}
          containerSx={{ maxWidth: '700px', width: '100%' }}
        >
          <Box
            sx={{
              maxWidth: '630px',
              margin: 'auto',
            }}
          >
            <img
              src="/assets/kitchen-displays/details.png"
              style={{ width: '100%' }}
            />
          </Box>
        </Subsection>

        <Subsection
          title={t('kitchenDisplaysPage.title2')}
          subtitle={t('kitchenDisplaysPage.description2')}
          containerSx={{ maxWidth: '700px', width: '100%' }}
        >
          <Box
            pt={3}
            sx={{
              maxWidth: '550px',
              margin: 'auto',
            }}
          >
            <img
              src="/assets/kitchen-displays/ticket.png"
              style={{ width: '100%' }}
            />
          </Box>
        </Subsection>
      </Box>
    </>
  );
}
