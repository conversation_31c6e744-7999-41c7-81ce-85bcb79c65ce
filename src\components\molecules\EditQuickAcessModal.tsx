import CloseIcon from '@mui/icons-material/Close';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import { useTheme } from '~/contexts';
import DraggableList from './DraggableList';
import MenuItems from './MenuItems';

const style = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '95%',
  maxWidth: 600,
  height: '70%',
  minHeight: 250,
  borderRadius: 3,
  bgcolor: 'background.paper',
  boxShadow: 24,
};

export default function EditQuickAccessModal({
  selectedItems,
  setSelectedItems,
  handleSave,
  handleOpen,
  handleClose,
  open,
}: {
  selectedItems: any;
  setSelectedItems: any;
  handleSave: any;
  handleOpen: any;
  handleClose: any;
  open: boolean;
}) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const handleDeleteItem = (itemLabel: any) => {
    setSelectedItems((prevItems: any) =>
      prevItems.filter((item: any) => item.label !== itemLabel)
    );
  };

  return (
    <div>
      <Button sx={{ fontSize: 13, py: 1, px: 2, color: '#3B8AF9'}} onClick={handleOpen}>
        {t('shared.edit')}
      </Button>
      <Modal open={open} onClose={handleClose}>
        <Box sx={{
          ...style,
          bgcolor: theme.palette.mode === 'dark' ? '#26262B' : 'white',
        }}>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 4,
            }}
          >
            <Button
              // @ts-ignore
              variant="close-btn"
              onClick={handleClose}
              sx={{ float: 'right' }}
            >
              <CloseIcon fontSize="small" />
            </Button>
            <Typography>{t('menu.editQuickAccess')}</Typography>
            <Button
              sx={{ fontSize: 14 }}
              variant="contained"
              onClick={handleSave}
            >
              {t('shared.save')}
            </Button>
          </Box>
          <Box
            sx={{
              overflowY: 'auto',
              width: '100%',
              height: 'calc(100% - 150px)',
              px: 4,
              pb: 4,
            }}
          >
            <DraggableList
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
              handleDeleteItem={handleDeleteItem}
            />
            <MenuItems
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
          </Box>
        </Box>
      </Modal>
    </div>
  );
}
