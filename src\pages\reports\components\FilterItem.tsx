import { useState } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Popover,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';

import type { MouseEvent } from 'react';

export type OptionType = { label: string; value: string };

interface FilterItemProps<T> {
  defaultValue: string;
  options: OptionType[];
  setSelectedOption: (value: T) => void;
}

export default function FilterItem<T>({
  defaultValue = '',
  options,
  setSelectedOption,
}: FilterItemProps<T>) {
  const [radioGroupValue, setRadioGroupValue] = useState(
    options.find(el => el.value === defaultValue)
  );
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const open = Boolean(anchorEl);
  const id = open ? 'time-range-popover' : undefined;

  const handleChange = (e: any) => {
    const el = options.find(el => el.value === e.target.value);
    setRadioGroupValue(el);
    setSelectedOption(e.target.value);
  };

  const openDropdown = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const closeDropdown = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Button
        //@ts-ignore
        variant="contained-light"
        aria-describedby={id}
        onClick={openDropdown}
      >
        {/* @ts-ignore */}
        <Typography variant="label" fontWeight={500} color="custom.gray800">
          {radioGroupValue?.label}
        </Typography>

        <KeyboardArrowDownIcon color="disabled" />
      </Button>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeDropdown}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box p={2}>
          <FormControl>
            <RadioGroup
              name="radio-btns-time-range"
              value={radioGroupValue?.value}
              onChange={handleChange}
            >
              {options.map(option => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  label={
                    // @ts-ignore
                    <Typography variant="label" fontWeight={300}>
                      {option.label}
                    </Typography>
                  }
                  control={<Radio />}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>
      </Popover>
    </>
  );
}
