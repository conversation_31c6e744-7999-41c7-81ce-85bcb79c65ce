import EventNoteIcon from '@mui/icons-material/EventNote';
import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export default function ComingSoon() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <Box
      sx={{
        height: 'calc(100vh - 160px)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <EventNoteIcon sx={{ fontSize: '160px' }} />
      <Typography
        fontSize="20px"
        fontWeight={500}
        sx={{ textTransform: 'uppercase' }}
      >
        {t('comingSoon.title')}
      </Typography>
      <Box sx={{ display: 'flex', gap: 4, marginTop: 4 }}>
        <Button
          onClick={handleGoBack}
          sx={{
            minWidth: 100,
            backgroundColor: '#FAFBFC',
            border: '1px solid rgba(27, 31, 35, 0.15)',
            borderRadius: '6px',
            boxShadow:
              'rgba(27, 31, 35, 0.04) 0 1px 0, rgba(255, 255, 255, 0.25) 0 1px 0 inset',
            color: '#24292E',
            cursor: 'pointer',
            padding: '6px 16px',
            transition: 'background-color 0.2s cubic-bezier(0.3, 0, 0.5, 1)',
            '&:hover': {
              backgroundColor: '#F3F4F6',
              textDecoration: 'none',
              transitionDuration: '0.1s',
            },
          }}
        >
          {t('comingSoon.goBack')}
        </Button>
        <Button
          onClick={handleGoHome}
          sx={{
            minWidth: 100,
            backgroundColor: '#FAFBFC',
            border: '1px solid rgba(27, 31, 35, 0.15)',
            borderRadius: '6px',
            boxShadow:
              'rgba(27, 31, 35, 0.04) 0 1px 0, rgba(255, 255, 255, 0.25) 0 1px 0 inset',
            color: '#24292E',
            cursor: 'pointer',
            padding: '6px 16px',
            transition: 'background-color 0.2s cubic-bezier(0.3, 0, 0.5, 1)',
            '&:hover': {
              backgroundColor: '#F3F4F6',
              textDecoration: 'none',
              transitionDuration: '0.1s',
            },
          }}
        >
          {t('comingSoon.home')}
        </Button>
      </Box>
    </Box>
  );
}
