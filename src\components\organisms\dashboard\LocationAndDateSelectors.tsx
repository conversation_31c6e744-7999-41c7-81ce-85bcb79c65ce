import { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Popover,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import DateRangePickerCustom from '~/components/molecules/DateRangePickerCustom';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';
import type { MouseEvent } from 'react';

interface LocationAndDateSelectorsProps {
  isDate?: boolean;
  hideShadow?: boolean;
  // External control props (when provided, component won't persist to global state)
  externalSellPoints?: Array<{ id: string; name?: string; [key: string]: any }>;
  externalSellPointId?: string;
  onSellPointChange?: (sellPointId: string) => void;
  externalDateRange?: DateRange<Dayjs>;
  onDateRangeChange?: (dateRange: DateRange<Dayjs>) => void;
  externalIsLoading?: boolean;
  fullWidthButtons?: boolean;
}

export default function LocationAndDateSelectors({
  isDate = true,
  hideShadow = false,
  externalSellPoints,
  externalSellPointId,
  onSellPointChange,
  externalDateRange,
  onDateRangeChange,
  externalIsLoading,
  fullWidthButtons = false,
}: LocationAndDateSelectorsProps) {
  const { t } = useTranslation('');

  // Use global hook when external props are not provided
  const globalFilters = useGlobalResourceFilters();

  // Determine which values to use based on whether external props are provided
  const isExternalMode = Boolean(
    externalSellPoints ||
      externalSellPointId !== undefined ||
      onSellPointChange ||
      externalDateRange ||
      onDateRangeChange ||
      externalIsLoading !== undefined
  );

  const sellPoints = externalSellPoints || globalFilters.sellPoints;
  const sellPointId = externalSellPointId ?? globalFilters.sellPointId;
  const setSellPointId = onSellPointChange || globalFilters.setSellPointId;
  const dateRange = externalDateRange || globalFilters.dateRange;
  const setDateRange = onDateRangeChange || globalFilters.setDateRange;
  const isLoadingGlobalResourceFilters =
    externalIsLoading ?? globalFilters.isLoading;

  const [componentSellPoint, setComponentSellPoint] = useState<any>();
  const [isScrolled, setIsScrolled] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (isLoadingGlobalResourceFilters || !sellPoints || !sellPointId) {
      setComponentSellPoint(undefined);
      return;
    }

    setComponentSellPoint(sellPoints?.find(({ id }) => id === sellPointId));
  }, [sellPointId, sellPoints, isLoadingGlobalResourceFilters]);

  const handleScroll = () => {
    const position = window.scrollY;
    setIsScrolled(!!position);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const openLocationDropdown = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const closeLocationDropdown = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        gap: 1,
        flexDirection: isXSmall ? 'column' : 'row',
        py: 1,
        bgcolor: 'background.default',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: isXSmall ? '-25px' : 0,
          width: isXSmall ? '100vw' : '100%',
          borderBottom: isScrolled && !hideShadow ? '1px solid' : 'none',
          borderColor: 'custom.gray200',
          boxShadow:
            isScrolled && !hideShadow
              ? '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)'
              : 'none',
        }}
      />
      {/* location selector */}
      <Button
        fullWidth={fullWidthButtons}
        //@ts-ignore
        variant="transparent"
        aria-describedby={id}
        onClick={openLocationDropdown}
      >
        <Typography variant="body2" color="custom.gray600">
          {t('shared.location')}
        </Typography>
        <Typography variant="body2" ml={1}>
          {componentSellPoint?.name}
        </Typography>
      </Button>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={closeLocationDropdown}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box p={2} width={isXSmall ? 'calc(100vw - 48px)' : 'auto'}>
          {sellPoints?.map((sellPoint, idx) => (
            <Box
              key={idx}
              onClick={() => {
                setSellPointId(sellPoint.id);
                closeLocationDropdown();
              }}
              py={1.5}
              px={1}
              sx={{
                cursor: 'pointer',
                bgcolor: 'transparent',
                transition: 'all 0.1s ease',
                borderBottom:
                  idx < sellPoints.length - 1 ? 'solid 1px' : 'none',
                borderColor: 'custom.gray200',
                '&:hover': {
                  bgcolor: 'primary.veryLight',
                  borderRadius: '6px',
                },
              }}
            >
              <Typography>{sellPoint?.name}</Typography>
            </Box>
          ))}
        </Box>
      </Popover>

      {/* date selector */}
      {isDate && (
        <DateRangePickerCustom
          dateRange={dateRange}
          setDateRange={setDateRange}
          sx={{ width: { xs: '100%', sm: 'fit-content' } }}
          fullWidthButtons={fullWidthButtons}
        />
      )}
    </Box>
  );
}
