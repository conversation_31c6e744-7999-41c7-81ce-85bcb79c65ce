import { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LicenseInfo } from '@mui/x-license-pro';
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Filler,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Tooltip,
} from 'chart.js';
import {
  Admin,
  Authenticated,
  CustomRoutes,
  DataProvider,
  Loading,
  localStorageStore,
  Resource,
} from 'react-admin';
import {
  BrowserRouter,
  Route,
  useLocation,
  useNavigate,
} from 'react-router-dom';

import { ConnectionMonitor, TabMonitor } from './components/organisms';
import CustomLayout from './components/organisms/layout/CustomLayout';
import { ThemeProvider, useTheme } from './contexts';
import { CookiesConsentProvider } from './contexts/CookiesConsentContext';
import { FirebaseProvider, useFirebase } from './contexts/FirebaseContext';
import Dashboard from './Dashboard';
import aboutMyBusiness from './pages/about-my-business';
import bankAccounts from './pages/bank-accounts';
import categories from './pages/categories';
import compAndVoid from './pages/comp-and-void';
import courses from './pages/courses';
import devices from './pages/devices';
import discounts from './pages/discounts';
import emailNotifications from './pages/email-notifications';
import floorPlans from './pages/floor-plans';
import giftCards from './pages/gift-cards/gift-cards';
import giftCardsSettings from './pages/gift-cards/gift-cards-settings';
import invoices from './pages/invoices';
import invoicesOverview from './pages/invoices-overview';
import invoicesSettings from './pages/invoices-settings';
import itemLibrary from './pages/item-library';
import kitchenDisplays from './pages/kitchen-displays';
import { LoginPage } from './pages/login/LoginPage';
import loyalty from './pages/loyalty';
import measureUnits from './pages/measure-units';
import members from './pages/members';
import menuBehaviour from './pages/menu-behaviour';
import menus from './pages/menus';
import modifiers from './pages/modifiers';
import myIntegrations from './pages/my-integrations';
import outageNotifications from './pages/outage-notifications';
import partnerIntegrations from './pages/partner-integrations';
import permissions from './pages/permissions';
import preferences from './pages/preferences';
import prepStations from './pages/prepStations';
import pricingSubscriptions from './pages/pricing-subscriptions';
import receipts from './pages/receipts';
import categorySales from './pages/reports/report-category-sales';
import comps from './pages/reports/report-comps';
import coupons from './pages/reports/report-coupons';
import reportDiscounts from './pages/reports/report-discounts';
import extraCharges from './pages/reports/report-extra-charges';
import giftCardsReports from './pages/reports/report-gift-cards';
import itemSales from './pages/reports/report-item-sales';
import kitchenPerformance from './pages/reports/report-kitchen-performance';
import modifierSales from './pages/reports/report-modifier-sales';
import paymentMethods from './pages/reports/report-payment-methods';
import promotions from './pages/reports/report-promotions';
import salesRevenue from './pages/reports/report-sales-revenue';
import reportTeamRevenue from './pages/reports/report-team-revenue';
import reportTips from './pages/reports/report-tips';
import reportTransactions from './pages/reports/report-transactions';
import taxes from './pages/reports/report-vat';
import voids from './pages/reports/report-voids';
import salesTaxes from './pages/sales-taxes';
import { AccountSelectionPage } from './pages/select-account/AccountSelectionPage';
import selioAI from './pages/selio-ai';
import sellpoints from './pages/sellpoints';
import signInSecurity from './pages/sign-in-security';
import teamMembersWorkingArea from './pages/team-members-working-area';
import tips from './pages/tips';
import transfers from './pages/transfers';
import { authProvider } from './providers/authProvider';
import {
  dummyDataProvider,
  getMainDataProvider,
} from './providers/mainDataProvider';
import { RESOURCES } from './providers/resources';
import { lightTheme } from './styles/lightTheme';

LicenseInfo.setLicenseKey(
  '7c19472732ab254da8e6ce860ab374c0Tz0xMDAwNjAsRT0xNzYwNjI1OTkzMDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1RMy0yMDI0LEtWPTI='
);

ChartJS.register(
  LineElement,
  BarElement,
  CategoryScale,
  LinearScale,
  PointElement,
  Tooltip,
  Legend,
  Filler
);

const AppInner = () => {
  const { theme } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const {
    loading: fbLoading,
    details: fbDetails,
    setSelectedAccount,
  } = useFirebase();
  const [dataProvider, setDataProvider] =
    useState<DataProvider>(dummyDataProvider);
  const [dataProviderIsInitializing, setDataProviderIsInitializing] =
    useState(false);
  const currentAccountRef = useRef<string | null>(null);

  useEffect(() => {
    if (fbLoading) {
      return;
    }
    // console.log('fbDetails', fbDetails, location.pathname);
    if (fbDetails.user) {
      if (
        !fbDetails.selectedAccount &&
        location.pathname !== '/select-account'
      ) {
        if (fbDetails.availableAccounts.length === 1) {
          setSelectedAccount(fbDetails.availableAccounts[0]);
        } else {
          console.log('no selected account, redirecting to select account');
          navigate('/select-account');
        }
      } else if (
        fbDetails.selectedAccount &&
        currentAccountRef.current !== fbDetails.selectedAccount
      ) {
        // Only update data provider if the selected account has changed
        setDataProviderIsInitializing(true);
        currentAccountRef.current = fbDetails.selectedAccount;
        console.log(
          'getting data provider for',
          fbDetails.selectedAccount,
          'on path',
          location.pathname
        );
        getMainDataProvider(fbDetails.selectedAccount, fbDetails.user).then(
          provider => {
            setDataProvider(() => provider);
            setDataProviderIsInitializing(false);
            console.log('got data provider for', fbDetails.selectedAccount);
          }
        );
      }
    } else if (location.pathname !== '/login') {
      console.log('not logged in, redirecting to login');
      navigate('/login');
    }
  }, [fbDetails, location, fbLoading]);

  if (fbLoading || dataProviderIsInitializing) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Loading
          loadingPrimary="Initializing..."
          loadingSecondary="Please wait..."
        />
      </Box>
    );
  }

  return (
    <Admin
      authProvider={authProvider}
      dataProvider={dataProvider}
      store={
        fbDetails.user?.uid && fbDetails.selectedAccount
          ? localStorageStore(
              __APP_VERSION__,
              `-${fbDetails.user.uid}-${fbDetails.selectedAccount}`
            )
          : undefined
      }
      layout={CustomLayout}
      dashboard={Dashboard}
      theme={theme}
      darkTheme={theme}
      loginPage={LoginPage}
      requireAuth
    >
      {/* Items & orders */}
      <Resource name={RESOURCES.HOSPITALITY_CATALOGS} {...menus} />
      <Resource name={RESOURCES.HOSPITALITY_CATEGORIES} {...categories} />
      <Resource name={RESOURCES.HOSPITALITY_ITEMS} {...itemLibrary} />
      <Resource name={RESOURCES.PREP_STATIONS} {...prepStations} />
      <Resource name={RESOURCES.MEASURE_UNITS} {...measureUnits} />
      <Resource name="modifiers" {...modifiers} />
      {/* Staff */}
      <Resource name={RESOURCES.TEAM_MEMBERS} {...members} />
      <Resource name={RESOURCES.PERMISSIONS} {...permissions} />
      <Resource
        name={RESOURCES.TEAM_MEMBERS_WORKING_AREA}
        {...teamMembersWorkingArea}
      />
      {/* Account settings */}
      <Resource name="sign-in-security" {...signInSecurity} />
      <Resource name="preferences" {...preferences} />
      <Resource name="about-my-business" {...aboutMyBusiness} />
      <Resource name="email-notifications" {...emailNotifications} />
      <Resource name="outage-notifications" {...outageNotifications} />
      <Resource name={RESOURCES.LOCATIONS} {...sellpoints} />
      <Resource name="bank-accounts" {...bankAccounts} />
      <Resource name="sales-taxes" {...salesTaxes} />
      {/* Gift cards */}
      <Resource name="gift-cards-overview" {...giftCards} />
      <Resource name="gift-cards-settings" {...giftCardsSettings} />
      <Resource name="loyalty" {...loyalty} />
      {/* Devices management */}
      <Resource name={RESOURCES.DEVICES} {...devices} />
      <Resource name={RESOURCES.FLOOR_PLANS} {...floorPlans} />
      <Resource name="menu-behaviour" {...menuBehaviour} />
      <Resource name="courses" {...courses} />
      <Resource name={RESOURCES.TIPS} {...tips} />
      <Resource name={RESOURCES.DISCOUNTS} {...discounts} />
      <Resource name="comp-and-void" {...compAndVoid} />
      <Resource name="kitchen-displays" {...kitchenDisplays} />
      {/* Pricing & Subscriptions */}
      <Resource name="pricing-subscriptions" {...pricingSubscriptions} />
      {/* Reports */}
      <Resource name="report-sales-revenue" {...salesRevenue} />
      <Resource name="report-payment-methods" {...paymentMethods} />
      <Resource name="report-item-sales" {...itemSales} />
      <Resource name="report-category-sales" {...categorySales} />
      <Resource name="report-kitchen-performance" {...kitchenPerformance} />
      <Resource name="report-discounts" {...reportDiscounts} />
      <Resource name="report-modifier-sales" {...modifierSales} />
      <Resource name="report-coupons" {...coupons} />
      <Resource name="report-promotions" {...promotions} />
      <Resource name="report-voids" {...voids} />
      <Resource name="report-vat" {...taxes} />
      <Resource name="report-tips" {...reportTips} />
      <Resource name="report-team-revenue" {...reportTeamRevenue} />
      <Resource name="report-gift-cards" {...giftCardsReports} />
      <Resource name="report-extra-charges" {...extraCharges} />
      <Resource name="report-comps" {...comps} />
      <Resource name="transactions" {...reportTransactions} />
      {/* Coming soon */}
      <Resource name="transfers" {...transfers} />
      <Resource name="receipts" {...receipts} />
      {/* Payments */}
      <Resource name="invoices-overview" {...invoicesOverview} />
      <Resource name="invoices" {...invoices} />
      <Resource name="invoices-settings" {...invoicesSettings} />
      <Resource name={RESOURCES.MY_INTEGRATIONS} {...myIntegrations} />
      {/* Selio AI */}
      <Resource name="selio-ai" {...selioAI} />
      {/* Partner Integrations */}
      <Resource name="partner-integrations" {...partnerIntegrations} />

      <CustomRoutes noLayout>
        <Route
          path="/select-account"
          element={
            <Authenticated>
              <AccountSelectionPage />
            </Authenticated>
          }
        />
      </CustomRoutes>
    </Admin>
  );
};

const App = () => {
  return (
    <ThemeProvider initialValue={lightTheme}>
      <TabMonitor>
        <BrowserRouter>
          <FirebaseProvider>
            <ConnectionMonitor>
              <CookiesConsentProvider>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <AppInner />
                </LocalizationProvider>
              </CookiesConsentProvider>
            </ConnectionMonitor>
          </FirebaseProvider>
        </BrowserRouter>
      </TabMonitor>
    </ThemeProvider>
  );
};

export default App;
