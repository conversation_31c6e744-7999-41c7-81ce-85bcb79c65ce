import { useMemo, useState } from 'react';
import { Box, Button, Dialog } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import { useGetListHospitalityItemsLive } from '~/providers/resources';
import { menuColors } from '../../../data/menu-colors';
import CheckboxList from '../../molecules/CheckboxList';
import ModalHeader from '../../molecules/ModalHeader';
import { ROWS_NO } from './CatalogContainer';
import { Coordinates } from './types';

interface AddExistingTileModalProps {
  path: string;
  data: any;
  columns: number;
  columnsBefore: number;
  position?: Coordinates;
  onClose: () => void;
}
export default function AddExistingTileModal({
  path,
  data,
  columns,
  columnsBefore,
  position,
  onClose,
}: AddExistingTileModalProps) {
  const { setValue } = useFormContext();
  const { data: allExistingItems } = useGetListHospitalityItemsLive({
    filter: { _d: false },
  });
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  const itemsLength = useMemo(() => {
    return selectedItems.length;
  }, [selectedItems]);

  const addItems = () => {
    if (!position) return;

    const items = [...data];

    // if we are inside display group, we add items and sort alphabetically
    if (path.includes('items')) {
      items.push(...selectedItems);
      items.sort(
        (a, b) =>
          (a.type ?? 'product').localeCompare(b.type ?? 'product') ||
          a.displayName.localeCompare(b.displayName)
      );
    }
    // else we need to check available positions
    else {
      const startIndex =
        position?.startY * columns + position?.startX - columnsBefore;
      let selectedItemIndex = 0;

      for (
        let i = startIndex;
        i < ROWS_NO * columns && selectedItemIndex < selectedItems.length;
        i++
      ) {
        const x = (i % columns) + columnsBefore;
        const y = Math.floor(i / columns);
        const position = { startX: x, startY: y, endX: x + 1, endY: y + 1 };
        if (checkAvailablePosition(position)) {
          let itemToAdd = {
            ...selectedItems[selectedItemIndex++],
            position,
            color: menuColors[0],
          };
          items.push(itemToAdd);
        }
      }
    }

    setValue(path, items);
    handleClose();
  };

  const checkAvailablePosition = (position: Coordinates): boolean => {
    for (const item of data) {
      if (
        (+item.position.startX === position.startX &&
          +item.position.startY === position.startY) ||
        (+item.position.endX === position.endX &&
          +item.position.endY === position.endY)
      ) {
        return false;
      }
    }
    return true;
  };

  const handleClose = () => {
    setSelectedItems([]);
    onClose();
  };

  return (
    <Dialog open onClose={handleClose} fullWidth={true} maxWidth={'md'}>
      <ModalHeader handleClose={handleClose} title={'Add Existing Items'}>
        <Button
          variant="contained"
          onClick={addItems}
          disabled={itemsLength === 0}
        >
          Add {itemsLength} {itemsLength === 1 ? 'item' : 'items'}
        </Button>
      </ModalHeader>
      <Box
        sx={{
          p: 4,
          pt: 2,
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(80vh - 80px)',
        }}
      >
        <CheckboxList
          type={'product'}
          items={allExistingItems}
          selectedItems={selectedItems}
          updateSelectedItems={setSelectedItems}
        />
      </Box>
    </Dialog>
  );
}
