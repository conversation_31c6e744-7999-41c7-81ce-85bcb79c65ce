interface ReducerInterface {
  compReasons: string[];
  voidReasons: string[];
}
const initialState: ReducerInterface = {
  compReasons: [],
  voidReasons: [],
};

const reducer = (state = initialState, action: any) => {
  switch (action.type) {
    case 'ADD_COMP':
      return {
        ...state,
        compReasons: [...state.compReasons, action.payload],
      };
    case 'ADD_VOID':
      return {
        ...state,
        voidReasons: [...state.voidReasons, action.payload],
      };
    case 'EDIT_COMP':
      return {
        ...state,
        compReasons: state.compReasons.map((reason, index) =>
          index === action.payload.index ? action.payload.newReason : reason
        ),
      };
    case 'EDIT_VOID':
      return {
        ...state,
        voidReasons: state.voidReasons.map((reason, index) =>
          index === action.payload.index ? action.payload.newReason : reason
        ),
      };
    case 'DELETE_COMP':
      return {
        ...state,
        compReasons: state.compReasons.filter(
          (_, index) => index !== action.payload
        ),
      };
    case 'DELETE_VOID':
      return {
        ...state,
        voidReasons: state.voidReasons.filter(
          (_, index) => index !== action.payload
        ),
      };
    case 'SET_REASONS':
      return {
        ...state,
        compReasons: action.payload.compReasons,
        voidReasons: action.payload.voidReasons,
      };
    default:
      return state;
  }
};

// Action creators
const addComp = (reason: string) => ({ type: 'ADD_COMP', payload: reason });
const addVoid = (reason: string) => ({ type: 'ADD_VOID', payload: reason });
const editComp = (index: number, newReason: string) => ({
  type: 'EDIT_COMP',
  payload: { index, newReason },
});
const editVoid = (index: number, newReason: string) => ({
  type: 'EDIT_VOID',
  payload: { index, newReason },
});
const deleteComp = (index: number) => ({ type: 'DELETE_COMP', payload: index });
const deleteVoid = (index: number) => ({ type: 'DELETE_VOID', payload: index });
const setReasons = (compReasons: string[], voidReasons: string[]) => ({
  type: 'SET_REASONS',
  payload: { compReasons, voidReasons },
});

export {
  reducer,
  addComp,
  addVoid,
  editComp,
  editVoid,
  deleteComp,
  deleteVoid,
  setReasons,
};
