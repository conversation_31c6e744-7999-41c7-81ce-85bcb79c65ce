import { useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  Alert,
  Box,
  Button,
  Dialog,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { MuiSwitchInput } from '~/components/atoms/inputs/SwitchInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { ItemPrepValues } from '../types';

interface ItemPrepTimeModalProps {
  initialValue?: ItemPrepValues;
  onClose: () => void;
  onSave: (values: ItemPrepValues) => void;
}

const DEFAULT_ITEM_PREP_TIME = 30;

export default function ItemPrepTimeModal({
  initialValue,
  onClose,
  onSave,
}: ItemPrepTimeModalProps) {
  //   const [values, setValues] = useState({
  //     useLocationDefault: initialValue?.useLocationDefault ?? false,
  //     itemPrepTime: initialValue?.itemPrepTime ?? 30,
  //   });
  const [itemPrepTime, setItemPrepTime] = useState(
    initialValue?.itemPrepTime ?? DEFAULT_ITEM_PREP_TIME
  );
  const { t } = useTranslation();

  return (
    <Dialog
      open
      disablePortal
      onClose={(e: any, reason: any) => {
        if (reason === 'backdropClick') {
          e.stopPropagation();
          return;
        }
        onClose();
      }}
      fullWidth
      maxWidth="sm"
    >
      <ModalHeader handleClose={onClose} title="" noBorder>
        <Button variant="contained" onClick={() => onSave({ itemPrepTime })}>
          {t('shared.done')}
        </Button>
      </ModalHeader>

      <Box p={2} mb={2}>
        <Typography variant="h3" mb={4}>
          {t('menu.itemPrepTime')}
        </Typography>
        <Typography variant="subtitle2" color="textPrimary" mb={4}>
          {t('menu.itemPrepTimeDescription')}
        </Typography>

        <Alert
          icon={<InfoIcon />}
          severity="info"
          sx={{
            mb: 3,
            backgroundColor: 'rgb(229, 237, 255)',
            '& .MuiAlert-icon': {
              color: 'rgb(28, 100, 242)',
            },
          }}
        >
          {t('menu.itemPrepTimeAlert')}
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 1,
            }}
          >
            <Box>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {t('menu.useDefault')}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                ({DEFAULT_ITEM_PREP_TIME} {t('shared.minutes')})
              </Typography>
            </Box>
            <MuiSwitchInput
              checked={itemPrepTime === DEFAULT_ITEM_PREP_TIME}
              onChange={e => {
                if (e.target.checked) {
                  setItemPrepTime(DEFAULT_ITEM_PREP_TIME);
                } else {
                  setItemPrepTime(0);
                }
              }}
            />
          </Box>
        </Box>

        {itemPrepTime !== DEFAULT_ITEM_PREP_TIME && (
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
              {t('menu.customItemPrepTime')}
            </Typography>
            <FormControl fullWidth>
              <InputLabel id="item-prep-time-label">
                {t('menu.itemPrepTime')}
              </InputLabel>
              <Select
                labelId="item-prep-time-label"
                label={t('menu.itemPrepTime')}
                value={itemPrepTime}
                onChange={e => setItemPrepTime(e.target.value as number)}
                fullWidth
                sx={{
                  height: '55px',
                  '& .MuiSelect-select': {
                    display: 'flex',
                    alignItems: 'center',
                  },
                }}
              >
                <MenuItem value={0}>{t('menu.availableImmediately')}</MenuItem>
                {[5, 10, 15, 20, 25, 30, 35, 40].map(minutes => (
                  <MenuItem key={minutes} value={minutes}>
                    {minutes} {t('shared.minutes')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )}

        <Typography variant="body2" fontWeight={200} sx={{ mt: 2 }}>
          {t('menu.ordersWithThisItemWillBeReady')}
          <b>
            {itemPrepTime === DEFAULT_ITEM_PREP_TIME
              ? t('menu.in30Minutes')
              : itemPrepTime === 0
                ? t('menu.immediately')
                : `${t('shared.in')} ${itemPrepTime} ${t('shared.minutes')}`}{' '}
          </b>
          {t('menu.whereItIsAvailable')}
        </Typography>
      </Box>
    </Dialog>
  );
}
