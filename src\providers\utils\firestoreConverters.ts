import {
  deleteField,
  DocumentReference,
  FieldValue,
  GeoPoint,
  Timestamp,
} from 'firebase/firestore';
import { get, set } from 'lodash';

import { itCanBeCastToNumber } from '~/fake-provider/reports/utils/isNumber';
import {
  isObject,
  isObjectAndNotEmpty,
} from '~/fake-provider/reports/utils/isObject';
import { RESOURCES, ResourcesInfo, resourcesInfo } from '~/providers/resources';

export const getFirestoreConverter = (resource: string) => {
  const resourceInfo = resourcesInfo[resource as keyof ResourcesInfo];
  if (resourceInfo) {
    if (resourceInfo.type === 'embeddedMap') {
      switch (resource) {
        case RESOURCES.PERMISSIONS:
          return permissionsConverter;
        default:
          return defaultEmbeddedMapConverter(resourceInfo.field);
      }
    } else if (resourceInfo.type === 'embeddedArray') {
      return defaultEmbeddedArrayConverter(resourceInfo.field);
    } else {
      switch (resource) {
        case RESOURCES.HOSPITALITY_CATALOGS:
          return catalogConverter;
        case RESOURCES.HOSPITALITY_ITEMS:
          return itemConverter;
        default:
          return defaultConverter;
      }
    }
  } else {
    return defaultConverter;
  }
};

// Helper function to recursively convert Firestore Timestamps to milliseconds
const convertTimestampsToMillis = (data: any): any => {
  // Base case: If it's null, not an object, or a specific Firestore type, return as is.
  if (
    data === null ||
    typeof data !== 'object' ||
    data instanceof Timestamp ||
    data instanceof FieldValue ||
    data instanceof GeoPoint ||
    data instanceof DocumentReference ||
    data instanceof Blob
  ) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map((item, index) => {
      if (typeof item === 'object' && item !== null) {
        return convertTimestampsToMillis(item); // Recurse
      } else {
        return item; // Primitives
      }
    });
  }

  const newData: any = {};
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];
      // convert all values to milliseconds because react-admin expects numbers for dates
      // and firestore Timestamp isn't supported by react-admin
      if (value instanceof Timestamp) {
        newData[key] = value.toMillis();
      } else if (typeof value === 'object' && value !== null) {
        newData[key] = convertTimestampsToMillis(value); // Recurse
      } else {
        newData[key] = value; // Primitives
      }
    }
  }
  return newData;
};
// Helper function to recursively convert milliseconds back to Firestore Timestamps
const convertMillisToTimestamps = (data: any): any => {
  // Base case: If it's null, not an object, or a specific Firestore type, return as is.
  if (
    data === null ||
    typeof data !== 'object' ||
    data instanceof Timestamp ||
    data instanceof FieldValue ||
    data instanceof GeoPoint ||
    data instanceof DocumentReference ||
    data instanceof Blob
  ) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map((item, index) => {
      if (typeof item === 'object' && item !== null) {
        return convertMillisToTimestamps(item); // Recurse
      } else {
        return item;
      }
    });
  }

  const newData: any = {};
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];

      // we convert millis to timestamps only for _c and _u because we do not want all numbers to be converted to timestamps
      if ((key === '_c' || key === '_u') && typeof value === 'number') {
        // Convert numeric timestamps for _c and _u
        if (value >= 0) {
          newData[key] = Timestamp.fromMillis(value);
        } else {
          newData[key] = value; // Keep negative values as is
        }
      } else if (typeof value === 'object' && value !== null) {
        newData[key] = convertMillisToTimestamps(value); // Recurse
      } else {
        // Handle primitives
        newData[key] = value;
      }
    }
  }
  return newData;
};

// Helper function to convert Firestore maps to arrays where the keys need to be the ids inside the object
const convertMapToArray = (obj: Record<string, any>, idField: string): any => {
  const newArray: any[] = [];
  const keys = Object.keys(obj);
  keys.forEach(key => {
    const item = obj[key];
    // Check if the item is an object
    if (typeof item === 'object' && item !== null) {
      // Add the key as the id field property
      item[idField] = key;
      // Add the item to the new array
      newArray.push(item);
    }
  });
  return newArray;
};

const convertArrayToMap = (
  arr: any[],
  idField: string
): Record<string, any> => {
  const newMap: Record<string, any> = {};
  arr.forEach((item, index) => {
    // Check if the item is an object and has an id field property
    if (typeof item === 'object' && item !== null && item[idField]) {
      // Use the id field as the key in the new map
      newMap[item[idField]] = item;
    } else {
      // If no id, use the index as the key
      newMap[String(index + 1)] = item;
    }
  });
  return newMap;
};

const defaultConverter = {
  toFirestore: (data: any) => {
    const dataWithTimestamps = convertMillisToTimestamps(data);
    return dataWithTimestamps;
  },
  fromFirestore: (snapshot: any, options: any) => {
    const data = snapshot.data(options);
    const dataWithMillis = convertTimestampsToMillis(data);
    return { id: snapshot.id, ...dataWithMillis };
  },
};

const defaultEmbeddedArrayConverter = (field: string) => {
  return {
    toFirestore: (data: any) => {
      const transformedData = convertReactAdminResourceToFirestoreArray(
        data,
        field
      );
      const dataWithTimestamps = defaultConverter.toFirestore(transformedData);
      return dataWithTimestamps;
    },
    fromFirestore: (snapshot: any, options: any) => {
      const dataWithMillis = defaultConverter.fromFirestore(snapshot, options);
      const transformedData = convertFirestoreArrayToReactAdminResource(
        dataWithMillis,
        field
      );
      return transformedData;
    },
  };
};

const convertReactAdminResourceToFirestoreArray = (
  documentData: any,
  field: string
): Array<any> => {
  // this function is used to convert the data from react-admin to firestore
  const data = get(documentData, field);
  // check if the data is an array
  if (Array.isArray(data) && data.length > 0) {
    // we are going to loop through the array and check if the item is an object
    // and if it has _isJustAValue field it means that it is a value and not an object
    // if it doesn't have _isJustAValue field it means that it is an object
    const result = data.map(item => {
      if (isObjectAndNotEmpty(item)) {
        if (item._isJustAValue !== undefined && item._isJustAValue) {
          return item.value;
        } else {
          if (
            typeof item.id === 'string' &&
            itCanBeCastToNumber(item.id) &&
            typeof item._index === 'number' &&
            item._index === Number(item.id) - 1
          ) {
            const { id: _removedId, _index: _removedIndex, ...newItem } = item;
            return newItem;
          } else {
            const { _index: _removedIndex, ...newItem } = item;
            return newItem;
          }
        }
      }
      return item;
    });
    return set(documentData, field, result);
  }
  return [];
};

const convertFirestoreArrayToReactAdminResource = (
  documentData: any,
  field: string
): Array<any> => {
  // this function is used to convert the data from firestore to react-admin
  const data = get(documentData, field);
  // check if the data is an array
  if (Array.isArray(data) && data.length > 0) {
    // this array can be an array of objects or an array of primitives or both
    // so we need to loop through the array and check if the item is an object or not
    const result = data.map((item, index) => {
      if (isObjectAndNotEmpty(item)) {
        if (item.id === undefined) {
          return { ...item, id: `${index + 1}`, _index: index };
        } else {
          return { ...item, _index: index };
        }
      } else {
        return {
          id: `${index + 1}`,
          value: item,
          _index: index,
          _isJustAValue: true,
        };
      }
    });
    return set(documentData, field, result);
  }
  return [];
};

const defaultEmbeddedMapConverter = (field: string) => {
  return {
    toFirestore: (data: any) => {
      const transformedData = convertReactAdminResourceToFirestoreMap(
        data,
        field
      );
      const dataWithTimestamps = defaultConverter.toFirestore(transformedData);
      return dataWithTimestamps;
    },
    fromFirestore: (snapshot: any, options: any) => {
      const dataWithMillis = defaultConverter.fromFirestore(snapshot, options);
      const transformedData = convertFirestoreMapToReactAdminResource(
        dataWithMillis,
        field
      );
      return transformedData;
    },
  };
};

const convertReactAdminResourceToFirestoreMap = (
  documentData: any,
  field: string
): Record<string, any> => {
  // this function is used to convert the data from react-admin to firestore
  const data = get(documentData, field);
  //
  if (isObjectAndNotEmpty(data)) {
    // if the data is an object and if it has _isJustAValue field it means that it is a key value pair
    // if it doesn't have _isJustAValue field it means that it is an object
    const result = Object.entries(data).reduce(
      (acc, [id, item]) => {
        if (item._isJustAValue) {
          acc[id] = item.value;
        } else {
          acc[id] = item;
        }
        return acc;
      },
      {} as Record<string, any>
    );
    // set the result to the documentData
    return set(documentData, field, result);
  }
  return documentData;
};

const convertFirestoreMapToReactAdminResource = (
  documentData: any,
  field: string
): Array<any> => {
  // this function is used to convert the data from firestore to react-admin
  const data = get(documentData, field);
  // check if the data is an object
  if (isObjectAndNotEmpty(data)) {
    // this object can be a map of objects or a key value map or both
    // so we need to loop through the object and check if the value is an object or not
    const result = Object.entries(data).map(([id, value]) => {
      if (isObjectAndNotEmpty(value)) {
        return { id, ...value };
      } else {
        return { id, value, _isJustAValue: true };
      }
    });
    return set(documentData, field, result);
  }
  return [];
};

const catalogConverter = {
  toFirestore: (data: any) => {
    // First, convert milliseconds to Timestamps recursively
    const dataWithTimestamps = convertMillisToTimestamps(data);
    // Then, handle the specific 'pages' array to object conversion
    const newPagesObject: any = {};
    if (dataWithTimestamps.pages && Array.isArray(dataWithTimestamps.pages)) {
      dataWithTimestamps.pages.forEach((page: any, index: number) => {
        newPagesObject[String(index + 1)] = page;
      });
    }
    const extraData: { [key: string]: any } = {};
    extraData._ur = 'this';
    // Return the final object with converted timestamps and pages structure
    return { ...dataWithTimestamps, pages: newPagesObject, ...extraData };
  },
  fromFirestore: (snapshot: any, options: any) => {
    const data = snapshot.data(options);
    // First, handle the specific 'pages' object to array conversion
    let processedPages: any[] = [];
    if (
      data.pages &&
      typeof data.pages === 'object' &&
      Object.keys(data.pages).length > 0
    ) {
      const pagesKeys = Object.keys(data.pages);
      // sort the keys because they are numbers
      pagesKeys.sort((a, b) => parseInt(a) - parseInt(b));
      pagesKeys.forEach(key => {
        processedPages.push(data.pages[key]);
      });
    }
    // Create the initial object structure with the processed pages
    const initialData = { id: snapshot.id, ...data, pages: processedPages };
    // Now, recursively convert Timestamps to milliseconds
    const dataWithMillis = convertTimestampsToMillis(initialData);
    return dataWithMillis;
  },
};

const itemConverter = {
  toFirestore: (data: any) => {
    /*
                        // Check if catalogSpecific exists and is an array
                        if (data.catalogSpecific && Array.isArray(data.catalogSpecific)) {
                          // convert the catalogSpecific array to an object
                          data.catalogSpecific = convertArrayToMap(
                            data.catalogSpecific,
                            'catalogId'
                          );
                          // Iterate over the catalogSpecific object to process each item
                          // convert price from float to integer
                          for (const value of Object.values(data.catalogSpecific) as any[]) {
                            if (value.price && typeof value.price === 'number') {
                              value.price = Math.floor(value.price * 10000);
                            }
                          }
                        }
                        */
    // First, convert milliseconds to Timestamps recursively
    const dataWithTimestamps = convertMillisToTimestamps(data);
    const extraData: { [key: string]: any } = {};
    extraData._ur = 'this';

    return { ...dataWithTimestamps, ...extraData };
  },
  fromFirestore: (snapshot: any, options: any) => {
    const data = snapshot.data(options);
    const initialData = { id: snapshot.id, ...data };
    // Now, recursively convert Timestamps to milliseconds
    const dataWithMillis = convertTimestampsToMillis(initialData);
    /*
                        // check if catalogSpecific exists and is an object
                        if (
                          dataWithMillis.catalogSpecific &&
                          typeof dataWithMillis.catalogSpecific === 'object'
                        ) {
                          // Iterate over the catalogSpecific object to process each item
                          // convert price from integer to float
                          for (const value of Object.values(
                            dataWithMillis.catalogSpecific
                          ) as any[]) {
                            if (value.price && typeof value.price === 'number') {
                              value.price = value.price / 10000;
                            }
                          }
                          // convert the catalogSpecific object to an array
                          dataWithMillis.catalogSpecific = convertMapToArray(
                            dataWithMillis.catalogSpecific,
                            'catalogId'
                          );
                        }
                        */
    return dataWithMillis;
  },
};

const permissionsConverter = {
  toFirestore: (data: any) => {
    const resourceInfo = resourcesInfo[RESOURCES.PERMISSIONS];
    const transformedData = defaultEmbeddedMapConverter(
      resourceInfo.field
    ).toFirestore(data);
    // Get the permissions object and loop through it to delete the access field if it is an empty array
    const permissions = get(transformedData, [resourceInfo.field]);
    if (isObjectAndNotEmpty(permissions)) {
      for (const [permissionId, permission] of Object.entries(permissions)) {
        if (
          Array.isArray(permission.access) &&
          permission.access.length === 0
        ) {
          set(permissions, [permissionId, 'access'], deleteField());
        }
      }
    }
    // will return transformedData with the access field deleted if it is an empty array
    // because we mutated the permissions object which is a reference to the transformedData[resourceInfo.field]
    return transformedData;
  },
  fromFirestore: (snapshot: any, options: any) => {
    const resourceInfo = resourcesInfo[RESOURCES.PERMISSIONS];
    const transformedData = defaultEmbeddedMapConverter(
      resourceInfo.field
    ).fromFirestore(snapshot, options);
    // we do not need to do anything else because if the access field is not present in the database it will be undefined
    // and react-admin will handle it correctly
    return transformedData;
  },
};
