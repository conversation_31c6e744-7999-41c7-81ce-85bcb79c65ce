import { Box, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

export default function PricingSubscriptionsCreateStep4Premium() {
  const { theme } = useTheme();
  return (
    <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
      <Box
        p={2}
        sx={{
          mt: 3,
          maxWidth: '600px',
        }}
      >
        <Typography
          sx={{
            width: '100%',
            fontWeight: 600,
            textAlign: 'center',
            mb: 3,
            fontSize: '28px',
          }}
        >
          Subscribe to Selio for Restaurants Premium
        </Typography>

        <Box
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
            backgroundColor:
              theme.palette.mode !== 'light' ? '#606060' : '#E5E5E5',
            borderRadius: 1.5,
            px: 3,
            pt: 2,
            pb: 4,
            gap: 2,
          }}
        >
          <Typography sx={{ textAlign: 'center' }}>85 € per month</Typography>
          <Typography
            color="textSecondary"
            sx={{ textAlign: 'center', fontSize: '14px' }}
          >
            You will be charged for a month (representing 60 € per 1 location +
            25 € per 1 POS device) on the same date of every following months.
            You may also be charged for sales tax if applicable. If you change
            your mind, you can pause or cancel your subscription at anytime by
            visiting the Pricing & Subscriptions page in your Selio Manager. For
            more information, visit our Support Center.
          </Typography>
        </Box>
        <Box
          sx={{
            width: '100%',
            border: '1.5px solid #E5E5E5',
            borderRadius: 1.5,
            px: 3,
            py: 2,

            mt: 2,
          }}
        >
          <Typography
            color="textSecondary"
            sx={{ textAlign: 'start', fontSize: '14px' }}
          >
            By subscribing, I agree to the{' '}
            <a
              style={{ textDecoration: 'none' }}
              href="https://selio.io/terms-of-service/"
              target="_blank"
            >
              <span style={{ color: '#006AFF', cursor: 'pointer' }}>
                Terms of Service
              </span>
            </a>{' '}
            and{' '}
            <a
              style={{ textDecoration: 'none' }}
              href="https://selio.io/privacy-policy/"
              target="_blank"
            >
              {' '}
              <span style={{ color: '#006AFF', cursor: 'pointer' }}>
                Privacy Policy.
              </span>
            </a>
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
