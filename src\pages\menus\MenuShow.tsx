import { useEffect } from 'react';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import {
  Box,
  Button,
  Theme,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import {
  ArrayField,
  Show,
  TextField,
  useCreatePath,
  useListContext,
  useRecordContext,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { RESOURCES } from '~/providers/resources';

const MenuShowInner = () => {
  const record = useRecordContext();
  const redirect = useRedirect();
  const navigate = useNavigate();
  const { t } = useTranslation('');

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  useEffect(() => {
    if (record && (record.pages.length != 1 || record.pages[0].length)) {
      redirect('edit', RESOURCES.HOSPITALITY_CATALOGS, record.id, undefined, {
        _scrollToTop: false,
      });
    }
  }, []);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        {/* @ts-ignore */}
        <Button variant="close-btn" onClick={() => navigate(-1)}>
          <ChevronLeftIcon />
        </Button>
        <Box>
          <TextField source="name" fontSize={20} />
          <ArrayField source="sellPoints" label="Locations" textAlign="right">
            <LocationsField />
          </ArrayField>
        </Box>
      </Box>
      <Box
        mb={6}
        display="flex"
        alignItems="center"
        flexDirection="column"
        my={10}
      >
        <MenuBookIcon sx={{ fontSize: 40, color: 'text.secondary' }} />
        <Typography variant="body1" fontWeight={500}>
          {t('menu.emptyMenu')}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {t('menu.emptyMenuDescription')}
        </Typography>
        <Button
          variant="contained"
          sx={{ m: 1 }}
          onClick={() => {
            redirect(
              'edit',
              RESOURCES.HOSPITALITY_CATALOGS,
              record?.id,
              undefined,
              {
                _scrollToTop: false,
              }
            );
          }}
        >
          {t('menu.addMenuGroup')}
        </Button>
        <Box mt={10} />
        <Typography variant="h5" mb={1}>
          {t('menu.stuck')}
        </Typography>
        <Typography variant="caption" mb={2}>
          {t('menu.stuckDescription')}
        </Typography>
        <Box>
          <Tooltip title="Coming soon">
            <span>
              {/* @ts-ignore */}
              <Button variant="contained-light" sx={{ m: 1 }} disabled>
                {t('menu.letUsBuildYourMenu')}
              </Button>
            </span>
          </Tooltip>
          <Button
            // @ts-ignore
            variant="contained-light"
            sx={{ m: 1 }}
            onClick={() => {
              window.open('https://selio.io/support-center', '_blank');
            }}
          >
            <PlayCircleOutlineIcon sx={{ mr: 1 }} />
            {t('menu.learnAboutMenus')}
          </Button>
        </Box>
      </Box>
    </>
  );
};

const LocationsField = () => {
  const { data } = useListContext();

  return (
    !!data?.length && (
      <Typography variant="body2" color="text.secondary">
        {data?.length} location{data?.length && data?.length > 1 ? 's' : ''}
      </Typography>
    )
  );
};

export default function MenuShow() {
  return (
    <Show component={'div'} sx={{ my: 2, mx: 'auto', width: '95%' }}>
      <MenuShowInner />
    </Show>
  );
}
