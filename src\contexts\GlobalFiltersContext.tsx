import { createContext, useContext, useEffect } from 'react';
import dayjs from 'dayjs';

import { useLocalStorage } from '~/hooks';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';
import type { PropsWithChildren } from 'react';

interface GlobalFiltersContextType {
  dateRange: DateRange<Dayjs>;
  sellpointId: string;
  setDateRange: (range: DateRange<Dayjs>) => void;
  setSellpointId: (sellpointId: string) => void;
  timeRange: DateRange<Dayjs> | null;
  setTimeRange: (range: DateRange<Dayjs> | null) => void;
}

const GlobalFiltersContext = createContext<
  GlobalFiltersContextType | undefined
>(undefined);

const transformToDayjsDateRange = (
  value: [string, string]
): DateRange<Dayjs> => {
  return [dayjs(value[0]), dayjs(value[1])];
};

const GlobalFiltersProvider = ({ children }: PropsWithChildren) => {
  const [dateRange, setDateRange] = useLocalStorage<DateRange<Dayjs>>(
    'selectedDateRange',
    [dayjs(), dayjs()],
    transformToDayjsDateRange
  );

  const [timeRange, setTimeRange] = useLocalStorage<DateRange<Dayjs> | null>(
    'selectedTimeRange',
    null,
    transformToDayjsDateRange
  );

  const [sellpointId, setSellpointId] = useLocalStorage<string>(
    'selectedSellpointId',
    ''
  );

  return (
    <GlobalFiltersContext.Provider
      value={{
        dateRange,
        sellpointId,
        setDateRange,
        setSellpointId,
        timeRange,
        setTimeRange,
      }}
    >
      {children}
    </GlobalFiltersContext.Provider>
  );
};

interface UseGlobalFiltersParams {
  sellpoints?: Array<{ id: string }>;
}

const useGlobalFilters = ({
  sellpoints,
}: UseGlobalFiltersParams): GlobalFiltersContextType => {
  const context = useContext(GlobalFiltersContext);
  if (!context) {
    throw new Error(
      'useGlobalFilters must be used within a GlobalFiltersProvider'
    );
  }

  const { sellpointId, setSellpointId } = context;

  useEffect(() => {
    if (!sellpoints || sellpoints.length === 0) {
      return;
    }

    // initialize sellpointId if not already set
    if (!sellpointId) {
      setSellpointId(sellpoints[0].id);
    }
  }, [sellpoints, sellpointId, setSellpointId]);

  return context;
};

export { GlobalFiltersProvider, useGlobalFilters };
