import * as React from 'react';
import Typography from '@mui/material/Typography';
import { Link, LinkProps } from '@mui/material';
import { useFieldValue, useTranslate } from 'ra-core';
import { FieldProps, sanitizeFieldRestProps } from 'react-admin';
import { genericMemo } from '~/utils/genericMemo';

const PhoneFieldImpl = <
    RecordType extends Record<string, any> = Record<string, any>,
>(
    props: PhoneFieldProps<RecordType>
) => {
    const { className, emptyText, ...rest } = props;
    const value = useFieldValue(props);
    const translate = useTranslate();

    if (value == null) {
        return emptyText ? (
            <Typography
                component="span"
                variant="body2"
                className={className}
                {...sanitizeFieldRestProps(rest)}
            >
                {emptyText && translate(emptyText, { _: emptyText })}
            </Typography>
        ) : null;
    }

    return (
        <Link
            className={className}
            href={`tel://${value}`}
            onClick={stopPropagation}
            variant="body2"
            {...sanitizeFieldRestProps(rest)}
        >
            {value}
        </Link>
    );
};
PhoneFieldImpl.displayName = 'PhoneFieldImpl';

export const PhoneField = genericMemo(PhoneFieldImpl);

export interface PhoneFieldProps<
    RecordType extends Record<string, any> = Record<string, any>,
> extends FieldProps<RecordType>,
        Omit<LinkProps, 'textAlign'> {}

// useful to prevent click bubbling in a Datagrid with rowClick
const stopPropagation = (e:any) => e.stopPropagation();
