import React, { useEffect, useState } from 'react';
import { ExpandLess, ExpandMore } from '@mui/icons-material';
import {
  Box,
  Collapse,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useSidebarState } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { useTheme } from '../../contexts';
import openUrl from '../../utils/openUrl';

export interface MenuItemI {
  label: string;
  icon?: React.ReactNode;
  href?: string;
  externalLink?: boolean;
  comingSoon?: boolean;
  searchable?: boolean;
  items?: MenuItemI[];
}

export interface MenuItemProps {
  menuItem: MenuItemI;
  level?: number; // New parameter for tracking the level
  open: boolean;
  setOpen: () => void;
}

export default function DropdownMenuItem({
  menuItem,
  level = 1,
  open: firstLvlOpen,
  setOpen: setFirstLvlOpen,
}: MenuItemProps) {
  const { label, icon, items, comingSoon } = menuItem;
  const [open, setOpen] = useState(firstLvlOpen);
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery('(max-width:600px)', { noSsr: true });
  const [_, setOpenSidebar] = useSidebarState();
  const { t } = useTranslation();
  const { theme } = useTheme();

  useEffect(() => {
    menuItem?.items?.forEach(element => {
      if (location.pathname === element.href) {
        setOpen(true);
        if (level == 1) {
          setFirstLvlOpen();
        }
      }
    });
  }, [location]);

  useEffect(() => {
    if (firstLvlOpen !== open) {
      setOpen(firstLvlOpen);
    }
  }, [firstLvlOpen]);

  const handleClick = (el: any) => {
    if (el.items?.length) {
      if (!open && level == 1) {
        setFirstLvlOpen();
      }
      setOpen(!open);
    } else if (el.href) {
      el.externalLink ? openUrl(el.href) : navigate(el.href);
      if (isMobile) {
        setOpenSidebar(false);
        window.scrollTo({ top: 0, left: 0 });
      }
    }
  };

  return (
    <>
      <ListItemButton
        onClick={() => handleClick(menuItem)}
        sx={{
          display: 'flex',
          alignItems: 'center',
          transition: 'none',
          color:
            (open && menuItem.items) ||
            (location.pathname === menuItem.href &&
              location.pathname !== 'coming-soon')
              ? 'primary.main'
              : 'inherit',
          backgroundColor:
            location.pathname === menuItem.href && menuItem.label !== "personalInformation" &&
            location.pathname !== 'coming-soon'
              ? 'primary.veryLight'
              : 'background.default',
          ':hover': {
            color: '#0069FF',
            backgroundColor:
              location.pathname === menuItem.href && menuItem.label !== "personalInformation" &&
              location.pathname !== 'coming-soon'
                ? 'primary.veryLight'
                : 'background.default',
          },
        }}
      >
        {menuItem.icon && (
          <ListItemIcon
            sx={{
              filter:
                theme.palette.mode === 'light' ? '' : 'invert(1) !important',
            }}
          >
            {menuItem.icon}
          </ListItemIcon>
        )}
        {icon && typeof icon === 'function' && (
          <ListItemIcon
            sx={{
              filter:
                theme.palette.mode === 'light' ? '' : 'invert(1) !important',
            }}
          >
            {icon}
          </ListItemIcon>
        )}
        <ListItemText
          sx={{ flex: 'none' }}
          // @ts-ignore
          primary={
            <Box sx={{ display: 'flex', alignItems: 'start' }}>
              {/* Adding padding based on level */}
              {Array.from({ length: level }, (_, i) => (
                <span key={i} style={{ marginLeft: '6px' }} />
              ))}
              <Typography
                variant="body1"
                fontSize={level === 1 ? '1rem' : '0.9rem'}
                fontWeight={500}
              >
                {t(`menu.${label}`)}
              </Typography>
              {comingSoon && (
                <Typography
                  display={'inline-block'}
                  sx={{ bgcolor: 'error.main', ml: 0.5, px: '4px' }}
                  color="white"
                  fontSize="8px"
                >
                  {t(`shared.soon`)}
                </Typography>
              )}
            </Box>
          }
        />
        <Box sx={{ pt: 1.2 }}>
          {items?.length && (open ? <ExpandLess /> : <ExpandMore />)}
        </Box>
      </ListItemButton>
      {items?.length && (
        <Collapse
          sx={{ marginLeft: '10px' }}
          in={open}
          timeout="auto"
          unmountOnExit
        >
          <List component="div" disablePadding>
            {/* @ts-ignore */}
            {items?.map((el: MenuItemI, index: number) => {
              return (
                <DropdownMenuItem
                  key={`${el.label}-${index}`}
                  menuItem={el}
                  level={level + 1}
                  open={false}
                  setOpen={() => {}}
                />
              );
            })}
          </List>
        </Collapse>
      )}
    </>
  );
}
