import { Box, Checkbox, FormControlLabel, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { permissionsIdToLabel } from './constants';

const PermissionsGroup = ({
  groupLabel,
  possiblePermissions,
  activePermissions,
  handlePermissionChange,
  groupDisabled,
}: {
  groupLabel: string;
  possiblePermissions: number[];
  activePermissions: number[];
  handlePermissionChange: (id: number) => void;
  groupDisabled: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <Box mt={3}>
      <Typography
        variant="caption"
        color="custom.gray600"
        fontWeight={600}
        style={{
          textTransform: 'uppercase',
          display: 'block',
          marginBottom: '10px',
        }}
      >
        {t(`permissions.groupTitles.${groupLabel}`)}
      </Typography>
      {possiblePermissions.map(id => (
        <FormControlLabel
          key={id}
          sx={{
            alignItems: 'flex-start',
            ml: 2.5,
            '.MuiCheckbox-sizeMedium': {
              padding: '5px',
            },
          }}
          label={
            <Box sx={{ padding: '6px' }}>
              <Typography variant="body2">
                {t(`permissions.${permissionsIdToLabel[id]}.title`)}
              </Typography>
              <Typography variant="caption" color="custom.gray600">
                {t(`permissions.${permissionsIdToLabel[id]}.description`)}
              </Typography>
            </Box>
          }
          control={
            <Checkbox
              checked={
                activePermissions.includes(id) || activePermissions[0] === 1
              }
              disabled={groupDisabled}
              onChange={() => handlePermissionChange(id)}
              inputProps={{ 'aria-label': 'controlled' }}
            />
          }
        />
      ))}
    </Box>
  );
};

export { PermissionsGroup };
