import { reportCommonFields } from './constants';
import { OmitKeysWithTypeTransform, Report, ReportType } from './types';

// call this function to get the common fields values of a report
// the function should return an object with the common fields and their values as an array
// the values should be unique and not repeated
// the function should be able to handle any report type
// the result can and will be used to provide the user with a list of possible values for each common field
export function getReportCommonFieldsValues<K extends keyof ReportType>(
  reportType: K,
  report: Array<OmitKeysWithTypeTransform<Report<K>>>
): {
  [C in keyof Omit<OmitKeysWithTypeTransform<Report<K>>, 'report'>]: Array<
    Omit<OmitKeysWithTypeTransform<Report<K>>, 'report'>[C]
  >;
} {
  const result: Record<string, Record<string | number, boolean>> = {};
  // go through the common fields and check if they contain @ in their name
  // if they do, split the field name by @ and get the first part
  // and add it to the result object with an empty object
  for (const commonFieldName of reportCommonFields) {
    if (commonFieldName.includes('@')) {
      result[commonFieldName.split('@')[0]] = {};
    } else {
      result[commonFieldName] = {};
    }
  }
  // check if the report is empty
  if (report.length > 0) {
    // go through the report and get the values of the common fields
    // and add them to the result object if they are not already there
    for (const reportItem of report) {
      for (const reportItemKey of Object.keys(reportItem)) {
        if (
          reportItemKey !== 'report' &&
          reportItemKey in result &&
          reportItem[
            reportItemKey as keyof Omit<
              OmitKeysWithTypeTransform<Report<K>>,
              'report'
            >
          ] !== undefined
        ) {
          result[reportItemKey][
            reportItem[
              reportItemKey as keyof Omit<
                OmitKeysWithTypeTransform<Report<K>>,
                'report'
              >
            ]
          ] = true;
        }
      }
    }
  }
  const finalResult: Record<string, Array<string | number>> = {};
  // go through the result object and transform the values into an array
  for (const resultKey of Object.keys(result)) {
    finalResult[resultKey] = Object.keys(result[resultKey]);
  }
  return finalResult as {
    [C in keyof Omit<OmitKeysWithTypeTransform<Report<K>>, 'report'>]: Array<
      Omit<OmitKeysWithTypeTransform<Report<K>>, 'report'>[C]
    >;
  };
}
