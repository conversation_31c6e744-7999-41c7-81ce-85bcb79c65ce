import { useEffect } from 'react';
import { Box } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import LoyaltysBreadcrumbs from './LoyaltyBreadcrumbs';
import LoyaltyCreateStep1 from './LoyaltyCreateStep1';
import LoyaltyCreateStep2 from './LoyaltyCreateStep2';
import LoyaltyCreateStep3 from './LoyaltyCreateStep3';
import LoyaltyCreateStep4 from './LoyaltyCreateStep4';

interface LoyaltyFormProps {
  step: number;
  setStep: (step: number) => void;
  edit?: boolean;
  isEditing: boolean;
  setIsContinueDisabled: (disabled: boolean) => void;
}

export default function LoyaltyForm({
  edit,
  step,
  isEditing,
  setStep,
  setIsContinueDisabled,
}: LoyaltyFormProps) {
  const { watch } = useFormContext();

  const units = watch('units');
  const customersEarn = watch('customersEarn');
  const rewardExpiration = watch('rewardExpiration');
  const rewardType = watch('rewardType');
  const couponAmount = watch('coupon-amount');
  const unitsNeeded = watch('units-needed');

  useEffect(() => {
    setIsContinueDisabled(true);

    switch (step) {
      case 0: {
        const { singular, plural } = units?.values || {};
        if (singular && plural) {
          setIsContinueDisabled(false);
        }
        break;
      }
      case 1: {
        if (
          // prettier-ignore
          customersEarn === 0 ||rewardExpiration === 0||
          ( customersEarn && rewardExpiration)
        ) {
          setIsContinueDisabled(false);
        }
        break;
      }
      case 2: {
        // prettier-ignore
        if (rewardType === 0 || (rewardType && couponAmount && unitsNeeded)) {
          setIsContinueDisabled(false);
        }
        break;
      }
      case 3: {
        setIsContinueDisabled(false);
        break;
      }
      default:
        break;
    }
  }, [
    step,
    units,
    customersEarn,
    rewardExpiration,
    rewardType,
    couponAmount,
    unitsNeeded,
  ]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '800px',
        width: '90%',
        mx: 'auto',
        my: 2,
        gap: 1,
      }}
    >
      <LoyaltysBreadcrumbs step={step} setStep={setStep} />
      {step === 0 && <LoyaltyCreateStep1 edit={edit} />}
      {step === 1 && <LoyaltyCreateStep2 />}
      {step === 2 && <LoyaltyCreateStep3 />}
      {step === 3 && (
        <LoyaltyCreateStep4 isEditing={isEditing} setStep={setStep} />
      )}
    </Box>
  );
}
