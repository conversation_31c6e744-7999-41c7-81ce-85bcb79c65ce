import ChevronRightIcon from '@mui/icons-material/ChevronRight'; // Chevron icon

import EditNoteIcon from '@mui/icons-material/EditNote';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'; // Info icon
import { Box, Icon, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';

export default function LoyaltyCreateStep4({
  setStep,
  isEditing,
}: {
  setStep: (step: number) => void;
  isEditing: boolean;
}) {
  const { formState, getValues } = useFormContext();
  const hasChanges = formState.isDirty;
  const selectedUnit = getValues('units')?.values || {
    singular: 'Unit',
    plural: 'Units',
  };
  const unitsNeeded = getValues('units-needed') || '-';
  const couponAmount = getValues('coupon-amount')
    ? Number(getValues('coupon-amount')).toFixed(2)
    : '-';
  const rewardExpiration = getValues('rewardExpiration');

  return (
    <>
      <Subsection
        title="Review & Finish"
        titleSx={{
          fontSize: '32px',
          textShadow: '1px 5px 2px rgba(0, 0, 0, 0.17)',
        }}
      >
        <Box
          sx={{
            px: 1.5,
            py: 0.5,
            bgcolor: '#E6F0FF',
            color: '#005AD9',
            borderRadius: 15,
            width: 'fit-content',
          }}
        >
          Free Reward Program • Cancel Anytime
        </Box>
      </Subsection>
      <Typography
        sx={{ width: '70%', mb: 1 }}
        fontWeight={300}
        fontSize={13}
        variant="caption"
      >
        Make sure all of your loyalty program details are correct before
        starting and making your program available to your customers.
      </Typography>
      {isEditing && !hasChanges && (
        <Box
          sx={{
            px: 1.5,
            py: 1,
            fontSize: 13,
            bgcolor: '#FFF3CD', // Warning yellow
            color: '#856404', // Warning text color
            borderRadius: 15,
            width: 'fit-content',
            mb: 2,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <InfoOutlinedIcon />
          No changes detected. Please modify the details before saving.
        </Box>
      )}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <ReviewCard
          callback={() => {
            setStep(0);
          }}
          title={'Reward'}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography fontWeight={500} fontSize={13} variant="caption">
              Coupon ( {unitsNeeded} {selectedUnit.singular})
            </Typography>
            <Typography fontWeight={300} fontSize={13} variant="caption">
              {couponAmount || '-'} RON discount on entire sale.
            </Typography>
          </Box>
        </ReviewCard>
        <ReviewCard
          callback={() => {
            setStep(1);
          }}
          title={'Program type'}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography fontWeight={500} fontSize={13} variant="caption">
              Spend based
            </Typography>
            <Typography
              sx={{ color: 'gray' }}
              fontWeight={300}
              fontSize={13}
              variant="caption"
            >
              Customers earn 1 {selectedUnit?.singular} for each 1,00 RON spent.
            </Typography>
            <Typography fontWeight={300} fontSize={13} variant="caption">
              {rewardExpiration === '0' ? (
                <>{selectedUnit?.plural} do not expire.</>
              ) : rewardExpiration === '1' ? (
                <>
                  {selectedUnit?.plural} expire after {rewardExpiration} day
                </>
              ) : (
                <>
                  {selectedUnit?.plural} expire after {rewardExpiration} days
                </>
              )}
            </Typography>
          </Box>
        </ReviewCard>
        <ReviewCard
          callback={() => {
            setStep(2);
          }}
          title={'Program customization'}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography fontWeight={500} fontSize={13} variant="caption">
              Unit name
            </Typography>
            <Typography
              sx={{ color: 'gray' }}
              fontWeight={300}
              fontSize={13}
              variant="caption"
            >
              {selectedUnit?.singular} / {selectedUnit?.plural}
            </Typography>
          </Box>
        </ReviewCard>
      </Box>
    </>
  );
}

function ReviewCard({
  title,
  children,
  callback,
}: {
  title: string;
  children: any;
  callback: () => void;
}) {
  return (
    <Box
      onClick={callback}
      sx={{
        p: 3,
        cursor: 'pointer',
        py: 6,
        boxShadow: 'rgba(99, 99, 99, 0.3) 0px 2px 8px 0px',
        borderRadius: 1.5,
        position: 'relative',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'scale(1.02)',
          boxShadow: 'rgba(99, 99, 99, 0.5) 0px 4px 12px 0px',
        },
      }}
    >
      <Subsection titleSx={{ mb: 5 }} title={title} />
      {children}
      <Box
        sx={{
          position: 'absolute',
          right: 16,
          top: '50%',
          transform: 'translateY(-50%)',
          fontSize: 30,
          color: 'primary.main',
        }}
      >
        <EditNoteIcon sx={{ fontSize: 33 }} />
      </Box>
    </Box>
  );
}
