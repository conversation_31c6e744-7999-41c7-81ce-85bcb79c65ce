import { httpsCallable } from 'firebase/functions';
import { ResourceCallbacks } from 'react-admin';

import { functions } from '~/configs/firebaseConfig';
import { RESOURCES } from '../resources';

const generateDeviceCode = httpsCallable(
  functions,
  'callables-generateDeviceCode'
);

export const generateDeviceCodeBeforeDeviceCreate: ResourceCallbacks = {
  resource: RESOURCES.DEVICES,
  beforeCreate: async (params, dataProvider, resource) => {
    if (!params.meta?.memberId) {
      throw new Error('Member ID is required');
    }
    if (typeof dataProvider.getAccountId !== 'function') {
      throw new Error('Account ID is required');
    }
    const result = await generateDeviceCode({
      accountId: dataProvider.getAccountId(),
      memberId: params.meta.memberId,
      memberName: params.meta?.memberName ?? 'N/A',
    });
    const data: { value: string } = result.data as any;
    params.data.id = data.value;
    return params;
  },
};
