import { useState } from 'react';
import CheckIcon from '@mui/icons-material/Check';
import { Box, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface ColorSelectInputGroupProps {
  value?: string;
  choices: Array<string>;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export default function ColorSelectInputGroup({
  value,
  onChange,
  choices,
  disabled,
}: ColorSelectInputGroupProps) {
  const [selectedValue, setSelectedValue] = useState<string | undefined>(value);
  const { t } = useTranslation();

  return (
    <Tooltip title={disabled ? t('menu.cannotChangeColor') : ''}>
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          alignItems: 'center',
          py: 4,
          px: 4,
          margin: 'auto',
          width: '100%',
          bgcolor: 'background.tinted',
          position: 'relative',
          borderRadius: 2,
        }}
      >
        {choices.map(color => (
          <Box
            key={color}
            onClick={() => {
              if (color != selectedValue) {
                onChange(color);
                setSelectedValue(color);
              }
            }}
            sx={{
              backgroundColor: color,
              cursor: 'pointer',
              width: '40px',
              height: '40px',
              m: '5px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {selectedValue === color && <CheckIcon sx={{ color: 'white' }} />}
          </Box>
        ))}
        {disabled && (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              bgcolor: 'custom.fadedText',
              position: 'absolute',
              top: 0,
              left: 0,
            }}
          ></Box>
        )}
      </Box>
    </Tooltip>
  );
}
