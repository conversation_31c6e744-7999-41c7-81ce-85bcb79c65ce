import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  CreateButton,
  DataTable,
  List,
  NumberField,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import { useTheme } from '../../contexts';

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ textAlign: 'center' }}>
      <LocationAndDateSelectors isDate={false} hideShadow />
      <img src="/assets/transactions/cash.svg" width="45px" />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('discountsPage.noDiscountsYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('discountsPage.noDiscountsYetDescription')}
      </Typography>
      <CreateButton variant="contained" label={t('discountsPage.createDiscount')} />
    </div>
  );
};

export default function DiscountsList(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation();
  const { theme } = useTheme();

  if (!sellPointId) {
    return null;
  }

  return (
    <>
      <List
        resource={RESOURCES.DISCOUNTS}
        sort={resourcesInfo[RESOURCES.DISCOUNTS].defaultSort}
        pagination={false}
        perPage={Number.MAX_SAFE_INTEGER}
        component="div"
        exporter={false}
        actions={false}
        empty={<CustomEmpty />}
        queryOptions={{ meta: { sellPointId: sellPointId } }}
        sx={{
          '& .RaFilterFormInput-spacer': {
            display: { xs: 'none', md: 'block' },
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <LocationAndDateSelectors isDate={false} hideShadow />
          <CreateButton
            variant="contained"
            label={t('discountsPage.createDiscount')}
            {...(isXSmall ? {} : { icon: <></> })}
          />
        </Box>

        <DataTable
          rowClick={(_, __, row) => {
            redirect('edit', RESOURCES.DISCOUNTS, row.id, row, {
              _scrollToTop: false,
            });
            return false;
          }}
          bulkActionButtons={false}
          // editForm={<SectionForm />}
          // actions={<CustomActions />}
          sx={{
            marginTop: '10px',
            '& .RaDataTable-headerCell': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
            '& .MuiTableCell-root:last-of-type': {
              textAlign: 'right',
              '& button': {
                visibility: 'visible',
              },
            },
          }}
        >
          <DataTable.Col source="id" label={t('shared.name')} />

          <DataTable.Col label={t('tips.percentage')} align="right">
            <NumberField
              transform={v => v / 10000}
              source="value"
              options={{
                style: 'percent',
                maximumFractionDigits: 2,
              }}
            />
          </DataTable.Col>

          <DataTable.Col label={t('prepStations.actions')} align="right">
            <ActionsField
              hasEdit
              hasDelete
              deleteMutationMode="optimistic"
              deleteMutationOptions={{ meta: { sellPointId: sellPointId } }}
            />
          </DataTable.Col>
        </DataTable>

        <ListLiveUpdate />
      </List>
    </>
  );
}
