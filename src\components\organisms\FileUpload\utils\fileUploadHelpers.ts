import { UploadedFile } from '~/types/fileUpload';
import { FileUploadConfig } from '../types';

/**
 * Generate a unique ID for files
 */
export const generateFileId = (file: File): string => {
    return `${file.name}-${file.size}-${file.lastModified}`;
};

/**
 * Check if a file is an image
 */
export const isImageFile = (file: File): boolean => {
    return file?.type?.startsWith('image/') || false;
};

/**
 * Check if a file is a video
 */
export const isVideoFile = (file: File): boolean => {
    return file?.type?.startsWith('video/') || false;
};

/**
 * Check if a file can be previewed
 */
export const canPreviewFile = (file: File): boolean => {
    return isImageFile(file) || isVideoFile(file) || file.type === 'application/pdf';
};

/**
 * Get file extension from filename
 */
export const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

/**
 * Get filename without extension
 */
export const getFilenameWithoutExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(0, lastDot) : filename;
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Convert file type to internal code
 */
export const mapFileTypeToCode = (fileType: FileUploadConfig['fileType']): 'i' | 'v' | 's' | 'p' => {
    const mapping = {
        images: 'i' as const,
        videos: 'v' as const,
        public: 's' as const,
        private: 'p' as const,
    };

    return mapping[fileType];
};

/**
 * Check if uploaded file should have variants
 */
export const shouldHaveVariants = (file: UploadedFile): boolean => {
    return file.t === 'i'; // Only images have variants
};

/**
 * Check if file is temporary
 */
export const isTempFile = (file: UploadedFile): boolean => {
    return file.x === true;
};

/**
 * Check if file is permanent
 */
export const isPermanentFile = (file: UploadedFile): boolean => {
    return !file.x;
};

/**
 * Get MIME type from file extension
 */
export const getMimeTypeFromExtension = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
        // Images
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        webp: 'image/webp',
        bmp: 'image/bmp',
        svg: 'image/svg+xml',

        // Videos
        mp4: 'video/mp4',
        webm: 'video/webm',
        mov: 'video/quicktime',
        avi: 'video/x-msvideo',
        mkv: 'video/x-matroska',

        // Documents
        pdf: 'application/pdf',
        doc: 'application/msword',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xls: 'application/vnd.ms-excel',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ppt: 'application/vnd.ms-powerpoint',
        pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        txt: 'text/plain',
        csv: 'text/csv',

        // Archives
        zip: 'application/zip',
        rar: 'application/x-rar-compressed',
        '7z': 'application/x-7z-compressed',
        tar: 'application/x-tar',
        gz: 'application/gzip',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * Check if file type is accepted
 */
export const isFileTypeAccepted = (file: File, acceptedTypes: string[]): boolean => {
    if (!file?.type) return false;
    return acceptedTypes.includes(file.type) ||
        acceptedTypes.some(type => type.endsWith('/*') && file.type.startsWith(type.slice(0, -1)));
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;

    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

/**
 * Create a promise that resolves after a delay
 */
export const delay = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Check if two files are the same
 */
export const areFilesEqual = (file1: File, file2: File): boolean => {
    return file1.name === file2.name &&
        file1.size === file2.size &&
        file1.lastModified === file2.lastModified &&
        file1.type === file2.type;
};

/**
 * Remove duplicate files from array
 */
export const removeDuplicateFiles = (files: File[]): File[] => {
    const seen = new Set<string>();
    return files.filter(file => {
        const id = generateFileId(file);
        if (seen.has(id)) {
            return false;
        }
        seen.add(id);
        return true;
    });
};

/**
 * Sort files by name, size, or date
 */
export const sortFiles = (files: File[], sortBy: 'name' | 'size' | 'date' = 'name'): File[] => {
    return [...files].sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'size':
                return b.size - a.size; // Largest first
            case 'date':
                return b.lastModified - a.lastModified; // Newest first
            default:
                return 0;
        }
    });
};

/**
 * Get file icon based on file type
 */
export const getFileIcon = (file: File): string => {
    if (isImageFile(file)) return '🖼️';
    if (isVideoFile(file)) return '🎥';
    if (!file?.type) return '📎';
    if (file.type === 'application/pdf') return '📄';
    if (file.type.includes('word')) return '📝';
    if (file.type.includes('excel') || file.type.includes('spreadsheet')) return '📊';
    if (file.type.includes('powerpoint') || file.type.includes('presentation')) return '📽️';
    if (file.type.startsWith('text/')) return '📄';
    if (file.type.includes('zip') || file.type.includes('archive')) return '🗜️';
    return '📎';
};

/**
 * Validate uploaded file structure
 */
export const isValidUploadedFile = (file: any): file is UploadedFile => {
    return file &&
        typeof file === 'object' &&
        typeof file.fn === 'string' &&
        typeof file.e === 'string' &&
        typeof file.t === 'string' &&
        ['i', 'v', 's', 'p'].includes(file.t) &&
        (file.rn === undefined || typeof file.rn === 'string') &&
        (file.x === undefined || typeof file.x === 'boolean') &&
        (file.url === undefined || typeof file.url === 'string');
};
