import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface ImagePickerProps {
  image?: string;
  setImage: (img: string) => void;
}

export default function ImagePicker({ image, setImage }: ImagePickerProps) {
  const { t } = useTranslation();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files[0]) {
      convertToBase64(files[0]);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files && files[0]) {
      convertToBase64(files[0]);
    }
  };

  const convertToBase64 = (file: File) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      setImage(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveFile = () => {
    setImage('');
  };

  return (
    <Box
      onDrop={handleDrop}
      onDragOver={event => event.preventDefault()}
      sx={{
        width: '100%',
        height: '60px',
        borderRadius: '6px',
        borderColor: 'custom.gray400',
        borderStyle: 'dashed',
        position: 'relative',
      }}
    >
      {!!image ? (
        <Box
          sx={{
            ...centerStyle,
            width: '100%',
            height: '100%',
            ':hover': {
              '> button': {
                opacity: '1 !important',
              },
            },
          }}
        >
          <img
            alt="preview image"
            src={image}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
            }}
          />
          <Button
            // @ts-ignore
            variant="contained-light"
            color="error"
            onClick={handleRemoveFile}
            sx={{ position: 'absolute', top: '6px', right: '6px', opacity: 0 }}
          >
            <DeleteOutlineIcon fontSize="small" />
          </Button>
        </Box>
      ) : (
        <>
          <input
            id="browse"
            type="file"
            hidden
            onChange={handleFileChange}
            accept="image/*"
          />
          <label
            htmlFor="browse"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '60px',
              cursor: 'pointer',
              padding: '10px',
              ...centerStyle,
            }}
          >
            <img src="/assets/image.svg" alt="image-icon" />
            {/* @ts-ignore */}
            <Typography variant="label" fontWeight={300} textAlign="center">
              {t('menu.dragAndDrop')} {/* @ts-ignore */}
              <Typography variant="label" color="primary">
                {t('menu.orBrowseImageLibrary')}
              </Typography>
            </Typography>
          </label>
        </>
      )}
    </Box>
  );
}

const centerStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};
