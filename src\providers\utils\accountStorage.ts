import { LOCAL_STORAGE_SELECTED_ACCOUNT_KEY } from './constants';

export const accountStorage = {
  getSelectedAccount(): string | null {
    return localStorage.getItem(LOCAL_STORAGE_SELECTED_ACCOUNT_KEY);
  },

  // The storage event of the Window interface fires when another document that shares the same storage area (either localStorage or sessionStorage)
  // as the current window updates that storage area. The event is not fired on the window that made the change.
  // so in our case when we update the storage we do not need to dispatch the event in the same window because we update the state also.
  setSelectedAccount(
    accountId: string | null,
    skipDispatch: boolean = true
  ): void {
    if (accountId === null) {
      this.clearSelectedAccount(skipDispatch);
      return;
    } else {
      localStorage.setItem(LOCAL_STORAGE_SELECTED_ACCOUNT_KEY, accountId);
    }
    if (skipDispatch) return;
    // Dispatch storage event for cross-tab communication
    window.dispatchEvent(
      new StorageEvent('storage', {
        key: LOCAL_STORAGE_SELECTED_ACCOUNT_KEY,
        newValue: accountId,
      })
    );
  },

  clearSelectedAccount(skipDispatch: boolean = true): void {
    localStorage.removeItem(LOCAL_STORAGE_SELECTED_ACCOUNT_KEY);
    if (skipDispatch) return;
    // Dispatch storage event for cross-tab communication
    window.dispatchEvent(
      new StorageEvent('storage', {
        key: LOCAL_STORAGE_SELECTED_ACCOUNT_KEY,
        newValue: null,
      })
    );
  },
};
