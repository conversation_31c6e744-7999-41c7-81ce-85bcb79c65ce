import { defaultTheme } from 'react-admin';

const gray600 = '#989898';
const modalHeader = '#222227'

export const darkTheme = {
  ...defaultTheme,
  palette: {
    mode: 'dark',
    background: {
      default: '#13131a',
      paper: '#0c0c12',
      tinted: '#ffffff14',
    },
    primary: {
      veryLight: '#0069ff0f',
      light: '#749dff61',
      main: '#3b8af9',
      dark: '#4C82FF',
    },
    secondary: {
      main: '#fff',
    },
    error: {
      light: '#84262654',
      main: '#DC4437',
      dark: '#CF6679',
    },
    warning: {
      light: '#965F0E5c',
      main: '#965F0E',
      dark: '#965F0E',
    },
    success: {
      light: '#295b405c',
      main: '#007d2a',
      dark: '#1b5e20',
    },
    custom: {
      gray200: '#f2f2f25c',
      gray400: '#272727',
      gray600: '#989898',
      gray800: '#6D6D6D',
      fieldBg: 'transparent',
      fadedText: '#ffffff57',
      text: '#e6e6eb',
      draggableTable: '#757575',
      modalHeader: '#222227',
      warningText: '#F4B400',
    },
  },
  typography: {
    fontFamily: ['Geologica', 'Roboto'].join(','),
    allVariants: {
      color: '#f2f2f2',
    },
    h1: {
      fontSize: '34px',
      fontWeight: '500',
      '@media (max-width:600px)': {
        fontSize: '28px',
      },
    },
    h2: {
      fontSize: '24px',
      fontWeight: '700',
      '@media (max-width:600px)': {
        fontSize: '18px',
      },
    },
    h3: {
      fontSize: '20px',
      fontWeight: '700',
      '@media (max-width:600px)': {
        fontSize: '16px',
      },
    },
    h4: {
      fontSize: '18px',
      fontWeight: '700',

      '@media (max-width:600px)': {
        fontSize: '16px',
      },
    },
    h5: {
      fontSize: '16px',
      fontWeight: '600',
      '@media (max-width:600px)': {
        fontSize: '15px',
      },
    },
    h6: {
      fontSize: '16px',
      fontWeight: '600',
      '@media (max-width:600px)': {
        fontSize: '15px',
      },
    },
    label: {
      fontFamily: ['Geologica', 'Roboto'].join(','),
      fontSize: '14px',
      fontWeight: '600',
    },
    subtitle2: {
      fontWeight: 200,
    },
  },
  components: {
    ...defaultTheme.components,
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: '40px',
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            padding: '8px',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          padding: '12px 20px',
          fontWeight: '500',
          boxShadow: 'none',
          transition: 'none',
          borderRadius: '6px',
          textTransform: 'none',
          fontSize: '15px',
          lineHeight: '1.5',
          color: '#f2f2f2',
          '&:hover': {
            boxShadow: 'none',
          },
          '@media (max-width:600px)': {
            padding: '8px 15px',
            fontSize: '14px',
          },
        },
      },
      variants: [
        {
          props: { variant: 'outlined-2' },
          style: {
            backgroundColor: 'background.paper',
            border: '1px solid gray',
            '&:hover': {
              opacity: 0.7,
            },
          },
        },
        {
          props: { variant: 'contained-light' },
          style: {
            padding: '12px',
            minWidth: 'auto',
            '@media (max-width:600px)': {
              padding: '8px',
            },
            background: '#ffffff14',
            ':hover': {
              background: '#ffffff2e',
            },
          },
        },
        {
          props: { variant: 'close-btn' },
          style: {
            background: '#2f2f2f',
            color: 'white',
            padding: '12px',
            minWidth: 'auto',
            '&:hover': {
              background: '#383838',
            },
            '@media (max-width:600px)': {
              padding: '8px',
            },
          },
        },
        {
          props: { variant: 'transparent' },
          style: {
            background: 'rgba(0,0,0,.0)',
            color: 'black',
            padding: '8px 12px',
            height: '40px',
            minWidth: 'auto',
            border: 'solid 1px #ffffff3b',
            '&:hover': {
              background: 'rgba(255,255,255,.1)',
            },
            '@media (max-width:600px)': {
              padding: '8px',
            },
          },
        },
      ],
    },
    MuiButtonBase: {
      defaultProps: {
        disableRipple: true,
      },
    },
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          width: '100%',
          '& .MuiFormHelperText-root': {
            display: 'none',
          },
          '& .MuiInputBase-root': {
            height: 45,
          },
          ' fieldset': {
            borderRadius: '6px',
          },
          '& .MuiInputBase-input, .MuiInputLabel-root': {
            fontSize: '14px',
            top: '3px',
          },
          '&.custom-outlined': {
            margin: 0,
            '& .MuiInputBase-input + input': {
              marginLeft: '200px',
            },
            '& .MuiInputBase-input + fieldset': {
              borderRadius: 0,
              border: 'none',
            },
          },
        },
      },
    },
    RaBulkActionsToolbar: {
      styleOverrides: {
        root: {
          '& .RaBulkActionsToolbar-toolbar': {
            backgroundColor: modalHeader,
          },
          '& .RaBulkActionsToolbar-icon:hover': {
            backgroundColor: modalHeader,
          },
        },
      },
    },
    RaBulkDeleteWithConfirmButton: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: 'transparent',
          },
        },
      },
    },
    RaDatagrid: {
      styleOverrides: {
        root: {
          '& .RaDatagrid-tableWrapper': {
            marginTop: '20px',
          },
          '& .RaDatagrid-row:hover': {},
          '& .RaDatagrid-headerCell': {
            background: 'transparent',
          },
          '& .RaDatagrid-thead, .RaDatagrid-row': {
            height: '48px',
          },
        },
      },
    },
  },
  sidebar: {
    width: 300,
    closedWidth: 0,
  },
};
