import { useEffect, useReducer } from 'react';
import PrintIcon from '@mui/icons-material/Print';
import { Box, Divider, IconButton, Theme, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

import DateRangePickerCustom from '~/components/molecules/DateRangePickerCustom';
import { useTheme } from '~/contexts/ThemeContext';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { ExportMenuButton } from '~/pages/item-library/ExportMenuButton';
import DatePickerFilterBtn from './DatePickerFilterBtn';
import DatePickerFilterBtnOutlined from './DatePickerFilterBtnOutlined';
import FilterItem, { OptionType } from './FilterItem';
import LocationPickerBtn from './LocationPickerBtn';
import {
  ReportFiltersActionType,
  reportFiltersReducer,
} from './reportFiltersReducer';
import TimeRangePickerBtn from './TimeRangePickerBtn';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

export enum DiningOption {
  ALL = 'all',
  HERE = 'here',
  TOGO = 'togo',
}

export enum SourceOption {
  ALL = 'all',
  DIRECT_POS = '@pos',
  ARIVA = 'ariva',
  GLOVO = 'glovo',
  BOLT = 'bolt',
}

export enum ServiceOption {
  'forhere' = 'For Here',
  'togo' = 'To Go',
  'here' = 'Here',
}

export interface ReportFiltersState {
  dateRange: DateRange<Dayjs>;
  sellpointId: string;
  timeRange: {
    allDay?: boolean;
    start?: Dayjs | null;
    end?: Dayjs | null;
  };
  member?: string;
  floor?: string;
  diningOption: DiningOption;
  source: SourceOption;
}

interface ReportFiltersProps {
  defaultValues: ReportFiltersState;
  onFiltersChange: (filters: ReportFiltersState) => void;
  contentRef?: any;
  handleExport?: () => void;
  printOnly?: boolean;
  globalFiltersVariant?: string;
  commonFields: {
    [key: string]: OptionType[];
  };
}

export default function ReportFilters({
  handleExport,
  defaultValues,
  onFiltersChange,
  contentRef,
  commonFields,
  globalFiltersVariant,
  printOnly,
}: ReportFiltersProps) {
  const { sellPointId, setSellPointId, setDateRange, setTimeRange } =
    useGlobalResourceFilters();
  const { t } = useTranslation();

  const [filters, dispatch] = useReducer(reportFiltersReducer, defaultValues);

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  useEffect(() => {
    onFiltersChange(filters);
  }, [filters]);

  const handleSellPointChange = (newSellPointId: string) => {
    setSellPointId(newSellPointId);
    dispatch({
      type: ReportFiltersActionType.SET_SELLPOINT_ID,
      payload: newSellPointId,
    });
  };

  const handleDateRangeChange = (newDateRange: DateRange<Dayjs>) => {
    setDateRange(newDateRange);
    dispatch({
      type: ReportFiltersActionType.SET_DATE_RANGE,
      payload: newDateRange,
    });
  };

  const handleTimeRangeChange = (newTimeRange: DateRange<Dayjs> | null) => {
    setTimeRange(newTimeRange);
    dispatch({
      type: ReportFiltersActionType.SET_TIME_RANGE,
      payload: {
        allDay: !newTimeRange,
        start: newTimeRange?.[0],
        end: newTimeRange?.[1],
      },
    });
  };
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        position: { xs: 'sticky', md: 'static' },
        top: '5px',
        zIndex: 1000,
        bgcolor: theme.palette.mode === 'dark' ? '#13131A' : 'white',
      }}
    >
      <Box
        py={3}
        display="flex"
        justifyContent="space-between"
        flexDirection={{ xs: 'column', md: 'row-reverse' }}
        alignItems="center"
        flexWrap="nowrap"
        rowGap={{ xs: 1, md: 0 }}
      >
        <ExportMenuButton contentRef={contentRef} handleExport={handleExport} printOnly={printOnly} />
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            width: '100%',
          }}
        >
          <LocationPickerBtn
            globalFiltersVariant={
              isXSmall ? 'outlined-2' : globalFiltersVariant
            }
            sellpointId={sellPointId}
            setSellpointId={handleSellPointChange}
          />
          <DateRangePickerCustom
            dateRange={filters.dateRange}
            setDateRange={handleDateRangeChange}
            ButtonComponent={
              globalFiltersVariant === 'outlined-2' || isXSmall
                ? DatePickerFilterBtnOutlined
                : DatePickerFilterBtn
            }
          />
          {!isXSmall && (
            <>
              <TimeRangePickerBtn
                defaultValues={filters.timeRange}
                setTimeRange={handleTimeRangeChange}
              />
              <FilterItem
                defaultValue="all"
                options={[
                  { label: t('reportFilters.allMembers'), value: 'all' },
                  ...(commonFields?.member || ''),
                ]}
                setSelectedOption={value => {
                  dispatch({
                    type: ReportFiltersActionType.SET_MEMBER,
                    payload: value,
                  });
                }}
              />
              <FilterItem
                defaultValue="all"
                options={[
                  { label: t('reportFilters.allFloors'), value: 'all' },
                  ...(commonFields?.floor || ''),
                ]}
                setSelectedOption={value => {
                  dispatch({
                    type: ReportFiltersActionType.SET_FLOOR,
                    payload: value,
                  });
                }}
              />
              <FilterItem<DiningOption>
                defaultValue={DiningOption.ALL}
                options={[
                  {
                    label: t('reportFilters.allServiceTypes'),
                    value: DiningOption.ALL,
                  },
                  ...(commonFields?.serviceType?.map(item => {
                    return {
                      label: t(
                        'reportFilters.' +
                          ServiceOption[
                            item.value as keyof typeof ServiceOption
                          ]
                      ),
                      value: item.value,
                    };
                  }) || ''),
                ]}
                setSelectedOption={value => {
                  dispatch({
                    type: ReportFiltersActionType.SET_DINING_OPTION,
                    payload: value,
                  });
                }}
              />
              <FilterItem<SourceOption>
                defaultValue={SourceOption.ALL}
                options={[
                  {
                    label: t('reportFilters.allSources'),
                    value: SourceOption.ALL,
                  },
                  ...(commonFields?.sources || ''),
                ]}
                setSelectedOption={value => {
                  dispatch({
                    type: ReportFiltersActionType.SET_SOURCE,
                    payload: value,
                  });
                }}
              />
            </>
          )}
        </Box>
      </Box>
      <Divider />
    </Box>
  );
}
