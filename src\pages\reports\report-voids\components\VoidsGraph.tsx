import { useCallback, useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import {
  useGetListHospitalityCategoriesLive,
  useGetListHospitalityItemsLive,
} from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import { formatNumber } from '~/utils/formatNumber';
import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function VoidsGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency: 'RON' | 'USD';
}) {
  // TODO! change to useGetListLive after imeplementation
  const { data: modifiersLibrary } = useGetList('modifiers');
  const { data: itemsLibrary } = useGetListHospitalityItemsLive();
  const { data: categories } = useGetListHospitalityCategoriesLive();
  const [graphData, setGraphData] = useState<
    { label: string; data: number[] }[]
  >([]);

  const fetchVoidsGraphData = useCallback(() => {
    if (!data.datasets) return;
    const controller = new AbortController();

    try {
      const resolvedData = data.datasets.map((item: any) => {
        let response;
        try {
          if (item.type === 'item') {
            response = itemsLibrary!.find(
              itemLibrary => itemLibrary.id === item.label
            );
          } else if (item.type === 'modifier') {
            response = modifiersLibrary!.find(
              modifierLibrary => modifierLibrary.id === item.label
            );
          } else {
            response = categories!.find(category => category.id === item.label);
          }
          if (!response) {
            throw new Error();
          }
        } catch (error) {
          console.warn(
            `Item ${item.label} not found in ${item.type}, retrying in modifiers...`
          );

          if (item.type === 'item') {
            try {
              response = modifiersLibrary!.find(
                modifierLibrary => modifierLibrary.id === item.id
              );
              if (!response) throw new Error();
            } catch (secondError) {
              console.error(
                `Failed to fetch record with id: ${item.label} in both item-library and modifiers`,
                secondError
              );
              return { ...item, label: item.label };
            }
          } else {
            console.error(
              `Failed to fetch record with id: ${item.label}`,
              error
            );
            return { ...item, label: item.label };
          }
        }

        return {
          ...item,
          label: capitalize(response?.name.toLowerCase()),
        };
      });
      setGraphData(resolvedData);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching voids graph data:', error);
      }
    }

    return () => controller.abort();
  }, [data.datasets, modifiersLibrary, itemsLibrary, categories]);

  useEffect(() => {
    fetchVoidsGraphData();
  }, [fetchVoidsGraphData]);

  const formatData = (value: string | number) => formatNumber(value, currency);

  const hasValidData = !!data.datasets && !!data.labels;
  const { t } = useTranslation();

  return (
    <div>
      {hasValidData && graphData.length > 0 && (
        <>
          <Typography
            sx={{
              display: { xs: 'none', md: 'block' },
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
            variant="body2"
            fontWeight="500"
            mb={1.5}
          >
            {graphData.length >= 3 &&
              `Top ${graphData.length} ${t('voids.topVoids')}`}
          </Typography>
          <ReportMultiLineChart
            datasets={graphData}
            labels={data.labels!}
            formatData={formatData}
          />
        </>
      )}
    </div>
  );
}
