import { groupBy } from 'lodash';

import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';

const formatters: Record<string, (value: any, record?: any) => string> = {
  price: value => `${formatAndDivideNumber(Number(value))}`,
  itemsQty: value => formatNumberIntl(value, true),
  modifiersQty: value => formatNumberIntl(value, true),
  giftCardsQty: value => formatNumberIntl(value, true),
  vat: value => `${value}%`,
  reason: value => (value === '@na' ? 'N/A' : value),
  groupId: (value, record) =>
    value === '@none'
      ? 'Regular'
      : record && record.groupName
        ? record.groupName
        : value,
};

const labelFormatters: Record<string, string> = {
  groupId: 'Category',
  Vat: 'VAT',
};

const getFormattedFieldLabel = (field: string) => {
  return labelFormatters[field] || capitalize(camelCaseToNormalWords(field));
};

const formatValue = (field: string, value: any, record?: any): string => {
  return formatters[field] ? formatters[field](value, record) : String(value);
};

enum parentFields {
  vat = 'VAT',
  groupId = 'Category',
}

const formatParentValue = (item: string) => {
  const formatedParent =
    parentFields[item as keyof typeof parentFields] || item;
  return formatedParent;
};

const remapReports = (
  reports: any[],
  firstColumnId: string,
  parentFields: string[] = []
) => {
  return reports.map((report: any) => {
    let transformedReport = { ...report };
    let currentParentFields = [...parentFields];

    if (report.groupedBy) {
      const formattedValue = formatValue(
        report.groupedBy.field,
        report.groupedBy.value,
        report
      );
      transformedReport[firstColumnId] =
        `${getFormattedFieldLabel(report.groupedBy.field)}: ${formattedValue}`;

      currentParentFields.push(formatParentValue(report.groupedBy.field));
    }

    transformedReport.parentFields =
      currentParentFields.length > 0 ? currentParentFields : undefined;

    if (report.subReport) {
      transformedReport.subItems = remapReports(
        report.subReport,
        firstColumnId,
        currentParentFields
      );
      delete transformedReport.subReport;
    }

    return transformedReport;
  });
};

export default remapReports;
