import { PropsWithChildren, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import ErrorOutlinedIcon from '@mui/icons-material/ErrorOutlined';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import { Box } from '@mui/material';

interface SlimBannerProps extends PropsWithChildren {
  type: 'warning' | 'error' | 'success';
  permanent?: boolean;
}

export default function SlimBanner({
  type,
  permanent,
  children,
}: SlimBannerProps) {
  const [show, setShow] = useState<boolean>(true);

  if (!show) return <></>;

  return (
    <Box
      sx={{
        borderRadius: '6px',
        bgcolor: `${type}.light`,
        px: 2,
        py: 1,
        my: 1,
      }}
    >
      <Box>
        {!permanent && (
          <Box
            onClick={() => setShow(false)}
            sx={{ float: 'right', cursor: 'pointer' }}
          >
            <CloseIcon color="disabled" />
          </Box>
        )}
        <Box sx={{ float: 'left', mr: 1 }}>
          {type === 'warning' && <WarningRoundedIcon />}
          {type === 'error' && <ErrorOutlinedIcon />}
          {type === 'success' && <FiberManualRecordIcon color="success" />}
        </Box>
      </Box>
      <Box display="flex">{children}</Box>
    </Box>
  );
}
