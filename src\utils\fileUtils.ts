import { UploadedFile } from '~/types/fileUpload';

// ==========================================
// FILE VALIDATION UTILITIES
// ==========================================

/**
 * Validates if an object is a valid UploadedFile according to new format
 */
export const isValidUploadedFile = (file: any): file is UploadedFile => {
    return (
        file &&
        typeof file.rn === 'string' &&
        typeof file.fn === 'string' &&
        typeof file.e === 'string' &&
        ['i', 'v', 's', 'p'].includes(file.t)
    );
};

/**
 * Filters array of files, keeping only valid ones and logging warnings for invalid ones
 */
export const filterValidFiles = (files: any[]): UploadedFile[] => {
    const validFiles: UploadedFile[] = [];

    files.forEach((file, index) => {
        if (isValidUploadedFile(file)) {
            validFiles.push(file);
        } else {
            // Invalid file format detected
        }
    });

    return validFiles;
};

/**
 * Maps fileType prop to internal type code
 */
export const mapFileTypeToCode = (
    fileType: 'images' | 'videos' | 'public' | 'private'
): 'i' | 'v' | 's' | 'p' => {
    const mapping = {
        images: 'i' as const,
        videos: 'v' as const,
        public: 's' as const,
        private: 'p' as const,
    };

    return mapping[fileType];
};

/**
 * Extracts file extension from filename (without dot)
 */
export const extractFileExtension = (filename: string): string => {
    if (!filename || typeof filename !== 'string') {
        return '';
    }
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
};

/**
 * Removes extension from filename
 */
export const removeFileExtension = (filename: string): string => {
    if (!filename || typeof filename !== 'string') {
        return '';
    }
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
};

/**
 * Converts bytes to KB (rounded)
 */
export const bytesToKB = (bytes: number): number => {
    return Math.round(bytes / 1024);
};

/**
 * Converts KB to bytes
 */
export const kbToBytes = (kb: number): number => {
    return kb * 1024;
};

// ==========================================
// FILE PREVIEW UTILITIES
// ==========================================

/**
 * Check if a file is an image based on its extension
 */
export const isImageFile = (file: UploadedFile): boolean => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
    return imageExtensions.includes(file.e.toLowerCase());
};

/**
 * Check if a file is a video based on its extension
 */
export const isVideoFile = (file: UploadedFile): boolean => {
    const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'm4v'];
    return videoExtensions.includes(file.e.toLowerCase());
};

/**
 * Check if a file can be previewed (is an image or video)
 * Note: URL availability is now handled by the useFileUrl hook
 */
export const canPreviewFile = (file: UploadedFile): boolean => {
    return isImageFile(file) || isVideoFile(file);
};

// ==========================================
// FILE DISPLAY UTILITIES
// ==========================================

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Legacy function - filters files and ensures they are valid
 * Used for backward compatibility during transition
 */
export const ensureFilesFlags = (files: any[]): UploadedFile[] => {
    return filterValidFiles(files);
};
