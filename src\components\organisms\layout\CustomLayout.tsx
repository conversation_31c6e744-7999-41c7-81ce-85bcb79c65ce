import { useState } from 'react';
import { CssBaseline } from '@mui/material';
import { AppLocationContext } from '@react-admin/ra-navigation';
import { CheckForApplicationUpdate, Layout, LayoutProps } from 'react-admin';

import useEnv from '~/hooks/useEnv';
import ContainerWithCustomMenu from '../../molecules/ContainerWithCustomMenu';
import CustomAppBar from './CustomAppBar';
import { CustomNotificationDialog } from './CustomNotificationDialog';
import CustomSidebar from './CustomSidebar';

const CHECK_INTERVAL: number = 10 * 60 * 1000;

export default function CustomLayout(props: LayoutProps) {
  const { NODE_ENV } = useEnv();
  const { children } = props;

  const [isOpen, setIsOpen] = useState(false);

  return (
    <AppLocationContext>
      <Layout {...props} appBar={CustomAppBar} sidebar={CustomSidebar}>
        <CssBaseline />
        <ContainerWithCustomMenu>{children}</ContainerWithCustomMenu>
        <CheckForApplicationUpdate
          interval={CHECK_INTERVAL}
          disabled={NODE_ENV === 'dev'}
          fetchOptions={{ cache: 'no-cache' }}
          onNewVersionAvailable={() => setIsOpen(true)}
        />
        <CustomNotificationDialog isOpen={isOpen} />
      </Layout>
    </AppLocationContext>
  );
}
