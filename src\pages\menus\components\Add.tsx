import { useState } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Button, Fade, Menu, MenuItem } from '@mui/material';

export default function Add() {
  const [dropdownAnchor, setDropdownAnchor] = useState<null | HTMLElement>(
    null
  );
  const open = <PERSON><PERSON><PERSON>(dropdownAnchor);

  return (
    <>
      <Button
        variant="contained"
        onClick={e => setDropdownAnchor(e.currentTarget)}
        endIcon={
          <ArrowDropDownIcon
            sx={{
              transition: 'transform 0.2s ease',
              transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
          />
        }
      >
        Add
      </Button>

      <Menu
        anchorEl={dropdownAnchor}
        open={open}
        onClose={() => setDropdownAnchor(null)}
        TransitionComponent={Fade}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        <MenuItem onClick={() => setDropdownAnchor(null)}>Items</MenuItem>
        <MenuItem onClick={() => setDropdownAnchor(null)}>Menu Group</MenuItem>
        <MenuItem onClick={() => setDropdownAnchor(null)}>
          Combo Product
        </MenuItem>
      </Menu>
    </>
  );
}
