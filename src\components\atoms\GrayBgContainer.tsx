import { Box, BoxProps } from '@mui/material';

import { useTheme } from '../../contexts';

export default function GrayBgContainer({ children, sx, ...props }: BoxProps) {
  const { theme } = useTheme();
  return (
    <Box
      sx={{
        ...{
          p: 2.5,
          bgcolor:
            theme.palette.mode == 'light' ? 'background.tinted' : 'transparent',
          borderRadius: '3px',
        },
        ...sx,
      }}
      {...props}
    >
      {children}
    </Box>
  );
}
