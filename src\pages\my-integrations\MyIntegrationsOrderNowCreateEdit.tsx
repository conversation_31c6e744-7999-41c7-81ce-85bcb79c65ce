import { useC<PERSON>back, useEffect, useMemo, useRef, useState } from 'react';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardContent,
  Dialog,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Select,
  Switch,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Theme } from '@mui/material/styles';
import {
  SaveButton,
  useNotify,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import MuiFileUploadInput from '~/components/atoms/inputs/MuiFileUploadInput';
import { ValueFieldWithError } from '~/components/atoms/ValueFieldWithError';
import { LocationInput } from '~/components/molecules/LocationInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { RangeInput } from '~/components/molecules/RangeInput';
import CloseWithConfirmationModal from '~/components/organisms/CloseWithConfirmationModal';
import { GoogleMapsModal } from '~/components/organisms/GoogleMapsModal';
import { standardSizesToTargetSizes } from '~/configs/imageSize';
import { useTheme } from '~/contexts';
import {
  useGetListDevicesLive,
  useGetListHospitalityCatalogsLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import { UploadedFile } from '~/types/fileUpload';
import { getPublicImageEditorConfig } from '~/utils/imageEditorUtils';

// ============================================================================
// TYPES
// ============================================================================

interface TimeSlot {
  startAt: string; // "HH:MM" format
  endAt: string; // "HH:MM" format
  catalogId: string; // The catalog assigned to this time slot
}

interface DaySchedule {
  timeSlots: TimeSlot[];
}

interface LocationSchedule {
  schedule: DaySchedule[]; // 7 arrays (Sun-Sat), each containing time slots
  active: boolean; // Whether this location is active/enabled
  orderProcessingDevice?: string; // Device ID for order processing (required when active)
  location: {
    lat: number; // Latitude coordinate (required when active)
    lng: number; // Longitude coordinate (required when active)
  };
  orderingRange: number; // Ordering range in meters (required when active)
  // Media fields
  logo?: UploadedFile[]; // Logo file (single image, max 1MB)
  intro?: UploadedFile[]; // Intro files (up to 3 files: images max 1MB, videos max 25MB)
}

interface OrderNowFormValues {
  sellPoints: {
    [locationId: string]: LocationSchedule;
  };
}

interface PartnerExtraData {
  name?: string;
  [key: string]: any;
}

interface MyIntegrationsOrderNowCreateEditProps {
  setOpen?: (open: boolean) => void;
  partnerExtraData?: PartnerExtraData;
  mode?: 'create' | 'edit';
}

interface BusinessHours {
  businessHoursSchedule: {
    startAt: string;
    endAt: string;
  };
  businessHoursExceptions?: {
    [dayIndex: string]: {
      startAt: string;
      endAt: string;
    };
  };
}

interface Catalog {
  id: string;
  name: string;
  [key: string]: any;
}

interface Location {
  id: string;
  name: string;
  businessHoursSchedule?: {
    startAt: string;
    endAt: string;
  };
  businessHoursExceptions?: {
    [dayIndex: string]: {
      startAt: string;
      endAt: string;
    };
  };
  [key: string]: any;
}

interface ColorPalette {
  bg: string;
  border: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

const DAYS_OF_WEEK = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
] as const;

const DAYS_OF_WEEK_SHORT = [
  'Sun',
  'Mon',
  'Tue',
  'Wed',
  'Thu',
  'Fri',
  'Sat',
] as const;

const DAYS_OF_WEEK_FIRST_LETTER_EN = [
  'S',
  'M',
  'T',
  'W',
  'T',
  'F',
  'S',
] as const;

const DAYS_OF_WEEK_FIRST_LETTER_RO = [
  'D',
  'L',
  'M',
  'M',
  'J',
  'V',
  'S',
] as const; // Romanian: Duminică, Luni, Marți, Miercuri, Joi, Vineri, Sâmbătă

const DEFAULT_SCHEDULE: DaySchedule[] = Array(7)
  .fill(null)
  .map(() => ({ timeSlots: [] }));

// Default location and delivery range constants
const DEFAULT_LOCATION = { lat: 0, lng: 0 };
const DEFAULT_ORDERING_RANGE = 100; // meters

const HOURS_PER_DAY = 24;
const MINUTES_PER_HOUR = 60;
const MINUTES_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR;

// Time constants
const TIME_FORMAT = {
  FULL_DAY_START: '00:00',
  FULL_DAY_END: '23:59',
  MIDNIGHT: '00:00',
  DAY_END: '23:59',
} as const;

const COLOR_PALETTE: ColorPalette[] = [
  { bg: '#e3f2fd', border: '#1565c0' }, // Light Blue
  { bg: '#e8f5e9', border: '#2e7d32' }, // Light Green
  { bg: '#fff3e0', border: '#ef6c00' }, // Light Orange
  { bg: '#f3e5f5', border: '#7b1fa2' }, // Light Purple
  { bg: '#e0f7fa', border: '#00838f' }, // Light Cyan
  { bg: '#f1f8e9', border: '#558b2f' }, // Light Lime
  { bg: '#e8eaf6', border: '#5e35b1' }, // Light Indigo
  { bg: '#fff8e1', border: '#f57f17' }, // Light Amber
  { bg: '#e0f2f1', border: '#00695c' }, // Light Teal
  { bg: '#fff9c4', border: '#f9a825' }, // Light Yellow Green
  { bg: '#d1c4e9', border: '#673ab7' }, // Light Deep Purple
  { bg: '#c8e6c9', border: '#388e3c' }, // Light Light Green Variant
  { bg: '#ffe0b2', border: '#fb8c00' }, // Light Deep Orange Variant
  { bg: '#dcedc8', border: '#689f38' }, // Light Light Green
  { bg: '#b39ddb', border: '#512da8' }, // Light Deep Purple Variant
  { bg: '#e1f5fe', border: '#0288d1' }, // Light Light Blue
  { bg: '#f9fbe7', border: '#827717' }, // Light Lime Green
  { bg: '#fef7e0', border: '#f57c00' }, // Light Yellow Orange
  { bg: '#ede7f6', border: '#6a1b9a' }, // Light Purple Variant
  { bg: '#e8f4fd', border: '#1976d2' }, // Light Blue Variant
];

// ============================================================================
// UTILITY FUNCTIONS - TIME HANDLING
// ============================================================================

/**
 * Time utility functions
 */
const timeUtils = {
  /**
   * Converts time string to minutes since midnight
   */
  parseTime: (timeStr: string): number => {
    if (!timeStr) return 0;
    const [hours, minutes = 0] = timeStr.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return 0;
    return hours * MINUTES_PER_HOUR + minutes;
  },

  /**
   * Converts minutes to time string in "HH:MM" format
   */
  formatTime: (minutes: number): string => {
    const hours = Math.floor(minutes / MINUTES_PER_HOUR);
    const mins = minutes % MINUTES_PER_HOUR;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  },

  /**
   * Checks if a time period spans overnight
   */
  isOvernightPeriod: (startTime: string, endTime: string): boolean => {
    const startMinutes = timeUtils.parseTime(startTime);
    const endMinutes = timeUtils.parseTime(endTime);
    return endMinutes <= startMinutes;
  },

  /**
   * Convert hour to time format (e.g., "07" -> "07:00")
   */
  hourToTime: (hour: string): string => `${hour}:00`,

  /**
   * Convert hour to end time format (e.g., "17" -> "16:59")
   */
  hourToEndTime: (hour: string): string => {
    const hourNum = parseInt(hour, 10);
    let endHour = hourNum - 1;
    if (endHour < 0) endHour = 23;
    return `${endHour.toString().padStart(2, '0')}:59`;
  },

  /**
   * Convert time to hour (e.g., "07:30" -> "07")
   */
  timeToHour: (time: string): string => time.split(':')[0],
};

// Legacy exports for backward compatibility
const parseTime = timeUtils.parseTime;
const formatTime = timeUtils.formatTime;

/**
 * Checks if a time period spans overnight (end time is before start time)
 */
const isOvernightPeriod = timeUtils.isOvernightPeriod;

// ============================================================================
// UTILITY FUNCTIONS - VALIDATION
// ============================================================================

/**
 * Checks if location is valid (not default location)
 */
const isValidLocation = (location?: { lat: number; lng: number }): boolean => {
  if (!location) return false;
  return !(
    location.lat === DEFAULT_LOCATION.lat &&
    location.lng === DEFAULT_LOCATION.lng
  );
};

/**
 * Checks if ordering range is valid
 */
const isValidOrderingRange = (range?: number): boolean => {
  return !!(range !== undefined && (range === 0 || range >= 15));
};

/**
 * Validates time slots for overlaps and logical consistency
 */
const validateTimeSlots = (slots: TimeSlot[]): string | null => {
  if (slots.length === 0) return null;

  // Sort slots by start time
  const sortedSlots = [...slots].sort((a, b) =>
    a.startAt.localeCompare(b.startAt)
  );

  for (let i = 0; i < sortedSlots.length; i++) {
    const slot = sortedSlots[i];

    // Validate time format and logic (only for same-day periods)
    if (
      !isOvernightPeriod(slot.startAt, slot.endAt) &&
      slot.startAt >= slot.endAt
    ) {
      return 'Start time must be before end time for same-day periods';
    }

    // Check for overlaps with next slot
    if (i < sortedSlots.length - 1) {
      const nextSlot = sortedSlots[i + 1];
      if (
        slot.endAt > nextSlot.startAt &&
        !isOvernightPeriod(slot.startAt, slot.endAt)
      ) {
        return 'Time slots cannot overlap';
      }
    }
  }

  return null;
};

const validateLocationCoverage = (
  locationSchedule: LocationSchedule
): string[] => {
  const warnings: string[] = [];

  for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
    const daySlots = locationSchedule.schedule[dayIndex].timeSlots;

    if (daySlots.length === 0) {
      warnings.push(`${DAYS_OF_WEEK[dayIndex]}: No catalogs available all day`);
      continue;
    }

    // Sort slots by start time
    const sortedSlots = [...daySlots].sort((a, b) =>
      a.startAt.localeCompare(b.startAt)
    );

    // Check if the day doesn't start at 00:00
    if (sortedSlots[0].startAt !== TIME_FORMAT.MIDNIGHT) {
      warnings.push(
        `${DAYS_OF_WEEK[dayIndex]}: No coverage before ${sortedSlots[0].startAt}`
      );
    }

    // Check for gaps between periods
    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const currentEnd = sortedSlots[i].endAt;
      const nextStart = sortedSlots[i + 1].startAt;

      if (currentEnd < nextStart) {
        warnings.push(
          `${DAYS_OF_WEEK[dayIndex]}: Gap between ${currentEnd} and ${nextStart}`
        );
      }
    }

    // Check if the day doesn't end at 23:59
    const lastSlot = sortedSlots[sortedSlots.length - 1];
    if (lastSlot.endAt !== TIME_FORMAT.DAY_END) {
      warnings.push(
        `${DAYS_OF_WEEK[dayIndex]}: No coverage after ${lastSlot.endAt}`
      );
    }
  }

  return warnings;
};

/**
 * Checks for overlaps between time slots, considering overnight periods
 */
const checkTimeSlotOverlaps = (
  newSlot: TimeSlot,
  existingSlots: TimeSlot[]
): boolean => {
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * MINUTES_PER_HOUR + minutes;
  };

  const newStart = timeToMinutes(newSlot.startAt);
  let newEnd = timeToMinutes(newSlot.endAt);

  // Handle overnight slots: if end time is before start time, add 24 hours to end time
  const isNewSlotOvernight = newEnd <= newStart;
  if (isNewSlotOvernight) {
    newEnd += MINUTES_PER_DAY;
  }

  for (const slot of existingSlots) {
    const existingStart = timeToMinutes(slot.startAt);
    let existingEnd = timeToMinutes(slot.endAt);

    // Handle overnight existing slots
    const isExistingOvernight = existingEnd <= existingStart;
    if (isExistingOvernight) {
      existingEnd += MINUTES_PER_DAY;
    }

    // Check for overlaps considering overnight scenarios
    if (isNewSlotOvernight && isExistingOvernight) {
      // Both overnight: check standard overlap
      if (newStart < existingEnd && newEnd > existingStart) {
        return true;
      }
    } else if (isNewSlotOvernight && !isExistingOvernight) {
      // New is overnight, existing is same-day
      // Check if existing overlaps with new slot's first part (start to midnight)
      if (existingStart < MINUTES_PER_DAY && existingEnd > newStart) {
        return true;
      }
      // Check if existing overlaps with new slot's second part (midnight to end)
      if (existingStart < newEnd - MINUTES_PER_DAY && existingEnd > 0) {
        return true;
      }
    } else if (!isNewSlotOvernight && isExistingOvernight) {
      // New is same-day, existing is overnight
      // Check if new overlaps with existing slot's first part (start to midnight)
      if (newStart < MINUTES_PER_DAY && newEnd > existingStart) {
        return true;
      }
      // Check if new overlaps with existing slot's second part (midnight to end)
      if (newStart < existingEnd - MINUTES_PER_DAY && newEnd > 0) {
        return true;
      }
    } else {
      // Both same-day: standard overlap check
      if (newStart < existingEnd && newEnd > existingStart) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Custom validation function for intro media files
 * Images: max 1MB, Videos: max 25MB
 */
const validateIntroMediaFile = (file: File): string | null => {
  const isImage = file.type.startsWith('image/');
  const isVideo = file.type.startsWith('video/');

  if (!isImage && !isVideo) {
    return 'File must be an image or video';
  }

  const maxImageSize = 1 * 1024 * 1024; // 1MB in bytes
  const maxVideoSize = 25 * 1024 * 1024; // 25MB in bytes

  if (isImage && file.size > maxImageSize) {
    return 'Image files must be smaller than 1MB';
  }

  if (isVideo && file.size > maxVideoSize) {
    return 'Video files must be smaller than 25MB';
  }

  return null;
};

// ============================================================================
// UTILITY FUNCTIONS - DISPLAY FORMATTING
// ============================================================================

const formatScheduleDisplay = (schedule: DaySchedule[]): string => {
  // Check if it's 24/7 (all days have single 00:00-23:59 slot)
  const is24_7 = schedule.every(
    daySchedule =>
      daySchedule.timeSlots.length === 1 &&
      daySchedule.timeSlots[0].startAt === TIME_FORMAT.FULL_DAY_START &&
      daySchedule.timeSlots[0].endAt === TIME_FORMAT.FULL_DAY_END
  );

  if (is24_7) return '24/7';

  // Group consecutive days with same schedule
  const dayGroups: string[] = [];
  let currentGroup: number[] = [];
  let currentScheduleStr = '';

  for (let i = 0; i < 7; i++) {
    const dayScheduleStr = schedule[i].timeSlots
      .map(slot => `${slot.startAt}-${slot.endAt}`)
      .join(', ');

    if (dayScheduleStr === currentScheduleStr && currentGroup.length > 0) {
      currentGroup.push(i);
    } else {
      if (currentGroup.length > 0) {
        dayGroups.push(formatDayGroup(currentGroup, currentScheduleStr));
      }
      currentGroup = dayScheduleStr ? [i] : [];
      currentScheduleStr = dayScheduleStr;
    }
  }

  if (currentGroup.length > 0) {
    dayGroups.push(formatDayGroup(currentGroup, currentScheduleStr));
  }

  return dayGroups.join(', ') || 'No schedule';
};

const formatDayGroup = (dayIndices: number[], schedule: string): string => {
  if (dayIndices.length === 0) return '';

  let dayStr = '';

  if (dayIndices.length === 1) {
    dayStr = DAYS_OF_WEEK_SHORT[dayIndices[0]];
  } else if (
    dayIndices.length > 1 &&
    dayIndices.every((day, i) => i === 0 || day === dayIndices[i - 1] + 1)
  ) {
    // Consecutive days
    dayStr = `${DAYS_OF_WEEK_SHORT[dayIndices[0]]}-${DAYS_OF_WEEK_SHORT[dayIndices[dayIndices.length - 1]]}`;
  } else {
    // Non-consecutive days
    dayStr = dayIndices.map(i => DAYS_OF_WEEK_SHORT[i]).join(', ');
  }

  return `${dayStr} ${schedule}`;
};

// ============================================================================
// UTILITY FUNCTIONS - BUSINESS HOURS
// ============================================================================

const getBusinessHoursForDay = (
  dayIndex: number,
  businessHours: BusinessHours
): { startMinutes: number; endMinutes: number; isClosed: boolean } => {
  const { businessHoursSchedule, businessHoursExceptions } = businessHours;

  // Check if there's an exception for this day
  const exception = businessHoursExceptions?.[dayIndex.toString()];

  if (exception) {
    // If both startAt and endAt are empty, the location is closed
    if (!exception.startAt && !exception.endAt) {
      return { startMinutes: 0, endMinutes: 0, isClosed: true };
    }

    // If startAt exists but endAt is empty, it means open until next day's startAt
    if (exception.startAt && !exception.endAt) {
      return {
        startMinutes: parseTime(exception.startAt),
        endMinutes: MINUTES_PER_DAY, // End at midnight of next day
        isClosed: false,
      };
    }

    const startMinutes = parseTime(
      exception.startAt || businessHoursSchedule.startAt
    );
    let endMinutes = parseTime(exception.endAt || businessHoursSchedule.endAt);

    // If end time is before start time, it means next day (overnight schedule)
    if (endMinutes <= startMinutes) {
      endMinutes += MINUTES_PER_DAY;
    }

    return {
      startMinutes,
      endMinutes,
      isClosed: false,
    };
  }

  // Use default business hours
  const startMinutes = parseTime(businessHoursSchedule.startAt);
  let endMinutes = parseTime(businessHoursSchedule.endAt);

  // If end time is before start time, it means next day
  if (endMinutes <= startMinutes) {
    endMinutes += MINUTES_PER_DAY;
  }

  return { startMinutes, endMinutes, isClosed: false };
};

const getGlobalBusinessHoursRange = (
  businessHours: BusinessHours
): { minStartHour: number; maxEndHour: number } => {
  let minStartMinutes = MINUTES_PER_DAY;
  let maxEndMinutes = 0;

  for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
    const { startMinutes, endMinutes, isClosed } = getBusinessHoursForDay(
      dayIndex,
      businessHours
    );

    if (!isClosed) {
      minStartMinutes = Math.min(minStartMinutes, startMinutes);
      maxEndMinutes = Math.max(maxEndMinutes, endMinutes);
    }
  }

  // Convert back to hours and ensure reasonable bounds
  const minStartHour = Math.max(
    0,
    Math.floor(minStartMinutes / MINUTES_PER_HOUR)
  );

  let maxEndHour;
  if (maxEndMinutes > MINUTES_PER_DAY) {
    // Overnight schedule: convert excess minutes to next day hours
    const overnightEndHour = Math.ceil(
      (maxEndMinutes - MINUTES_PER_DAY) / MINUTES_PER_HOUR
    );
    // Add 24 to represent next day hours in continuous display
    maxEndHour = 24 + overnightEndHour;
  } else {
    maxEndHour = Math.min(
      HOURS_PER_DAY,
      Math.ceil(maxEndMinutes / MINUTES_PER_HOUR)
    );
  }

  return { minStartHour, maxEndHour };
};

const isHourInBusinessHours = (
  hour: number,
  dayIndex: number,
  businessHours: BusinessHours
): boolean => {
  const { startMinutes, endMinutes, isClosed } = getBusinessHoursForDay(
    dayIndex,
    businessHours
  );

  if (isClosed) return false;

  const hourMinutes = hour * MINUTES_PER_HOUR;

  // Handle overnight schedules (endMinutes > MINUTES_PER_DAY)
  if (endMinutes > MINUTES_PER_DAY) {
    // Current day: hour >= startHour OR hour < (endMinutes - MINUTES_PER_DAY)
    const endHourNextDay = (endMinutes - MINUTES_PER_DAY) / MINUTES_PER_HOUR;
    return hourMinutes >= startMinutes || hour < endHourNextDay;
  }

  // Normal schedule: start <= hour < end
  return hourMinutes >= startMinutes && hourMinutes < endMinutes;
};

const limitSlotToBusinessHours = (
  slot: TimeSlot,
  dayIndex: number,
  businessHours: BusinessHours
): { startHour: number; endHour: number; isVisible: boolean } => {
  const { startMinutes, endMinutes, isClosed } = getBusinessHoursForDay(
    dayIndex,
    businessHours
  );

  if (isClosed) {
    return { startHour: 0, endHour: 0, isVisible: false };
  }

  let slotStartMinutes = parseTime(slot.startAt);
  let slotEndMinutes = parseTime(slot.endAt);

  // Handle overnight slots (e.g., 13:00-03:00)
  if (slotEndMinutes <= slotStartMinutes) {
    slotEndMinutes += MINUTES_PER_DAY; // Add 24 hours to end time
  }

  // Check if slot overlaps with business hours
  const overlapStart = Math.max(slotStartMinutes, startMinutes);
  const overlapEnd = Math.min(slotEndMinutes, endMinutes);

  if (overlapStart >= overlapEnd) {
    return { startHour: 0, endHour: 0, isVisible: false };
  }

  // Convert back to hours - for overnight business hours, allow display beyond 24
  const displayStartHour = Math.floor(overlapStart / MINUTES_PER_HOUR);
  let displayEndHour = Math.ceil(overlapEnd / MINUTES_PER_HOUR);

  // If business hours go overnight, we need to allow display hours beyond 24
  if (endMinutes > MINUTES_PER_DAY && overlapEnd > MINUTES_PER_DAY) {
    // Convert the end hour to the extended display format (24+)
    displayEndHour =
      24 + Math.ceil((overlapEnd - MINUTES_PER_DAY) / MINUTES_PER_HOUR);
  }

  return {
    startHour: displayStartHour,
    endHour: displayEndHour,
    isVisible: true,
  };
};

/**
 * Finds gaps in schedule coverage during business hours for a specific day
 */
const findScheduleGaps = (
  dayIndex: number,
  timeSlots: TimeSlot[],
  businessHours: BusinessHours | null
): Array<{ startHour: number; endHour: number }> => {
  if (!businessHours) return [];

  const { startMinutes, endMinutes, isClosed } = getBusinessHoursForDay(
    dayIndex,
    businessHours
  );

  if (isClosed) return [];

  // Convert business hours to hour boundaries
  const businessStartHour = Math.floor(startMinutes / MINUTES_PER_HOUR);
  let businessEndHour = Math.ceil(endMinutes / MINUTES_PER_HOUR);

  // Handle overnight business hours
  const isOvernightBusiness = endMinutes > MINUTES_PER_DAY;
  if (isOvernightBusiness) {
    businessEndHour =
      24 + Math.ceil((endMinutes - MINUTES_PER_DAY) / MINUTES_PER_HOUR);
  }

  // Create array to track coverage for each hour (extended for overnight)
  const maxHours = isOvernightBusiness ? businessEndHour : 24;
  const coverage = new Array(maxHours).fill(false);

  // Mark hours covered by time slots
  timeSlots.forEach(slot => {
    const { startHour, endHour, isVisible } = limitSlotToBusinessHours(
      slot,
      dayIndex,
      businessHours
    );

    if (isVisible) {
      for (let hour = startHour; hour < endHour; hour++) {
        if (hour < coverage.length) {
          coverage[hour] = true;
        }
      }
    }
  });

  // Find gaps within business hours
  const gaps: Array<{ startHour: number; endHour: number }> = [];
  let gapStart = -1;

  for (let hour = businessStartHour; hour < businessEndHour; hour++) {
    const coverageIndex = isOvernightBusiness && hour >= 24 ? hour : hour % 24;
    const isCovered = coverage[coverageIndex];

    if (!isCovered) {
      if (gapStart === -1) {
        gapStart = hour;
      }
    } else {
      if (gapStart !== -1) {
        gaps.push({ startHour: gapStart, endHour: hour });
        gapStart = -1;
      }
    }
  }

  // Handle gap that extends to end of business hours
  if (gapStart !== -1) {
    gaps.push({ startHour: gapStart, endHour: businessEndHour });
  }

  return gaps;
};

// ============================================================================
// UTILITY FUNCTIONS - COLOR GENERATION
// ============================================================================

/**
 * Generates consistent colors for catalogs using a deterministic hash
 */
const generateCatalogColors = (
  catalogs: Catalog[]
): { [key: string]: ColorPalette } => {
  const colors: { [key: string]: ColorPalette } = {};

  catalogs.forEach(catalog => {
    // Create a robust hash for consistent colors
    let hash = 0;
    const str = catalog.id;
    if (str.length === 0) return;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Use absolute value and ensure we get a consistent positive index
    const colorIndex = Math.abs(hash) % COLOR_PALETTE.length;
    colors[catalog.id] = COLOR_PALETTE[colorIndex];
  });

  return colors;
};

// ============================================================================
// CUSTOM HOOKS
// ============================================================================

/**
 * Custom hook for OrderNow form management
 */
const useOrderNowForm = () => {
  const { setValue, getValues, watch } = useFormContext<OrderNowFormValues>();

  const updateLocationSchedule = useCallback(
    (locationId: string, scheduleData: LocationSchedule) => {
      const currentSellPoints = getValues('sellPoints') || {};

      // Ensure all required properties are present
      const normalizedScheduleData: LocationSchedule = {
        schedule: scheduleData.schedule || DEFAULT_SCHEDULE,
        active: scheduleData.active ?? false,
        orderProcessingDevice: scheduleData.orderProcessingDevice || '',
        location: scheduleData.location || DEFAULT_LOCATION,
        orderingRange: scheduleData.orderingRange ?? DEFAULT_ORDERING_RANGE,
        logo: scheduleData.logo || [],
        intro: scheduleData.intro || [],
      };

      setValue(
        'sellPoints',
        {
          ...currentSellPoints,
          [locationId]: normalizedScheduleData,
        },
        { shouldDirty: true }
      );
    },
    [setValue, getValues]
  );

  const updateTimeSlot = useCallback(
    (
      locationId: string,
      dayIndex: number,
      slotIndex: number,
      field: keyof TimeSlot,
      value: string
    ) => {
      const currentSellPoints = getValues('sellPoints') || {};
      const currentLocation = currentSellPoints[locationId];

      if (!currentLocation) return;

      const newSchedule = [...currentLocation.schedule];
      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        timeSlots: [...newSchedule[dayIndex].timeSlots],
      };
      newSchedule[dayIndex].timeSlots[slotIndex] = {
        ...newSchedule[dayIndex].timeSlots[slotIndex],
        [field]: value,
      };

      updateLocationSchedule(locationId, {
        schedule: newSchedule,
        active: currentLocation.active,
        orderProcessingDevice: currentLocation.orderProcessingDevice || '',
        location: currentLocation.location || DEFAULT_LOCATION,
        orderingRange: currentLocation.orderingRange ?? DEFAULT_ORDERING_RANGE,
        logo: currentLocation.logo || [],
        intro: currentLocation.intro || [],
      });
    },
    [getValues, updateLocationSchedule]
  );

  const addTimeSlot = useCallback(
    (
      locationId: string,
      dayIndex: number,
      catalogId: string = '',
      isAllDay: boolean = false
    ) => {
      const currentSellPoints = getValues('sellPoints') || {};
      const currentLocation = currentSellPoints[locationId];

      if (!currentLocation) {
        // Initialize location with empty schedule
        updateLocationSchedule(locationId, {
          schedule: DEFAULT_SCHEDULE,
          active: false,
          orderProcessingDevice: '',
          location: DEFAULT_LOCATION,
          orderingRange: DEFAULT_ORDERING_RANGE,
          logo: [],
          intro: [],
        });
        return;
      }

      const newSchedule = [...currentLocation.schedule];
      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        timeSlots: [
          ...newSchedule[dayIndex].timeSlots,
          {
            startAt: isAllDay ? TIME_FORMAT.FULL_DAY_START : '09:00',
            endAt: isAllDay ? TIME_FORMAT.FULL_DAY_END : '17:00',
            catalogId,
          },
        ],
      };

      updateLocationSchedule(locationId, {
        schedule: newSchedule,
        active: currentLocation.active,
        orderProcessingDevice: currentLocation.orderProcessingDevice || '',
        location: currentLocation.location || DEFAULT_LOCATION,
        orderingRange: currentLocation.orderingRange ?? DEFAULT_ORDERING_RANGE,
        logo: currentLocation.logo || [],
        intro: currentLocation.intro || [],
      });
    },
    [getValues, updateLocationSchedule]
  );

  const removeTimeSlot = useCallback(
    (locationId: string, dayIndex: number, slotIndex: number) => {
      const currentSellPoints = getValues('sellPoints') || {};
      const currentLocation = currentSellPoints[locationId];

      if (!currentLocation) return;

      const newSchedule = [...currentLocation.schedule];
      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        timeSlots: newSchedule[dayIndex].timeSlots.filter(
          (_, i) => i !== slotIndex
        ),
      };

      updateLocationSchedule(locationId, {
        schedule: newSchedule,
        active: currentLocation.active,
        orderProcessingDevice: currentLocation.orderProcessingDevice || '',
        location: currentLocation.location || DEFAULT_LOCATION,
        orderingRange: currentLocation.orderingRange ?? DEFAULT_ORDERING_RANGE,
        logo: currentLocation.logo || [],
        intro: currentLocation.intro || [],
      });
    },
    [getValues, updateLocationSchedule]
  );

  const copyDaySchedule = useCallback(
    (locationId: string, fromDay: number, toDays: number[]) => {
      const currentSellPoints = getValues('sellPoints') || {};
      const currentLocation = currentSellPoints[locationId];

      if (!currentLocation) return;

      const newSchedule = [...currentLocation.schedule];
      const sourceDaySchedule = newSchedule[fromDay];

      toDays.forEach(dayIndex => {
        newSchedule[dayIndex] = {
          timeSlots: sourceDaySchedule.timeSlots.map(slot => ({ ...slot })),
        };
      });

      updateLocationSchedule(locationId, {
        schedule: newSchedule,
        active: currentLocation.active,
        orderProcessingDevice: currentLocation.orderProcessingDevice || '',
        location: currentLocation.location || DEFAULT_LOCATION,
        orderingRange: currentLocation.orderingRange ?? DEFAULT_ORDERING_RANGE,
        logo: currentLocation.logo || [],
        intro: currentLocation.intro || [],
      });
    },
    [getValues, updateLocationSchedule]
  );

  const updateLocationMedia = useCallback(
    (
      locationId: string,
      mediaType: 'logo' | 'intro',
      files: UploadedFile[]
    ) => {
      const currentSellPoints = getValues('sellPoints') || {};
      const currentLocation = currentSellPoints[locationId];

      if (!currentLocation) {
        // Initialize location with media fields
        updateLocationSchedule(locationId, {
          schedule: DEFAULT_SCHEDULE,
          active: false,
          orderProcessingDevice: '',
          location: DEFAULT_LOCATION,
          orderingRange: DEFAULT_ORDERING_RANGE,
          logo: mediaType === 'logo' ? files : [],
          intro: mediaType === 'intro' ? files : [],
        });
        return;
      }

      updateLocationSchedule(locationId, {
        ...currentLocation,
        [mediaType]: files,
      });
    },
    [getValues, updateLocationSchedule]
  );

  return {
    updateLocationSchedule,
    updateLocationMedia,
    updateTimeSlot,
    addTimeSlot,
    removeTimeSlot,
    copyDaySchedule,
    watch,
  };
};

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Props for DayScheduleModal component
 */
interface DayScheduleModalProps {
  open: boolean;
  onClose: () => void;
  locationId: string;
  locationName: string;
  businessHours?: BusinessHours | null;
}

/**
 * Modal component for editing day schedules
 */
const DayScheduleModal = ({
  open,
  onClose,
  locationId,
  locationName,
  businessHours,
}: DayScheduleModalProps) => {
  // ========================================
  // State Management
  // ========================================
  const [selectedDay, setSelectedDay] = useState(0);
  const [originalSchedule, setOriginalSchedule] =
    useState<LocationSchedule | null>(null);
  const [localSchedule, setLocalSchedule] = useState<LocationSchedule | null>(
    null
  );
  const [hasChanges, setHasChanges] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Form state for adding new time slots
  const [selectedCatalogId, setSelectedCatalogId] = useState<string>('');
  const [startHour, setStartHour] = useState('00');
  const [endHour, setEndHour] = useState('24');

  // ========================================
  // Hooks and Context
  // ========================================
  const { updateLocationSchedule, watch } = useOrderNowForm();
  const { t } = useTranslation('');
  const notify = useNotify();
  const isMobile = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const sellPoints = watch('sellPoints') || {};
  const currentLocationSchedule = sellPoints[locationId];

  // Get available catalogs for this location
  const {
    data: availableCatalogs,
    isLoading: catalogsLoading,
    error: catalogsError,
  } = useGetListHospitalityCatalogsLive({
    filter: { sellPointIds_inc: locationId, _d: false },
  });

  // Get current locale for day labels
  const { i18n } = useTranslation('');
  const currentLocale: string = i18n.language || 'en';
  const dayLabels =
    currentLocale === 'ro'
      ? DAYS_OF_WEEK_FIRST_LETTER_RO
      : DAYS_OF_WEEK_FIRST_LETTER_EN;

  // ========================================
  // Computed Values
  // ========================================

  // Get business hours for the selected day
  const currentDayBusinessHours = useMemo(() => {
    if (!businessHours) return null;
    return getBusinessHoursForDay(selectedDay, businessHours);
  }, [selectedDay, businessHours]);

  // ========================================
  // Helper Functions
  // ========================================

  // Use timeUtils instead of local helper functions
  const { hourToTime, hourToEndTime, timeToHour } = timeUtils;

  /**
   * Get default start and end hours based on business hours
   */
  const getDefaultHours = useCallback(() => {
    if (currentDayBusinessHours && !currentDayBusinessHours.isClosed) {
      const defaultStartHour = Math.floor(
        currentDayBusinessHours.startMinutes / MINUTES_PER_HOUR
      )
        .toString()
        .padStart(2, '0');

      // Handle overnight business hours
      let businessEndMinutes = currentDayBusinessHours.endMinutes;
      if (businessEndMinutes > MINUTES_PER_DAY) {
        // Convert overnight end time back to next day format
        businessEndMinutes = businessEndMinutes - MINUTES_PER_DAY;
      }

      // For end hour, we want the business end hour + 1
      // If business hours end at 01:59 (next day), we want default end to be 02
      const businessEndHour = Math.floor(businessEndMinutes / MINUTES_PER_HOUR);
      let defaultEndHour = businessEndHour + 1;

      // Handle edge case where business ends at 23:xx - next hour would be 24
      if (defaultEndHour > 23) {
        defaultEndHour = 24;
      }

      const defaultEndHourStr = defaultEndHour.toString().padStart(2, '0');

      return { defaultStartHour, defaultEndHour: defaultEndHourStr };
    }
    return { defaultStartHour: '00', defaultEndHour: '24' };
  }, [currentDayBusinessHours]);

  /**
   * Reset form inputs to default state
   */
  const resetFormInputs = useCallback(() => {
    setSelectedCatalogId('');
    const { defaultStartHour, defaultEndHour } = getDefaultHours();
    setStartHour(defaultStartHour);
    setEndHour(defaultEndHour);
  }, [getDefaultHours]);

  /**
   * Validate time slot against business hours
   */
  const validateTimeSlotAgainstBusinessHours = (
    newSlot: TimeSlot
  ): string | null => {
    if (!currentDayBusinessHours) return null;

    if (currentDayBusinessHours.isClosed) {
      return `${DAYS_OF_WEEK[selectedDay]} is closed according to business hours`;
    }

    let slotStartMinutes = parseTime(newSlot.startAt);
    let slotEndMinutes = parseTime(newSlot.endAt);

    // Handle overnight slots
    if (slotEndMinutes <= slotStartMinutes) {
      slotEndMinutes += MINUTES_PER_DAY;
    }

    // Check start time
    if (slotStartMinutes < currentDayBusinessHours.startMinutes) {
      return `Time slot starts before business hours (${formatTime(currentDayBusinessHours.startMinutes)})`;
    }

    // For overnight business hours, we need more complex validation
    if (currentDayBusinessHours.endMinutes > MINUTES_PER_DAY) {
      // Overnight business hours - slot is valid if it fits within the overnight period
      if (slotEndMinutes > currentDayBusinessHours.endMinutes) {
        const businessEndNextDay =
          currentDayBusinessHours.endMinutes - MINUTES_PER_DAY;
        return `Time slot ends after business hours (${formatTime(businessEndNextDay)} next day)`;
      }
    } else {
      // Normal business hours
      if (slotEndMinutes > currentDayBusinessHours.endMinutes) {
        return `Time slot ends after business hours (${formatTime(currentDayBusinessHours.endMinutes)})`;
      }
    }

    return null;
  };

  // ========================================
  // Effect Hooks
  // ========================================

  // Reset form inputs when day changes or when default hours change
  useEffect(() => {
    resetFormInputs();
  }, [selectedDay, resetFormInputs]);

  // Initialize local schedule when modal opens and reset form inputs
  useEffect(() => {
    if (open && currentLocationSchedule && !localSchedule) {
      const schedule = JSON.parse(JSON.stringify(currentLocationSchedule)); // Deep clone
      setOriginalSchedule(schedule);
      setLocalSchedule(schedule);
      setHasChanges(false);
      // Reset form inputs when modal opens
      resetFormInputs();
    }
  }, [open, currentLocationSchedule, resetFormInputs, localSchedule]);

  // Reset local schedule when modal closes
  useEffect(() => {
    if (!open) {
      setLocalSchedule(null);
      setOriginalSchedule(null);
      setHasChanges(false);
      setSelectedDay(0); // Reset to first day (Sunday)
      resetFormInputs();
    }
  }, [open, resetFormInputs]);

  // ========================================
  // Event Handlers
  // ========================================

  /**
   * Add new time slot
   */
  const handleAddTimeSlot = useCallback(() => {
    if (!selectedCatalogId || !localSchedule || !startHour || !endHour) {
      notify('Please fill in all required fields', { type: 'error' });
      return;
    }

    const startTime = hourToTime(startHour);
    const endTime = hourToEndTime(endHour);

    // Handle overnight schedules: if startHour > endHour, it's an overnight slot
    const isOvernightSlot = parseInt(startHour, 10) > parseInt(endHour, 10);

    if (!isOvernightSlot && parseInt(startHour, 10) > parseInt(endHour, 10)) {
      notify('Start hour must be before end hour for same-day schedules', {
        type: 'error',
      });
      return;
    }

    const newSlot: TimeSlot = {
      startAt: startTime,
      endAt: endTime,
      catalogId: selectedCatalogId,
    };

    // Validate against business hours
    const businessHoursError = validateTimeSlotAgainstBusinessHours(newSlot);
    if (businessHoursError) {
      notify(businessHoursError, { type: 'warning' });
      // Don't return here - allow user to add slots outside business hours but warn them
    }

    const currentDaySlots = localSchedule.schedule[selectedDay].timeSlots;

    if (checkTimeSlotOverlaps(newSlot, currentDaySlots)) {
      notify('Time periods overlap with existing slots', { type: 'error' });
      return;
    }

    const newSchedule = { ...localSchedule };
    newSchedule.schedule[selectedDay] = {
      ...newSchedule.schedule[selectedDay],
      timeSlots: [...currentDaySlots, newSlot].sort((a, b) =>
        a.startAt.localeCompare(b.startAt)
      ),
    };

    setLocalSchedule(newSchedule);
    setHasChanges(true);

    // Reset form inputs to default state
    resetFormInputs();
  }, [
    selectedCatalogId,
    localSchedule,
    startHour,
    endHour,
    selectedDay,
    notify,
    validateTimeSlotAgainstBusinessHours,
    resetFormInputs,
  ]);

  /**
   * Remove time slot
   */
  const handleRemoveTimeSlot = useCallback(
    (slotIndex: number) => {
      if (!localSchedule) return;

      const newSchedule = { ...localSchedule };
      newSchedule.schedule[selectedDay] = {
        ...newSchedule.schedule[selectedDay],
        timeSlots: newSchedule.schedule[selectedDay].timeSlots.filter(
          (_, i) => i !== slotIndex
        ),
      };

      setLocalSchedule(newSchedule);
      setHasChanges(true);
    },
    [localSchedule, selectedDay]
  );

  /**
   * Save changes
   */
  const handleSave = useCallback(() => {
    if (localSchedule) {
      updateLocationSchedule(locationId, localSchedule);
      setHasChanges(false);
      onClose();
    }
  }, [localSchedule, updateLocationSchedule, locationId, onClose]);

  /**
   * Handle close with confirmation
   */
  const handleClose = useCallback(() => {
    if (hasChanges) {
      setShowConfirmModal(true);
    } else {
      // Reset form inputs when closing without changes
      resetFormInputs();
      onClose();
    }
  }, [hasChanges, resetFormInputs, onClose]);

  /**
   * Discard changes
   */
  const handleDiscard = useCallback(() => {
    setShowConfirmModal(false);
    setHasChanges(false);
    // Reset form inputs when discarding changes
    resetFormInputs();
    onClose();
  }, [resetFormInputs, onClose]);

  /**
   * Handle tab change (day selection)
   */
  const handleDayChange = useCallback(
    (newDay: number) => {
      setSelectedDay(newDay);
      // Only reset form inputs when changing tabs, don't affect the schedule data
      resetFormInputs();
    },
    [resetFormInputs]
  );

  const handleCopySchedule = useCallback(() => {
    if (!localSchedule) return;

    const newSchedule = { ...localSchedule };
    const sourceDaySchedule = newSchedule.schedule[selectedDay];
    let copiedDaysCount = 0;

    // Copy to all other days (excluding the current day)
    DAYS_OF_WEEK.forEach((_, dayIndex) => {
      if (dayIndex !== selectedDay) {
        newSchedule.schedule[dayIndex] = {
          timeSlots: sourceDaySchedule.timeSlots.map(slot => ({ ...slot })),
        };
        copiedDaysCount++;
      }
    });

    setLocalSchedule(newSchedule);
    setHasChanges(true);
    notify(`Schedule copied to ${copiedDaysCount} day(s)`, {
      type: 'success',
    });
  }, [localSchedule, selectedDay, notify]);

  // ========================================
  // Render Guards
  // ========================================

  if (!open || !localSchedule) return null;

  const currentDaySlots = localSchedule.schedule[selectedDay].timeSlots;

  // ========================================
  // Render
  // ========================================

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        fullScreen={isMobile}
      >
        <ModalHeader
          handleClose={handleClose}
          title={`${locationName} Catalogs Schedule`}
        >
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={!hasChanges}
          >
            {t('shared.save')}
          </Button>
        </ModalHeader>

        <Box sx={{ p: 3 }}>
          {/* Day Tabs */}
          <Tabs
            value={selectedDay}
            onChange={(_, newValue) => handleDayChange(newValue)}
            variant="fullWidth"
            sx={{
              mb: 3,
              '& .MuiTab-root': {
                minWidth: 0,
                padding: '6px 8px',
                fontSize: '0.875rem',
              },
            }}
          >
            {DAYS_OF_WEEK.map((day, index) => (
              <Tab
                key={day}
                label={dayLabels[index]}
                aria-label={`Schedule for ${day}`}
                sx={{
                  minWidth: 0,
                  padding: '6px 4px',
                }}
              />
            ))}
          </Tabs>

          {/* Business Hours Info - One liner */}
          {currentDayBusinessHours && (
            <>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mb: 2, textAlign: 'center' }}
              >
                {DAYS_OF_WEEK[selectedDay]} Business Hours:{' '}
                {currentDayBusinessHours.isClosed
                  ? 'Closed'
                  : (() => {
                      // Format start time
                      const startHour = Math.floor(
                        currentDayBusinessHours.startMinutes / 60
                      );
                      const startMin =
                        currentDayBusinessHours.startMinutes % 60;
                      const startTime = `${startHour.toString().padStart(2, '0')}:${startMin.toString().padStart(2, '0')}`;

                      // Format end time (handle overnight)
                      let endMinutes = currentDayBusinessHours.endMinutes;
                      let isOvernightEnd = false;

                      if (endMinutes >= 24 * 60) {
                        endMinutes = endMinutes - 24 * 60;
                        isOvernightEnd = true;
                      }

                      const endHour = Math.floor(endMinutes / 60);
                      const endMin = endMinutes % 60;
                      const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;

                      return `${startTime} - ${endTime}${isOvernightEnd ? ' (next day)' : ''}`;
                    })()}
              </Typography>
              {/* Horizontal divider */}
              <Box sx={{ borderBottom: '1px solid #e0e0e0', mb: 3 }} />
            </>
          )}

          {/* Add Time Slot Form - Now inside the selected day */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Add Time Slot
          </Typography>

          {/* Show loading state for catalogs */}
          {catalogsLoading && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Loading catalogs...
            </Typography>
          )}

          {/* Show error state for catalogs */}
          {catalogsError && (
            <Typography variant="body2" color="error" sx={{ mb: 2 }}>
              Error loading catalogs. Please try again.
            </Typography>
          )}

          <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
            <Grid item xs={7}>
              <Select
                value={selectedCatalogId}
                onChange={e => setSelectedCatalogId(e.target.value)}
                displayEmpty
                size="small"
                fullWidth
                aria-label="Select catalog"
              >
                <MenuItem value="">Select catalog...</MenuItem>
                {availableCatalogs?.map(catalog => (
                  <MenuItem key={catalog.id} value={catalog.id}>
                    {catalog.name}
                  </MenuItem>
                ))}
              </Select>
            </Grid>

            <Grid item xs={2}>
              <Select
                value={startHour}
                onChange={e => setStartHour(e.target.value)}
                size="small"
                fullWidth
                displayEmpty
                aria-label="Start hour"
                IconComponent={isMobile ? () => null : undefined}
                sx={
                  isMobile ? { '& .MuiSelect-icon': { display: 'none' } } : {}
                }
              >
                <MenuItem value="" disabled>
                  S...
                </MenuItem>
                {Array.from({ length: 24 }, (_, i) => {
                  const hour = i.toString().padStart(2, '0');
                  return (
                    <MenuItem key={hour} value={hour}>
                      {hour}
                    </MenuItem>
                  );
                })}
              </Select>
            </Grid>

            <Grid item xs={2}>
              <Select
                value={endHour}
                onChange={e => setEndHour(e.target.value)}
                size="small"
                fullWidth
                displayEmpty
                aria-label="End hour"
                IconComponent={isMobile ? () => null : undefined}
                sx={
                  isMobile ? { '& .MuiSelect-icon': { display: 'none' } } : {}
                }
              >
                <MenuItem value="" disabled>
                  E...
                </MenuItem>
                {Array.from({ length: 24 }, (_, i) => {
                  const hour = (i + 1).toString().padStart(2, '0'); // Start from 01 to 24
                  return (
                    <MenuItem key={hour} value={hour}>
                      {hour}
                    </MenuItem>
                  );
                })}
              </Select>
            </Grid>

            <Grid
              item
              xs={1}
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <IconButton
                onClick={handleAddTimeSlot}
                disabled={!selectedCatalogId || catalogsLoading}
                color="primary"
                size="small"
                aria-label="Add time slot"
              >
                <AddIcon />
              </IconButton>
            </Grid>
          </Grid>

          {/* Horizontal divider after Add Time Slot */}
          <Box sx={{ borderBottom: '1px solid #e0e0e0', mb: 3 }} />

          {/* Current Time Slots Section */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Current Time Slots
          </Typography>

          {currentDaySlots.length === 0 ? (
            <Typography
              color="text.secondary"
              sx={{ py: 4, textAlign: 'center' }}
            >
              No time slots configured for this day
            </Typography>
          ) : (
            <List>
              {currentDaySlots.map((slot, index) => {
                const catalog = availableCatalogs?.find(
                  c => c.id === slot.catalogId
                );

                // Check if this slot is outside business hours
                const businessHoursWarning =
                  validateTimeSlotAgainstBusinessHours(slot);

                // Check if catalog is missing
                const isCatalogMissing = !catalog;

                return (
                  <ListItem
                    key={index}
                    sx={{
                      border: theme =>
                        isCatalogMissing
                          ? `1px solid ${theme.palette.error.main}`
                          : `1px solid ${theme.palette.divider}`,
                      borderRadius: 1,
                      mb: 1,
                      backgroundColor: theme => {
                        if (isCatalogMissing) {
                          return theme.palette.mode === 'dark'
                            ? 'rgba(211, 47, 47, 0.1)'
                            : '#ffebee';
                        }
                        if (businessHoursWarning) {
                          return theme.palette.mode === 'dark'
                            ? 'rgba(255, 152, 0, 0.1)'
                            : '#fff3e0';
                        }
                        return theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.05)'
                          : '#f8f9fa';
                      },
                      opacity: isCatalogMissing ? 0.7 : 1,
                    }}
                    secondaryAction={
                      <IconButton
                        edge="end"
                        onClick={() => handleRemoveTimeSlot(index)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    }
                  >
                    <ListItemText
                      primary={
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 3,
                          }}
                        >
                          <Typography variant="body1" sx={{ minWidth: 120 }}>
                            {slot.startAt} - {slot.endAt}
                          </Typography>
                          <Typography variant="body1" color="text.secondary">
                            {catalog?.name || 'Unknown Catalog'}
                          </Typography>
                          {isCatalogMissing && (
                            <ValueFieldWithError
                              value={null}
                              errorMessage="Catalog not found or deleted"
                            />
                          )}
                          {businessHoursWarning && (
                            <Tooltip title={businessHoursWarning}>
                              <ErrorIcon color="warning" fontSize="small" />
                            </Tooltip>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          )}

          {/* Copy Schedule Button */}
          {currentDaySlots.length > 0 && (
            <Button
              variant="outlined"
              onClick={handleCopySchedule}
              fullWidth
              sx={{ mt: 2 }}
            >
              Copy {DAYS_OF_WEEK[selectedDay]} Schedule to All Other Days
            </Button>
          )}
        </Box>
      </Dialog>

      {/* Confirmation Modal */}
      <CloseWithConfirmationModal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onDiscard={handleDiscard}
        saveButton={
          <Button variant="contained" onClick={handleSave}>
            {t('shared.save')}
          </Button>
        }
        title="Unsaved Changes"
        message="You have unsaved changes to the schedule. Would you like to save them?"
        btnDiscardText="Discard Changes"
      />
    </>
  );
};

/**
 * Props for LocationCard component
 */
interface LocationCardProps {
  location: Location;
}

/**
 * Location card component for displaying and managing location schedules
 */
const LocationCard = ({ location }: LocationCardProps) => {
  // ========================================
  // Hooks and Context
  // ========================================
  const { theme } = useTheme();
  const { watch, updateLocationSchedule, updateLocationMedia } =
    useOrderNowForm();

  // ========================================
  // State Management
  // ========================================
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);
  const [mapsModalOpen, setMapsModalOpen] = useState(false);
  const initializationRef = useRef<Set<string>>(new Set());

  // ========================================
  // Data and Computed Values
  // ========================================
  const sellPoints = watch('sellPoints') || {};
  const locationSchedule = sellPoints[location.id];

  // Get business hours for this location
  const businessHours: BusinessHours | null = useMemo(() => {
    if (location.businessHoursSchedule) {
      return {
        businessHoursSchedule: location.businessHoursSchedule,
        businessHoursExceptions: location.businessHoursExceptions || {},
      };
    }
    return null;
  }, [location.businessHoursSchedule, location.businessHoursExceptions]);

  // Calculate global business hours range for calendar display
  const { minStartHour, maxEndHour } = useMemo(() => {
    if (!businessHours) {
      return { minStartHour: 0, maxEndHour: HOURS_PER_DAY };
    }
    return getGlobalBusinessHoursRange(businessHours);
  }, [businessHours]);

  // Get current locale for day labels
  const { i18n } = useTranslation('');
  const currentLocale: string = i18n.language || 'en';
  const dayLabels =
    currentLocale === 'ro'
      ? DAYS_OF_WEEK_FIRST_LETTER_RO
      : DAYS_OF_WEEK_FIRST_LETTER_EN;

  // Check for coverage warnings
  const coverageWarnings = useMemo(() => {
    if (!locationSchedule) return [];
    return validateLocationCoverage(locationSchedule);
  }, [locationSchedule]);

  // Get unique catalogs used in the schedule
  const usedCatalogs = useMemo(() => {
    if (!locationSchedule) return [];
    const catalogIds = new Set<string>();
    locationSchedule.schedule.forEach(day => {
      day.timeSlots.forEach(slot => {
        if (slot.catalogId) {
          catalogIds.add(slot.catalogId);
        }
      });
    });
    return Array.from(catalogIds);
  }, [locationSchedule]);

  // Get available catalogs for this location
  const {
    data: availableCatalogs,
    isLoading: catalogsLoading,
    error: catalogsError,
  } = useGetListHospitalityCatalogsLive({
    filter: { sellPointIds_inc: location.id, _d: false },
  });

  // Get available devices for order processing
  const {
    data: availableDevices,
    isLoading: devicesLoading,
    error: devicesError,
  } = useGetListDevicesLive({
    filter: { sellPointId: location.id },
    sort: { field: 'name', order: 'ASC' },
  });

  // Generate consistent colors for catalogs
  const catalogColors = useMemo(() => {
    if (!availableCatalogs) return {};
    return generateCatalogColors(availableCatalogs);
  }, [availableCatalogs]);

  // ========================================
  // Event Handlers
  // ========================================

  const handleActiveToggle = useCallback(
    (checked: boolean) => {
      if (locationSchedule) {
        updateLocationSchedule(location.id, {
          ...locationSchedule,
          active: checked,
        });
      }
    },
    [locationSchedule, updateLocationSchedule, location.id]
  );

  const handleDeviceChange = useCallback(
    (deviceId: string) => {
      if (locationSchedule) {
        updateLocationSchedule(location.id, {
          ...locationSchedule,
          orderProcessingDevice: deviceId,
        });
      }
    },
    [locationSchedule, updateLocationSchedule, location.id]
  );

  const openScheduleModal = useCallback(() => setScheduleModalOpen(true), []);
  const closeScheduleModal = useCallback(() => setScheduleModalOpen(false), []);

  const handleLocationChange = useCallback(
    (newLocation: { lat: number; lng: number }) => {
      if (locationSchedule) {
        updateLocationSchedule(location.id, {
          ...locationSchedule,
          location: newLocation,
        });
      }
    },
    [locationSchedule, updateLocationSchedule, location.id]
  );

  const handleRangeChange = useCallback(
    (newRange: number) => {
      if (locationSchedule) {
        updateLocationSchedule(location.id, {
          ...locationSchedule,
          orderingRange: newRange,
        });
      }
    },
    [locationSchedule, updateLocationSchedule, location.id]
  );

  const openMapsModal = useCallback(() => setMapsModalOpen(true), []);
  const closeMapsModal = useCallback(() => setMapsModalOpen(false), []);

  // ========================================
  // Helper Functions
  // ========================================

  const getCatalogName = useCallback(
    (catalogId: string) => {
      const catalog = availableCatalogs?.find(c => c.id === catalogId);
      return catalog?.name || 'Unknown Catalog';
    },
    [availableCatalogs]
  );

  // ========================================
  // Effect Hooks
  // ========================================

  // Initialize location if it doesn't exist or ensure all required properties are set
  useEffect(() => {
    const locationKey = location.id;

    if (!locationSchedule && !initializationRef.current.has(locationKey)) {
      initializationRef.current.add(locationKey);
      updateLocationSchedule(location.id, {
        schedule: DEFAULT_SCHEDULE,
        active: false,
        orderProcessingDevice: '',
        location: DEFAULT_LOCATION,
        orderingRange: DEFAULT_ORDERING_RANGE,
        logo: [],
        intro: [],
      });
    } else if (
      locationSchedule &&
      !initializationRef.current.has(locationKey + '_updated')
    ) {
      // Ensure all required properties are set for existing locations
      const updates: Partial<LocationSchedule> = {};

      if (locationSchedule.orderingRange === undefined) {
        updates.orderingRange = DEFAULT_ORDERING_RANGE;
      }

      if (locationSchedule.logo === undefined) {
        updates.logo = [];
      }

      if (locationSchedule.intro === undefined) {
        updates.intro = [];
      }

      if (Object.keys(updates).length > 0) {
        initializationRef.current.add(locationKey + '_updated');
        updateLocationSchedule(location.id, {
          ...locationSchedule,
          ...updates,
        });
      }
    }
  }, [location.id, locationSchedule]); // Removed updateLocationSchedule from dependencies

  // ========================================
  // Render Guards
  // ========================================

  if (!locationSchedule) {
    return (
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: theme.palette.mode === 'dark' ? '#222227' : 'white',
        }}
      >
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            {location.name}
          </Typography>
          <Typography color="text.secondary">Loading schedule...</Typography>
        </CardContent>
      </Card>
    );
  }

  // ========================================
  // Render
  // ========================================

  // Calculate the number of hours to display in the calendar
  const displayHours = maxEndHour - minStartHour;
  const hoursArray = Array.from(
    { length: displayHours },
    (_, i) => minStartHour + i
  );

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: theme.palette.mode === 'dark' ? '#222227' : 'white',
          opacity: locationSchedule?.active === false ? 0.5 : 1,
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          {/* Location Header with Toggle */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {location.name}
            </Typography>
            <Switch
              checked={locationSchedule?.active || false}
              onChange={e => handleActiveToggle(e.target.checked)}
              size="small"
            />
          </Box>

          {/* Media Section */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1, display: 'block', fontWeight: 500 }}
            >
              Media
            </Typography>

            {/* Logo and Intro Fields in one row */}
            <Box sx={{ display: 'flex', gap: 2, mb: 0 }}>
              {/* Logo Field */}
              <Box sx={{ flex: 1 }}>
                <MuiFileUploadInput
                  label="Logo"
                  value={locationSchedule?.logo || []}
                  onChange={files =>
                    updateLocationMedia(location.id, 'logo', files)
                  }
                  variant="compact"
                  multiple={false}
                  maxFiles={1}
                  maxSize={1 * 1024 * 1024} // 1MB
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
                  fileType="images"
                  enableImageEditor={true}
                  imageEditorConfig={{
                    outputFormat: 'webp',
                    outputQuality: 0.9,
                    minWidth: 40,
                    minHeight: 40,
                    maxWidth: 2000,
                    maxHeight: 2000,
                    targetSizes: standardSizesToTargetSizes(['ordernow_logo']),
                  }}
                  helperText="Upload a logo image (max 1MB)"
                  infoText="This logo will be displayed at the top of your ordering interface to help customers identify your business."
                  disabled={!locationSchedule?.active}
                  placeholder="Select logo image..."
                />
              </Box>

              {/* Intro Field */}
              <Box sx={{ flex: 1 }}>
                <MuiFileUploadInput
                  label="Intro"
                  value={locationSchedule?.intro || []}
                  onChange={files =>
                    updateLocationMedia(location.id, 'intro', files)
                  }
                  variant="compact"
                  multiple={true}
                  maxFiles={3}
                  maxSize={50 * 1024 * 1024} // 50MB fallback
                  acceptedTypes={[
                    'image/jpeg',
                    'image/png',
                    'image/webp',
                    'video/mp4',
                    'video/webm',
                    'video/ogg',
                  ]}
                  fileType="public"
                  customValidation={validateIntroMediaFile}
                  helperText="Upload up to 3 intro files (images max 1MB, videos max 25MB)"
                  infoText="Intro media will be shown to customers when they first open your ordering interface. Use this to showcase your best dishes or special offers."
                  disabled={!locationSchedule?.active}
                  placeholder="Select intro files..."
                />
              </Box>
            </Box>
          </Box>

          {/* Order Processing Device Selection */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1, display: 'block', fontWeight: 500 }}
            >
              Order Processing Device {locationSchedule?.active ? '*' : ''}
            </Typography>
            <Select
              value={locationSchedule?.orderProcessingDevice || ''}
              onChange={e => handleDeviceChange(e.target.value)}
              displayEmpty
              size="small"
              fullWidth
              disabled={devicesLoading || !locationSchedule?.active}
              required={locationSchedule?.active}
              error={
                locationSchedule?.active &&
                !locationSchedule?.orderProcessingDevice
              }
              sx={{
                '& .MuiSelect-select': {
                  fontSize: '0.875rem',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor:
                    locationSchedule?.active &&
                    !locationSchedule?.orderProcessingDevice
                      ? '#d32f2f'
                      : undefined,
                },
              }}
            >
              <MenuItem value="" disabled>
                <em>Select a device...</em>
              </MenuItem>
              {devicesLoading ? (
                <MenuItem disabled>Loading devices...</MenuItem>
              ) : devicesError ? (
                <MenuItem disabled>Error loading devices</MenuItem>
              ) : (
                availableDevices?.map(device => (
                  <MenuItem key={device.id} value={device.id}>
                    {device.name || device.id}
                  </MenuItem>
                ))
              )}
            </Select>
            {locationSchedule?.active &&
              !locationSchedule?.orderProcessingDevice && (
                <Typography
                  variant="caption"
                  color="error"
                  sx={{ mt: 0.5, display: 'block', fontSize: '0.75rem' }}
                >
                  Please select an order processing device
                </Typography>
              )}
          </Box>

          {/* Location Coordinates Input */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1, display: 'block', fontWeight: 500 }}
            >
              Ordering Location {locationSchedule?.active ? '*' : ''}
            </Typography>
            <LocationInput
              location={locationSchedule?.location || DEFAULT_LOCATION}
              onChange={handleLocationChange}
              onOpenMap={openMapsModal}
              disabled={!locationSchedule?.active}
              error={
                locationSchedule?.active &&
                !isValidLocation(locationSchedule?.location)
              }
              helperText={
                locationSchedule?.active &&
                !isValidLocation(locationSchedule?.location)
                  ? 'Please set a valid ordering location'
                  : undefined
              }
              label="Ordering Center Location"
            />
          </Box>

          {/* Ordering Range Input */}
          <Box sx={{ mb: 4 }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 1,
              }}
            >
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontWeight: 500 }}
              >
                Maximum Ordering Range {locationSchedule?.active ? '*' : ''}
              </Typography>
              <RangeInput
                value={
                  locationSchedule?.orderingRange ?? DEFAULT_ORDERING_RANGE
                }
                onChange={handleRangeChange}
                disabled={!locationSchedule?.active}
                error={
                  locationSchedule?.active &&
                  !isValidOrderingRange(locationSchedule?.orderingRange)
                }
                label=""
                min={0}
                max={200}
                showSlider={false}
              />
            </Box>
            <RangeInput
              value={locationSchedule?.orderingRange ?? DEFAULT_ORDERING_RANGE}
              onChange={handleRangeChange}
              disabled={!locationSchedule?.active}
              error={
                locationSchedule?.active &&
                ((!locationSchedule?.orderingRange &&
                  locationSchedule?.orderingRange !== 0) ||
                  (locationSchedule?.orderingRange !== 0 &&
                    locationSchedule?.orderingRange < 15))
              }
              helperText={
                locationSchedule?.active &&
                ((!locationSchedule?.orderingRange &&
                  locationSchedule?.orderingRange !== 0) ||
                  (locationSchedule?.orderingRange !== 0 &&
                    locationSchedule?.orderingRange < 15))
                  ? 'Please set a valid ordering range (0 for disabled or minimum 15m)'
                  : undefined
              }
              label=""
              min={0}
              max={200}
              showSlider={true}
              showChip={false}
            />
          </Box>

          {/* Ordering Schedule Section */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1, display: 'block', fontWeight: 500 }}
            >
              Ordering Schedule
            </Typography>

            {/* Business Hours Info */}
            {businessHours && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Business Hours: {businessHours.businessHoursSchedule.startAt}{' '}
                  - {businessHours.businessHoursSchedule.endAt}
                  {Object.keys(businessHours.businessHoursExceptions || {})
                    .length > 0 && ' (with exceptions)'}
                </Typography>
              </Box>
            )}

            {/* Schedule Overview & Calendar */}
            {catalogsLoading ? (
              <Box sx={{ my: 3, textAlign: 'center' }}>
                <Typography color="text.secondary" variant="body2">
                  Loading catalogs...
                </Typography>
              </Box>
            ) : catalogsError ? (
              <Box sx={{ my: 3, textAlign: 'center' }}>
                <Typography color="error" variant="body2">
                  Error loading catalogs
                </Typography>
              </Box>
            ) : usedCatalogs.length === 0 ? (
              <Box sx={{ my: 3, textAlign: 'center' }}>
                <Typography color="text.secondary" variant="body2">
                  No schedule configured yet
                </Typography>
              </Box>
            ) : !locationSchedule.active ? (
              <Box sx={{ my: 3, textAlign: 'center' }}>
                <Typography color="text.secondary" variant="body2">
                  Schedule configured but location is disabled
                </Typography>
              </Box>
            ) : (
              <Box sx={{ mb: 3 }}>
                {/* Calendar Weekly Overview */}
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: '40px 1fr 1fr 1fr 1fr 1fr 1fr 1fr',
                    border: theme =>
                      `1px solid ${theme.palette.mode === 'dark' ? theme.palette.divider : '#e0e0e0'}`,
                    borderRadius: 1,
                    overflow: 'hidden',
                    fontSize: '0.75rem',
                    width: '100%',
                    '& > *': {
                      borderRight: theme =>
                        `1px solid ${theme.palette.mode === 'dark' ? theme.palette.divider : '#e0e0e0'}`,
                      borderBottom: theme =>
                        `1px solid ${theme.palette.mode === 'dark' ? theme.palette.divider : '#e0e0e0'}`,
                      boxSizing: 'border-box',
                    },
                    '& > *:last-child': {
                      borderRight: 'none',
                    },
                    '& > *:nth-last-of-type(-n+8)': {
                      borderBottom: 'none',
                    },
                  }}
                >
                  {/* Calendar Header */}
                  <Box
                    sx={{
                      p: 1,
                      fontWeight: 'bold',
                      textAlign: 'center',
                      backgroundColor: theme =>
                        theme.palette.mode === 'dark'
                          ? theme.palette.background.paper
                          : '#f5f5f5',
                      gridColumn: '1 / 2',
                      gridRow: '1 / 2',
                    }}
                  >
                    h
                  </Box>
                  {dayLabels.map((day, index) => (
                    <Box
                      key={index}
                      sx={{
                        p: 1,
                        fontWeight: 'bold',
                        textAlign: 'center',
                        backgroundColor: theme =>
                          theme.palette.mode === 'dark'
                            ? theme.palette.background.paper
                            : '#f5f5f5',
                        gridColumn: `${index + 2} / ${index + 3}`,
                        gridRow: '1 / 2',
                      }}
                    >
                      {day}
                    </Box>
                  ))}

                  {/* Hour Labels */}
                  {hoursArray.map((hour, hourIndex) => {
                    // Convert hours beyond 24 to 24-hour format (e.g., 25 -> 01)
                    const displayHour = hour >= 24 ? hour - 24 : hour;
                    return (
                      <Box
                        key={`hour-label-${hour}`}
                        sx={{
                          p: 1,
                          textAlign: 'center',
                          backgroundColor: theme =>
                            theme.palette.mode === 'dark'
                              ? theme.palette.background.default
                              : '#f9f9f9',
                          fontSize: '0.7rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gridRow: `${hourIndex + 2} / ${hourIndex + 3}`,
                          gridColumn: '1 / 2',
                        }}
                      >
                        {displayHour.toString().padStart(2, '0')}
                      </Box>
                    );
                  })}

                  {/* Day Columns with Slots */}
                  {DAYS_OF_WEEK.map((day, dayIndex) => (
                    <Box
                      key={`day-column-${dayIndex}`}
                      sx={{
                        gridColumn: `${dayIndex + 2} / ${dayIndex + 3}`,
                        gridRow: `2 / ${displayHours + 2}`,
                        display: 'grid',
                        gridTemplateColumns: '1fr',
                        gridTemplateRows: `repeat(${displayHours}, 1fr)`,
                        position: 'relative',
                        gap: 0,
                        padding: 0,
                        margin: 0,
                        width: '100%',
                        height: '100%',
                        boxSizing: 'border-box',
                      }}
                    >
                      {/* Background cells for business hours */}
                      {hoursArray.map((hour, hourIndex) => {
                        const isBusinessHour = businessHours
                          ? isHourInBusinessHours(hour, dayIndex, businessHours)
                          : true;

                        const disabledBg =
                          theme.palette.mode === 'light'
                            ? theme.palette.custom?.gray400 || '#e0e0e0'
                            : theme.palette.background.tinted;

                        return (
                          <Box
                            key={`bg-${hour}`}
                            sx={{
                              gridRow: `${hourIndex + 1} / ${hourIndex + 2}`,
                              backgroundColor: isBusinessHour
                                ? 'transparent'
                                : theme =>
                                    theme.palette.mode === 'dark'
                                      ? theme.palette.background.default
                                      : '#f5f5f5',
                              backgroundImage: !isBusinessHour
                                ? `repeating-linear-gradient(135deg,transparent,transparent 1px,${disabledBg} 1px,${disabledBg} 5px,transparent 5px,transparent 6px,${disabledBg} 6px,${disabledBg} 10px)`
                                : 'none',
                              backgroundSize: '7px 7px',
                              opacity: isBusinessHour ? 1 : 0.3,
                            }}
                          />
                        );
                      })}

                      {/* Catalog time slots */}
                      {locationSchedule.schedule[dayIndex].timeSlots.map(
                        (slot, slotIndex) => {
                          const catalog = availableCatalogs?.find(
                            c => c.id === slot.catalogId
                          );

                          // Limit slot to business hours if business hours are defined
                          const { startHour, endHour, isVisible } =
                            businessHours
                              ? limitSlotToBusinessHours(
                                  slot,
                                  dayIndex,
                                  businessHours
                                )
                              : (() => {
                                  let slotStartMinutes = parseTime(
                                    slot.startAt
                                  );
                                  let slotEndMinutes = parseTime(slot.endAt);

                                  // Handle overnight slots when no business hours are defined
                                  if (slotEndMinutes <= slotStartMinutes) {
                                    slotEndMinutes += 24 * 60;
                                  }

                                  return {
                                    startHour: Math.floor(
                                      slotStartMinutes / 60
                                    ),
                                    endHour:
                                      slotEndMinutes > MINUTES_PER_DAY
                                        ? 24 +
                                          Math.ceil(
                                            (slotEndMinutes - MINUTES_PER_DAY) /
                                              60
                                          )
                                        : Math.ceil(slotEndMinutes / 60),
                                    isVisible: true,
                                  };
                                })();

                          if (!isVisible) return null;

                          // Adjust for display range
                          const displayStartHour = Math.max(
                            startHour,
                            minStartHour
                          );
                          const displayEndHour = Math.min(endHour, maxEndHour);

                          if (displayStartHour >= displayEndHour) return null;

                          const slotColor = catalog
                            ? catalogColors[catalog.id]
                            : { bg: '#ffebee', border: '#c62828' };

                          return (
                            <Tooltip
                              key={`slot-${dayIndex}-${slotIndex}`}
                              title={catalog?.name || 'Unknown Catalog'}
                              placement="top"
                              arrow
                            >
                              <Box
                                sx={{
                                  gridRow: `${displayStartHour - minStartHour + 1} / ${displayEndHour - minStartHour + 1}`,
                                  gridColumn: '1 / -1',
                                  backgroundColor: slotColor.bg,
                                  border: `2px solid ${slotColor.border}`,
                                  borderRadius: '4px',
                                  position: 'relative',
                                  zIndex: 1,
                                  cursor: 'pointer',
                                  margin: '1px',
                                  padding: '2px',
                                  width: 'calc(100% - 2px)',
                                  height: 'calc(100% - 2px)',
                                  boxSizing: 'border-box',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  minHeight: '16px',
                                  boxShadow:
                                    '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
                                  '&:hover': {
                                    opacity: 0.8,
                                    boxShadow:
                                      '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
                                    transform: 'translateY(-1px)',
                                  },
                                  '&:active': {
                                    opacity: 0.6,
                                    transform: 'translateY(0px)',
                                  },
                                  transition: 'all 0.2s ease-in-out',
                                }}
                              >
                                {!catalog && (
                                  <ValueFieldWithError
                                    value={null}
                                    errorMessage="Catalog not found"
                                  />
                                )}
                                {catalog && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      fontSize: '0.65rem',
                                      fontWeight: 500,
                                      color: slotColor.border,
                                      textAlign: 'center',
                                      lineHeight: 1.1,
                                      textOverflow: 'ellipsis',
                                      overflow: 'hidden',
                                      whiteSpace: 'nowrap',
                                      maxWidth: '100%',
                                      display: { xs: 'none', sm: 'block' }, // Hide on mobile
                                    }}
                                  >
                                    {catalog.name.length > 8
                                      ? `${catalog.name.substring(0, 8)}...`
                                      : catalog.name}
                                  </Typography>
                                )}
                              </Box>
                            </Tooltip>
                          );
                        }
                      )}

                      {/* Schedule gaps - periods during business hours without catalog coverage */}
                      {businessHours &&
                        findScheduleGaps(
                          dayIndex,
                          locationSchedule.schedule[dayIndex].timeSlots,
                          businessHours
                        ).map((gap, gapIndex) => {
                          // Adjust gap for display range
                          const displayStartHour = Math.max(
                            gap.startHour,
                            minStartHour
                          );
                          const displayEndHour = Math.min(
                            gap.endHour,
                            maxEndHour
                          );

                          if (displayStartHour >= displayEndHour) return null;

                          return (
                            <Tooltip
                              key={`gap-${dayIndex}-${gapIndex}`}
                              title="No catalog assigned for this time period"
                              placement="top"
                              arrow
                            >
                              <Box
                                sx={{
                                  gridRow: `${displayStartHour - minStartHour + 1} / ${displayEndHour - minStartHour + 1}`,
                                  gridColumn: '1 / -1',
                                  backgroundColor: theme =>
                                    theme.palette.mode === 'dark'
                                      ? '#3a2f2f'
                                      : '#ffebee',
                                  border: '2px solid #d32f2f',
                                  borderStyle: 'dashed',
                                  borderRadius: '4px',
                                  position: 'relative',
                                  zIndex: 1,
                                  margin: '1px',
                                  padding: '2px',
                                  width: 'calc(100% - 2px)',
                                  height: 'calc(100% - 2px)',
                                  boxSizing: 'border-box',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  minHeight: '16px',
                                  opacity: 0.7,
                                  backgroundImage: `repeating-linear-gradient(135deg, transparent, transparent 2px, rgba(211, 47, 47, 0.1) 2px, rgba(211, 47, 47, 0.1) 4px)`,
                                }}
                              >
                                <ValueFieldWithError
                                  value={null}
                                  errorMessage="No catalog assigned for this time period"
                                />
                              </Box>
                            </Tooltip>
                          );
                        })}
                    </Box>
                  ))}
                </Box>
              </Box>
            )}
          </Box>

          {/* Action Button */}
          <Button
            onClick={openScheduleModal}
            variant="contained"
            fullWidth
            size="large"
            disabled={
              !locationSchedule?.active ||
              catalogsLoading ||
              !locationSchedule?.orderProcessingDevice ||
              (locationSchedule?.location?.lat === DEFAULT_LOCATION.lat &&
                locationSchedule?.location?.lng === DEFAULT_LOCATION.lng)
            }
          >
            {catalogsLoading ? 'Loading...' : 'Edit Catalogs Schedule'}
          </Button>
        </CardContent>
      </Card>

      {/* Schedule Modal */}
      <DayScheduleModal
        open={scheduleModalOpen}
        onClose={closeScheduleModal}
        locationId={location.id}
        locationName={location.name}
        businessHours={businessHours}
      />

      {/* Google Maps Modal */}
      <GoogleMapsModal
        open={mapsModalOpen}
        onClose={closeMapsModal}
        location={locationSchedule?.location || DEFAULT_LOCATION}
        deliveryRange={
          locationSchedule?.orderingRange ?? DEFAULT_ORDERING_RANGE
        }
        onLocationChange={handleLocationChange}
        title={`Set Ordering Location for ${location.name}`}
      />
    </>
  );
};

// Locations Section Component
const LocationsSection = () => {
  const { t } = useTranslation('');

  const {
    data: locationsList,
    error: locationsError,
    isLoading: locationsLoading,
  } = useGetListLocationsLive({
    filter: { _d: false },
  });

  if (locationsLoading) {
    return (
      <Box>
        <Typography variant="h6">Locations</Typography>
        <Typography color="text.secondary" sx={{ py: 2 }}>
          Loading locations...
        </Typography>
      </Box>
    );
  }

  if (locationsError) {
    return (
      <Box>
        <Typography variant="h6">Locations</Typography>
        <Typography color="error" sx={{ py: 2 }}>
          Error loading locations: {locationsError.message}
        </Typography>
      </Box>
    );
  }

  if (!locationsList || locationsList.length === 0) {
    return (
      <Box>
        <Typography variant="h6">Locations</Typography>
        <Typography color="text.secondary" sx={{ py: 2 }}>
          No locations available
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Locations
      </Typography>
      <Grid container spacing={3}>
        {locationsList.map((location: Location) => (
          <Grid item xs={12} md={6} xl={4} key={location.id}>
            <LocationCard location={location} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

// Main Component
export const MyIntegrationsOrderNowCreateEdit = ({
  setOpen,
  partnerExtraData,
  mode = 'create',
}: MyIntegrationsOrderNowCreateEditProps) => {
  const { t } = useTranslation('');
  const resource = useResourceContext();
  const record = useRecordContext();
  const redirect = useRedirect();
  const { setValue, watch } = useFormContext<OrderNowFormValues>();

  const handleClose = () => {
    if (mode === 'create' && setOpen) {
      setOpen(false);
    } else {
      redirect('list', resource, record?.id, undefined, {
        _scrollToTop: false,
      });
    }
  };

  // Initialize form data when in edit mode
  useEffect(() => {
    if (mode === 'edit' && record?.sellPoints) {
      // Convert old format to new format if needed
      const convertedSellPoints: { [locationId: string]: LocationSchedule } =
        {};

      Object.entries(record.sellPoints).forEach(
        ([locationId, locationData]) => {
          const typedLocationData = locationData as any; // Type assertion for legacy data

          if (typedLocationData.schedule) {
            // New format - already using LocationSchedule
            convertedSellPoints[locationId] = {
              ...typedLocationData,
              active:
                typedLocationData.active !== undefined
                  ? typedLocationData.active
                  : true, // Default to true for existing data
            };
          } else {
            // Old format - convert from catalog-based to day-based
            const daySchedules: DaySchedule[] = Array(7)
              .fill(null)
              .map(() => ({ timeSlots: [] }));

            Object.entries(typedLocationData).forEach(
              ([catalogId, catalogData]: [string, any]) => {
                if (catalogData?.schedule) {
                  catalogData.schedule.forEach(
                    (daySlots: any[], dayIndex: number) => {
                      daySlots.forEach((slot: any) => {
                        daySchedules[dayIndex].timeSlots.push({
                          startAt: slot.startAt,
                          endAt: slot.endAt,
                          catalogId: catalogId,
                        });
                      });
                    }
                  );
                }
              }
            );

            convertedSellPoints[locationId] = {
              schedule: daySchedules,
              active: true, // Default to true for converted data
              location: DEFAULT_LOCATION, // Default location for converted data
              orderingRange: DEFAULT_ORDERING_RANGE, // Default range for converted data
            };
          }
        }
      );

      setValue('sellPoints', convertedSellPoints, { shouldDirty: true });
    }
  }, [mode, record, setValue]);

  const getTitle = () => {
    if (mode === 'create') {
      return `Create ${partnerExtraData?.name || 'OrderNow'} Integration`;
    }
    return `Edit ${record?.name || 'OrderNow'} Integration`;
  };

  return (
    <>
      <ModalHeader handleClose={handleClose} title={getTitle()}>
        <SaveButton type="submit" label={t('shared.save')} icon={<></>} />
      </ModalHeader>
      <Box sx={{ p: 3, pt: 2, width: '100%' }}>
        <LocationsSection />
      </Box>
    </>
  );
};
