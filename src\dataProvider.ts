import fakeDataProvider from 'ra-data-fakerest';

import { floorPlans } from './data/floor-plans';
import { groupsObject } from './data/groups';
import { itemLibraryObject } from './data/item-library';
import { menusData } from './data/menus';
import { modifiersObject } from './data/modifiers';
import { sellpoints as sellpointsData } from './data/sellpoints';

export const dataProvider = {
  ...fakeDataProvider({
    ...{
      'gift-cards-settings': [
        {
          id: 1,
          amount1: 10,
          amount2: 15,
          amount3: 20,
          amount4: 25,
        },

        {
          id: 2,
          amount1: 10,
          amount2: 15,
          amount3: 20,
          amount4: 25,
          minimumLoadAmount: 500,
          maximumLoadAmount: 1000,
        },

        {
          id: 3,
          eGiftCardPolicy: '',
          additionalPolicy: '',
        },
      ],
      'gift-cards-overview': [
        {
          balance: 50,
          type: 'Electronic',
          location: 'DABO DONER',
          lastUsed: 'Apr 24, 2023',
          status: 'Active',
          dateSent: 'Apr 24, 2023',
          firstLoad: 'Point of sale',
          recipient: '<EMAIL>',
          cardEndingIn: '4180',
          tags: ['Unused since activation'],
        },
        {
          balance: 20,
          type: 'Third Party',
          location: 'DABO KEBAP',
          lastUsed: 'Jan 24, 2023',
          status: 'Inactive',
          dateSent: 'Jan 24, 2023',
          firstLoad: 'Point of sale',
          recipient: '<EMAIL>',
          cardEndingIn: '5832',
          tags: ['Unused since activation'],
        },
        {
          balance: 50,
          type: 'Electronic',
          location: 'DABO DONER',
          lastUsed: 'Apr 24, 2023',
          status: 'Active',
          dateSent: 'Apr 24, 2023',
          firstLoad: 'Point of sale',
          recipient: '<EMAIL>',
          cardEndingIn: '4180',
          tags: ['Unused since activation'],
        },
        {
          balance: 20,
          type: 'Plastic',
          location: 'DABO KEBAP',
          lastUsed: 'Jan 24, 2023',
          status: 'Inactive',
          dateSent: 'Jan 24, 2023',
          firstLoad: 'Point of sale',
          recipient: '<EMAIL>',
          cardEndingIn: '5832',
          tags: ['Unused since activation'],
        },
        {
          balance: 20,
          type: 'Plastic',
          location: 'DABO KEBAP',
          lastUsed: 'Jan 24, 2023',
          status: 'Inactive',
          dateSent: 'Jan 24, 2023',
          firstLoad: 'Point of sale',
          recipient: '<EMAIL>',
          cardEndingIn: '5832',
          tags: ['Unused since activation'],
        },
      ],
      outageNotifications: [
        {
          id: 1,
          email: '',
          phone: { number: '', prefix: '+40' },
        },
      ],
      accountInfo: [
        {
          id: 1,
          email: '<EMAIL>',
          phone: { number: '*********', prefix: '+40' },
          password: 'secret1',
          businessName: "Let's burger",
          businessOwner: 'Gelu Bumbac',
        },
      ],
      receiptOptions: [
        {
          id: 'alwaysShow',
          name: 'Always show receipt screen',
          description:
            'Customers can choose if they want to receive digital or printed receipts.',
        },
        {
          id: 'skip',
          name: 'Skip receipt screen',
          description:
            'Select this when speed is important and customers rarely need receipts.',
        },
      ],
      openBillsOptions: [
        {
          id: 'bills',
          name: 'Return to bills',
          description:
            'After completing a sale you will return to the floor plans or list of bills',
        },
        {
          id: 'itemGrid',
          name: 'Return to item grid',
          description:
            'After completing a sale you will return to a New Sale and see your grid of items',
        },
      ],
      salesTaxes: [
        {
          taxName: 'TVA',
          status: 'Enabled',
          taxRate: '19%',
        },
        {
          taxName: 'TVA5',
          status: 'Enabled',
          taxRate: '5%',
        },
        ,
        {
          taxName: 'TVA9',
          status: 'Enabled',
          taxRate: '9%',
        },
      ],
      // sellpoints: [
      //   { id: 'W1HXNP7cHSCDfhskGxUd', name: 'Restaurant XYZ' },
      //   { id: 'dkjsalDJSAD41230', name: 'Restaurant Fictiv' },
      //   { id: 'dkjsalDJSAdsaD41230', name: 'Restaurant cu nume foarte lung' },
      // ],
      roles: [
        {
          id: 'V971FOAMdkVUOzXHE5Xn',
          name: 'Waiter',
          permissions: [2, 3, 4, 5, 6],
        },
        { id: '1tgMiFyQ7BNw36xaQHXQ', name: 'Owner', permissions: [0] },
      ],
      payTypes: [
        {
          id: '39281039123',
          name: 'Hourly',
        },
        { id: 'daskj1l2kj3l', name: 'Salary' },
      ],
      deviceTypes: [
        {
          label: 'Table Service',
          id: 'tableService',
        },
        {
          label: 'Counter service',
          id: 'pos',
        },
        {
          label: 'Bar or Lounge',
          id: 'barOrLounge',
        },
        {
          label: 'Other',
          id: 'other',
        },
      ],
      members: [
        {
          id: 'kwPqjIPO11NuWoIzSGApsvEblmc2',
          email: '<EMAIL>',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '1701',
          displayName: 'Flavia 1',
          firstName: 'Flavia',
          lastName: 'Popescu',
          phone: '+401234567890',
        },
        {
          id: '6t9vNpGhKKPh94sp7DEeywTzU533',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '7777',
          email: '<EMAIL>',
          displayName: 'Cosmin',
          firstName: 'Cosmin',
          lastName: 'Ionescu',
          phone: '+402345678901',
        },
        {
          id: '3cm5bvjRb5TLD0Edi3RfifNpmAE3',
          displayName: 'Andrei',
          email: '<EMAIL>',
          pin: '8888',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          firstName: 'Andrei',
          lastName: 'Georgescu',
          phone: '+403456789012',
        },
        {
          id: 'Iu8GmIpBCsRXWOx9GZiXbUeEbQJ2',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '1702',
          displayName: 'Flavia 2',
          email: '<EMAIL>',
          firstName: 'Flavia',
          lastName: 'Marinescu',
          phone: '+404567890123',
        },
        {
          id: 'eWLIGi89NGZLdmoBhbKBNfRtzX33',
          email: '<EMAIL>',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          displayName: 'Cristi 1',
          pin: '4321',
          firstName: 'Cristian',
          lastName: 'Vasilescu',
          phone: '+405678901234',
        },
        {
          id: 'Av6Q0KbbE8c4ekq8HKvxBmDGrR53',
          displayName: 'Mircea',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '1707',
          email: '<EMAIL>',
          firstName: 'Mircea',
          lastName: 'Dumitrescu',
          phone: '+406789012345',
        },
        {
          id: 'IgVO5Qzo6OWWAedvSLMAoJBkDL02',
          email: '<EMAIL>',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '1700',
          displayName: 'Ionut',
          firstName: 'Ionut',
          lastName: 'Popa',
          phone: '+407890123456',
        },
        {
          id: 'R2rd1AqonQfw4dL0rSscFUzKF483',
          email: '<EMAIL>',
          displayName: 'Cristi 2',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '1234',
          firstName: 'Cristian',
          lastName: 'Stanescu',
          phone: '+408901234567',
        },
        {
          id: 'oqoj2x0HZmb0GaFVJPZjaXF5Ca12',
          email: '<EMAIL>',
          displayName: 'Mihai',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          pin: '0000',
          firstName: 'Mihai',
          lastName: 'Ene',
          phone: '+409012345678',
        },
        {
          id: '60unalieRlRHDWyRYuZUyeiCo9c2',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          email: '<EMAIL>',
          pin: '5555',
          displayName: 'Bogdan',
          firstName: 'Bogdan',
          lastName: 'Niculae',
          phone: '+400123456789',
        },
        {
          id: 'JMWjERDaZ4Wtp3yxEEJNhbSBzCG3',
          email: '<EMAIL>',
          displayName: 'Dorin',
          pin: '9999',
          role: '1tgMiFyQ7BNw36xaQHXQ',
          firstName: 'Dorin',
          lastName: 'Pavel',
          phone: '+401234567890',
        },
      ],
      devices: [
        {
          id: 'CCCC-CCCC-CCCI',
          deviceType: 'pos',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          disabled: false,
          deviceUniqueId: '93caa1fa86123698',
          name: 'POS 5',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCG',
          deviceUniqueId: 'a998fbf69276397c',
          disabled: false,
          deviceType: 'pos',
          name: 'FLAVIA 2 POS',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          createdAt: '28/03/2023',
        },
        {
          id: 'AAAA-AAAA-AAAA',
          name: 'ANDREI',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          deviceUniqueId: '8b8d371c4aee4d7c',
          deviceType: 'pos',
          disabled: false,
          createdAt: '28/03/2023',
        },
        {
          id: '7H5G-TRE5-B78I',
          disabled: true,
          name: 'POS DISABLED',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCH',
          disabled: false,
          deviceUniqueId: 'cbf7e77f3455a290',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          name: 'MIRCEA POS',
          deviceType: 'pos',
          createdAt: '28/03/2023',
        },
        {
          id: 'D6F1-ZBX5-WZA6',
          disabled: false,
          name: 'POS SALON',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          deviceUniqueId: 'e70dc187e9163042',
          deviceType: 'pos',
          createdAt: '28/03/2023',
        },
        {
          id: 'DDDD-DDDD-DDDD',
          disabled: false,
          deviceUniqueId: '397b5a27d824bac7',
          deviceType: 'pos',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          name: 'DORIN',
          createdAt: '28/03/2023',
        },
        {
          id: 'BBBB-BBBB-BBBB',
          disabled: false,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          name: 'BOGDAN',
          createdAt: '28/03/2023',
        },
        {
          id: 'MMMM-MMMM-MMMM',
          name: 'MIHAI PHONE',
          deviceType: 'pos',
          disabled: false,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          deviceUniqueId: 'e8a0e6b2985650c6',
          createdAt: '28/03/2023',
        },
        {
          id: 'CMCM-CMCM-CMCM',
          name: 'COSMIN',
          deviceUniqueId: 'd5006fa95641d7df',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          deviceType: 'pos',
          disabled: false,
          createdAt: '28/03/2023',
        },
        {
          id: 'B2Z4-X5F7-Y029',
          disabled: false,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          name: 'POS NOT PAIRED',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCF',
          disabled: false,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          name: 'FLAVIA 1 POS',
          createdAt: '28/03/2023',
        },
        {
          id: '76HW-023H-HT56',
          name: 'POS PAIRED BUT DISABLED',
          deviceUniqueId: '1234567890',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          disabled: true,
          createdAt: '28/03/2023',
        },
        {
          id: 'MTMT-MTMT-MTMT',
          disabled: false,
          name: 'MIHAI 2',
          deviceType: 'pos',
          deviceUniqueId: 'eab526efbbbd94db',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCC',
          deviceUniqueId: '5fd7dfad07a3cc29',
          deviceType: 'pos',
          name: 'CRISTI PHONE',
          disabled: false,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCE',
          disabled: false,
          name: 'IONUT POS',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          deviceUniqueId: 'fb603970e7be1e48',
          deviceType: 'pos',
          createdAt: '28/03/2023',
        },
        {
          id: 'CCCC-CCCC-CCCD',
          disabled: false,
          deviceUniqueId: 'f3ffa3bd47375994',
          name: 'CRISTI TABLET',
          deviceType: 'pos',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          createdAt: '28/03/2023',
        },
      ],
      notifications: [
        // {
        //   id: '213123-123',
        //   unread: false,
        //   title: 'Curated for you',
        //   description: 'Lorem ipsum descriere de notificare',
        //   created_at: new Date(),
        //   href: 'https://www.google.com/',
        // },
        // {
        //   id: '213123-124',
        //   unread: true,
        //   title: 'Curated for you',
        //   description: 'Lorem ipsum descriere de notificare',
        //   created_at: new Date(),
        //   href: 'whttps://www.google.com/',
        // },
        // {
        //   id: '213123-125',
        //   unread: true,
        //   title: 'Curated for you',
        //   description: 'Lorem ipsum descriere de notificare',
        //   created_at: new Date(),
        //   href: 'https://www.google.com/',
        // },
      ],
      setupProgress: [
        {
          id: '3213-321',
          title: 'Setup kitchen',
          description: 'View your kitchen performance reports',
          href: 'https://www.google.com/',
          completed: false,
        },
        {
          id: '3213-3521',
          title: 'Items',
          description: 'Create an item',
          href: 'https://www.google.com/',
          completed: true,
        },
        {
          id: '3213-3231',
          title: 'Team',
          description: 'Add team members',
          href: 'https://www.google.com/',
          completed: true,
        },
        {
          id: '3213-3121',
          title: 'Activate your account',
          description:
            'Before we can accept payments, we need to verify your identity.',
          href: 'https://www.google.com/',
          completed: true,
        },
      ],
      categoriesStats: [
        {
          id: '858-3818',
          name: 'Racoritoare',
          sales: 3594,
        },
        {
          id: '858-38238',
          name: 'Pizza',
          sales: 3123.55,
        },
        {
          id: '858-3518',
          name: 'Desert',
          sales: 600,
        },
        {
          id: '858-3878',
          name: 'Bere',
          sales: 1906.4,
        },
      ],
      existingDisplayGroups: [
        {
          color: '#42f590',
          displayName: 'Combos',
          id: 'P1BgS3aEOnxxLCuU4jdp',
          type: 'displayGroup',
        },
        {
          color: '#00B8AC',
          displayName: 'Pizza',
          active: true,
          id: 'hjHCtKaWaVjgeFyywFRr',
          type: 'displayGroup',
        },
      ],
      functions: [
        {
          displayName: 'Courses',
          active: true,
          id: 'coursesOnOff',
          type: 'function',
        },
        {
          displayName: 'Split check',
          active: true,
          id: 'splitCheck',
          type: 'function',
        },
        {
          displayName: '$5 off',
          active: true,
          id: '5DollarsOff',
          type: 'function',
        },
      ],
      permissions: [
        {
          id: 'das321',
          name: 'Owner',
          access: ['0', '1', '2'],
          permissions: [1],
        },
        {
          id: 'das1321',
          name: 'Team Permissions',
          access: ['0', '1', '2'],
          permissions: [2, 3, 15],
        },
        {
          id: 'da2s1321',
          name: 'Appointments Manager',
          access: ['0'],
          permissions: [0],
        },
        {
          id: 'das1331221',
          name: 'Kitchen Staff',
          access: ['1'],
          permissions: [2, 3],
        },
      ],
      'pricing-subscriptions': [
        {
          id: 1,
          isPremium: false,

          card: '',
        },
      ],
      bills: [
        {
          id: 1,
          date: 'Jan 24, 2023',
          service: 'Selio for Restaurant Premium',
          'invoice No': '#12345',
          amount: 100,
        },
        {
          id: 2,
          date: 'Jan 24, 2023',
          'invoice No': '#12345',
          service: 'Selio for Restaurant Premium',
          amount: 100,
        },
      ],
      loyalty: [
        {
          rewardType: '0',
          'coupon-amount': '20',
          'units-needed': 50,
          customersEarn: 1,
          rewardExpiration: '7',
          units: {
            id: 0,
            title: 'Points',
            values: {
              singular: 'Point',
              plural: 'Points',
            },
          },
          id: 0,
        },
        {
          rewardType: '0',
          'coupon-amount': '40',
          'units-needed': 100,
          customersEarn: 1,
          rewardExpiration: '21',
          units: {
            id: 0,
            title: 'Points',
            values: {
              singular: 'Star',
              plural: 'Stars',
            },
          },
          id: 1,
        },
      ],
      menuBehaviour: [
        {
          id: 3213,
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          behaviour: 'return',
        },
        {
          id: 32313,
          sellPointId: 'dkjsalDJSAD41230',
          behaviour: 'stayInPlace',
        },
        {
          id: 2313,
          sellPointId: 'dkjsalDJSAdsaD41230',
          behaviour: 'return',
        },
      ],
      courses: [
        {
          id: 2313,
          name: 'Course 1',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          index: 0,
        },
        {
          id: 231133,
          name: 'Course 2',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          index: 1,
        },
        {
          id: 23513,
          name: 'Desert',
          sellPointId: 'W1HXNP7cHSCDfhskGxUd',
          index: 2,
        },
        {
          id: 23193,
          name: 'Course 1',
          sellPointId: 'dkjsalDJSAdsaD41230',
          index: 0,
        },
        {
          id: 2371133,
          name: 'Course 2',
          sellPointId: 'dkjsalDJSAdsaD41230',
          index: 1,
        },
        {
          id: 2631133,
          name: 'Course 3',
          sellPointId: 'dkjsalDJSAdsaD41230',
          index: 2,
        },
        {
          id: 2351513,
          name: 'Course 4',
          sellPointId: 'dkjsalDJSAdsaD41230',
          index: 3,
        },
      ],
      tips: [
        {
          id: 1,
          percentage: 10,
        },
        {
          id: 2,
          percentage: 15,
        },
        {
          id: 3,
          percentage: 20,
        },
      ],
    },
    bankAccounts: [
      {
        id: '1',
        name: 'Bank account 1',
      },
      {
        id: '2',
        name: 'Bank account 2',
      },
    ],
    languages: [
      {
        id: 'ro-RO',
        name: 'Romana',
      },
      {
        id: 'en-EN',
        name: 'English',
      },
    ],
    locationTypes: [
      {
        id: 'physical',
        name: 'Physical location',
      },
      {
        id: 'mobile',
        name: 'Mobile location',
      },
    ],
    customersEarn: [
      {
        id: 0,
        value: 1,
      },
      {
        id: 1,
        value: 2,
      },
      {
        id: 2,
        value: 3,
      },
    ],
    rewardExpiration: [
      {
        id: 0,
        value: '0',
      },
      {
        id: 21,
        value: '21',
      },
      {
        id: 7,
        value: '7',
      },
      {
        id: 1,
        value: '1',
      },
    ],
    rewardType: [
      {
        id: 0,
        value: 'Coupon Discount on entire sale',
      },
      {
        id: 2,
        value: 'Exemple 2',
      },
      {
        id: 1,
        value: 'Exemple 1',
      },
    ],
    ...menusData,
    ...floorPlans,
    ...sellpointsData,
    ...groupsObject,
    ...itemLibraryObject,
    ...modifiersObject,
  }),
  // Subscription methods (not implemented in dummy provider)
  subscribe: () => Promise.resolve({ data: null }),
  unsubscribe: () => Promise.resolve({ data: null }),
  publish: () => Promise.resolve({ data: null }),
};
