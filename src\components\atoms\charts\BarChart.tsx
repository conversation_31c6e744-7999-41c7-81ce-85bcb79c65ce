import { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';

import { useTheme } from '~/contexts';
import { formatNumber } from '~/utils/formatNumber';
import { DarkTooltip } from '../DarkTooltip';

type ChartItem = {
  value: number;
  label: string;
};

interface BarChartProps {
  chartData: Array<ChartItem>;
  chartHeight?: string;
  type: 'currency' | 'number' | 'percentage';
  withTable?: boolean;
}

export default function BarChart({
  chartData = [],
  chartHeight = '120px',
  type,
  withTable,
}: BarChartProps) {
  const { getChartColours } = useTheme();
  const chartColors = getChartColours(chartData.length);

  const [heights, setHeights] = useState<string[]>([]);

  useEffect(() => {
    const max = chartData
      .map(el => el.value)
      .reduce((a, b) => Math.max(a, b), -Infinity);

    const tmp = chartData.map(el =>
      formatNumber(1 / (max / el.value), 'percentage')
    );

    setHeights(tmp as string[]);
  }, [chartData]);

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        py: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          width: '100%',
          height: chartHeight,
          alignItems: 'flex-end',
        }}
      >
        {chartData?.map((el, index) => (
          <DarkTooltip
            key={index}
            text={formatNumber(el.value, type)}
            label={el.label}
          >
            <Box
              mr={0.5}
              sx={{
                flex: 1,
                height: heights[index],
                width: el,
                bgcolor: chartColors[index],
                borderRadius: '4px',
                border: '2px solid',
                borderColor: 'transparent',
                cursor: 'pointer',
                '&:hover': {
                  borderColor: 'black',
                },
              }}
            />
          </DarkTooltip>
        ))}
      </Box>

      {withTable && (
        <Box sx={{ mt: 3 }}>
          {chartData.map((el, index) => {
            return (
              <Box
                key={`${index}-${el.label}`}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
                mb={2}
              >
                <Box
                  sx={{
                    p: '7px',
                    borderRadius: '20%',
                    backgroundColor: chartColors[index],
                    mr: 2,
                  }}
                />
                {/* @ts-ignore */}
                <Typography variant="label" fontWeight={400}>
                  {el.label}
                </Typography>
                <Box sx={{ flex: 1 }} />
                {/* @ts-ignore */}
                {/* <Typography variant="label" fontWeight={300}>
                  {formatNumber(el.value, type)}
                </Typography> */}
              </Box>
            );
          })}
        </Box>
      )}
    </Box>
  );
}
