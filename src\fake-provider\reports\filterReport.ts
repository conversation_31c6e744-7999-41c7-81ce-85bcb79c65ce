import {
  OmitKeysWithTypeTransform,
  Report,
  ReportFilterCommonFieldsCriterion,
  ReportFilterSpecificFieldsCriterion,
  ReportType,
} from './types';

// filter the report based on the common fields criteria and the specific fields criteria
// the common fields criteria will be applied to the common fields of the report
// the specific fields criteria will be applied to the specific fields of the report inside the report field
// the report field is an array of objects where each object represents a row in the report
// so we need to remove the rows that do not match the criteria of the specific fields
// and remove the whole report item if the report field is empty
// after filtering the specific fields we need to filter the common fields
// if the common fields do not match the criteria we need to remove the whole report item
export function filterReport<K extends keyof ReportType>(
  reportType: K,
  rawReport: Array<OmitKeysWithTypeTransform<Report<K>>>,
  commonFieldsCriteria: Array<ReportFilterCommonFieldsCriterion<K>>,
  specificFieldsCriteria: Array<ReportFilterSpecificFieldsCriterion<K>>
): Array<OmitKeysWithTypeTransform<Report<K>>> {
  // check if the report is empty
  if (rawReport.length === 0) {
    return [];
  }
  // First, filter based on the common fields criteria
  const filteredByCommonFields = rawReport.filter(reportItem => {
    return commonFieldsCriteria.every(criterion => {
      const value = reportItem[criterion.field];
      if (typeof value === 'number' || typeof value === 'string') {
        return evaluateCriterion(value, criterion);
      } else {
        return false;
      }
    });
  });

  // If there are no specific fields criteria, return the filtered result
  if (specificFieldsCriteria.length === 0) {
    return filteredByCommonFields;
  }

  // Then, filter based on the specific fields criteria
  return filteredByCommonFields
    .map(reportItem => {
      const filteredReport = reportItem.report.filter(item => {
        return specificFieldsCriteria.every(criterion => {
          const value = item[criterion.field];
          if (typeof value === 'number' || typeof value === 'string') {
            return evaluateCriterion(value, criterion);
          } else {
            return false;
          }
        });
      });
      if (filteredReport.length === 0) {
        return null;
      }
      return {
        ...reportItem,
        report: filteredReport,
      };
    })
    .filter((reportItem): reportItem is Report<K> => reportItem !== null);
}

function evaluateCriterion<K extends keyof ReportType>(
  value: string | number,
  criterion:
    | ReportFilterCommonFieldsCriterion<K>
    | ReportFilterSpecificFieldsCriterion<K>
): boolean {
  switch (criterion.operator) {
    case '==':
      return criterion.value === value;
    case '!=':
      return criterion.value !== value;
    case '>':
      return (
        typeof value === 'number' &&
        typeof criterion.value === 'number' &&
        value > criterion.value
      );
    case '<':
      return (
        typeof value === 'number' &&
        typeof criterion.value === 'number' &&
        value < criterion.value
      );
    case '>=':
      return (
        typeof value === 'number' &&
        typeof criterion.value === 'number' &&
        value >= criterion.value
      );
    case '<=':
      return (
        typeof value === 'number' &&
        typeof criterion.value === 'number' &&
        value <= criterion.value
      );
    case 'in':
      return Array.isArray(criterion.value) && criterion.value.includes(value);
    case 'notIn':
      return Array.isArray(criterion.value) && !criterion.value.includes(value);
    case 'contains':
      return (
        typeof value === 'string' &&
        typeof criterion.value === 'string' &&
        value.includes(criterion.value)
      );
    case 'notContains':
      return (
        typeof value === 'string' &&
        typeof criterion.value === 'string' &&
        !value.includes(criterion.value)
      );
    case 'startsWith':
      return (
        typeof value === 'string' &&
        typeof criterion.value === 'string' &&
        value.startsWith(criterion.value)
      );
    case 'endsWith':
      return (
        typeof value === 'string' &&
        typeof criterion.value === 'string' &&
        value.endsWith(criterion.value)
      );
    default:
      return false;
  }
}
