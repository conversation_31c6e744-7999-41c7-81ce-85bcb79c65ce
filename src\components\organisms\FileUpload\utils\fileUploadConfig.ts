import { FileUploadConfig, ImageConfig, ValidationConfig, UIConfig, CallbackConfig } from '../types';
import { DEFAULT_FILE_UPLOAD_CONFIG, FILE_TYPE_CONFIGS } from '~/types/fileUpload';

/**
 * Default configurations for different file types
 */
export const DEFAULT_CONFIGS: Record<string, Partial<FileUploadConfig>> = {
    images: {
        fileType: 'images',
        multiple: false,
        maxFiles: 10,
        maxSize: 10 * 1024 * 1024, // 10MB
        acceptedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
        imageConfig: {
            autoGenerateThumbnail: true,
            quality: 0.9,
        },
        ui: {
            variant: 'default',
            dragAndDrop: true,
            showProgress: true,
            showValidationErrors: true,
            validationDisplay: 'both',
        },
    },
    videos: {
        fileType: 'videos',
        multiple: false,
        maxFiles: 5,
        maxSize: 100 * 1024 * 1024, // 100MB
        acceptedTypes: ['video/mp4', 'video/webm', 'video/mov', 'video/avi'],
        ui: {
            variant: 'default',
            dragAndDrop: true,
            showProgress: true,
            showValidationErrors: true,
            validationDisplay: 'both',
        },
    },
    public: {
        fileType: 'public',
        multiple: true,
        maxFiles: 20,
        maxSize: 50 * 1024 * 1024, // 50MB
        acceptedTypes: [
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        ui: {
            variant: 'default',
            dragAndDrop: true,
            showProgress: true,
            showValidationErrors: true,
            validationDisplay: 'both',
        },
    },
    private: {
        fileType: 'private',
        multiple: true,
        maxFiles: 50,
        maxSize: 50 * 1024 * 1024, // 50MB
        acceptedTypes: [
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
        ],
        ui: {
            variant: 'default',
            dragAndDrop: true,
            showProgress: true,
            showValidationErrors: true,
            validationDisplay: 'both',
        },
    },
};

/**
 * Merge user config with defaults
 */
export const mergeWithDefaults = (userConfig: Partial<FileUploadConfig>): FileUploadConfig => {
    const fileType = userConfig.fileType || 'images';
    const defaultConfig = DEFAULT_CONFIGS[fileType] || DEFAULT_CONFIGS.images;

    return {
        // Base file constraints
        fileType,
        multiple: userConfig.multiple ?? defaultConfig.multiple ?? false,
        maxFiles: userConfig.maxFiles ?? defaultConfig.maxFiles ?? 5,
        maxSize: userConfig.maxSize ?? defaultConfig.maxSize ?? (5 * 1024 * 1024),
        acceptedTypes: userConfig.acceptedTypes ?? defaultConfig.acceptedTypes ?? ['image/jpeg', 'image/png'],

        // Image config
        imageConfig: userConfig.imageConfig ? {
            autoGenerateThumbnail: userConfig.imageConfig.autoGenerateThumbnail ?? defaultConfig.imageConfig?.autoGenerateThumbnail ?? true,
            quality: userConfig.imageConfig.quality ?? defaultConfig.imageConfig?.quality ?? 0.9,
            targetSizes: userConfig.imageConfig.targetSizes ?? defaultConfig.imageConfig?.targetSizes,
            editorConfig: userConfig.imageConfig.editorConfig ?? defaultConfig.imageConfig?.editorConfig,
        } : defaultConfig.imageConfig,

        // Validation config
        validation: userConfig.validation ? {
            minFileCount: userConfig.validation.minFileCount,
            minFileSize: userConfig.validation.minFileSize,
            allowInvalidFiles: userConfig.validation.allowInvalidFiles ?? false,
            customValidation: userConfig.validation.customValidation,
        } : defaultConfig.validation,

        // UI config
        ui: userConfig.ui ? {
            variant: userConfig.ui.variant ?? defaultConfig.ui?.variant ?? 'default',
            showProgress: userConfig.ui.showProgress ?? defaultConfig.ui?.showProgress ?? true,
            disabled: userConfig.ui.disabled ?? defaultConfig.ui?.disabled ?? false,
            readOnly: userConfig.ui.readOnly ?? defaultConfig.ui?.readOnly ?? false,
            placeholder: userConfig.ui.placeholder ?? defaultConfig.ui?.placeholder,
            dragAndDrop: userConfig.ui.dragAndDrop ?? defaultConfig.ui?.dragAndDrop ?? true,
            showValidationErrors: userConfig.ui.showValidationErrors ?? defaultConfig.ui?.showValidationErrors ?? true,
            validationDisplay: userConfig.ui.validationDisplay ?? defaultConfig.ui?.validationDisplay ?? 'both',
        } : defaultConfig.ui,

        // Callbacks
        callbacks: userConfig.callbacks ?? defaultConfig.callbacks,

        // Private context
        privateContext: userConfig.privateContext ?? defaultConfig.privateContext,
    };
};

/**
 * Validate file upload configuration
 */
export const validateConfig = (config: FileUploadConfig): string[] => {
    const errors: string[] = [];

    // Validate basic constraints
    if (config.maxFiles <= 0) {
        errors.push('maxFiles must be greater than 0');
    }

    if (config.maxSize <= 0) {
        errors.push('maxSize must be greater than 0');
    }

    if (!config.acceptedTypes || config.acceptedTypes.length === 0) {
        errors.push('acceptedTypes must contain at least one MIME type');
    }

    // Validate file type
    if (!['images', 'videos', 'public', 'private'].includes(config.fileType)) {
        errors.push('fileType must be one of: images, videos, public, private');
    }

    // Validate image config
    if (config.imageConfig) {
        if (config.imageConfig.quality && (config.imageConfig.quality <= 0 || config.imageConfig.quality > 1)) {
            errors.push('imageConfig.quality must be between 0 and 1');
        }

        if (config.imageConfig.targetSizes) {
            config.imageConfig.targetSizes.forEach((size, index) => {
                if (size.width <= 0 || size.height <= 0) {
                    errors.push(`imageConfig.targetSizes[${index}] must have positive width and height`);
                }
                // Accept either 'key' or 'name' property for backward compatibility
                if (!size.key && !size.name) {
                    errors.push(`imageConfig.targetSizes[${index}] must have a key or name`);
                }
            });
        }
    }

    // Validate validation config
    if (config.validation) {
        if (config.validation.minFileCount && config.validation.minFileCount < 0) {
            errors.push('validation.minFileCount must be non-negative');
        }

        if (config.validation.minFileSize && config.validation.minFileSize < 0) {
            errors.push('validation.minFileSize must be non-negative');
        }
    }

    // Validate private context for private files
    if (config.fileType === 'private' && !config.privateContext) {
        errors.push('privateContext is required for private file type');
    }

    return errors;
};

/**
 * Create a configuration for common use cases
 */
export const createConfig = {
    /**
     * Configuration for item images with image editor
     */
    itemImages: (targetSizes?: ImageConfig['targetSizes']): FileUploadConfig => mergeWithDefaults({
        fileType: 'images',
        multiple: true,
        maxFiles: 10,
        imageConfig: {
            targetSizes,
            autoGenerateThumbnail: true,
            quality: 0.9,
        },
    }),

    /**
     * Configuration for user profile images
     */
    profileImage: (): FileUploadConfig => mergeWithDefaults({
        fileType: 'images',
        multiple: false,
        maxFiles: 1,
        imageConfig: {
            autoGenerateThumbnail: true,
            quality: 0.8,
            targetSizes: [
                { width: 150, height: 150, key: 'thumbnail', name: 'Thumbnail' },
                { width: 400, height: 400, key: 'profile', name: 'Profile' },
            ],
        },
    }),

    /**
     * Configuration for document uploads
     */
    documents: (): FileUploadConfig => mergeWithDefaults({
        fileType: 'public',
        multiple: true,
        maxFiles: 20,
    }),

    /**
     * Configuration for private documents
     */
    privateDocuments: (privateContext: any): FileUploadConfig => mergeWithDefaults({
        fileType: 'private',
        multiple: true,
        maxFiles: 50,
        privateContext,
    }),

    /**
     * Configuration for video uploads
     */
    videos: (): FileUploadConfig => mergeWithDefaults({
        fileType: 'videos',
        multiple: false,
        maxFiles: 5,
    }),
};
