import { useMemo } from 'react';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import RefreshIcon from '@mui/icons-material/Refresh';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import { Box, CircularProgress } from '@mui/material';

import { useFileUrl } from '~/hooks/useFileUrl';
import { UploadedFile } from '~/types/fileUpload';
import { isImageFile, isVideoFile } from '~/utils/fileUtils';

interface FilePreviewThumbnailProps {
  file: UploadedFile;
  size?: number;
  onClick?: () => void;
  disabled?: boolean;
  theme: any;
}

/**
 * Component for displaying file preview thumbnails with automatic URL management
 * and expiration handling via the useFileUrl hook. Supports both images and videos.
 */
export function FilePreviewThumbnail({
  file,
  size = 40,
  onClick,
  disabled = false,
  theme,
}: FilePreviewThumbnailProps) {
  // Memoize options to prevent unnecessary re-renders - only depend on actual file properties
  const options = useMemo(() => {
    if (!isImageFile(file)) return undefined;

    return { imageVariant: 'thumbnail' as const };
  }, [file.t, file.e, file.x]); // Include x flag to detect bucket transitions

  const { url, loading, error, refresh } = useFileUrl(file, options);
  const isImage = isImageFile(file);
  const isVideo = isVideoFile(file);

  if (loading) {
    return (
      <Box
        sx={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          backgroundColor: theme.palette.action.hover,
        }}
      >
        <CircularProgress size={size * 0.6} />
      </Box>
    );
  }

  if (error || !url) {
    return (
      <Box
        sx={{
          width: size,
          height: size,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${theme.palette.error.main}`,
          borderRadius: 1,
          cursor: 'pointer',
          backgroundColor: theme.palette.error.light,
          opacity: 0.7,
          '&:hover': {
            opacity: 1,
          },
        }}
        onClick={refresh}
        title={`Click to retry loading ${isVideo ? 'video' : 'image'}`}
      >
        <RefreshIcon fontSize="small" color="error" />
      </Box>
    );
  }

  // For images, show the actual image thumbnail
  if (isImage) {
    return (
      <Box
        component="img"
        src={url}
        alt={file.fn}
        onClick={onClick}
        sx={{
          width: size,
          height: size,
          borderRadius: 1,
          objectFit: 'cover',
          border: `1px solid ${theme.palette.divider}`,
          cursor: onClick && !disabled ? 'pointer' : 'default',
          opacity: disabled ? 0.6 : 1,
          transition: 'filter 0.2s ease-in-out',
          '&:hover':
            onClick && !disabled
              ? {
                  filter: 'brightness(0.8)',
                }
              : {},
        }}
      />
    );
  }

  // For videos, show an enhanced placeholder with play overlay
  if (isVideo) {
    return (
      <Box
        sx={{
          position: 'relative',
          width: size,
          height: size,
          borderRadius: 1,
          border: `1px solid ${theme.palette.divider}`,
          cursor: onClick && !disabled ? 'pointer' : 'default',
          opacity: disabled ? 0.6 : 1,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${theme.palette.primary.main}20 0%, ${theme.palette.secondary.main}20 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover':
            onClick && !disabled
              ? {
                  '& .video-overlay': {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  },
                  transform: 'scale(1.02)',
                }
              : {},
          transition: 'transform 0.2s ease-in-out',
        }}
        onClick={onClick}
      >
        {/* Video file icon background */}
        <VideoFileIcon
          sx={{
            color: theme.palette.text.secondary,
            fontSize: size * 0.4,
            opacity: 0.3,
            position: 'absolute',
          }}
        />

        {/* Play button overlay */}
        <Box
          className="video-overlay"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            transition: 'background-color 0.2s ease-in-out',
          }}
        >
          <PlayCircleOutlineIcon
            sx={{
              color: 'white',
              fontSize: size * 0.5,
              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.8))',
            }}
          />
        </Box>
      </Box>
    );
  }

  // For other file types, show a generic file icon
  return (
    <Box
      sx={{
        width: size,
        height: size,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 1,
        backgroundColor: theme.palette.action.hover,
        cursor: onClick && !disabled ? 'pointer' : 'default',
        opacity: disabled ? 0.6 : 1,
      }}
      onClick={onClick}
    >
      <VideoFileIcon
        sx={{
          color: theme.palette.text.secondary,
          fontSize: size * 0.6,
        }}
      />
    </Box>
  );
}
