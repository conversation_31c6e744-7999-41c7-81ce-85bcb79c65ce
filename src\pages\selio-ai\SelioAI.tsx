import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Typography,
} from '@mui/material';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
} from 'recharts';

const data = [
  { name: 'Gross', value: 13853.26 },
  { name: 'Net', value: 11663.25 },
  { name: 'Tips', value: 1076.35 },
  { name: 'Total', value: 12739.6 },
  { name: 'Comps', value: 6853.78 },
  { name: 'Voids', value: 1114 },
];

function RecommendationCard({
  recommendations,
}: {
  recommendations: string[];
}) {
  return (
    <Card
      sx={{
        '&:hover': {
          boxShadow: 3,
          transition: 'all 0.3s ease-in-out',
        },
      }}
    >
      <CardContent sx={{ py: 2 }}>
        <Typography variant="h6" gutterBottom>
          Recomandări AI
        </Typography>
        <Box
          component="ul"
          sx={{
            listStyle: 'none',
            pl: 0,
            '& > li': {
              color: 'text.secondary',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                pl: 2,
              },
            },
          }}
        >
          {recommendations.map((r, i) => (
            <Typography component="li" key={i}>
              {r}
            </Typography>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}

function StatsCard({ label, value }: { label: string; value: string }) {
  return (
    <Card
      sx={{
        '&:hover': {
          boxShadow: 2,
          transform: 'scale(1.02)',
          transition: 'all 0.3s ease-in-out',
        },
      }}
    >
      <CardContent sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {label}
        </Typography>
        <Typography variant="h6" color="text.primary">
          {value}
        </Typography>
      </CardContent>
    </Card>
  );
}

function ExportPDF() {
  return (
    <Button variant="contained" sx={{ mt: 2 }}>
      Generează PDF / Trimite pe Email
    </Button>
  );
}

export default function SelioAI() {
  return (
    <Box
      sx={{
        maxWidth: 'screen-xl',
        mx: 'auto',
        px: 4,
        py: 4,
        '& > *': { mb: 3 },
      }}
    >
      <Box>
        <Typography variant="h4" gutterBottom>
          Selio AI
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Pagina de analiză zilnică automată oferă o perspectivă completă asupra
          performanței operaționale. Include recomandări inteligente, date cheie
          și vizualizări interactive.
        </Typography>
      </Box>

      <RecommendationCard
        recommendations={[
          '🔒 Activează aprobarea pentru Comps peste 500 LEI.',
          '📉 Verifică Voids frecvente – cere motiv valid și notifică managerul.',
          '🕐 Închide automat Happy Hour în afara orelor programate.',
          '🔁 Nu permite stacking de reduceri (coupon + promo + gift card).',
          '🎯 Corelează sumele de tips din toate modulele.',
        ]}
      />

      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="Gross Sales" value="13.853,26 LEI" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="Net Sales" value="11.663,25 LEI" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="Tips" value="1.076,35 LEI" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="Total Collected" value="12.739,60 LEI" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="TVA" value="1.067,31 LEI" />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatsCard label="Comps" value="6.853,78 LEI" />
        </Grid>
      </Grid>

      <Card sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Grafic Vânzări & Anomalii
        </Typography>
        <Box sx={{ height: 300 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#65A65D" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </Box>
      </Card>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <ExportPDF />
      </Box>
    </Box>
  );
}
