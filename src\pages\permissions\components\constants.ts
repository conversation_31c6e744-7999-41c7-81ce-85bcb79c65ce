export enum PermissionPages {
  ORDERS = 'orders',
  CUSTOMERS = 'customers',
  GIFT_CARDS = 'giftCards',
  SETTINGS = 'settings',
}

export const permissionsIdToLabel: { [key: number]: string } = {
  0: 'owner',
  1: 'super',
  2: 'accessBillOwnedByOther',
  3: 'sellItems',
  4: 'takePayments',
  5: 'moveItems',
  6: 'assignBill',
  7: 'voidItems',
  8: 'compItems',
  9: 'printBill',
  10: 'applyDiscount',
  11: 'searchCustomer',
  12: 'editCustomerNote',
  13: 'addCustomerDetails',
  14: 'viewCustomerPersonalInfo',
  15: 'removeCustomerFromBill',
  16: 'createCustomer',
  17: 'redeemCustomerRewards',
  18: 'applyTips',
  19: 'sellPhysicalGiftCard',
  20: 'sellEGiftCard',
  21: 'topUpGiftCard',
  22: 'changeItemAvailability',
  23: 'viewClosedBillOwnedByOther',
  24: 'runCloseOfDay',
  25: 'accessFiscalOperations',
  26: 'manageHardware',
};

export const permissionsTree = {
  orders: [
    {
      groupLabel: 'orders',
      possiblePermissions: [2, 23, 22],
    },
    {
      groupLabel: 'payments',
      possiblePermissions: [3, 4, 9],
    },
    {
      groupLabel: 'actions',
      possiblePermissions: [5, 6, 7, 8],
    },
    {
      groupLabel: 'discountsAndTips',
      possiblePermissions: [10, 18],
    },
  ],
  customers: [
    {
      groupLabel: 'actions',
      possiblePermissions: [11, 16, 12, 13, 14, 15],
    },
    {
      groupLabel: 'rewards',
      possiblePermissions: [17],
    },
  ],
  giftCards: [
    {
      groupLabel: 'actions',
      possiblePermissions: [19, 20, 21],
    },
  ],
  settings: [
    {
      groupLabel: 'actions',
      possiblePermissions: [24, 25, 26],
    },
  ],
};
