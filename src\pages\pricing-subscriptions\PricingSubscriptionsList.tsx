import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import {
  Box,
  Button,
  Divider,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { EditableDatagrid } from '@react-admin/ra-editable-datagrid';
import { List, TextField, useCreatePath, useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import { useTheme } from '~/contexts';

export default function PricingSubscriptionsList() {
  const navigate = useNavigate();
  const { data: subscriptions } = useGetList('pricing-subscriptions');
  const createPath = useCreatePath();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mt: { xs: 0, sm: 3 },
        height: '100%',
      }}
    >
      <Box
        sx={{
          width: '100%',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1400px !important',
          height: '100%',
        }}
      >
        <Box sx={{ mb: 5 }}>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: '18px',

              pb: 3,
              width: 'fit-content',
              borderBottom: '2px solid black',
            }}
          >
            {t('pricingSubscriptions.subscriptions')}
          </Typography>
          <Divider />
          <Box
            sx={{
              display: 'flex',
              justifyContent: { lg: 'space-between', xs: 'flex-start' },
              gap: 4,
              p: { lg: 2, xs: 0 },
              pt: 2,
              mb: 10,
              flexDirection: { lg: 'row', xs: 'column' },
            }}
          >
            <Box sx={{ width: 'fit-content' }}>
              <Typography
                sx={{
                  fontSize: '18px',
                  color: 'primary.main',
                  fontWeight: 500,
                }}
              >
                Selio for Restaurants
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Typography fontSize={'14px'} color="textSecondary">
                  {t('pricingSubscriptions.youHave')}
                </Typography>
                <Typography fontSize={'14px'}>
                  {' '}
                  1 {t('shared.location', { context: 'lower' })} - Selio for Restaurants{' '}
                  {subscriptions && subscriptions[0].isPremium
                    ? 'Premium'
                    : 'Free'}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Typography fontSize={'14px'} color="textSecondary">
                  {t('pricingSubscriptions.youHave')}
                </Typography>
                <Typography fontSize={'14px'}>
                  1 {t('pricingSubscriptions.posDevice')}
                </Typography>
              </Box>
            </Box>
            <Box sx={{ width: 'fit-content' }}>
              <Typography fontSize={'14px'} sx={{ color: '#10C16B' }}>
                Selio for Restaurants{' '}
                {subscriptions && subscriptions[0].isPremium
                  ? 'Premium'
                  : 'Free'}
              </Typography>
              <Typography fontSize={'14px'}>
                {t('pricingSubscriptions.started')} January 03, 2024
              </Typography>
            </Box>
            <Box sx={{ width: 'fit-content' }}>
              <Typography
                sx={{
                  fontSize: '15px',
                  color: 'primary.main',
                  textAlign: { lg: 'end', xs: 'start' },
                  fontWeight: 500,
                  width: '100%',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  navigate(
                    createPath({
                      resource: 'pricing-subscriptions',
                      type: 'edit',
                      id: subscriptions && subscriptions[0]?.id,
                    })
                  );
                }}
              >
                {subscriptions && subscriptions[0].isPremium
                  ? 'Downgrade'
                  : 'Upgrade'}{' '}
                {t('pricingSubscriptions.to')}{' '}
                {subscriptions && !subscriptions[0].isPremium
                  ? 'Premium'
                  : 'Free'}{' '}
                plan
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Typography
                  fontSize={'14px'}
                  sx={{
                    textAlign: { lg: 'end', xs: 'start' },
                    width: '100%',
                  }}
                  color="textSecondary"
                >
                  {t('pricingSubscriptions.contactSelioSupport')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Typography
                  fontSize={'14px'}
                  sx={{ textAlign: { lg: 'end', xs: 'start' }, width: '100%' }}
                  color="textSecondary"
                >
                  Selio for Restaurants{' '}
                  {subscriptions && subscriptions[0].isPremium
                    ? 'Premium'
                    : 'Free'}
                </Typography>
              </Box>
            </Box>
          </Box>

          <List
            resource="bills"
            component="div"
            exporter={false}
            actions={false}
            empty={false}
            sx={{
              '& .RaFilterFormInput-spacer': {
                display: { xs: 'none', md: 'block' },
              },
            }}
          >
            {isXSmall ? (
              <MobileGrid>
                <MobileCard titleSource="date" actions={false}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body1" fontWeight={300} fontSize={14}>
                      {t('pricingSubscriptions.invoiceNo')}
                    </Typography>
                    <TextField source="invoice No" textAlign="right" />
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body1" fontWeight={300} fontSize={14}>
                      {t('pricingSubscriptions.service')}
                    </Typography>
                    <TextField source="service" textAlign="right" />
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body1" fontWeight={300} fontSize={14}>
                      {t('pricingSubscriptions.amount')}
                    </Typography>
                    <TextField source="amount" textAlign="right" />
                  </Box>
                </MobileCard>
              </MobileGrid>
            ) : (
              <EditableDatagrid
                editForm={<></>}
                bulkActionButtons={false}
                actions={<CustomActions />}
                sx={{
                  '& .MuiTableCell-root:last-of-type': {
                    textAlign: 'right',
                    '& button': {
                      visibility: 'visible',
                    },
                  },
                  '& .MuiTableCell-root:first-of-type': {
                    textAlign: 'left',
                    '& button': {
                      visibility: 'visible',
                    },
                  },
                  '& .MuiFormControl-root': {
                    margin: 0,
                    height: '30px',
                    textAlign: 'right',
                  },
                  '& .MuiTableCell-root': {
                    width: '33%',
                  },
                  '& .MuiTableHead-root .MuiTableCell-root': {
                    backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
                    borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
                  },
                }}
              >
                <TextField source="date" label={t('pricingSubscriptions.date')} />
                <TextField source="invoice No" label={t('pricingSubscriptions.invoiceNo')} textAlign="right" />
                <TextField source="service" label={t('pricingSubscriptions.service')} textAlign="right" />
                <TextField source="amount" label={t('pricingSubscriptions.amount')} textAlign="right" />
              </EditableDatagrid>
            )}
          </List>
        </Box>
      </Box>
    </Box>
  );
}

const CustomActions = () => (
  <Box sx={{ display: 'flex', flexWrap: 'nowrap', justifyContent: 'flex-end' }}>
    <Button sx={{ whiteSpace: 'nowrap', width: 'fit-content' }}>
      <MoreHorizIcon />
    </Button>
  </Box>
);
