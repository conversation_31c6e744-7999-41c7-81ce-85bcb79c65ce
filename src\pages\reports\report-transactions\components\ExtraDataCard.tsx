import React from 'react';
import { Box } from '@mui/material';

import { useTheme } from '~/contexts/ThemeContext';

export default function ExtraDataCard({ children }: React.PropsWithChildren) {
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        p: 2,
        bgcolor:
          theme.palette.mode === 'light' ? 'background.paper' : '#343439',
        borderRadius: 2,
        border:
          theme.palette.mode === 'light'
            ? '2px solid #D9D9D9'
            : '2px solidrgb(68, 68, 68)',
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
      }}
    >
      {children}
    </Box>
  );
}
