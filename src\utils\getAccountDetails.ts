import { FirebaseStorage, getBytes, ref } from 'firebase/storage';

export interface AccountDetails {
  company?: {
    name?: string;
  };
}

export const getAccountDetails = async (
  storage: FirebaseStorage,
  accountId: string
): Promise<string | null> => {
  try {
    const detailsPath = `accounts/${accountId}/details.json`;
    const fileRef = ref(storage, detailsPath);

    const bytes = await getBytes(fileRef);
    const jsonString = new TextDecoder().decode(bytes);
    const details: AccountDetails = JSON.parse(jsonString);

    return details.company?.name || null;
  } catch (error) {
    return null;
  }
};
