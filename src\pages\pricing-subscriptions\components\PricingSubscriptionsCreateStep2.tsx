import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import NavigateNextRoundedIcon from '@mui/icons-material/NavigateNextRounded';
import { Box, Button, Typography } from '@mui/material';

import Subsection from '../../../components/molecules/Subsection';

export default function PricingSubscriptionsCreateStep2({
  handleClose,
}: {
  handleClose: () => void;
}) {
  const downGradeFeatures = [
    'Multiple Floors and Tables',
    '24/7 Support',
    'Unlimited team management',
    'Advanced reporting',
    'KDS devices',
    'Advanced POS features',
  ];

  return (
    <>
      <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
        <img
          style={{ marginTop: '60px', width: '80%' }}
          src="/assets/pricing-subscriptions/hardware.png"
        />
      </Box>

      <Subsection
        title="By downgrading from Premium Plan, you’ll lose access to:"
        titleSx={{
          fontSize: '23px',
          mt: 6,
        }}
      >
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Typography>
            Keep these features and more with Selio for Restaurants Premium.
          </Typography>
          <Box
            sx={{
              cursor: 'pointer',
              color: '#0064F0',
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <a
              href="https://selio.io/support-center-center/"
              target="_blank"
              style={{ textDecoration: 'none' }}
            >
              <Typography>Learn more</Typography>
            </a>
            <NavigateNextRoundedIcon
              sx={{
                position: 'relative',
                left: -3,
                bottom: -1,
                fontSize: 20,
              }}
            />
          </Box>
        </Box>
      </Subsection>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: 3,
          mt: 4,
        }}
      >
        {downGradeFeatures.map((feature: string, index: number) => {
          return (
            <Box
              key={index}
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <CloseRoundedIcon sx={{ color: '#CC0023E5' }} />
              <Typography>{feature}</Typography>
            </Box>
          );
        })}
      </Box>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          backgroundColor: '#F2F2F2',
          justifyContent: 'flex-end',
          mb: 2,
          mt: 5,
          height: '1px',
        }}
      />
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-start',
        }}
      >
        <Button
          // @ts-ignore
          variant="contained-light"
          href="/pricing-subscriptions"
          sx={{ px: 3, width: 'fit-content' }}
          onClick={handleClose}
        >
          Keep premium plan
        </Button>
      </Box>
    </>
  );
}
