## How to run

After having cloned the repository, run the following commands at the react-admin root:

```sh
npm install

npm run dev
```

## Available Scripts

In the project directory, you can run:

### `npm run dev`

Runs the app in the development mode.<br>
Starts a local web server with Hot Module Replacement for development, and will automatically change when code changes.

### `npm run build`

Builds the app for production to the `dist` folder.<br>
It correctly bundles React in production mode and optimizes the build for the best performance.

### `npm run preview`

Starts a local web server that serves the built solution from `dist` folder for previewing.<br>

- You need to run build before preview.
- Preview will always preview the latest build, and will not update automatically when code changes.
