import { ReactElement } from 'react';
import { Box, SxProps, Theme, Typography } from '@mui/material';

interface PageTitleProps {
  title: string;
  description?: string | ReactElement;
  hideBorder?: boolean;
  sx?: SxProps<Theme>;
  doNotPrint?: boolean;
}
export default function PageTitle({
  title,
  description,
  hideBorder,
  doNotPrint = false,
  sx = {},
}: PageTitleProps) {
  return (
    <Box
      sx={{
        ...{
          borderBottom: hideBorder ? 'none' : '1px solid rgba(0,0,0,.1)',
          my: 2,
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        },
        ...sx,
      }}
    >
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="h3"
      >
        {title}
      </Typography>
      <Typography
        className={doNotPrint ? 'do-not-print' : ''}
        variant="subtitle2"
        mt={1}
      >
        {description}
      </Typography>
    </Box>
  );
}
