import { useEffect, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import { Box, Button, Typography } from '@mui/material';
import { ReferenceInput, SimpleForm, useDataProvider } from 'react-admin';

import CustomInput from '../../../components/atoms/inputs/CustomInput';
import PageTitle from '../../../components/molecules/PageTitle';
import Subsection from '../../../components/molecules/Subsection';

export default function GiftCardsSettings() {
  const [formData, setFormData] = useState<any>({
    plasticData: {},
    eGiftData: {},
    redemptionData: {},
  });
  const [initialState, setInitialState] = useState<any>({});
  const [editValues, setEditValues] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const dataProvider = useDataProvider();

  const fetchData = async () => {
    try {
      const [plasticResponse, eGiftResponse, redemptionResponse] =
        await Promise.all([
          dataProvider.getOne('gift-cards-settings', { id: '1' }),
          dataProvider.getOne('gift-cards-settings', { id: '2' }),
          dataProvider.getOne('gift-cards-settings', { id: '3' }),
        ]);

      setFormData({
        plasticData: plasticResponse.data,
        eGiftData: eGiftResponse.data,
        redemptionData: redemptionResponse.data,
      });
    } catch (error: any) {
      console.error(error.message);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dataProvider]);

  const updateAccountInfo = async (updatedInfo: any) => {
    try {
      await Promise.all([
        dataProvider.update('gift-cards-settings', {
          id: '1',
          data: {
            amount1: updatedInfo.plasticData.amount1,
            amount2: updatedInfo.plasticData.amount2,
            amount3: updatedInfo.plasticData.amount3,
            amount4: updatedInfo.plasticData.amount4,
          },
          previousData: {},
        }),
        dataProvider.update('gift-cards-settings', {
          id: '2',
          data: {
            amount1: updatedInfo.eGiftData.amount1,
            amount2: updatedInfo.eGiftData.amount2,
            amount3: updatedInfo.eGiftData.amount3,
            amount4: updatedInfo.eGiftData.amount4,
            minimumLoadAmount: updatedInfo.eGiftData.minimumLoadAmount,
            maximumLoadAmount: updatedInfo.eGiftData.maximumLoadAmount,
          },
          previousData: {},
        }),
        dataProvider.update('gift-cards-settings', {
          id: '3',
          data: {
            eGiftPolicy: updatedInfo.redemptionData.eGiftPolicy,
            additionalPolicy: updatedInfo.redemptionData.additionalPolicy,
          },
          previousData: {},
        }),
      ]);
    } catch (error: any) {
      console.error('Update failed:', error.message);
    }
  };

  const handleSave = async () => {
    await updateAccountInfo(formData);
    setEditValues(false);
    setHasUnsavedChanges(false);
  };

  const handleCancel = () => {
    setFormData(initialState);
    setEditValues(false);
    setHasUnsavedChanges(false);
  };

  const handleInputChange = () => {
    if (editValues) {
      setHasUnsavedChanges(true);
    }
  };

  const handleEdit = () => {
    setEditValues(true);
    setInitialState(formData);
  };

  const handleFormDataChange = (
    section: string,
    key: string,
    value: string
  ) => {
    setFormData((prevState: any) => ({
      ...prevState,
      [section]: {
        ...prevState[section],
        [key]: value,
      },
    }));
    handleInputChange();
  };

  return (
    <Box
      sx={{
        my: 2,
        mx: 'auto',
        maxWidth: '1400px',
        display: 'flex',
        flexDirection: 'column',
        gap: { xs: 2, sm: 3 },
      }}
    >
      {hasUnsavedChanges && (
        <Box
          sx={{
            backgroundColor: '#CCE1FF',
            padding: '10px',
            borderRadius: '4px',
            mb: { sm: 2 },
            pl: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'start',
          }}
        >
          <InfoIcon sx={{ color: '#015AD9', mr: 1 }} />
          <Typography variant="body1">You have unsaved changes.</Typography>
        </Box>
      )}

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          position: 'relative',
          flexWrap: 'wrap',
          borderBottom: '1px solid rgba(0,0,0,.1)',
        }}
      >
        <PageTitle
          hideBorder
          title="Gift Cards Settings"
          description="Effortlessly customize gift card amount to load on Selio for Restaurants app for every occasion. Delight customers with instant eGift card delivery or offer traditional gift cards for in-person gifting."
          sx={{ maxWidth: '700px' }}
        />
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            my: 2,
            position: { xs: 'absolute', sm: 'relative' },
            top: { xs: -15, sm: 'auto' },
            right: { xs: 0, sm: 'auto' },
          }}
        >
          {editValues && (
            <Button
              onClick={() => {
                if (hasUnsavedChanges) {
                  handleCancel();
                } else {
                  handleCancel();
                }
              }}
              sx={{
                zIndex: 10,
                height: '97%',
              }}
            >
              Cancel
            </Button>
          )}
          <Button
            onClick={() => {
              if (editValues) {
                handleSave();
              } else {
                handleEdit();
              }
            }}
            variant="contained"
            sx={{
              zIndex: 10,
              height: '97%',
            }}
          >
            {editValues ? 'Save' : 'Edit'}
          </Button>
        </Box>
      </Box>

      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <Box
          sx={{
            width: '100%',
            pt: { xs: 0, sm: 2 },
            display: 'flex',
            flexDirection: 'column',
            gap: 7,
          }}
        >
          <Box>
            <Subsection title="Plastic Gift Cards Amounts" />
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: {
                  xs: 'column',
                  md: 'row',
                },
              }}
            >
              <Box sx={{ width: '100%' }}>
                <ReferenceInput source="id1" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="Amount 1"
                    source="amount1"
                    disabled={!editValues}
                    value={formData.plasticData.amount1 || ''}
                    defaultValue={formData.plasticData.amount1 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'plasticData',
                        'amount1',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>

                <ReferenceInput source="id3" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="Amount 3"
                    source="amount3"
                    disabled={!editValues}
                    value={formData.plasticData.amount3 || ''}
                    defaultValue={formData.plasticData.amount3 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'plasticData',
                        'amount3',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
              </Box>
              <Box sx={{ width: '100%', mt: { xs: 2, md: 0 } }}>
                <ReferenceInput source="id2" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="Amount 2"
                    source="amount2"
                    disabled={!editValues}
                    value={formData.plasticData.amount2 || ''}
                    defaultValue={formData.plasticData.amount2 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'plasticData',
                        'amount2',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
                <ReferenceInput source="id4" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="Amount 4"
                    source="amount4"
                    disabled={!editValues}
                    value={formData.plasticData.amount4 || ''}
                    defaultValue={formData.plasticData.amount4 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'plasticData',
                        'amount4',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
              </Box>
            </Box>
          </Box>
          <Box>
            <Subsection title="eGift Cards" />
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: {
                  xs: 'column',
                  md: 'row',
                },
              }}
            >
              <Box sx={{ width: '100%' }}>
                <ReferenceInput source="id5" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="eGift Amount 1"
                    source="eGiftAmount1"
                    disabled={!editValues}
                    value={formData.eGiftData.amount1 || ''}
                    defaultValue={formData.eGiftData.amount1 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'eGiftData',
                        'amount1',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>

                <ReferenceInput source="id7" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="eGift Amount 3"
                    source="eGiftAmount3"
                    disabled={!editValues}
                    value={formData.eGiftData.amount3 || ''}
                    defaultValue={formData.eGiftData.amount3 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'eGiftData',
                        'amount3',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
              </Box>
              <Box sx={{ width: '100%', mt: { xs: 2, md: 0 } }}>
                <ReferenceInput source="id6" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="eGift Amount 2"
                    source="eGiftAmount2"
                    disabled={!editValues}
                    value={formData.eGiftData.amount2 || ''}
                    defaultValue={formData.eGiftData.amount2 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'eGiftData',
                        'amount2',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
                <ReferenceInput source="id8" reference="gift-cards-settings">
                  <CustomInput
                    type="text"
                    label="eGift Amount 4"
                    source="eGiftAmount4"
                    disabled={!editValues}
                    value={formData.eGiftData.amount4 || ''}
                    defaultValue={formData.eGiftData.amount4 || ''}
                    onChange={e => {
                      handleFormDataChange(
                        'eGiftData',
                        'amount4',
                        e.target.value
                      );
                    }}
                  />
                </ReferenceInput>
              </Box>
            </Box>
            <Box sx={{ mt: 2 }}>
              <ReferenceInput source="id9" reference="gift-cards-settings">
                <CustomInput
                  type="text"
                  label="Minimum Load Amount"
                  source="minimumLoadAmount"
                  disabled={!editValues}
                  value={formData.eGiftData.minimumLoadAmount || ''}
                  defaultValue={formData.eGiftData.minimumLoadAmount || ''}
                  onChange={e => {
                    handleFormDataChange(
                      'eGiftData',
                      'minimumLoadAmount',
                      e.target.value
                    );
                  }}
                />
              </ReferenceInput>
              <ReferenceInput source="id10" reference="gift-cards-settings">
                <CustomInput
                  type="text"
                  label="Maximum Load Amount"
                  source="maximumLoadAmount"
                  disabled={!editValues}
                  value={formData.eGiftData.maximumLoadAmount || ''}
                  defaultValue={formData.eGiftData.maximumLoadAmount || ''}
                  onChange={e => {
                    handleFormDataChange(
                      'eGiftData',
                      'maximumLoadAmount',
                      e.target.value
                    );
                  }}
                />
              </ReferenceInput>
            </Box>
          </Box>
          <Box>
            <Subsection title="Redemption Policy" />
            <ReferenceInput source="id11" reference="gift-cards-settings">
              <CustomInput
                type="text"
                label="eGift Policy"
                source="eGiftPolicy"
                disabled={!editValues}
                value={formData.redemptionData.eGiftPolicy || ''}
                defaultValue={formData.redemptionData.eGiftPolicy || ''}
                placeholder={
                  editValues
                    ? formData.redemptionData.eGiftPolicy
                    : 'Value does not expire.'
                }
                onChange={e => {
                  handleFormDataChange(
                    'redemptionData',
                    'eGiftPolicy',
                    e.target.value
                  );
                }}
              />
            </ReferenceInput>
            <ReferenceInput source="id12" reference="gift-cards-settings">
              <CustomInput
                type="text"
                label="Additional Policy"
                source="additionalPolicy"
                disabled={!editValues}
                value={formData.redemptionData.additionalPolicy || ''}
                defaultValue={formData.redemptionData.additionalPolicy || ''}
                placeholder={
                  editValues
                    ? formData.redemptionData.additionalPolicy
                    : 'Add business name, address, contact information, and any other information required by law or any additional policy specific to your gift card program here. Limited to 1000 characters'
                }
                onChange={e => {
                  handleFormDataChange(
                    'redemptionData',
                    'additionalPolicy',
                    e.target.value
                  );
                }}
              />
            </ReferenceInput>
          </Box>
        </Box>
      </SimpleForm>
    </Box>
  );
}
