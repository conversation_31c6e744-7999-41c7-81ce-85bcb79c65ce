import React, { useEffect, useState } from 'react';
import {
  Box,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { useLocaleState } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useTheme } from '../../contexts';

interface LanguageSelectorProps {
  sx?: object;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ sx }) => {
  const [locale, setLocale] = useLocaleState();

  const { i18n } = useTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);
  const { theme } = useTheme();
  const languages = [
    { text: i18n.t('preferences.english'), code: 'en' },
    { text: i18n.t('preferences.romanian'), code: 'ro' },
  ];

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
    setSelectedLanguage(language);
    setLocale(language);
  };

  useEffect(() => {
    setSelectedLanguage(i18n.language);
    setLocale(i18n.language);
  }, [i18n.language]);

  useEffect(() => {
    const savedLang = localStorage.getItem('language');
    if (savedLang && savedLang !== selectedLanguage) {
      setSelectedLanguage(savedLang);
      setLocale(savedLang);
    }
  }, []);

  const handleLanguageChange = (event: SelectChangeEvent<string>) => {
    changeLanguage(event.target.value as string);
  };

  return (
    <Box sx={{ minWidth: { xs: 300, md: 520 }, width: '97%', ...sx }}>
      <Select
        value={selectedLanguage}
        onChange={handleLanguageChange}
        displayEmpty
        autoWidth={false}
        renderValue={selected => (
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Box sx={{ width: '100%' }}>
              <Typography
                variant="body2"
                sx={{ fontSize: '14px', fontWeight: 500 }}
              >
                {i18n.t('preferences.language')}
              </Typography>
              {languages.map((language, index) => {
                if (language.code === selected) {
                  return (
                    <Typography
                      key={index}
                      variant="body2"
                      sx={{
                        fontSize: '15px',
                        fontWeight: 300,
                        mt: 0.5,
                      }}
                    >
                      {language.text}
                    </Typography>
                  );
                }
              })}
            </Box>
          </Box>
        )}
        sx={{
          '&.MuiInputBase-root': {
            width: '100% !important',
          },
          '.MuiSelect-select': {
            paddingTop: '8px',
            paddingBottom: '8px',
            display: 'flex',
            alignItems: 'center',
            width: '100% !important',
          },
        }}
      >
        {languages.map(language => (
          <MenuItem
            sx={{
              '&.Mui-selected': {
                backgroundColor:
                  theme.palette.mode === 'light'
                    ? '#e8f0ff !important'
                    : '#4f4f4f !important',
              },
            }}
            key={language.code}
            value={language.code}
          >
            {language.text}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

export default LanguageSelector;
