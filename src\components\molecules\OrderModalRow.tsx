import { Box, Typography } from '@mui/material';
import { NumberField } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useTheme } from '../../contexts';
import Pill from '../atoms/Pill';

export default function OrderModalRow({ el, name }: { el: any; name: string }) {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('');
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        borderBottom: theme =>
          theme.palette.mode === 'dark'
            ? '1px solid #515151'
            : '1px solid #F2F2F2',
        padding: '16px',
      }}
    >
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1.5fr',
          justifyContent: 'space-between',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'space-between',
            gap: '8px',
          }}
        >
          <Typography variant="body2" component="p" sx={{ paddingLeft: '7px' }}>
            {el.billName}
          </Typography>
          {el.customerName && (
            <Box
              sx={{
                width: '33px',
                height: '33px',
                backgroundColor: '#0064F0',
                borderRadius: '100px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                fontWeight: '500',
                fontSize: '14px',
                color: 'white',
                flexShrink: 0,
                boxSizing: 'border-box',
                overflow: 'hidden',
              }}
            >
              {getInitials(el.customerName)}
            </Box>
          )}
        </Box>

        <Box sx={{ textAlign: 'right', display: 'flex', flexDirection: 'column', alignItems: 'end', justifyContent: 'space-between' }}>
          <Typography component="p" sx={{ fontSize: '12px' }}>
            {name}
          </Typography>

          <Typography variant="subtitle2" component="p">
            {Math.floor(
              (Math.floor(new Date().getTime() / 1000) - el.openedAt) / 60 < 0
                ? 0
                : (Math.floor(new Date().getTime() / 1000) - el.openedAt) / 60
            )}
            m
          </Typography>
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            alignItems: 'end',
          }}
        >
          <NumberField
            sx={{
              paddingRight: '8px',
            }}
            defaultValue={el.value / 10000}
            source=""
            locales={'ro-RO'}
            options={{
              style: 'currency',
              currency: 'RON',
              currencyDisplay: 'narrowSymbol',
            }}
          />
          {el.paymentDue === 0 && el.value !== 0 ? (
            <Pill bgColor={theme.palette.mode === 'dark' ? 'white' : 'black'}>
              <Typography
                variant="body2"
                component="p"
                sx={{
                  color: theme.palette.mode === 'dark' ? 'black' : 'white',
                }}
              >
                PAID
              </Typography>
            </Pill>
          ) : (
            el.paymentDue !== el.value && (
              <Pill bgColor={theme.palette.mode === 'dark' ? 'white' : 'black'}>
                <NumberField
                  sx={{
                    color: theme.palette.mode === 'dark' ? 'black' : 'white',
                  }}
                  defaultValue={el.paymentDue / 10000}
                  source=""
                  locales={'ro-RO'}
                  options={{
                    style: 'currency',
                    currency: 'RON',
                    currencyDisplay: 'narrowSymbol',
                  }}
                />
              </Pill>
            )
          )}
        </Box>
      </Box>

      {el.note && (
        <Box>
          <Typography variant="subtitle2" component="p">
            {t('dashboard.orderNote')} {el.note}
          </Typography>
        </Box>
      )}
    </Box>
  );
}
