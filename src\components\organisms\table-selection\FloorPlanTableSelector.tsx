import { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';

import { BackgroundGrid } from '~/components/molecules/BackgroundGrid';
import {
  GRID_HEIGHT,
  GRID_SIZE,
  GRID_WIDTH,
} from '~/pages/floor-plans/SectionEdit';
import { Table } from '../table-dnd/types';
import QRCodeTable from './QRCodeTable';
import SelectableTable from './SelectableTable';

interface FloorPlanTableSelectorProps {
  floorPlan: any;
  selectedTables?: Set<string>;
  assignedTableKeys?: Set<string>;
  assignedTableToMember?: Map<string, string>; // New prop for table key to member name mapping
  onTableSelectionChange?: (
    tableKey: string,
    selected: boolean,
    tableData: any
  ) => void;
  // QR mode props
  mode?: 'selection' | 'qr';
  onViewQR?: (table: Table) => void;
}

export default function FloorPlanTableSelector({
  floorPlan,
  selectedTables = new Set(),
  assignedTableKeys = new Set(),
  assignedTableToMember = new Map(),
  onTableSelectionChange,
  mode = 'selection',
  onViewQR,
}: FloorPlanTableSelectorProps) {
  const [tables, setTables] = useState<Table[]>([]);

  useEffect(() => {
    setTables(floorPlan?.items || []);
  }, [floorPlan]);

  const generateTableKey = (table: Table) =>
    `${floorPlan.id}-${table.number}-${table.tag || ''}`;

  const generateAreaItemId = (floorPlanId: string, itemNumber: number) => {
    return `${floorPlanId}-${itemNumber}`;
  };

  const handleTableClick = (table: Table) => {
    if (mode === 'qr') return; // No click handling in QR mode

    const tableKey = generateTableKey(table);
    const isAssigned = assignedTableKeys.has(tableKey);

    // Don't allow selection of assigned tables
    if (isAssigned) return;

    const isSelected = selectedTables.has(tableKey);

    const tableData = {
      id: generateAreaItemId(floorPlan.id, table.number),
      floorPlanId: floorPlan.id,
      floorPlanName: floorPlan.name || '',
      floorPlanLabel: floorPlan.label || '',
      itemNumber: table.number,
      itemTag: table.tag || undefined,
    };

    onTableSelectionChange?.(tableKey, !isSelected, tableData);
  };

  const handleBackgroundClick = () => {
    // Prevent any background interactions - no event handling needed
  };

  const renderTable = (table: Table, index: number) => {
    if (mode === 'qr') {
      return (
        <QRCodeTable
          key={`qr-table-${index}`}
          table={table}
          floorPlanLabel={floorPlan.label || ''}
          onViewQR={() => onViewQR?.(table)}
        />
      );
    } else {
      const tableKey = generateTableKey(table);
      const isSelected = selectedTables.has(tableKey);
      const isAssigned = assignedTableKeys.has(tableKey);
      const assignedMemberName = assignedTableToMember.get(tableKey);

      return (
        <SelectableTable
          key={`selectable-table-${index}`}
          table={table}
          floorPlanLabel={floorPlan.label || ''}
          selected={isSelected}
          disabled={isAssigned}
          assignedMemberName={assignedMemberName}
          onClick={() => handleTableClick(table)}
        />
      );
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {tables.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body2" color="text.secondary">
            No tables found in this floor plan
          </Typography>
        </Box>
      ) : (
        <>
          <Box
            sx={{
              position: 'relative',
              width: 'fit-content',
              mx: 'auto',
              overflowX: 'auto',
              borderStyle: 'solid',
              borderWidth: '20px 60px 20px 20px',
              borderColor: 'black',
              borderRadius: '20px',
              zIndex: 1,
            }}
          >
            <Box
              sx={{
                position: 'relative',
                width: GRID_WIDTH * GRID_SIZE,
                height: GRID_HEIGHT * GRID_SIZE,
                overflow: 'hidden',
              }}
            >
              <BackgroundGrid
                tileWidth={GRID_SIZE}
                tileHeight={GRID_SIZE}
                onClick={handleBackgroundClick}
                style={{ borderRadius: 0 }}
              />

              {tables.map((table, index) => renderTable(table, index))}
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
}
