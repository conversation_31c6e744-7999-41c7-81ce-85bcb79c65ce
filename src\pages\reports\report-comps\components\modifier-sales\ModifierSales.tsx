import { useCallback, useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import {
  useGetListHospitalityCategoriesLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import cleanStringArond from '~/utils/cleanStringArond';
import { FieldOption } from '../../../../../../types/globals';
import ModifierSalesTable from './components/ModifierSalesTable';

const REPORT_TYPE = 'compedModifiers';
const fieldsConstant = [
  { isChecked: true, value: 'groupId' },
  { isChecked: true, value: 'id' },
  { isChecked: true, value: 'measureUnit' },
  { isChecked: true, value: 'variant' },
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'prepStation' },
  { isChecked: true, value: 'netValue' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'reason' },
];

export default function ModifierSales({
  updateCompsData,
  filters,
  commonFields,
}: {
  updateCompsData?: any;
  filters: any;
  commonFields: any;
}) {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [rawData, setRawData] = useState<any>();
  const { data: sellpoints } = useGetListLocationsLive();
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [groupingItems, setGroupingItems] = useState<string[]>(['reason']);
  const { sellPointId, dateRange } = useGlobalResourceFilters();
  // TODO! change to useGetListLive after imeplementation
  const { data: modifiersLibrary } = useGetList('modifiers');
  const { data: categories } = useGetListHospitalityCategoriesLive();

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }
      if (!dateRange[0] || !dateRange[1]) return;
      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { tableData, graphData, composedFilters } = useMemo(() => {
    if (!rawData || !filters)
      return { tableData: [], graphData: {}, composedFilters: {} };

    const appliedFilters = composeFilters(filters, REPORT_TYPE);

    const rawDataFiltered = filterReport(REPORT_TYPE, rawData, appliedFilters, [
      { field: 'variant', operator: 'notIn', value: ['no', 'allergy'] },
      { field: 'value', operator: '!=', value: 0 },
    ]);

    const highestValueProductIds = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      ['id']
    )[0]
      ?.report.sort((a, b) => b.value - a.value)
      ?.slice(0, 5)
      ?.map(el => el.id);

    const filteredByIds = filterReport(REPORT_TYPE, rawData, appliedFilters, [
      { field: 'id', operator: 'in', value: highestValueProductIds },
      { field: 'variant', operator: 'notIn', value: ['no', 'allergy'] },
      { field: 'value', operator: '!=', value: 0 },
    ]);

    const groupedByHour = groupReport(
      REPORT_TYPE,
      filteredByIds,
      ['hourOfDay'],
      ['id']
    );

    const labels = groupedByHour?.map(el => el.hourOfDay.toString());
    const datasets = highestValueProductIds?.map(id => ({
      label: id,
      data: [],
    }));

    groupedByHour.forEach(({ report: items }) => {
      datasets.forEach((ds: any) => {
        const item = items.find(i => i.id === ds.label);
        const itemsValue = item?.value || 0;
        const formattedValue = Math.round((itemsValue / 10000) * 10) / 10;
        ds.data.push(formattedValue);
      });
    });

    const computedGraphData = {
      datasets: datasets?.map(ds => ({
        ...ds,
        label: ds.label.substring(0, 10),
      })),
      labels,
    };

    const availableFields = tableFields.filter((field: any) =>
      reportSpecificFields.compedModifiers.some(
        f => cleanStringArond(field.value) === cleanStringArond(f)
      )
    );

    const rawGroupedData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      availableFields.map(item => item.value)
    );

    const hierarchicalGroupedData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        rawGroupedData,
        groupingItems as []
      )[0]?.report || [];

    return {
      tableData: hierarchicalGroupedData,
      graphData: computedGraphData,
      composedFilters: appliedFilters,
    };
  }, [filters, rawData, groupingItems, tableFields]);

  const [enrichedData, setEnrichedData] = useState<any[]>([]);

  const fetchEnrichedData = useCallback(() => {
    if (!tableData) return;

    const abortController = new AbortController();

    const collectLeafAndGroupIds = (
      items: any[]
    ): { leafIds: string[]; groupIds: Set<string> } => {
      let leafIds: string[] = [];
      let groupIds: Set<string> = new Set();

      for (const item of items) {
        if (!item.subReport || item.subReport.length === 0) {
          leafIds.push(item.id);
          groupIds.add(item.groupId);
        } else {
          groupIds.add(item.groupId);
          const { leafIds: childLeafIds, groupIds: childGroupIds } =
            collectLeafAndGroupIds(item.subReport);

          leafIds = [...leafIds, ...childLeafIds];
          childGroupIds.forEach(id => groupIds.add(id));
        }
      }
      return { leafIds, groupIds };
    };
    try {
      const { leafIds, groupIds } = collectLeafAndGroupIds(tableData);
      const itemNameMap: Record<string, string> = {};

      leafIds.map(id => {
        try {
          const modifierFound = modifiersLibrary?.find(item => item.id === id);
          itemNameMap[id] = modifierFound?.name;
        } catch (error) {
          console.error(`Failed to fetch item with id: ${id}`, error);
          itemNameMap[id] = id;
        }
      });
      const categoryNameMap: Record<string, string> = {};
      Array.from(groupIds).map(groupId => {
        try {
          const categoryFound = categories?.find(
            category => category.id === groupId
          );
          categoryNameMap[groupId] = categoryFound?.name;
        } catch (error) {
          console.error(`Failed to fetch category with id: ${groupId}`, error);
          categoryNameMap[groupId] = groupId;
        }
      });
      const enrichItems = (items: any[]): any[] => {
        return items.map(item => {
          if (item.groupedBy) {
            const groupField = item.groupedBy.field;
            const groupValue = item.groupedBy.value;
            return {
              ...item,
              name:
                groupField === 'groupId'
                  ? categoryNameMap[groupValue] || groupValue
                  : `${capitalize(groupField)}: ${groupValue}`,
              groupName:
                groupField === 'groupId' && item.groupedBy
                  ? categoryNameMap[groupValue] || groupValue
                  : '',
              subReport: item.subReport
                ? enrichItems(item.subReport)
                : undefined,
            };
          } else {
            const enrichedItem = {
              ...item,
              name: itemNameMap[item.id] || item.id,
              groupName: categoryNameMap[item.groupId] || item.groupId,
            };

            if (item.subReport && item.subReport.length > 0) {
              enrichedItem.subReport = enrichItems(item.subReport);
            }
            return enrichedItem;
          }
        });
      };

      const enriched = enrichItems(tableData);
      setEnrichedData(enriched);
    } catch (error: any) {
      if (error.name !== 'AbortError')
        console.error('Error fetching enriched data:', error);
    }
    return () => abortController.abort();
  }, [tableData, modifiersLibrary, categories]);

  useEffect(() => {
    fetchEnrichedData();
  }, [fetchEnrichedData]);

  const sortDataRecursively = (items: any[]): any[] => {
    if (!items || items.length === 0) return [];

    return items
      .map(item => ({
        ...item,
        subReport: item.subReport ? sortDataRecursively(item.subReport) : [],
      }))
      .sort((a, b) => {
        if (groupingItems.length > 0) {
          const quantityA = a.quantity ?? 0;
          const quantityB = b.quantity ?? 0;
          if (quantityA !== quantityB) return quantityB - quantityA;
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const finalTableData = useMemo(() => {
    const sortedData = sortData(enrichedData);
    return remapReports(sortedData, 'id');
  }, [enrichedData]);

  const onChangeGrouping = (items: string[]) => setGroupingItems(items);

  useEffect(() => {
    if (!tableData || (tableData && tableData.length === 0)) {
      updateCompsData('totalModifier', {});
    }
  }, [tableData]);

  const formattedFilters = useMemo(() => {
    if (!filters) return {};

    const getLabel = (field: string, value: string) => {
      const options = commonFields[field];
      const found = options?.find((opt: any) => opt.value === value);
      return found?.label || value;
    };

    return {
      sellpoint: filters.sellpointId,
      dateRange: filters.dateRange.map((d: any) => d?.format('YYYY-MM-DD')),
      timeRange: filters.timeRange,
      member: getLabel('member', filters.member ?? ''),
      floor: getLabel('floor', filters.floor ?? ''),
      serviceType: getLabel('serviceType', filters.diningOption),
      source: getLabel('sources', filters.source),
    };
  }, [filters, commonFields]);

  return (
    <Box sx={{ width: '100%' }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('giftCards.Comped Modifiers')}
        hideBorder
      />
      <ModifierSalesTable
        tableData={finalTableData || []}
        fields={tableFields}
        updateCompsData={updateCompsData}
        setFields={setTableFields}
        groupingItems={groupingItems}
        formattedFilters={formattedFilters}
        reportType={REPORT_TYPE}
        rawData={rawData}
        composedFilters={composedFilters}
        filters={filters}
        onChangeGrouping={onChangeGrouping}
      />
    </Box>
  );
}
