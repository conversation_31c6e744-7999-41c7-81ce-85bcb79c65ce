import React from 'react';
import { LocationOn, MyLocation } from '@mui/icons-material';
import {
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Tooltip,
} from '@mui/material';

import {
  formatCoordinates,
  getCurrentLocation,
  isValidCoordinates,
} from '../../utils/googleMapsUtils';

interface LocationInputProps {
  location: { lat: number; lng: number };
  onChange: (location: { lat: number; lng: number }) => void;
  onOpenMap?: () => void;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  label?: string;
}

export const LocationInput: React.FC<LocationInputProps> = ({
  location,
  onChange,
  onOpenMap,
  disabled = false,
  error = false,
  helperText,
  label = 'Location',
}) => {
  const [latValue, setLatValue] = React.useState(location.lat.toString());
  const [lngValue, setLngValue] = React.useState(location.lng.toString());
  const [latError, setLatError] = React.useState(false);
  const [lngError, setLngError] = React.useState(false);
  const [isGettingLocation, setIsGettingLocation] = React.useState(false);

  // Update local state when prop changes
  React.useEffect(() => {
    setLatValue(location.lat.toString());
    setLngValue(location.lng.toString());
  }, [location]);

  const validateAndUpdate = (lat: string, lng: string) => {
    const latNum = parseFloat(lat);
    const lngNum = parseFloat(lng);

    const isLatValid = !isNaN(latNum) && latNum >= -90 && latNum <= 90;
    const isLngValid = !isNaN(lngNum) && lngNum >= -180 && lngNum <= 180;

    setLatError(!isLatValid && lat !== '');
    setLngError(!isLngValid && lng !== '');

    if (isLatValid && isLngValid) {
      onChange({ lat: latNum, lng: lngNum });
    }
  };

  const handleLatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setLatValue(value);
    validateAndUpdate(value, lngValue);
  };

  const handleLngChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setLngValue(value);
    validateAndUpdate(latValue, value);
  };

  const handleGetCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      const currentLocation = await getCurrentLocation();
      onChange(currentLocation);
      setLatValue(currentLocation.lat.toString());
      setLngValue(currentLocation.lng.toString());
      setLatError(false);
      setLngError(false);
    } catch (error) {
      console.error('Error getting current location:', error);
      // You might want to show a snackbar or toast here
    } finally {
      setIsGettingLocation(false);
    }
  };

  const isValid = isValidCoordinates(location.lat, location.lng);
  const displayHelperText =
    helperText ||
    (isValid
      ? `Coordinates: ${formatCoordinates(location.lat, location.lng)}`
      : 'Enter valid coordinates');

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
      {/* Latitude Input - takes equal space */}
      <TextField
        label="Latitude"
        value={latValue}
        onChange={handleLatChange}
        disabled={disabled}
        error={error || latError}
        helperText={latError ? 'Must be between -90 and 90' : ''}
        placeholder="e.g. 44.4268"
        type="number"
        inputProps={{
          step: 'any',
          min: -90,
          max: 90,
        }}
        size="small"
        sx={{ flex: 1 }}
      />

      {/* Longitude Input - takes equal space */}
      <TextField
        label="Longitude"
        value={lngValue}
        onChange={handleLngChange}
        disabled={disabled}
        error={error || lngError}
        helperText={lngError ? 'Must be between -180 and 180' : ''}
        placeholder="e.g. 26.1025"
        type="number"
        inputProps={{
          step: 'any',
          min: -180,
          max: 180,
        }}
        size="small"
        sx={{ flex: 1 }}
      />

      {/* Icon buttons - fixed space on the right */}
      <Box
        sx={{
          display: 'flex',
          gap: 0.5,
          alignItems: 'center',
          mt: 2, // Align with input fields (accounting for label space)
        }}
      >
        <Tooltip
          title={
            isGettingLocation ? 'Getting location...' : 'Use current location'
          }
        >
          <span>
            <IconButton
              onClick={handleGetCurrentLocation}
              disabled={disabled || isGettingLocation}
              size="medium"
              sx={{
                p: 0.5,
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.dark',
                },
                '&:disabled': {
                  color: 'action.disabled',
                },
              }}
            >
              <MyLocation fontSize="medium" />
            </IconButton>
          </span>
        </Tooltip>

        {onOpenMap && (
          <Tooltip title="Select on map">
            <span>
              <IconButton
                onClick={onOpenMap}
                disabled={disabled}
                size="medium"
                sx={{
                  p: 0.5,
                  color: 'primary.main',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                    color: 'primary.dark',
                  },
                  '&:disabled': {
                    color: 'action.disabled',
                  },
                }}
              >
                <LocationOn fontSize="medium" />
              </IconButton>
            </span>
          </Tooltip>
        )}
      </Box>
    </Box>
  );
};
