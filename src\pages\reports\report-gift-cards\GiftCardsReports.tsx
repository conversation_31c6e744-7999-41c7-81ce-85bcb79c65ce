import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, capitalize } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import cleanStringArond from '~/utils/cleanStringArond';
import { FieldOption } from '../../../../types/globals';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import GiftCards from './components/GiftCards';
import GiftCardsReportsTable from './components/GiftCardsReportsTable';
import { downloadCSV } from 'react-admin';
import { useGetListLocationsLive } from '~/providers/resources';
import { useGetListLive } from '@react-admin/ra-realtime';

const REPORT_TYPE = 'giftCards';

const fieldsConstant = [
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'price' },
  { isChecked: true, value: 'type' },
  { isChecked: true, value: 'discountsValue' },
  { isChecked: true, value: 'couponsValue' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'value' },
  { isChecked: true, value: 'netValue' },
];

export default function GiftCardsReports() {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [filters, setFilters] = useState<ReportFiltersState>();
  const [rawData, setRawData] = useState<any>();
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [groupingItems, setGroupingItems] = useState<string[]>([]);

  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});

  const updateCommonField = (key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  };

  const contentRef = useRef<HTMLDivElement>(null);


  //TODO: asta nu mai e ok la dining si la source
  const defaultValues: ReportFiltersState = {
    dateRange,
    sellpointId: sellPointId,
    timeRange: {
      allDay: !timeRange,
      start: timeRange?.[0],
      end: timeRange?.[1],
    },
    diningOption: DiningOption.ALL,
    source: SourceOption.ALL,
  };

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.type || '').localeCompare(b.type || '');
      });
  };

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
        const commonFieldsValues = getReportCommonFieldsValues(
          REPORT_TYPE,
          data
        );

        const tmpMembers: OptionType[] = [];
        commonFieldsValues.member.forEach((el, index) => {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId[index],
          });
        });

        updateCommonField('member', tmpMembers);

        const tmpFloors: OptionType[] = [];
        commonFieldsValues.section.forEach(el => {
          tmpFloors.push({
            label: el,
            value: el,
          });
        });

        updateCommonField('floor', tmpFloors);

        const tmpServiceType: OptionType[] = [];
        commonFieldsValues.serviceType.forEach(el => {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        });

        updateCommonField('serviceType', tmpServiceType);

        const tmpSources: OptionType[] = [];
        commonFieldsValues.source.forEach(el => {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        });

        updateCommonField('sources', tmpSources);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { tableData, cardsData } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [], cardsData: [] };

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const cardsData = groupReport(REPORT_TYPE, rawDataFiltered, [], ['type'])[0]
      ?.report;

    const filteredFields = tableFields.filter((field: any) => {
      return (
        field.isChecked &&
        reportSpecificFields.giftCards.some(
          discountField =>
            cleanStringArond(field.value) === cleanStringArond(discountField)
        )
      );
    });

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      filteredFields.map((item: FieldOption) => item.value)
    );

    const groupedByItemsTableData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        groupedTableData,
        groupingItems as []
      )[0]?.report || [];

    const tableData = remapReports(
      sortData(groupedByItemsTableData) || [],
      'type'
    );

    return { tableData, cardsData };
  }, [filters, rawData, groupingItems, tableFields]);

  const onChangeGrouping = (items: []) => {
    setGroupingItems(items);
  };

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report gift cards';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Type',
        'VAT',
        'Price',
        'Quantity',
        'Gross Sales',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Sales',
      ].join(','),
      ...tableData.map(el =>
        [
          el.type === '@physical' ? 'Physical' : 'Digital',
          el.vat,
          el.price / 10000 || 0,
          el.quantity / 1000 || 0,
          el.value / 10000 || 0,
          -el.discountsValue / 10000 || 0,
          -el.couponsValue / 10000 || 0,
          el.promotionsValue / 10000 || 0,
          el.netValue / 10000 || 0,
        ].join(',')
      ),
      [
        'Total',
        '',
        tableData.reduce((acc, el) => acc + el.price, 0) / 10000,
        tableData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        tableData.reduce((acc, el) => acc + (el.value || 0) / 10000, 0),
        -tableData.reduce((acc, el) => acc + (el.discountsValue || 0) / 10000, 0),
        -tableData.reduce((acc, el) => acc + (el.couponsValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.promotionsValue || 0) / 10000, 0),
        tableData.reduce((acc, el) => acc + (el.netValue || 0) / 10000, 0),
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'giftCards');
  };

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('giftCards.title')}
        description={
          <>
            {t('giftCards.description')}
            <a href="https://selio.io/support-center" target="_blank">
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={setFilters}
        commonFields={commonFields}
        contentRef={contentRef}
        handleExport={handleExport}
      />
      <ReportDateTitle />
      <GiftCards tableData={cardsData || []} />
      <Box sx={{ pb: 3 }}>
        <GiftCardsReportsTable
          fields={tableFields || []}
          setFields={setTableFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          tableData={tableData || []}
        />
      </Box>
    </Box>
  );
}
