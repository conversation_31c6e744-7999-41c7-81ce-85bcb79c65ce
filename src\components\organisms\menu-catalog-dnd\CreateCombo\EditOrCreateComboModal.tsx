import { useMemo, useState } from 'react';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import { Box, Button, Dialog, Grid, TextField } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import MuiCustomInput from '~/components/atoms/inputs/MuiCustomInput';
import { menuColors } from '~/data/menu-colors';
import { useGetListVatsLive } from '~/providers/resources';
import { generateFirestoreId } from '~/utils/generateFirestoreId';
import getFullscreenModalProps from '../../../../utils/getFullscreenModalProps';
import HelpSuggestion from '../../../molecules/HelpSuggestion';
import ModalHeader from '../../../molecules/ModalHeader';
import Subsection from '../../../molecules/Subsection';
import ColorPicker from '../add-item-modal/ColorPicker';
import { Coordinates } from '../types';
import CreateComboAccordion from './CreateComboAccordion';

interface CreateComboModalProps {
  path: string;
  data: any;
  tileIndex: number | null;
  initialValues: any;
  create?: boolean;
  edit?: boolean;
  position?: Coordinates;
  onClose: () => void;
  displayGroupColor?: string;
}

export interface ComboStep {
  name: string;
  type: string;
  selection: {
    min: number;
    max: number;
  };
  items: { id: string; type: string }[];
}

interface ComboState {
  id?: string;
  price?: number;
  displayName?: string;
  vat?: number;
  measureUnit?: string;
  type?: string;
  steps: ComboStep[];
  active?: boolean;
  color?: string;
  image?: string;
}

const emptyStep: ComboStep = {
  name: '',
  type: 'required',
  selection: {
    min: 1,
    max: 9,
  },
  items: [],
};

const isPriceValid = (value: any) => {
  return (
    (typeof value === 'string' && value.trim() !== '') ||
    (typeof value === 'number' && !isNaN(value))
  );
};

export default function EditOrCreateComboModal({
  path,
  data,
  position,
  create = false,
  initialValues,
  edit = false,
  tileIndex,
  onClose,
  displayGroupColor,
}: CreateComboModalProps) {
  const { setValue } = useFormContext();
  const { t } = useTranslation();

  const [openStep, setOpenStep] = useState<number>(0);
  const [checkValidation, setCheckValidation] = useState(false);

  const isInDisplayGroup = useMemo(() => {
    return path.includes('items');
  }, [path]);

  const [comboState, setComboState] = useState<ComboState>(
    edit
      ? initialValues
      : {
          color: menuColors[0],
          price: undefined,
          displayName: '',
          measureUnit: 'BUC',
          type: 'productCombo',
          ...(isInDisplayGroup ? {} : { position }),
          active: true,
          steps: [emptyStep],
          vat: undefined,
        }
  );

  const updateValue = (field: string, value: unknown) => {
    setCheckValidation(false);
    setComboState(prev => {
      return { ...prev, ...{ [field]: value } };
    });
  };

  const handleClose = () => {
    onClose();
  };

  const createCombo = () => {
    if (
      !comboState.displayName?.trim() ||
      !comboState.price ||
      comboState.vat === undefined
    ) {
      setCheckValidation(true);
      return;
    }

    if (edit) {
      const newData = [...data];
      newData[tileIndex ?? 0] = comboState;
      setValue(path, newData);
    } else {
      const id = generateFirestoreId();
      setValue(path, [...data, { ...comboState, id }]);
    }

    onClose();
  };

  const addNewStep = () => {
    setComboState(prev => {
      return { ...prev, ...{ steps: [...prev.steps, emptyStep] } };
    });
    setOpenStep(comboState.steps.length);
  };

  const updateStepField = (
    stepIndex: number,
    fieldName: keyof ComboStep,
    value: any
  ) => {
    setComboState(prev => {
      const updatedSteps = [...prev.steps];
      updatedSteps[stepIndex] = {
        ...updatedSteps[stepIndex],
        [fieldName]: value,
      };
      return {
        ...prev,
        steps: updatedSteps,
      };
    });
  };

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      label: `${vat.value}%`,
    }));
  }, [vats]);

  return (
    <Dialog open onClose={handleClose} {...getFullscreenModalProps()}>
      <ModalHeader
        handleClose={handleClose}
        title={create ? t('menu.newComboProduct') : t('menu.editComboProduct')}
      >
        <Button variant="contained" onClick={createCombo}>
          {create ? t('shared.create') : t('shared.save')}
        </Button>
      </ModalHeader>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection title={t('menu.details')}>
          <Grid container pl={0} spacing={3}>
            <Grid item xs={12} sm={8}>
              <MuiCustomInput
                fullWidth
                label={t('menu.displayName')}
                value={comboState.displayName}
                onChange={ev => {
                  setComboState(prev => {
                    return { ...prev, ...{ displayName: ev.target.value } };
                  });
                }}
                error={checkValidation && !comboState.displayName}
              />
              <MuiCustomInput
                label={t('shared.price')}
                type="number"
                value={comboState.price ? comboState.price / 10000 : ''}
                onChange={event => {
                  const value = parseFloat(event.target.value);
                  updateValue('price', Math.round(value * 10000));
                }}
                error={checkValidation && !isPriceValid(comboState.price)}
              />
              <MuiCustomInput
                type="autocomplete-select"
                options={vatsChoices}
                getOptionLabel={option => option.label}
                label={t('shared.tva')}
                value={
                  vatsChoices.find(option => option.id == comboState.vat) ??
                  null
                }
                // @ts-ignore
                onChange={(_, newValue) => {
                  updateValue('vat', newValue?.id ?? 0);
                }}
                error={checkValidation && comboState.vat === undefined}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={t('menu.tvaTaxes')}
                    fullWidth
                    required
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ColorPicker
                color={displayGroupColor || comboState.color}
                setColor={color => updateValue('color', color)}
                disabledColorPicker={!!displayGroupColor || isInDisplayGroup}
              />
            </Grid>
          </Grid>
        </Subsection>

        <Subsection title={t('menu.steps')}>
          {comboState.steps.map((step: ComboStep, index: number) => {
            return (
              <CreateComboAccordion
                key={index}
                index={index + 1}
                step={step}
                isExpanded={openStep === index}
                setOpenStep={(open: boolean) => {
                  if (open) setOpenStep(index);
                  else setOpenStep(-1);
                }}
                updateStepField={(fieldName: keyof ComboStep, value: any) =>
                  updateStepField(index, fieldName, value)
                }
              />
            );
          })}
        </Subsection>

        <Button variant="outlined" sx={{ width: '100%' }} onClick={addNewStep}>
          {t('menu.addNewStep')}
        </Button>

        <HelpSuggestion>
          {/* @ts-ignore */}
          <Button variant="contained-light">
            <PlayCircleOutlineIcon sx={{ mr: 1 }} />
            {t('menu.learnAboutMenus')}
          </Button>
        </HelpSuggestion>
      </Box>
    </Dialog>
  );
}
