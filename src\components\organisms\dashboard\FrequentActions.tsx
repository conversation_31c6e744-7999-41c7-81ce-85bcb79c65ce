import { Box, Button, Theme, useMediaQuery } from '@mui/material';
import { Link } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';

export default function FrequentActions() {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { t } = useTranslation();

  const actions = [
    {
      route: '/transactions',
      label: `${t('dashboard.goToTransations')}`,
      dark: true,
    },
    {
      route: '/report-sales-revenue',
      label: `${t('dashboard.viewReports')}`,
    },
    {
      route: `/${RESOURCES.HOSPITALITY_CATALOGS}`,
      label: t('dashboard.editMenu'),
    },
    {
      route: `/${RESOURCES.PERMISSIONS}`,
      label: `${t('dashboard.viewPermissions')}`,
    },
  ];

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        flexDirection: isXSmall ? 'column' : 'row',
        gap: 1,
      }}
    >
      {actions?.map(action => (
        <Link
          key={action.label}
          sx={{ width: isXSmall ? '100%' : 'auto' }}
          to={action.route}
        >
          <Button
            key={action.label}
            // sx={{ mt: 1 }}
            sx={{ width: isXSmall ? '100%' : 'auto' }}
            /* @ts-ignore */
            variant={action.dark ? 'contained' : 'contained-light'}
          >
            {action.label}
          </Button>
        </Link>
      ))}
    </Box>
  );
}
