import MenuBookIcon from '@mui/icons-material/MenuBook';
import {
  Box,
  IconButton,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import {
  EditableDatagrid,
  EditRowButton,
  RowForm,
} from '@react-admin/ra-editable-datagrid';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  ArrayField,
  CreateButton,
  Datagrid,
  List,
  required,
  TextField,
  TextInput,
  useListContext,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import Pill from '~/components/atoms/Pill';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { useTheme } from '~/contexts';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import CustomDeleteWithConfirmIconButton from '../../components/molecules/CustomDeleteWithConfirmIconButton';
import extractItems from '../../utils/extractItemsFromObj';

const MenuListInner = () => {
  const redirect = useRedirect();
  const { data } = useListContext();
  const { t } = useTranslation('');
  const { theme } = useTheme();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <LocationAndDateSelectors isDate={false} hideShadow />
        <CreateButton
          variant="contained"
          label={t('menu.createMenu')}
          {...(isXSmall ? {} : { icon: <></> })}
        />
      </Box>
      {isXSmall ? (
        <MobileGrid>
          <MobileCard cardClick="edit" actions={true}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('shared.location_few')}
              </Typography>
              <ArrayField
                source="sellPointIds"
                label={t('shared.location_few')}
                textAlign="right"
              >
                <LocationsField />
              </ArrayField>
            </Box>

            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="body1"
                fontWeight={300}
                fontSize={14}
                sx={{ textTransform: 'capitalize' }}
              >
                {t('shared.items')}
              </Typography>
              <ArrayField
                source="pages"
                label={t('shared.items')}
                textAlign="right"
              >
                <ProductsNumberField />
              </ArrayField>
            </Box>
          </MobileCard>
        </MobileGrid>
      ) : (
        <Datagrid
          rowClick={(id: any) => {
            const row = data?.find(el => el.id === id);
            if (row.pages.length === 1 && row.pages[0].length === 0) {
              return 'show';
            }
            redirect('edit', RESOURCES.HOSPITALITY_CATALOGS, id, undefined, {
              _scrollToTop: false,
            });
            return false;
          }}
          bulkActionButtons={false}
          // mutationMode="optimistic"
          // editForm={<MenuForm />}
          // actions={<CustomActions />}
          sx={{
            '& .MuiTableCell-root:last-of-type': {
              textAlign: 'right',
              '& button': {
                visibility: 'visible',
              },
            },
            '& .MuiFormControl-root': {
              margin: 0,
              height: '30px',
            },
            '& .MuiTableCell-root': {
              width: '25%',
            },
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
        >
          <TextField source="name" label={t('shared.name')} />
          <ArrayField
            source="sellPointIds"
            label={t('shared.location_few')}
            textAlign="right"
          >
            <LocationsField />
          </ArrayField>
          <ArrayField
            source="pages"
            label={t('shared.items', { context: 'capitalize' })}
            textAlign="right"
          >
            <ProductsNumberField />
          </ArrayField>
          <ActionsField
            label={t('permissions.groupTitles.actions')}
            hasEdit
            hasDelete
            deleteMutationMode="pessimistic"
            deleteMutationOptions={{}}
          />
        </Datagrid>
      )}
    </>
  );
};

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ padding: '48px 0', textAlign: 'center' }}>
      <LocationAndDateSelectors isDate={false} hideShadow />
      <MenuBookIcon sx={{ fontSize: 40 }} />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('menu.noMenusYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('menu.noMenusYetDescription')}
      </Typography>
      <CreateButton variant="contained" label={t('menu.createMenu')} />
    </div>
  );
};

export default function MenuList() {
  const { sellPointId } = useGlobalResourceFilters();

  return (
    <Box>
      <List
        resource={RESOURCES.HOSPITALITY_CATALOGS}
        sort={resourcesInfo[RESOURCES.HOSPITALITY_CATALOGS].defaultSort}
        perPage={Number.MAX_SAFE_INTEGER}
        component="div"
        exporter={false}
        pagination={false}
        empty={<CustomEmpty />}
        actions={false}
        filter={{ sellPointIds_inc: sellPointId, _d: false }}
      >
        <MenuListInner />
        <ListLiveUpdate />
      </List>
    </Box>
  );
}

// NUMARA DOAR PRODUSELE DE PE PRIMUL NIVEL DIN OBIECT
const ProductsNumberField = () => {
  const { data } = useListContext();
  const { length } = extractItems(data!, 'product');
  const { t } = useTranslation('');
  const { theme } = useTheme();

  return (
    <Pill sx={{ marginLeft: 'auto' }}>
      {length ? (
        <>
          <Typography variant="body2">{length}</Typography>
          <img
            src="/assets/icons/food.svg"
            width="17px"
            style={{
              filter: theme.palette.mode === 'dark' ? 'invert(1)' : 'none',
            }}
          />
        </>
      ) : (
        <Typography variant="body2" color="error">
          {t('menu.noItems')}
        </Typography>
      )}
    </Pill>
  );
};

const LocationsField = () => {
  const { data } = useListContext();
  const { theme } = useTheme();

  return (
    <Pill sx={{ marginLeft: 'auto' }}>
      <Typography variant="body2">{data?.length}</Typography>
      <img
        src="/assets/icons/storefront.svg"
        width="17px"
        style={{ filter: theme.palette.mode === 'dark' ? 'invert(1)' : 'none' }}
      />
    </Pill>
  );
};

const MenuForm = () => {
  const { t } = useTranslation('');
  return (
    <RowForm>
      <TextInput label={t('shared.name')} source="name" validate={required()} />
      <ArrayField source="sellPointIds" label="Locations" textAlign="right">
        <LocationsField />
      </ArrayField>
      <ArrayField source="pages" label="Items" textAlign="right">
        <ProductsNumberField />
      </ArrayField>
    </RowForm>
  );
};

const CustomActions = () => {
  const { t } = useTranslation();
  return (
    <Box
      sx={{ display: 'flex', flexWrap: 'nowrap', justifyContent: 'flex-end' }}
    >
      <EditRowButton label="Edit" />
      <IconButton disabled>
        <img src="/assets/icons/clock.svg" width="17px" />
      </IconButton>
      <CustomDeleteWithConfirmIconButton
        field="name"
        confirmContent={t('menu.deleteConfirmation')}
      />
    </Box>
  );
};
