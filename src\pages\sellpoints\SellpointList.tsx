import { Fragment } from 'react';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  BulkDeleteWithConfirmButton,
  CreateButton,
  Datagrid,
  FunctionField,
  List,
  TextField,
  TopToolbar,
  useGetList,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import { useTheme } from '~/contexts';
import { RESOURCES } from '~/providers/resources';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { resourcesInfo } from '../../providers/resources';

const filters = [
  <CustomSearchInput
    placeholder="Filter Sellpoints"
    key="search-input"
    source="q"
    alwaysOn
  />,
];

const PostListActions = () => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation();

  return (
    <TopToolbar>
      <CreateButton
        variant="contained"
        label={t('sellPointsPage.addSellpoint')}
        {...(isXSmall ? {} : { icon: <></> })}
      />
    </TopToolbar>
  );
};

const AssetBulkActionButtons = (props: any) => (
  <Fragment>
    <BulkDeleteWithConfirmButton {...props} />
  </Fragment>
);

export const SellpointList = () => {
  const params = useParams<'id'>();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  const postRowStyle = (record: any) => {
    const id = (params as any)['*'].split('/')[0];

    if (id == record.id)
      return {
        background: theme.palette.primary.main,
        color: 'white',
      };
    return {};
  };

  return (
    <List
      resource={RESOURCES.LOCATIONS}
      sort={resourcesInfo[RESOURCES.LOCATIONS].defaultSort}
      filter={{ _d: false }}
      component="div"
      filters={filters}
      actions={<PostListActions />}
      sx={{
        '& .RaFilterFormInput-spacer': {
          display: { xs: 'none', md: 'block' },
        },
      }}
    >
      {isXSmall ? (
        <MobileGrid>
          <MobileCard actions={true} cardClick="edit">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('giftCards.type')}
              </Typography>
              <FunctionField
                source="type"
                render={(record: any) =>
                  record.type[0].toUpperCase() + record.type.slice(1)
                }
              />
            </Box>

            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('sellPointsPage.address')}
              </Typography>
              <FunctionField
                source="address"
                render={(record: any) =>
                  `${record.address.line1}, ${record.address.city}`
                }
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                Email
              </Typography>
              <TextField
                label="Email"
                source="contactInfo.email"
                textAlign="right"
              />
            </Box>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={300} fontSize={14}>
                {t('menu.phone')}
              </Typography>
              <TextField source="contactInfo.phoneNumber" />
            </Box>
          </MobileCard>
        </MobileGrid>
      ) : (
        <Datagrid
          rowClick="edit"
          rowStyle={postRowStyle}
          sx={{
            '& .RaBulkActionsToolbar-topToolbar': {
              backgroundColor: 'transparent',
              textAlign: 'right',
            },
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
              borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
            },
          }}
          bulkActionButtons={<AssetBulkActionButtons />}
        >
          <TextField source="name" label={t('shared.name')} />
          <FunctionField
            source="type"
            label={t('giftCards.type')}
            render={(record: any) =>
              record.type[0].toUpperCase() + record.type.slice(1)
            }
            textAlign="right"
          />
          <FunctionField
            source="address"
            label={t('sellPointsPage.address')}
            render={(record: any) =>
              `${record.address.line1}, ${record.address.city}`
            }
            textAlign="right"
          />
          <TextField
            label="Email"
            source="contactInfo.email"
            textAlign="right"
          />
          <TextField
            label={t('menu.phone')}
            source="contactInfo.phoneNumber"
            textAlign="right"
          />
        </Datagrid>
      )}
      <ListLiveUpdate />
    </List>
  );
};
