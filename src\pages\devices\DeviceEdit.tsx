import { useCallback } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import { QRCodeSVG } from 'qrcode.react';
import {
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useNotify,
  useRecordContext,
  useRedirect,
  useUnique,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import CustomInput from '../../components/atoms/inputs/CustomInput';
import CustomDeleteWithConfirmButton from '../../components/molecules/CustomDeleteWithConfirmButton';
import RadioInputGroup from '../../components/molecules/input-groups/RadioInputGroup';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { validateName } from '~/utils/validateName';

const DeviceEditInner = () => {
  const record = useRecordContext();
  const unique = useUnique({ filter: { id_neq: record!.id } });
  const redirect = useRedirect();
  const isMobile = useMediaQuery('(max-width:600px)', { noSsr: true });
  const { t } = useTranslation();
  const notify = useNotify();

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.DEVICES);
  }, [redirect]);

  const copyDeviceCode = useCallback(() => {
    if (record?.id) {
      navigator.clipboard.writeText(record.id as string);
      notify('Device code copied to clipboard!', { type: 'success' });
    }
  }, [record?.id, notify]);

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('devicesPage.editDevice')}
      >
        <>
          <CustomDeleteWithConfirmButton
            icon={isMobile ? <DeleteIcon /> : <></>}
            sx={{ mr: 2, background: 'rgba(0,0,0,.05)' }}
            field="name"
            label={t('shared.delete')}
          />
          <SaveButton
            type="button"
            icon={<></>}
            label={t('shared.save')}
            disabled={record?.disabled ?? false}
          />
        </>
      </ModalHeader>
      {/* Body */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          justifyContent: 'center',
          maxWidth: { xs: '100%', md: '800px' },
          width: '90%',
          mx: 'auto',
          my: { xs: 4, md: 8 },
          gap: 6,
        }}
      >
        <Subsection title={t('devicesPage.deviceDetails')}>
          <CustomInput
            source="id"
            label={t('shared.code')}
            readOnly
            slotProps={{
              input: {
                endAdornment: (
                  <Box
                    sx={{
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      minWidth: '50px',
                      pr: 0.5,
                    }}
                    onClick={copyDeviceCode}
                  >
                    <Typography variant="caption" color="primary">
                      {t('shared.copy')}
                    </Typography>
                  </Box>
                ),
              },
            }}
          />
          <CustomInput
            source="name"
            label={t('devicesPage.deviceName')}
            validate={[required(), unique(), validateName]}
          />
          <ReferenceInput source="sellPointId" reference={RESOURCES.LOCATIONS}>
            <CustomInput
              type="select"
              label={t('shared.location')}
              optionText="name"
              optionValue="id"
              placeholder="None"
              readOnly
            />
          </ReferenceInput>
          <CustomInput
            uppercase
            source="deviceType"
            label={t('devicesPage.deviceType')}
            placeholder="Not set yet!"
            readOnly
          />
        </Subsection>

        <Subsection title="" >
          <QRCodeSVG
            value={record?.id! as string}
            imageSettings={{
              src: '/assets/logo/logo_cropped_2.svg',
              height: 15,
              width: 90,
              excavate: true,
            }}
          />
        </Subsection>
      </Box>
    </>
  );
};

export default function DeviceEdit() {
  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="optimistic">
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <DeviceEditInner />
      </SimpleForm>
    </EditDialog>
  );
}
