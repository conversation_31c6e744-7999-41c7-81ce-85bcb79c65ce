import { useState } from 'react';
import { Box, SxProps, Theme, Typography, useMediaQuery } from '@mui/material';
import { InputProps, useInput } from 'react-admin';
import { useTranslation } from 'react-i18next';

export interface CodeInputProps extends Omit<InputProps, 'children'> {
  source: string;
  containerStyle?: SxProps<Theme>;
  digits?: number;
}

export default function CodeInput({
  source,
  containerStyle,
  validate,
  digits = 6,
  ...rest
}: CodeInputProps) {
  const { t } = useTranslation('');
  const [focused, setFocused] = useState<boolean>(false);

  const isDisabled = rest.disabled || rest.readOnly;

  // Use react-admin's useInput hook to handle validation and field state
  const {
    field: { onChange, value, onBlur },
    fieldState,
    isRequired,
  } = useInput({
    source,
    validate,
    ...rest,
  });

  // Convert value to string for display
  const codeValue = value?.toString() || '';

  // Create an array of digits (filled or empty)
  const digitBoxes = Array(digits).fill('');

  const regenerateCode = () => {
    // Generate a random code with the specified number of digits
    const min = Math.pow(10, digits - 1);
    const max = Math.pow(10, digits) - 1;
    const newCode = Math.floor(min + Math.random() * (max - min)).toString();
    onChange(newCode);
  };

  return (
    <Box
      sx={{
        height: '45px',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        overflow: 'hidden',
        ...containerStyle,
      }}
      onFocus={() => setFocused(true)}
      onBlur={() => {
        setFocused(false);
        onBlur();
      }}
    >
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          flex: 1,
          borderRight: '1px solid',
          borderColor: 'divider',
        }}
      >
        {digitBoxes.map((_, idx) => (
          <Box
            key={idx}
            sx={{
              height: '100%',
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRight: idx < digits - 1 ? '1px solid' : 'none',
              borderColor: 'divider',
            }}
          >
            <Typography variant="body1" fontWeight="medium" fontSize="1.25rem">
              {codeValue[idx] || ''}
            </Typography>
          </Box>
        ))}
      </Box>
      <Box
        onClick={regenerateCode}
        sx={{
          cursor: isDisabled ? 'default' : 'pointer',
          px: 2,
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? 'none' : 'auto',
        }}
      >
        <Typography
          color="primary.main"
          fontWeight="medium"
          sx={{ whiteSpace: 'nowrap' }}
        >
          {t('shared.generate')}
        </Typography>
      </Box>
    </Box>
  );
}
