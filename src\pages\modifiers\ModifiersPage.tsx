import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';

export default function ModifiersPage() {
  const { t } = useTranslation('');
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('menu.modifiers')}
        description={
          <>
            {t('menu.modifiersDescription')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
    </Box>
  );
}
