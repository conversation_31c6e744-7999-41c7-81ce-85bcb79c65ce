import { useEffect, useState } from 'react';
import SmartScreenOutlinedIcon from '@mui/icons-material/SmartScreenOutlined';
import { Box, Grid, Typography } from '@mui/material';
import { useChoicesContext, useRecordContext } from 'react-admin';
import { useFormContext } from 'react-hook-form';

export interface ImageSelectInputGroupProps {
  source: string;
  choices?: Array<{
    icon?: React.ReactNode;
    label: string;
    id: string;
  }>;
}

export default function ImageSelectInputGroup({
  source,
  choices: choiceProp,
}: ImageSelectInputGroupProps) {
  const [selectedValue, setSelectedValue] = useState<string>();
  const record = useRecordContext();
  const { setValue } = useFormContext();

  const { allChoices } = useChoicesContext({
    choices: choiceProp,
  });

  useEffect(() => {
    if (!selectedValue && allChoices?.length)
      setSelectedValue(allChoices[0].id);
    if (record) setSelectedValue(record[source]);
  }, [record, allChoices]);

  useEffect(() => {
    setValue(source, selectedValue, { shouldDirty: true, shouldTouch: true });
  }, [selectedValue]);

  return (
    <Grid container spacing={2}>
      {allChoices?.map(choice => {
        return (
          <Grid
            key={choice.id}
            item
            xs={12}
            sm={6}
            md={12 / allChoices.length}
            onClick={() => setSelectedValue(choice.id)}
          >
            <Box
              sx={{
                border: '1px solid',
                borderColor:
                  choice.id === selectedValue
                    ? 'primary.main'
                    : 'custom.gray400',
                borderRadius: '6px',
                p: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                cursor: 'pointer',
                ':hover':
                  choice.id !== selectedValue
                    ? {
                        borderColor: 'custom.gray600',
                      }
                    : {},
                bgcolor:
                  choice.id === selectedValue ? 'primary.main' : 'inherit',
              }}
            >
              {choice.icon ? (
                choice.icon
              ) : (
                <SmartScreenOutlinedIcon
                  fontSize="large"
                  fontWeight={'light'}
                  sx={{
                    color:
                      choice.id === selectedValue ? 'white' : 'primary.main',
                  }}
                />
              )}
              {/* @ts-ignore */}
              <Typography
                variant="body2"
                color={choice.id === selectedValue ? 'white' : 'primary.main'}
                mt={1}
              >
                {choice.label}
              </Typography>
            </Box>
          </Grid>
        );
      })}
    </Grid>
  );
}
