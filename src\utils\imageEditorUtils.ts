import { Crop } from 'react-image-crop';
import { ImageEditorConfig, TargetSize, AspectRatioGroup } from '~/types/fileUpload';

/**
 * Check if an image's aspect ratio matches the target aspect ratio within tolerance
 */
export const hasMatchingAspectRatio = (
    imageWidth: number,
    imageHeight: number,
    targetAspectRatio: number,
    tolerance: number = 0.05
): boolean => {
    const imageAspectRatio = imageWidth / imageHeight;
    return Math.abs(imageAspectRatio - targetAspectRatio) <= tolerance;
};

/**
 * Check if a file should be processed through the image editor for public images
 * Public images always need an automatic thumbnail (50×50, 1:1 aspect ratio).
 * If target sizes are configured, we need to check all required aspect ratios
 * (target sizes + automatic thumbnail). If the image doesn't match any required
 * aspect ratio, it needs editing.
 */
export const shouldEditPublicImage = async (
    file: File,
    fileType?: string,
    imageConfig?: any // Accept the full imageConfig object
): Promise<boolean> => {
    // Only applies to image uploads for 'images' and 'public' fileTypes
    if (fileType !== 'public' && fileType !== 'images') return false;
    if (!file.type.startsWith('image/')) return false;

    return new Promise((resolve) => {
        const image = new Image();
        const url = URL.createObjectURL(file);

        image.onload = () => {
            URL.revokeObjectURL(url);

            // Check if target sizes are configured
            const hasTargetSizes = imageConfig?.targetSizes && imageConfig.targetSizes.length > 0;

            // Show editor only if target sizes are configured
            // This creates folder structure with variants for both 'images' and 'public' fileTypes
            if (hasTargetSizes) {
                resolve(true); // Show editor for user control
                return;
            }

            // No target sizes configured:
            // - For 'images' fileType: auto-generate thumbnail with aspect ratio preservation
            // - For 'public' fileType: store as single file (no variants)
            resolve(false); // No editor, handle based on fileType
        };

        image.onerror = () => {
            URL.revokeObjectURL(url);
            // If we can't load the image, don't show editor
            resolve(false);
        };

        image.src = url;
    });
};

/**
 * Check if a file should be processed through the image editor
 */
export const shouldEditImage = (
    file: File,
    enableImageEditor: boolean,
    config?: ImageEditorConfig,
    fileType?: string
): boolean => {
    if (!enableImageEditor) return false;
    if (!file.type.startsWith('image/')) return false;

    // Only show editor for images when target sizes are configured
    // This applies to both 'images' and 'public' fileTypes
    if (fileType === 'images' || fileType === 'public') {
        // Show editor only if target sizes are configured
        return !!(config?.targetSizes?.length);
    }

    // For other fileTypes (videos, private), never show image editor
    // Even if they upload images, they should be stored as single files
    return false;
};

/**
 * Get default crop configuration based on image editor config
 */
export const getDefaultCrop = (
    imageWidth: number,
    imageHeight: number,
    config?: ImageEditorConfig
): Crop => {
    if (!config) {
        // Default crop to center 80% of the image
        return {
            unit: '%',
            x: 10,
            y: 10,
            width: 80,
            height: 80,
        };
    }

    // If aspect ratio is specified, calculate crop based on that
    if (config.aspectRatio) {
        const imageAspectRatio = imageWidth / imageHeight;

        if (imageAspectRatio > config.aspectRatio) {
            // Image is wider, fit height and center horizontally
            const cropWidth = (imageHeight * config.aspectRatio / imageWidth) * 100;
            return {
                unit: '%',
                x: (100 - cropWidth) / 2,
                y: 0,
                width: cropWidth,
                height: 100,
            };
        } else {
            // Image is taller, fit width and center vertically
            const cropHeight = (imageWidth / config.aspectRatio / imageHeight) * 100;
            return {
                unit: '%',
                x: 0,
                y: (100 - cropHeight) / 2,
                width: 100,
                height: cropHeight,
            };
        }
    }

    // If target dimensions are specified, calculate aspect ratio
    if (config.targetWidth && config.targetHeight) {
        const targetAspectRatio = config.targetWidth / config.targetHeight;
        return getDefaultCrop(imageWidth, imageHeight, { ...config, aspectRatio: targetAspectRatio });
    }

    // Default crop
    return {
        unit: '%',
        x: 10,
        y: 10,
        width: 80,
        height: 80,
    };
};

/**
 * Convert crop data to pixel coordinates
 */
export const cropToPixelCrop = (
    crop: Crop,
    imageWidth: number,
    imageHeight: number
): Crop => {
    if (crop.unit === 'px') return crop;

    return {
        unit: 'px',
        x: (crop.x / 100) * imageWidth,
        y: (crop.y / 100) * imageHeight,
        width: (crop.width / 100) * imageWidth,
        height: (crop.height / 100) * imageHeight,
    };
};

/**
 * Create a cropped canvas from an image and crop data
 */
export const getCroppedCanvas = (
    image: HTMLImageElement,
    crop: Crop,
    config?: ImageEditorConfig
): HTMLCanvasElement => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('Failed to get canvas context');
    }

    const pixelCrop = cropToPixelCrop(crop, image.naturalWidth, image.naturalHeight);

    // Set canvas dimensions to target size or crop size
    if (config?.targetWidth && config?.targetHeight) {
        canvas.width = config.targetWidth;
        canvas.height = config.targetHeight;
    } else {
        canvas.width = pixelCrop.width;
        canvas.height = pixelCrop.height;
    }

    // Draw the cropped image
    ctx.drawImage(
        image,
        pixelCrop.x,
        pixelCrop.y,
        pixelCrop.width,
        pixelCrop.height,
        0,
        0,
        canvas.width,
        canvas.height
    );

    return canvas;
};

/**
 * Convert a canvas to a File object
 */
export const canvasToFile = (
    canvas: HTMLCanvasElement,
    originalFile: File,
    config?: ImageEditorConfig
): Promise<File> => {
    return new Promise((resolve, reject) => {
        const outputFormat = config?.outputFormat || 'jpeg';
        const quality = config?.outputQuality || 0.9;

        // Determine MIME type and file extension
        let mimeType: string;
        let extension: string;

        switch (outputFormat) {
            case 'png':
                mimeType = 'image/png';
                extension = 'png';
                break;
            case 'webp':
                mimeType = 'image/webp';
                extension = 'webp';
                break;
            case 'jpeg':
            default:
                mimeType = 'image/jpeg';
                extension = 'jpg';
                break;
        }

        canvas.toBlob(
            (blob) => {
                if (!blob) {
                    reject(new Error('Failed to create blob from canvas'));
                    return;
                }

                // Create filename with edited suffix if different format
                const originalName = originalFile.name ? originalFile.name.replace(/\.[^/.]+$/, '') : `cropped_image_${Date.now()}`;
                const originalExt = originalFile.name ? originalFile.name.split('.').pop()?.toLowerCase() : null;
                const needsSuffix = originalExt !== extension;
                const filename = needsSuffix || !originalFile.name
                    ? `${originalName}_edited.${extension}`
                    : `${originalName}.${extension}`;

                const editedFile = new File([blob], filename, {
                    type: mimeType,
                    lastModified: Date.now(),
                });

                resolve(editedFile);
            },
            mimeType,
            outputFormat === 'jpeg' ? quality : undefined
        );
    });
};

/**
 * Load an image from a File and return HTMLImageElement
 * Note: The blob URL is NOT revoked automatically - it should be cleaned up by the caller
 */
export const loadImageFromFile = (file: File): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
        const image = new Image();
        const url = URL.createObjectURL(file);

        image.onload = () => {
            // Don't revoke URL here - let the component handle cleanup
            resolve(image);
        };

        image.onerror = () => {
            URL.revokeObjectURL(url);
            reject(new Error('Failed to load image'));
        };

        image.src = url;
    });
};

/**
 * Clean up blob URL from an image element
 */
export const cleanupImageUrl = (image: HTMLImageElement | null): void => {
    if (image?.src?.startsWith('blob:')) {
        URL.revokeObjectURL(image.src);
    }
};

/**
 * Get crop constraints based on configuration
 */
export const getCropConstraints = (config?: ImageEditorConfig) => {
    if (!config) return {};

    const constraints: any = {};

    if (config.aspectRatio) {
        constraints.aspect = config.aspectRatio;
    }

    if (config.minWidth || config.minHeight) {
        constraints.minWidth = config.minWidth;
        constraints.minHeight = config.minHeight;
    }

    if (config.maxWidth || config.maxHeight) {
        constraints.maxWidth = config.maxWidth;
        constraints.maxHeight = config.maxHeight;
    }

    return constraints;
};

/**
 * Calculate the optimal zoom level for initial display
 */
export const getOptimalZoom = (
    imageWidth: number,
    imageHeight: number,
    containerWidth: number,
    containerHeight: number
): number => {
    const scaleX = containerWidth / imageWidth;
    const scaleY = containerHeight / imageHeight;

    // Use the smaller scale to ensure image fits in container
    return Math.min(scaleX, scaleY, 1); // Don't zoom beyond 100%
};

/**
 * Validate crop dimensions against configuration constraints
 */
export const validateCropDimensions = (
    crop: Crop,
    imageWidth: number,
    imageHeight: number,
    config?: ImageEditorConfig
): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const pixelCrop = cropToPixelCrop(crop, imageWidth, imageHeight);

    if (config?.minWidth && pixelCrop.width < config.minWidth) {
        errors.push(`Crop width must be at least ${config.minWidth}px`);
    }

    if (config?.minHeight && pixelCrop.height < config.minHeight) {
        errors.push(`Crop height must be at least ${config.minHeight}px`);
    }

    if (config?.maxWidth && pixelCrop.width > config.maxWidth) {
        errors.push(`Crop width must not exceed ${config.maxWidth}px`);
    }

    if (config?.maxHeight && pixelCrop.height > config.maxHeight) {
        errors.push(`Crop height must not exceed ${config.maxHeight}px`);
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Group target sizes by aspect ratio for multi-ratio cropping
 * Tolerance is used to group sizes with similar aspect ratios (accounts for rounding)
 */
export const groupSizesByAspectRatio = (
    targetSizes: TargetSize[],
    tolerance: number = 0.01
): AspectRatioGroup[] => {
    if (!targetSizes || targetSizes.length === 0) {
        return [];
    }

    const groups: Map<number, TargetSize[]> = new Map();

    // Group sizes by aspect ratio
    targetSizes.forEach(size => {
        const aspectRatio = size.width / size.height;

        // Find existing group with similar aspect ratio
        let groupKey = aspectRatio;
        for (const existingRatio of groups.keys()) {
            if (Math.abs(existingRatio - aspectRatio) <= tolerance) {
                groupKey = existingRatio;
                break;
            }
        }

        if (!groups.has(groupKey)) {
            groups.set(groupKey, []);
        }
        groups.get(groupKey)!.push(size);
    });

    // Convert to AspectRatioGroup array
    return Array.from(groups.entries()).map(([aspectRatio, sizes]) => {
        // Find the largest size for optimal editing quality
        const editingSize = sizes.reduce((largest, current) => {
            const currentArea = current.width * current.height;
            const largestArea = largest.width * largest.height;
            return currentArea > largestArea ? current : largest;
        });

        // Generate a friendly name for the aspect ratio
        const name = getAspectRatioName(aspectRatio);

        return {
            aspectRatio,
            targetSizes: sizes,
            editingSize: { width: editingSize.width, height: editingSize.height },
            name,
        };
    });
};

/**
 * Get a friendly name for an aspect ratio
 */
export const getAspectRatioName = (aspectRatio: number): string => {
    const commonRatios = [
        { ratio: 1, name: 'Square' },
        { ratio: 16 / 9, name: 'Widescreen' },
        { ratio: 4 / 3, name: 'Standard' },
        { ratio: 3 / 2, name: 'Photo' },
        { ratio: 2 / 1, name: 'Banner' },
        { ratio: 3 / 1, name: 'Wide Banner' },
        { ratio: 9 / 16, name: 'Portrait' },
        { ratio: 3 / 4, name: 'Portrait Standard' },
        { ratio: 2 / 3, name: 'Portrait Photo' },
    ];

    const tolerance = 0.05;
    const match = commonRatios.find(r => Math.abs(r.ratio - aspectRatio) <= tolerance);

    if (match) {
        return match.name;
    }

    // Generate descriptive name for custom ratios
    if (aspectRatio > 1) {
        return `${aspectRatio.toFixed(1)}:1`;
    } else {
        return `1:${(1 / aspectRatio).toFixed(1)}`;
    }
};

/**
 * Process ImageEditorConfig to extract aspect ratio groups
 * Handles backward compatibility and auto-grouping
 * Always includes automatic thumbnail (50×50, 1:1) for public images
 */
export const getAspectRatioGroups = (config: ImageEditorConfig, fileType?: string): AspectRatioGroup[] => {
    // For public images, we ALWAYS need a thumbnail, regardless of configuration
    const isPublicImage = fileType === 'public' || fileType === 'images';

    // If aspectRatioGroups is explicitly provided, use it
    if (config.aspectRatioGroups && config.aspectRatioGroups.length > 0) {
        const groups = [...config.aspectRatioGroups];

        // For public images, ensure automatic thumbnail is included
        if (isPublicImage) {
            const hasSquareGroup = groups.some(group => Math.abs(group.aspectRatio - 1.0) <= 0.01);
            if (!hasSquareGroup) {
                // Add automatic thumbnail group
                groups.unshift({
                    aspectRatio: 1.0,
                    targetSizes: [{ width: 50, height: 50, name: 'thumbnail' }],
                    editingSize: { width: 50, height: 50 },
                    name: 'Automatic Thumbnail',
                });
            } else {
                // Add automatic thumbnail to existing square group
                const squareGroup = groups.find(group => Math.abs(group.aspectRatio - 1.0) <= 0.01);
                if (squareGroup) {
                    const hasThumbnail = squareGroup.targetSizes.some(size =>
                        size.width === 50 && size.height === 50
                    );
                    if (!hasThumbnail) {
                        squareGroup.targetSizes.unshift({ width: 50, height: 50, name: 'thumbnail' });
                    }
                }
            }
        }

        return groups;
    }

    // If targetSizes is provided, auto-group by aspect ratio
    if (config.targetSizes && config.targetSizes.length > 0) {
        const allTargetSizes = [...config.targetSizes];

        // For public images, ensure automatic thumbnail is included
        if (isPublicImage) {
            const hasThumbnail = allTargetSizes.some(size =>
                size.width === 50 && size.height === 50
            );
            if (!hasThumbnail) {
                allTargetSizes.unshift({ width: 50, height: 50, name: 'thumbnail' });
            }
        }

        return groupSizesByAspectRatio(allTargetSizes);
    }

    // For public images with no target sizes, still include automatic thumbnail
    if (isPublicImage) {
        return [{
            aspectRatio: 1.0,
            targetSizes: [{ width: 50, height: 50, name: 'thumbnail' }],
            editingSize: { width: 50, height: 50 },
            name: 'Automatic Thumbnail',
        }];
    }

    // Fallback to single group for backward compatibility
    if (config.targetWidth && config.targetHeight) {
        const aspectRatio = config.targetWidth / config.targetHeight;
        return [{
            aspectRatio,
            targetSizes: [{ width: config.targetWidth, height: config.targetHeight }],
            editingSize: { width: config.targetWidth, height: config.targetHeight },
            name: getAspectRatioName(aspectRatio),
        }];
    }

    return [];
};

/**
 * Check if configuration requires multi-aspect-ratio cropping
 */
export const isMultiAspectRatioConfig = (config: ImageEditorConfig, fileType?: string): boolean => {
    const groups = getAspectRatioGroups(config, fileType);
    return groups.length > 1;
};

/**
 * Get the total number of crop sessions needed
 */
export const getCropSessionCount = (config: ImageEditorConfig, fileType?: string): number => {
    return getAspectRatioGroups(config, fileType).length;
};

/**
 * Get editing dimensions for a specific aspect ratio group
 */
export const getEditingDimensionsForGroup = (group: AspectRatioGroup): { width: number; height: number } => {
    return group.editingSize;
};

/**
 * Get the optimal editing dimensions from an ImageEditorConfig
 * If multiple aspect ratios exist, returns the largest overall dimensions
 * For single aspect ratio, returns the largest size in that ratio
 */
export const getEditingDimensions = (config: ImageEditorConfig, fileType?: string): { width: number; height: number } => {
    const groups = getAspectRatioGroups(config, fileType);

    if (groups.length === 0) {
        // Fallback to reasonable defaults
        return { width: 400, height: 400 };
    }

    if (groups.length === 1) {
        // Single aspect ratio - use its editing size
        return groups[0].editingSize;
    }

    // Multiple aspect ratios - find the largest overall dimensions
    return groups.reduce((largest, group) => {
        const groupArea = group.editingSize.width * group.editingSize.height;
        const largestArea = largest.width * largest.height;
        return groupArea > largestArea ? group.editingSize : largest;
    }, { width: 0, height: 0 });
};

/**
 * Generate a canvas at a specific target size from a source canvas
 */
export const generateSizedCanvas = (
    sourceCanvas: HTMLCanvasElement,
    targetWidth: number,
    targetHeight: number
): HTMLCanvasElement => {
    const targetCanvas = document.createElement('canvas');
    const targetCtx = targetCanvas.getContext('2d');

    if (!targetCtx) {
        throw new Error('Could not get 2D context for target canvas');
    }

    targetCanvas.width = targetWidth;
    targetCanvas.height = targetHeight;

    // Calculate scaling to fit the source content within target dimensions while preserving aspect ratio
    const sourceAspectRatio = sourceCanvas.width / sourceCanvas.height;
    const targetAspectRatio = targetWidth / targetHeight;

    let drawWidth: number;
    let drawHeight: number;
    let drawX: number;
    let drawY: number;

    // Add small epsilon for floating point comparison
    const epsilon = 0.0001;

    if (Math.abs(sourceAspectRatio - targetAspectRatio) < epsilon) {
        // Same aspect ratio - fill the entire target canvas
        drawWidth = targetWidth;
        drawHeight = targetHeight;
        drawX = 0;
        drawY = 0;
    } else if (sourceAspectRatio > targetAspectRatio) {
        // Source is wider - fit to width, center vertically
        drawWidth = targetWidth;
        drawHeight = targetWidth / sourceAspectRatio;
        drawX = 0;
        drawY = (targetHeight - drawHeight) / 2;
    } else {
        // Source is taller - fit to height, center horizontally
        drawWidth = targetHeight * sourceAspectRatio;
        drawHeight = targetHeight;
        drawX = (targetWidth - drawWidth) / 2;
        drawY = 0;
    }

    // Fill background with white (or transparent for PNG)
    targetCtx.fillStyle = '#ffffff';
    targetCtx.fillRect(0, 0, targetWidth, targetHeight);

    // Draw the source canvas scaled and centered within the target dimensions
    targetCtx.drawImage(sourceCanvas, drawX, drawY, drawWidth, drawHeight);

    return targetCanvas;
};

/**
 * Convert a canvas to multiple files based on target sizes or aspect ratio group
 */
export const canvasToMultipleFiles = async (
    sourceCanvas: HTMLCanvasElement,
    originalFile: File,
    config: ImageEditorConfig,
    currentGroup?: AspectRatioGroup
): Promise<File[]> => {
    // If we're processing a specific aspect ratio group, use its target sizes
    if (currentGroup) {
        return await generateFilesForGroup(sourceCanvas, originalFile, currentGroup, config);
    }

    // For backward compatibility: if no targetSizes or groups, generate single file
    const groups = getAspectRatioGroups(config, 'images'); // Default to 'images' for backward compatibility
    if (groups.length === 0) {
        const singleFile = await canvasToFile(sourceCanvas, originalFile, config);
        return [singleFile];
    }

    // If single aspect ratio group, process it directly
    if (groups.length === 1) {
        return await generateFilesForGroup(sourceCanvas, originalFile, groups[0], config);
    }

    // This function should not be called for multi-aspect-ratio configs
    // Multi-aspect-ratio should be handled by the modal with separate crop sessions
    throw new Error('Multi-aspect-ratio configurations require separate crop sessions. Use the ImageEditorModal workflow instead.');
};

/**
 * Generate files for a specific aspect ratio group
 */
async function generateFilesForGroup(
    sourceCanvas: HTMLCanvasElement,
    originalFile: File,
    group: AspectRatioGroup,
    config: ImageEditorConfig
): Promise<File[]> {
    const files: File[] = [];
    const outputFormat = config.outputFormat || 'jpeg';
    const quality = config.outputQuality || 0.9;

    // Determine MIME type and file extension
    let mimeType: string;
    let extension: string;

    switch (outputFormat) {
        case 'png':
            mimeType = 'image/png';
            extension = 'png';
            break;
        case 'webp':
            mimeType = 'image/webp';
            extension = 'webp';
            break;
        case 'jpeg':
        default:
            mimeType = 'image/jpeg';
            extension = 'jpg';
            break;
    }

    // Generate a file for each target size in the group
    for (const targetSize of group.targetSizes) {
        try {
            console.log('🖼️ Processing target size:', targetSize, 'for group:', group.name);

            const sizedCanvas = generateSizedCanvas(
                sourceCanvas,
                targetSize.width,
                targetSize.height
            );

            console.log('📐 Generated sized canvas:', {
                width: sizedCanvas.width,
                height: sizedCanvas.height,
                targetSize
            });

            const file = await new Promise<File>((resolve, reject) => {
                sizedCanvas.toBlob(
                    (blob) => {
                        if (!blob) {
                            reject(new Error(`Failed to create blob for size ${targetSize.width}x${targetSize.height}`));
                            return;
                        }

                        console.log('💾 Created blob:', {
                            size: blob.size,
                            type: blob.type,
                            targetSize
                        });

                        // Create filename with target size name or fallback naming
                        const originalName = originalFile.name
                            ? originalFile.name.replace(/\.[^/.]+$/, '')
                            : `image_${Date.now()}`;

                        let filename: string;
                        if (targetSize.name) {
                            // Use the name from the target size (e.g., "card", "detail", "banner_small")
                            filename = `${targetSize.name}.${extension}`;
                        } else if (group.name && group.name !== 'Square') {
                            // Use group name and size for non-square ratios
                            const groupPrefix = group.name.toLowerCase().replace(/\s+/g, '_');
                            filename = `${groupPrefix}_${targetSize.width}x${targetSize.height}.${extension}`;
                        } else {
                            // Default size naming
                            filename = `${targetSize.width}x${targetSize.height}.${extension}`;
                        }

                        const editedFile = new File([blob], filename, {
                            type: mimeType,
                            lastModified: Date.now(),
                        });

                        console.log('✅ Created file:', {
                            name: editedFile.name,
                            size: editedFile.size,
                            type: editedFile.type
                        });

                        resolve(editedFile);
                    },
                    mimeType,
                    outputFormat === 'jpeg' ? quality : undefined
                );
            });

            files.push(file);
        } catch (error) {
            console.error('❌ Error processing target size:', targetSize, error);
            throw new Error(`Failed to process target size ${targetSize.width}x${targetSize.height}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    return files;
}

/**
 * Validate target sizes configuration
 */
export const validateTargetSizes = (targetSizes: TargetSize[]): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!Array.isArray(targetSizes) || targetSizes.length === 0) {
        errors.push('Target sizes must be a non-empty array');
        return { isValid: false, errors };
    }

    targetSizes.forEach((size, index) => {
        if (!size.width || !size.height) {
            errors.push(`Target size at index ${index} must have valid width and height`);
        }
        if (size.width <= 0 || size.height <= 0) {
            errors.push(`Target size at index ${index} must have positive dimensions`);
        }
        if (size.width > 4000 || size.height > 4000) {
            errors.push(`Target size at index ${index} dimensions are too large (max 4000px)`);
        }
    });

    return { isValid: errors.length === 0, errors };
};

/**
 * Check if image editor configuration is using multiple target sizes
 */
export const isMultiSizeConfig = (config: ImageEditorConfig): boolean => {
    return !!(config.targetSizes && config.targetSizes.length > 0);
};

/**
 * Get a human-readable description of the target sizes
 */
export const getTargetSizesDescription = (config: ImageEditorConfig): string => {
    if (config.targetSizes && config.targetSizes.length > 0) {
        return config.targetSizes
            .map(size => size.name ? `${size.name} (${size.width}×${size.height})` : `${size.width}×${size.height}`)
            .join(', ');
    }

    // Fallback to single target size
    const editingDimensions = getEditingDimensions(config);
    return `${editingDimensions.width}×${editingDimensions.height}`;
};

/**
 * Get default image editor configuration for public images (logos)
 * This ensures automatic thumbnails (50×50) are always generated with the correct 1:1 aspect ratio
 */
export const getPublicImageEditorConfig = (): ImageEditorConfig => {
    return {
        aspectRatio: 1.0, // 1:1 for square thumbnails
        outputFormat: 'webp',
        outputQuality: 0.9,
        targetWidth: 50, // Automatic thumbnail size
        targetHeight: 50,
        minWidth: 50,
        minHeight: 50,
        maxWidth: 2000,
        maxHeight: 2000,
    };
};
