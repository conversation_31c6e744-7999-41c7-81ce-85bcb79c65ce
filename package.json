{"name": "mock-admin", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/incrementVersion.js && tsc && vite build", "preview": "vite preview", "format": "prettier --config .prettierrc --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --config .prettierrc --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fnando/sparkline": "^0.3.10", "@googlemaps/js-api-loader": "^1.16.10", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.3.1", "@mui/x-date-pickers": "^7.23.3", "@mui/x-date-pickers-pro": "^7.23.3", "@mui/x-license-pro": "^6.10.2", "@react-admin/ra-editable-datagrid": "^5.2.1", "@react-admin/ra-form-layout": "^5.15.0", "@react-admin/ra-navigation": "^6.1.0", "@react-admin/ra-realtime": "^5.1.1", "@react-admin/ra-relationships": "^5.3.1", "@react-admin/ra-search": "^5.4.0", "@types/jsonexport": "^3.0.5", "@types/qrcode": "^1.5.5", "chart.js": "^4.5.0", "crypto-js": "^4.2.0", "date-fns-v4": "npm:date-fns@^4.1.0", "dayjs": "^1.11.13", "fakerest": "^4.1.3", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.2.5", "firebase": "^11.10.0", "i18next": "^25.3.1", "jsonexport": "^3.2.0", "jszip": "^3.10.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "mui-tel-input": "^8.0.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "ra-data-fakerest": "^5.9.1", "react": "^18.3.1", "react-admin": "^5.9.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^15.6.0", "react-image-crop": "^11.0.10", "react-router-dom": "^7.6.3", "react-to-print": "^3.1.1", "recharts": "^2.15.3", "vanilla-cookieconsent": "^3.1.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@types/crypto-js": "^4.2.2", "@types/fnando__sparkline": "^0.3.7", "@types/google.maps": "^3.58.1", "@types/lodash": "^4.17.20", "@types/node": "^22.14.1", "@types/react": "^19.1.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.6", "@types/react-helmet": "^6.1.11", "@types/react-image-crop": "^8.1.6", "@types/recharts": "^2.0.1", "@vitejs/plugin-react": "^4.6.0", "prettier": "^3.6.2", "type-fest": "^4.41.0", "typescript": "^5.4.5", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.17.2"}}