import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
} from 'react';

import 'vanilla-cookieconsent/dist/cookieconsent.css';

import * as CookieConsent from 'vanilla-cookieconsent';

import { useTheme } from './ThemeContext';
import { useTranslation } from 'react-i18next';
interface CookiesConsentContextProps {
  handleInsideButtonClick: () => void;
}

const CookiesConsentContext = createContext<
  CookiesConsentContextProps | undefined
>(undefined);

const CookiesConsentProvider = ({ children }: any) => {
  const outsideButtonRef = useRef<HTMLButtonElement>(null);
  const { theme } = useTheme();
  const { t } = useTranslation();
  const handleInsideButtonClick = useCallback(() => {
    if (outsideButtonRef.current) {
      outsideButtonRef.current.click();
    }
  }, []);

  useEffect(() => {
    if (theme.palette.mode === 'light') {
      document.documentElement.classList.remove('cc--darkmode');
    } else {
      document.documentElement.classList.add('cc--darkmode');
    }
  }, [theme]);

  useEffect(() => {
    CookieConsent.run({
      guiOptions: {
        consentModal: {
          layout: 'box inline',
          position: 'bottom left',
          equalWeightButtons: true,
          flipButtons: false,
        },
        preferencesModal: {
          layout: 'box',
          position: 'right',
          equalWeightButtons: true,
          flipButtons: false,
        },
      },
      categories: {
        necessary: {
          enabled: true,
          readOnly: true,
        },
        analytics: {},
        functionality: {},
        retargeting: {},
      },
      language: {
        default: 'en',
        autoDetect: 'browser',
        translations: {
          en: {
            consentModal: {
              title: 'Manage cookies',
              description:
                'Selio uses cookies (and similar technologies) for many purposes, including maintaining security, improving and customizing our services, and enabling relevant marketing. Please review our use of cookies to accept or reject non-essential cookies.',
              acceptAllBtn: 'Accept all cookies',
              acceptNecessaryBtn: 'Reject all',
              showPreferencesBtn: 'Customize cookie preferences',
              footer:
                '<a href="https://selio.io/cookie-preferences/">Cookie Preferences</a>\n<a href="https://selio.io/terms-of-service/">Terms of Service</a>',
            },
            preferencesModal: {
              title: t('cookiesConsent.title'),
              acceptAllBtn: t('cookiesConsent.acceptAllBtn'),
              acceptNecessaryBtn: t('cookiesConsent.acceptNecessaryBtn'),
              savePreferencesBtn: t('cookiesConsent.savePreferencesBtn'),
              closeIconLabel: t('cookiesConsent.closeIconLabel'),
              serviceCounterLabel: t('cookiesConsent.serviceCounterLabel'),
              sections: [
                {
                  description: t('cookiesConsent.description'),
                },
                {
                  title: '',
                  description:
                    `<div> ${t('cookiesConsent.description2')} cookies below.</div><br/><br/><div><a class="cc__link" target="_blank" href="https://selio.io/cookie-preferences/">${t('cookiesConsent.moreInformation')}</a><span>	&nbsp;	&nbsp;</span><a class="cc__link" target="_blank" href="https://selio.io/privacy-policy/">${t('cookiesConsent.privacyNotice')}</a></div>`,
                },

                {
                  title: `${t('cookiesConsent.necessary')} <span class="pm__badge">${t('cookiesConsent.alwaysEnabled')}</span>`,
                  description: t('cookiesConsent.necessaryDescription'),
                  linkedCategory: 'necessary',
                },
                {
                  title: t('cookiesConsent.analytics'),
                  description: t('cookiesConsent.analyticsDescription'),
                  linkedCategory: 'analytics',
                },
                {
                  title: t('cookiesConsent.functionality'),
                  description: t('cookiesConsent.functionalityDescription'),
                  linkedCategory: 'functionality',
                },
                {
                  title: t('cookiesConsent.retargeting'),
                  description: t('cookiesConsent.retargetingDescription'),
                  linkedCategory: 'retargeting',
                },
              ],
            },
          },
        },
      },
    });
  }, []);

  return (
    <CookiesConsentContext.Provider value={{ handleInsideButtonClick }}>
      {children}
      {/* Workaround to make the modal popup on click */}
      <button
        ref={outsideButtonRef}
        type="button"
        style={{ display: 'none' }}
        data-cc="show-preferencesModal"
      />
    </CookiesConsentContext.Provider>
  );
};

const useCookiesConsent = (): CookiesConsentContextProps => {
  const context = useContext(CookiesConsentContext);
  if (!context) {
    throw new Error(
      'useCookiesConsent must be used within a CookiesConsentProvider'
    );
  }
  return context;
};

export { CookiesConsentProvider, useCookiesConsent };
