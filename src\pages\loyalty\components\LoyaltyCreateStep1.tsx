import { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { TextInput } from 'react-admin';
import { useFormContext, useWatch } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';
import { useTheme } from '../../../contexts';
import UnitsSelect from './UnitsSelect';

export default function LoyaltyCreateStep1({ edit }: { edit?: boolean }) {
  const [showCustomInputs, setShowCustomInputs] = useState(false);
  const [singularCount, setSingularCount] = useState(0);
  const [pluralCount, setPluralCount] = useState(0);
  const { getValues } = useFormContext();
  const { theme } = useTheme();

  const { setValue, control } = useFormContext();

  const singular = useWatch({ control, name: 'unit-name-singular' }) || '';
  const plural = useWatch({ control, name: 'unit-name-plural' }) || '';
  const selectedUnit = useWatch({ control, name: 'units' }) || null;

  const selectedUnits = getValues('units')?.values || {
    singular: 'Unit',
    plural: 'Units',
  };

  useEffect(() => {
    if (singular || plural) {
      setValue('units', {
        values: { singular: singular || '', plural: plural || '' },
      });
    }
  }, [singular, plural, setValue]);

  useEffect(() => {
    if (selectedUnit && !showCustomInputs) {
      setValue('unit-name-singular', '');
      setValue('unit-name-plural', '');
      setSingularCount(0);
      setPluralCount(0);
    }
  }, [selectedUnit, showCustomInputs, setValue]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setCount: React.Dispatch<React.SetStateAction<number>>,
    maxLength: number
  ) => {
    const { value } = e.target;
    if (value.length <= maxLength) {
      setCount(value.length);
    }
  };

  const handleAddCustomUnit = () => {
    setShowCustomInputs(true);
    setValue('units', null);
  };

  return (
    <>
      <UnitsSelect setShowCustomInputs={setShowCustomInputs} />

      {!showCustomInputs && (
        <Box
          color="primary"
          onClick={handleAddCustomUnit}
          sx={{
            position: 'relative',
            top: -10,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '15px',
            border: 'solid 1px #D9D9D9',
            color: theme.palette.mode == 'light' ? 'black' : 'white',
            borderRadius: '6px',
            px: 2.5,
            py: 2,
            fontSize: 16,
            fontWeight: 600,
            cursor: 'pointer',
            ':hover': {
              bgcolor: 'primary.veryLight',
            },
          }}
        >
          Custom
        </Box>
      )}

      {showCustomInputs && (
        <Box
          sx={{
            p: 2,
            border: '2px #006AFF solid',
            borderRadius: 1.5,
            position: 'relative',
            top: -10,
          }}
        >
          <Subsection
            titleSx={{ fontSize: 16, fontWeight: 600 }}
            title="Custom"
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                mt: 4,
                mb: 5,
                width: '100%',
              }}
            >
              <Box sx={{ width: '100%' }}>
                <TextInput
                  source="unit-name-singular"
                  label="Singular"
                  onChange={e => handleInputChange(e, setSingularCount, 16)}
                  size="medium"
                  fullWidth
                />
                <Typography variant="caption" color="textSecondary">
                  {singularCount}/16 Characters
                </Typography>
              </Box>

              <Box sx={{ width: '100%' }}>
                <TextInput
                  source="unit-name-plural"
                  label="Plural"
                  onChange={e => handleInputChange(e, setPluralCount, 16)}
                  size="medium"
                  fullWidth
                />
                <Typography variant="caption" color="textSecondary">
                  {pluralCount}/16 Characters
                </Typography>
              </Box>
            </Box>
          </Subsection>
        </Box>
      )}
      <Box sx={{ position: 'relative' }}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: 0,
            right: 0,

            height: 'fit-content',
            width: '80%',
            margin: 'auto',
            transform: 'translateY(-50%)',
          }}
        >
          <Typography
            sx={{
              color: 'black',
              textAlign: 'center',
              fontSize: { xs: 13, sm: 18 },
              fontWeight: 600,
            }}
          >
            20 Total {selectedUnits?.plural}
          </Typography>
          <Typography
            sx={{
              color: 'black',
              textAlign: 'center',
              fontSize: { xs: 12, sm: 17 },
            }}
          >
            You got 1 for your purchase today.
          </Typography>
          <Typography
            sx={{
              color: 'black',
              textAlign: 'center',
              fontSize: { xs: 12, sm: 17 },
            }}
          >
            You’re all done!
          </Typography>
        </Box>
        <img
          style={{ width: '100%', marginTop: '20px' }}
          src="/assets/loyalty/step1/phone.png"
          alt="Phone Illustration"
        />
      </Box>
    </>
  );
}
