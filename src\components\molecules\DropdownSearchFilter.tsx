import { useState } from 'react';
import {
  Checkbox,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
} from '@mui/material';

import { CustomSelectMui } from '../atoms/inputs/CustomSelect';

interface DropdownSearchFilterProps {
  variants: any;
  label: string;
}

export default function DropdownSearchFilter({
  variants = [],
  label,
}: DropdownSearchFilterProps) {
  const [variantName, setVariantName] = useState([]);

  const handleChange = (event: any) => {
    const {
      target: { value },
    } = event;

    let duplicateRemoved: any = [];

    value.forEach((item: any) => {
      if (duplicateRemoved.findIndex((o: any) => o.id === item.id) >= 0) {
        duplicateRemoved = duplicateRemoved.filter(
          (x: any) => x.id === item.id
        );
      } else {
        duplicateRemoved.push(item);
      }
    });

    setVariantName(duplicateRemoved);
  };

  return (
    <FormControl sx={{ width: 300, mb: 0 }}>
      <InputLabel id="multiple-checkbox-label">{label}</InputLabel>
      <CustomSelectMui
        variant="standard"
        labelId="multiple-checkbox-label"
        id="multiple-checkbox"
        multiple
        value={variantName}
        onChange={handleChange}
        input={<OutlinedInput label={label} />}
        renderValue={(selected: any) =>
          selected.map((x: any) => x.name).join(', ')
        }
      >
        {variants.map((variant: any) => (
          <MenuItem key={variant.id} value={variant}>
            <Checkbox
              checked={
                variantName.findIndex((item: any) => item.id === variant.id) >=
                0
              }
            />
            <ListItemText primary={variant.name} />
          </MenuItem>
        ))}
      </CustomSelectMui>
    </FormControl>
  );
}
