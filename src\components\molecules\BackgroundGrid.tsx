import React, { PropsWithChildren } from 'react';

import { useTheme } from '../../contexts';
import styles from './BackgroundGrid.module.css';

export interface BackgroundGridProps extends PropsWithChildren {
  tileWidth?: number;
  tileHeight?: number;
  onClick?: () => void;
  style?: any;
}

export function BackgroundGrid({
  tileWidth = 0,
  tileHeight = 0,
  onClick = () => {},
  style,
  children,
}: BackgroundGridProps) {
  const { theme } = useTheme();

  return (
    <div
      onClick={onClick}
      className={styles.grid}
      style={
        {
          '--grid-color': theme.palette.mode === 'dark' ? '#515151' : '#cdcaca',
          '--grid-size-width': `${tileWidth}px`,
          '--grid-size-height': `${tileHeight}px`,
          ...style,
        } as React.CSSProperties
      }
    >
      {children}
    </div>
  );
}
