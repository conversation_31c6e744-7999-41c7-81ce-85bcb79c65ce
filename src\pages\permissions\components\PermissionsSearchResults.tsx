import { useEffect, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import { cloneDeep } from 'lodash';
import { useTranslation } from 'react-i18next';

import {
  PermissionPages,
  permissionsIdToLabel,
  permissionsTree,
} from './constants';

interface PermissionsSearchResultsProps {
  query: string;
  setCurrentPage: (page: PermissionPages) => void;
  resetQuery: () => void;
}

interface TranslatedPermission {
  title: string;
  description: string;
}

interface AggregatedPermissions {
  orders: TranslatedPermission[];
  customers: TranslatedPermission[];
  giftCards: TranslatedPermission[];
  settings: TranslatedPermission[];
}

const initialValues = {
  orders: [],
  customers: [],
  giftCards: [],
  settings: [],
};

const PermissionsSearchResults = ({
  query,
  setCurrentPage,
  resetQuery,
}: PermissionsSearchResultsProps) => {
  const [permissionsTranslated, setPermissionsTranslated] =
    useState<AggregatedPermissions>(initialValues);
  const [filteredResults, setFilteredResults] =
    useState<AggregatedPermissions>(initialValues);

  const { t, i18n } = useTranslation();

  useEffect(() => {
    const aggregatedPermissions: AggregatedPermissions =
      cloneDeep(initialValues);

    // Iterate through permission groups in permissionsTree
    Object.keys(permissionsTree).forEach(pageKey => {
      const category = permissionsTree[pageKey as PermissionPages];
      category.forEach(
        (group: { groupLabel: string; possiblePermissions: number[] }) => {
          group.possiblePermissions.forEach((permissionId: number) => {
            aggregatedPermissions[pageKey as PermissionPages].push({
              title: t(
                `permissions.${permissionsIdToLabel[permissionId]}.title`
              ),
              description: t(
                `permissions.${permissionsIdToLabel[permissionId]}.description`
              ),
            });
          });
        }
      );
    });

    setPermissionsTranslated(aggregatedPermissions);
  }, [i18n.language]);

  useEffect(() => {
    if (!query) {
      return;
    }

    const checkIncludes = (el: TranslatedPermission) =>
      el.title.toLocaleLowerCase().includes(query.toLocaleLowerCase()) ||
      el.description.toLocaleLowerCase().includes(query.toLocaleLowerCase());

    setFilteredResults({
      orders: permissionsTranslated.orders.filter(checkIncludes),
      customers: permissionsTranslated.customers.filter(checkIncludes),
      giftCards: permissionsTranslated.giftCards.filter(checkIncludes),
      settings: permissionsTranslated.settings.filter(checkIncludes),
    });
  }, [query]);

  if (!query) {
    return <></>;
  }

  return (
    <Box>
      {Object.keys(filteredResults).map(pageKey => {
        if (!filteredResults[pageKey as PermissionPages].length) {
          return <></>;
        }

        return (
          <Box mt={1}>
            <Divider />
            <Box sx={{ display: 'flex', alignItems: 'center', p: 1, gap: 2 }}>
              <img width="20px" src={`/assets/icons/${pageKey}.svg`} />
              <Typography variant="body2">
                {t(`permissions.pages.${pageKey}.title`)}
              </Typography>
            </Box>
            <Divider />
            <Box ml={5} py={1.5}>
              {filteredResults[pageKey as PermissionPages].map(permission => (
                <Box
                  onClick={() => {
                    setCurrentPage(pageKey as PermissionPages);
                    resetQuery();
                  }}
                  mt={1}
                  sx={{ cursor: 'pointer' }}
                >
                  <Typography variant="caption">
                    {permission.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export { PermissionsSearchResults };
