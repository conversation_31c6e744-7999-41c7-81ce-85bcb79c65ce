import { useMemo } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import { Box, Typography } from '@mui/material';
import { DateRange } from '@mui/x-date-pickers-pro';
import { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

import { formatNumber } from '../../utils/formatNumber';
import { DarkTooltip } from './DarkTooltip';

interface PercentageChangeProps {
  value: number | string | undefined;
  tooltipTitle?: string;
  date?: DateRange<Dayjs>;
}

export default function PercentageChange({
  value,
  date,
  // tooltipTitle = 'period',
}: PercentageChangeProps) {
  const { t } = useTranslation();
  const hasData = typeof value === 'number';

  const formattedDate = useMemo(() => {
    return date
      ? date[0]?.isSame(date[1])
        ? date[0]?.format('DD MMM YYYY')
        : date[0]?.format('DD MMM YYYY') +
          ' - ' +
          date[1]?.format('DD MMM YYYY')
      : '';
  }, [date]);

  const isNegative = typeof value === 'number' && value < 0;
  const isPositive = typeof value === 'number' && value > 0;

  return (
    <DarkTooltip
      text={`${t('dashboard.vsPrior')}`}
      label={hasData ? formattedDate : t('dashboard.notEnoughData')}
    >
      <Box
        sx={{
          borderRadius: '6px',
          bgcolor: isPositive
            ? 'success.light'
            : isNegative
              ? 'error.light'
              : 'custom.gray200',
          color: isPositive
            ? 'success.main'
            : isNegative
              ? 'error.main'
              : 'custom.gray600',
          padding: 0.5,
          paddingRight: 1,
          display: 'flex',
          alignItems: 'center',
          fontWeight: 600,
          fontSize: 14,
          width: 'fit-content',
          height: 'fit-content',
        }}
      >
        {isPositive && <ArrowDropUpIcon />}
        {isNegative && <ArrowDropDownIcon />}
        {!isPositive && !isNegative && null}
        {hasData ? formatNumber(value, 'percentage') : 'N/A'}
      </Box>
    </DarkTooltip>
  );
}
