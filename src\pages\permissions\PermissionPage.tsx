import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '../../components/molecules/PageTitle';
import PermissionCreate from './PermissionCreate';
import PermissionEdit from './PermissionEdit';
import PermissionList from './PermissionList';

export default function PermissionPage() {
  const { t } = useTranslation();

  return (
    <Box p={2}>
      <PageTitle
        title={t('permissions.title')}
        description={
          <>
            {t('permissions.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
      />
      <PermissionList />
      <PermissionEdit />
      <PermissionCreate />
    </Box>
  );
}
