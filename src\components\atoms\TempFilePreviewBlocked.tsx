import React from 'react';
import { AccessTime, Block, Save } from '@mui/icons-material';
import { Alert, Box, Card, CardContent, Chip, Typography } from '@mui/material';

interface TempFilePreviewBlockedProps {
  fileName?: string;
  fileType?: string;
  onSave?: () => void;
  variant?: 'card' | 'alert' | 'minimal';
}

/**
 * Component displayed when temp file preview is blocked
 * Shows clear messaging about why preview is not available
 */
export const TempFilePreviewBlocked: React.FC<TempFilePreviewBlockedProps> = ({
  fileName,
  fileType,
  onSave,
  variant = 'card',
}) => {
  const message = 'Preview not available for temporary files';
  const description =
    'File preview will be available after saving your changes.';

  if (variant === 'minimal') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: 'text.secondary',
        }}
      >
        <Block fontSize="small" />
        <Typography variant="body2">{message}</Typography>
      </Box>
    );
  }

  if (variant === 'alert') {
    return (
      <Alert
        severity="info"
        icon={<AccessTime />}
        action={
          onSave && (
            <Chip
              label="Save Changes"
              onClick={onSave}
              size="small"
              color="primary"
              icon={<Save />}
            />
          )
        }
      >
        <Typography variant="body2" fontWeight="medium">
          {message}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
        {fileName && (
          <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
            File: {fileName}
          </Typography>
        )}
      </Alert>
    );
  }

  // Card variant (default)
  return (
    <Card
      variant="outlined"
      sx={{
        textAlign: 'center',
        backgroundColor: 'grey.50',
        borderStyle: 'dashed',
        borderColor: 'grey.300',
      }}
    >
      <CardContent sx={{ py: 4 }}>
        <Box sx={{ mb: 2 }}>
          <AccessTime
            sx={{
              fontSize: 48,
              color: 'text.secondary',
              opacity: 0.7,
            }}
          />
        </Box>

        <Typography variant="h6" gutterBottom color="text.secondary">
          {message}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>

        {fileName && (
          <Box sx={{ mb: 2 }}>
            <Chip
              label={fileName}
              size="small"
              variant="outlined"
              sx={{
                backgroundColor: 'background.paper',
                borderColor: 'grey.300',
              }}
            />
            {fileType && (
              <Chip
                label={fileType.toUpperCase()}
                size="small"
                variant="filled"
                color="secondary"
                sx={{ ml: 1 }}
              />
            )}
          </Box>
        )}

        {onSave && (
          <Box sx={{ mt: 3 }}>
            <Chip
              label="Save Changes to Enable Preview"
              onClick={onSave}
              color="primary"
              icon={<Save />}
              clickable
              sx={{
                px: 2,
                py: 0.5,
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default TempFilePreviewBlocked;
