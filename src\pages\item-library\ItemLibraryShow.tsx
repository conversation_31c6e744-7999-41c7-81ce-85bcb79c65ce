import { useCallback, useMemo, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Box,
  Card,
  FormControl,
  Grid2 as Grid,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  Tooltip,
  Typography,
} from '@mui/material';
import { ShowDialog } from '@react-admin/ra-form-layout';
import {
  AutocompleteInput,
  NumberInput,
  SelectInput,
  SimpleShowLayout,
  TextField,
  TextInput,
  useGetList,
  useLocaleState,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import FileUploadInput from '~/components/atoms/inputs/FileUploadInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { PublicNameAndDescriptionSection } from '~/components/molecules/PublicNameAndDescriptionSection';
import {
  useGetListHospitalityCatalogsLive,
  useGetListHospitalityCategoriesLive,
  useGetListMeasureUnits,
  useGetListVatsLive,
} from '~/providers/resources';
import { getCurrencySymbolByCode } from '~/providers/utils/getCurrencySymbol';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import { MenuItem as MenuItemI } from '../../components/organisms/menu-catalog-dnd/types';

interface ShowItemModalProps {
  path?: string;
  initialValues?: MenuItemI;
}

const PriceVariationsSection = (props: any) => {
  const { t } = useTranslation('');
  const { catalogSpecific, getCatalogName, vatsChoices } = props;

  // TODO! something about the flicker ... this whole part doesn't render because temporarily there are no vats
  if (vatsChoices.length === 0) return null;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h5">{t('itemLibrary.variations')}</Typography>
      {Object.entries(catalogSpecific).map(
        ([catalogId, catalogSpecificValues]: [string, any]) => {
          const catalogName = getCatalogName(catalogId);
          if (!catalogName) return null;
          return (
            <Box key={catalogId}>
              <Box>
                <Grid container spacing={2} alignItems="center">
                  <Grid size={{ xs: 4 }}>
                    <TextField source="" defaultValue={catalogName} />
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <NumberInput
                      readOnly
                      defaultValue={catalogSpecificValues.price}
                      source={`catalogSpecific.${catalogId}.price`}
                      label={t('shared.price')}
                      variant="outlined"
                      format={value => value / 10000}
                      slotProps={{
                        input: {
                          endAdornment: getCurrencySymbolByCode('RON'),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <SelectInput
                      readOnly
                      source={`catalogSpecific.${catalogId}.vat`}
                      label={t('shared.tva')}
                      choices={vatsChoices}
                      translateChoice={false}
                    />
                  </Grid>
                </Grid>
              </Box>
            </Box>
          );
        }
      )}
    </Box>
  );
};

const ItemLibraryShowInner = () => {
  const [locale] = useLocaleState();
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');

  const [isOpenAgeModal, setIsOpenAgeModal] = useState(false);
  const [isOpenNutritionalValuesModal, setIsOpenNutritionalValuesModal] =
    useState(false);

  // Watch form values (read-only in show mode)
  const catalogSpecific = record?.catalogSpecific || {};
  const age = record?.age;
  const dietaryPreferences = record?.dietaryPreferences || [];
  const allergens = record?.allergens || [];
  const nutritionalValues = record?.nutritionalValues;

  const { data: catalogs } = useGetListHospitalityCatalogsLive();
  const getCatalogName = useCallback(
    (catalogId: string) => {
      const catalog = catalogs?.find((c: any) => c.id === catalogId);
      if (catalog) {
        return catalog.name;
      } else {
        return undefined;
      }
    },
    [catalogs]
  );

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      name: `${vat.value}%`,
    }));
  }, [vats]);

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  const dietaryPreferencesOptions = [
    'dairy-free',
    'gluten-free',
    'halal',
    'kosher',
    'nut-free',
    'vegan',
    'vegetarian',
    'low-sugar',
    'low-carb',
    'low-sodium',
  ];

  const allergensOptions = [
    'celery',
    'crustaceans',
    'eggs',
    'fish',
    'gluten',
    'lupin',
    'milk',
    'molluscs',
    'mustard',
    'peanuts',
    'sesame',
    'soy',
    'sulphites',
    'tree-nuts',
  ];

  const { data: categories } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });

  const { data: measureUnits } = useGetListMeasureUnits();
  const MeasureUnitsOptionRenderer = () => {
    const record = useRecordContext();
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}
      >
        <span>
          {record!.name[locale]}{' '}
          {record!.symbol[locale] ? `(${record!.symbol[locale]})` : ''}
        </span>
        <span>{t(`measureUnits.types.${record!.type}`)}</span>
      </Box>
    );
  };

  const measureUnitsOptionText = <MeasureUnitsOptionRenderer />;

  interface MeasureUnitChoice {
    name: Record<string, string>;
    symbol: Record<string, string>;
    type: string;
  }

  const measureUnitsInputText = (choice: MeasureUnitChoice): string =>
    `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`;

  const measureUnitsMatchSuggestion = (
    filter: string,
    choice: MeasureUnitChoice
  ): boolean => {
    return `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`
      .toLocaleLowerCase()
      .includes(filter.toLowerCase());
  };

  return (
    <>
      <ModalHeader handleClose={handleClose} title={t('itemsLibrary.viewItem')}>
        {/* No save button in show mode */}
      </ModalHeader>
      <Box
        sx={{
          padding: 3,
          width: '100%',
          maxWidth: '800px',
          margin: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        <Box>
          <Typography variant="h5">{t('menu.details')}</Typography>
        </Box>
        <Box>
          <TextInput readOnly source="name" label={t('shared.name')} />
        </Box>
        <Box>
          <AutocompleteInput
            readOnly
            defaultValue=""
            source="groupId"
            label={t('shared.category_capitalize')}
            choices={categories}
          />
        </Box>
        <Box>
          <AutocompleteInput
            readOnly
            defaultValue=""
            source="measureUnit"
            label={t('measure-units.title')}
            choices={measureUnits}
            optionText={measureUnitsOptionText}
            inputText={measureUnitsInputText}
            matchSuggestion={measureUnitsMatchSuggestion}
          />
        </Box>
        <CustomDivider />
        <PriceVariationsSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
          vatsChoices={vatsChoices}
        />
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">
            {t('itemLibrary.identifications')}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2,
              mb: 2,
            }}
          >
            <TextInput readOnly source="sku" label="SKU" />
            <TextInput readOnly source="gtin" label="GTIN" />
            <TextInput readOnly source="ean" label="EAN" />
          </Box>
        </Box>
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">Images</Typography>
          <FileUploadInput
            readOnly
            source="images"
            label="Product Images"
            multiple={true}
            maxFiles={3}
            maxSize={300 * 1024} // 300KB
            disabled={true}
            acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
            helperText="Product images (view only)"
          />
        </Box>
        <CustomDivider />

        {/* Restrictions Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">Restrictions</Typography>

          {/* Age Restriction */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: 'solid 1px',
              borderColor: 'custom.gray400',
              borderRadius: 2,
              height: '56px',
              paddingX: 2,
              opacity: 0.7, // Show as read-only
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Typography color="textSecondary" fontSize="14px">
                {t('menu.ageRestriction')}
              </Typography>
              <Tooltip title={t('menu.ageRestrictionTooltip')}>
                <IconButton>
                  <InfoOutlinedIcon color="disabled" />
                </IconButton>
              </Tooltip>
            </Box>
            {age ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Box>
                  <Typography
                    color="textSecondary"
                    fontSize="15px"
                    textAlign="left"
                    fontWeight={'bold'}
                  >
                    {age === 18
                      ? t('menu.ageRestriction18')
                      : `${t('menu.ageRestriction')} ${age}`}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <Typography color="textSecondary" fontSize="14px">
                {t('shared.notSet')}
              </Typography>
            )}
            {age ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <img src="/assets/icons/check.svg" alt="check-icon" />
              </Box>
            ) : (
              ''
            )}
          </Box>
        </Box>

        <CustomDivider />

        <PublicNameAndDescriptionSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
        />

        <Box>
          <CustomDivider />
        </Box>

        <Box>
          <Typography variant="h5">
            {t('menu.nutritionalInformation')}
          </Typography>
        </Box>

        <Box>
          <NumberInput
            readOnly
            source="calories"
            fullWidth
            label={t('menu.calorieCount')}
            sx={{ mb: 3 }}
          />

          {/* Dietary Preferences - Read Only */}
          <FormControl fullWidth>
            <InputLabel id="dietary-preferences-label">
              {t('menu.dietaryPreferences')}
            </InputLabel>
            <Select
              disabled
              fullWidth
              labelId="dietary-preferences-label"
              id="dietary-preferences-checkbox"
              multiple
              value={dietaryPreferences}
              input={<OutlinedInput label="Dietary preferences" />}
              renderValue={selected =>
                selected
                  .map((el: string) =>
                    t(`add-item.dietary-preferences.${el}.title`)
                  )
                  .join(', ')
              }
              sx={{ mb: 3 }}
            >
              {dietaryPreferencesOptions.map(key => (
                <MenuItem key={key} value={key}>
                  <ListItemText
                    primary={t(`add-item.dietary-preferences.${key}.title`)}
                    secondary={t(
                      `add-item.dietary-preferences.${key}.description`
                    )}
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Allergens - Read Only */}
          <FormControl fullWidth>
            <InputLabel id="allergens-label">{t('menu.allergens')}</InputLabel>
            <Select
              disabled
              fullWidth
              labelId="allergens-label"
              id="allergens-checkbox"
              multiple
              value={allergens}
              input={<OutlinedInput label={t('menu.allergens')} />}
              renderValue={selected =>
                selected
                  .map((el: string) => t(`add-item.allergens.${el}.title`))
                  .join(', ')
              }
              sx={{ mb: 3 }}
            >
              {allergensOptions.map(key => (
                <MenuItem key={key} value={key}>
                  <ListItemText
                    primary={t(`add-item.allergens.${key}.title`)}
                    secondary={t(`add-item.allergens.${key}.description`)}
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Nutritional Values - Read Only */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: 'solid 1px',
              borderColor: 'custom.gray400',
              borderRadius: 2,
              height: '56px',
              paddingX: 2,
              opacity: 0.7, // Show as read-only
            }}
          >
            <Typography color="textSecondary" fontSize="14px">
              {t('menu.nutritionalValues')}
            </Typography>
            {nutritionalValues && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <img src="/assets/icons/check.svg" alt="check-icon" />
              </Box>
            )}
          </Box>
        </Box>

        <Box>
          <CustomDivider />
        </Box>
      </Box>
    </>
  );
};

export const ItemLibraryShow = ({ initialValues }: ShowItemModalProps) => {
  return (
    <ShowDialog {...getFullscreenModalProps()}>
      <SimpleShowLayout sx={{ p: 0 }}>
        <ItemLibraryShowInner />
      </SimpleShowLayout>
    </ShowDialog>
  );
};
