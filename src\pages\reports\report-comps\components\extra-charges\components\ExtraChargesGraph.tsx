import { Typography } from '@mui/material';

import ReportMultiLineChart from '~/pages/reports/components/ReportMultiLineChart';
import { formatNumber } from '~/utils/formatNumber';

export default function ExtraChargesGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency: 'RON' | 'USD';
}) {
  if (!data.datasets || !data.labels) {
    return <></>;
  }

  const formatData = (data: string | number) => {
    return formatNumber(data, currency);
  };

  return (
    <>
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        fontWeight="500"
        mb={1.5}
      >
        {data.datasets.length >= 3 &&
          `Top ${data.datasets.length} Categories: Value`}
      </Typography>
      <ReportMultiLineChart
        datasets={data.datasets}
        labels={data.labels}
        formatData={formatData}
      />
    </>
  );
}
