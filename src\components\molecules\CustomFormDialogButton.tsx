import React, { ReactElement, useMemo, useRef, useState } from 'react';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { IconButton, Tooltip, Button } from '@mui/material';
import {
  FormDialogContext,
  OnCloseCallbackType,
} from '@react-admin/ra-form-layout';
import {ButtonProps, useEvent, useTranslate } from 'react-admin';
import { useTranslation } from 'react-i18next';

/**
 * Internal component which creates a dialog, along with a `<Button>` to open it.
 * This component is also responsible for managing the open/close state of the Dialog
 * (using an internal state, not the router).
 *
 * @param props.dialog A React Element containing the dialog
 * @param props.inline Optional - Set to true for an inline button, having only an icon and a tooltip
 * @param props.icon Optional - The icon associated to the button label
 * @param props.label Optional - The button label
 * @param props.ButtonProps Optional - An object containing props to pass to the MUI Button
 */
export const CustomFormDialogButton = (props: any) => {
  const { t } = useTranslation();
  const translate = useTranslate();
  const [isOpen, setIsOpen] = useState(false);
  const onCloseCallbackRef = useRef<OnCloseCallbackType>(null);

  const open = useEvent(() => {
    setIsOpen(true);
  });

  const close = useEvent(async () => {
    let canClose: boolean | void = true;
    if (onCloseCallbackRef.current) {
      // Wait next tick to support pessimistic mode
      await new Promise(res => setTimeout(res, 0));
      canClose = await onCloseCallbackRef.current();
    }
    if (canClose) {
      setIsOpen(false);
      props.setOpen(false);
    }
  });

  const registerOnCloseCallback = useEvent(
    (onCloseCallback: OnCloseCallbackType) => {
      onCloseCallbackRef.current = onCloseCallback;
    }
  );

  const contextValue = useMemo(
    () => ({
      isOpen,
      open,
      close,
      registerOnCloseCallback,
    }),
    [close, isOpen, open, registerOnCloseCallback]
  );

  const { icon = defaultIcon, label = '', inline, dialog, ButtonProps } = props;

  const { onClick: onClickProp, ...restButtonProps } = ButtonProps || {};

  const onClick = useEvent((e: any) => {
    if (onClickProp) {
      onClickProp(e);
    }
    // open();
    props.setOpen(true);
    e.stopPropagation();
  });

  const translatedLabel = translate(label, { _: label });

  const button = inline ? (
    <Tooltip title={translatedLabel}>
      <IconButton
        aria-label={translatedLabel}
        size="large"
        color="primary"
        {...restButtonProps}
        onClick={onClick}
      >
        {icon}
      </IconButton>
    </Tooltip>
  ) : (
    <Button  {...restButtonProps} onClick={onClick}>
      {t('partnerIntegrations.getStarted')}
    </Button>
  );

  return (
    <FormDialogContext.Provider value={contextValue}>
      {button}
      {dialog}
    </FormDialogContext.Provider>
  );
};

const defaultIcon = <OpenInNewIcon />;

export type FormDialogButtonProps = {
  inline?: boolean;
  icon?: ReactElement;
  dialog: ReactElement;
  label?: string;
  ButtonProps?: ButtonProps;
};
