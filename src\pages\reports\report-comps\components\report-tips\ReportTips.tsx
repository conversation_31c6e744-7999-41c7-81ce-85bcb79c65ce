import { useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListLocationsLive } from '~/providers/resources';
import ReportTipsTable from './components/ReportTipsTable';

const REPORT_TYPE = 'compedTips';

export default function ReportTips({
  updateCompsData,
  filters,
}: {
  updateCompsData?: any;
  filters: any;
}) {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [rawData, setRawData] = useState<any>();
  const [currency, setCurrency] = useState<string>('RON');
  const { data: sellpoints } = useGetListLocationsLive();
  const { sellPointId, dateRange } = useGlobalResourceFilters();

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const { tableData } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [] };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      ['vat', 'reason']
    );

    const tableData =
      groupedTableData[0]?.report?.sort((a, b) => {
        return (b.value ?? 0) - (a.value ?? 0);
      }) || [];

    return { tableData };
  }, [filters, rawData]);

  useEffect(() => {
    if (!tableData || (tableData && tableData.length === 0)) {
      updateCompsData('totalTips', {});
    }
  }, [tableData]);

  return (
    <Box>
      <PageTitle title={t('giftCards.Comped Tips')} hideBorder />
      <Box sx={{ pb: 3 }}>
        <ReportTipsTable
          updateCompsData={updateCompsData}
          tableData={tableData}
          filters={filters}
        />
      </Box>
    </Box>
  );
}
