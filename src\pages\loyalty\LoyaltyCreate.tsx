import { useState } from 'react';
import { Box, Button, Theme, useMediaQuery } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm } from 'react-admin';
import { useNavigate } from 'react-router-dom';

import ModalHeader from '../../components/molecules/ModalHeader';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import LoyaltyForm from './components/LoyaltyForm';

export default function LoyaltyCreate() {
  const navigate = useNavigate();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const [isContinueDisabled, setIsContinueDisabled] = useState(true);
  const [step, setStep] = useState<number>(0);

  const handleClose = () => {
    setStep(0);
    navigate('/loyalty');
  };

  return (
    <CreateDialog {...getFullscreenModalProps()}>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ModalHeader
          handleClose={handleClose}
          title="Rewards"
          alignCenter={!isXSmall}
        >
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              // @ts-ignore
              variant="contained-light"
              onClick={() => {
                if (step === 0) {
                  handleClose();
                } else {
                  setStep(step - 1);
                }
              }}
            >
              Back
            </Button>
            {step !== 3 ? (
              <Button
                disabled={isContinueDisabled}
                variant="contained"
                onClick={() => step !== 3 && setStep(step + 1)}
              >
                Continue
              </Button>
            ) : (
              <SaveButton
                disabled={false}
                type="submit"
                label="Finish"
                icon={<></>}
              />
            )}
          </Box>
        </ModalHeader>
        <LoyaltyForm
          isEditing={false}
          setIsContinueDisabled={setIsContinueDisabled}
          step={step}
          setStep={setStep}
        />
      </SimpleForm>
    </CreateDialog>
  );
}
