import { useState } from 'react';
import { Box } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm, useRedirect } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { redirect, useNavigate } from 'react-router-dom';

import { RESOURCES } from '~/providers/resources';
import ModalHeader from '../../components/molecules/ModalHeader';
import MenuCreateStep1 from '../../components/organisms/menu-catalog-dnd/menu-create-steps/MenuCreateStep1';
import MenuCreateStep2 from '../../components/organisms/menu-catalog-dnd/menu-create-steps/MenuCreateStep2';

const MenuCreateInner = () => {
  const { setValue } = useFormContext();
  const redirect = useRedirect();
  const { t } = useTranslation('');

  const [step, setStep] = useState<number>(0);

  const handleClose = () => {
    setStep(0);
    redirect('list', RESOURCES.HOSPITALITY_CATALOGS);
  };

  const beforeHandleSubmit = () => {
    setValue('pages', [[]]);
  };

  return (
    <>
      <ModalHeader handleClose={handleClose} title={t('menu.createMenu')}>
        {step === 1 && (
          <SaveButton
            onClick={beforeHandleSubmit}
            type="submit"
            label={t('shared.save')}
            icon={<></>}
            alwaysEnable
          />
        )}
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        {step === 0 && (
          <MenuCreateStep1
            pickOption={() => {
              setStep(1);
            }}
          />
        )}
        {step === 1 && <MenuCreateStep2 />}
      </Box>
    </>
  );
};

export default function MenuCreate() {
  return (
    <CreateDialog maxWidth="sm" fullWidth>
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <MenuCreateInner />
      </SimpleForm>
    </CreateDialog>
  );
}
