import { useCallback, useMemo } from 'react';
import { useDraggable } from '@dnd-kit/core';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Box, Typography } from '@mui/material';

import { useTheme } from '~/contexts';
import { formatNumber } from '~/utils/formatNumber';
import { calculateClosestSnap } from '../../../utils/calculateClosestSnap';
import {
  MENU_TILE_HEIGHT,
  MENU_TILE_WIDTH,
  MOBILE_MENU_TILE_WIDTH,
  ROWS_NO,
} from './CatalogContainer';
import styles from './CatalogItem.module.css';
import { BaseMenuItem, MenuGroup, MenuItem } from './types';

type CatalogItemProps = Omit<
  Partial<MenuItem> & Partial<MenuGroup> & BaseMenuItem,
  'id'
> & {
  selected: boolean;
  error: boolean;
  id: number;
  startIndex: number;
  columns: number;
  openDisplayGroup: () => void;
};

export default function CatalogItem({
  displayName,
  id,
  type,
  color,
  position,
  selected,
  error,
  startIndex,
  columns,
  items,
  price,
  openDisplayGroup,
}: CatalogItemProps) {
  const { startX, startY, endX, endY } = position || {
    startX: 0,
    startY: 0,
    endX: 1,
    endY: 1,
  };
  const { theme } = useTheme();

  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: id + 1,
    });

  const getBackgroundColor = useCallback(() => {
    if (error) return theme.palette.error.main;
    if (type === 'displayGroup') return color;
    return theme.palette.background.paper;
  }, [error, color, type]);

  const getTextColor = useCallback(() => {
    if (type === 'function') return 'primary.main';
    if (type === 'displayGroup' || theme.palette.mode === 'dark')
      return 'white';
    return 'black';
  }, [selected, type]);

  const isMobile = columns === 2;

  const transformPos = useMemo(() => {
    const x = calculateClosestSnap(
      transform?.x ?? 0,
      isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH
    );
    const y = calculateClosestSnap(transform?.y ?? 0, MENU_TILE_HEIGHT);

    const newSX = +startX + x;
    const finalX = newSX < 0 ? 0 : newSX >= columns ? columns - 1 : newSX;

    const newSY = +startY + y;
    // check if tile is small or tall
    const height = +endY - +startY;
    const finalY =
      newSY < 0 ? 0 : newSY > ROWS_NO - height ? ROWS_NO - height : newSY;

    return `${(finalX - startX) * (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH)}px, ${
      (finalY - startY) * MENU_TILE_HEIGHT
    }px`;
  }, [transform, startX, startY, endY]);

  return (
    <>
      <div
        className={styles.draggable + ' ' + (isDragging && styles.dragging)}
        style={
          {
            '--translate-x': `${transform?.x ?? 0}px`,
            '--translate-y': `${transform?.y ?? 0}px`,
            '--bg-color': getBackgroundColor(),
            '--z-index': selected ? 2 : 1,
            top: `${startY * MENU_TILE_HEIGHT + 3}px`,
            left: `${(startX - startIndex) * (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH) + 3}px`,
            width: `${(endX - startX) * (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH) - 4}px`,
            height: `${(endY - startY) * MENU_TILE_HEIGHT - 4}px`,
          } as React.CSSProperties
        }
      >
        <div
          {...attributes}
          aria-label="Draggable"
          data-cypress="draggable-item"
          {...listeners}
          tabIndex={undefined}
          ref={setNodeRef}
          style={{
            borderLeft:
              !type || type === 'product' || type === 'productCombo'
                ? `solid 5px ${color}`
                : 'none',
            alignItems: type === 'displayGroup' ? 'flex-start' : 'center',
          }}
        >
          <label>
            <Typography
              variant="caption"
              fontSize={type === 'displayGroup' ? 16 : 14}
              lineHeight={1}
              color={getTextColor()}
            >
              {displayName}
            </Typography>
            {type === 'displayGroup' && (
              <Typography
                display="block"
                variant="caption"
                sx={{ opacity: 0.5 }}
              >
                {items?.length} items
              </Typography>
            )}
          </label>
          {(type === 'product' || !type) && (
            <span
              style={{
                position: 'absolute',
                right: 0,
                bottom: 0,
                backgroundColor:
                  theme.palette.mode === 'dark' ? '#26262B' : '#f0f0f0',
                padding: '0 4px',
                borderRadius: '6px',
                height: '20px',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Typography variant="caption" fontSize={10}>
                {!!price ? formatNumber(+price / 10000, 'currency') : ''}
              </Typography>
            </span>
          )}
          <div
            className={selected ? styles.selectedTile : ''}
            style={{
              borderTopLeftRadius:
                !type || type === 'product' || type === 'productCombo'
                  ? '0px'
                  : '6px',
              borderBottomLeftRadius:
                !type || type === 'product' || type === 'productCombo'
                  ? '0px'
                  : '6px',
            }}
          />
        </div>
        {type === 'displayGroup' && (
          <Box
            className={styles.openGroupHandle}
            style={selected ? { display: 'flex' } : {}}
            onClick={openDisplayGroup}
          >
            <KeyboardArrowRightIcon />
          </Box>
        )}
      </div>

      {/* Renders a shadow tile when an item is being dragged. */}
      {isDragging && (transform?.x || transform?.y) && !error && (
        <div
          className={styles.draggable}
          style={
            {
              backgroundColor: '#878787',
              borderRadius: '6px',
              top: `${startY * MENU_TILE_HEIGHT + 3}px`,
              left: `${(startX - startIndex) * (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH) + 3}px`,
              width: `${(endX - startX) * (isMobile ? MOBILE_MENU_TILE_WIDTH : MENU_TILE_WIDTH) - 4}px`,
              height: `${(endY - startY) * MENU_TILE_HEIGHT - 4}px`,
              transform: `translate3d(${transformPos}, 0)`,
              zIndex: 1,
            } as React.CSSProperties
          }
        />
      )}
    </>
  );
}
