import { Locale } from 'date-fns-v4';

// create a constant that will cache the imported locales
const localeCache: Record<string, Locale> = {};

export async function getDateFnsLocale(localization: string): Promise<Locale> {
  // if the locale is already in the cache return it
  if (localeCache[localization]) {
    return localeCache[localization];
  }
  // if the locale is not in the cache we need to import it
  // and store it in the cache
  localeCache[localization] = await getDateFnsLocaleImport(localization);
  return localeCache[localization];
}

export async function getDateFnsLocaleImport(
  localization: string
): Promise<Locale> {
  // dynamically import the locale of date-fns according to the localization
  // if the localization is not found we need to try to import again only with the language
  // if the language is not found we need to use the default locale
  let localeModule;
  let dateFnsLocaleString;
  switch (localization) {
    case 'ro-RO':
      dateFnsLocaleString = 'ro';
      localeModule = (await import(`date-fns-v4/locale/ro`)) as unknown;
      break;
    case 'ro':
      dateFnsLocaleString = 'ro';
      localeModule = (await import(`date-fns-v4/locale/ro`)) as unknown;
      break;
    case 'en-US':
      dateFnsLocaleString = 'enUS';
      localeModule = (await import(`date-fns-v4/locale/en-US`)) as unknown;
      break;
    case 'en-GB':
      dateFnsLocaleString = 'enGB';
      localeModule = (await import(`date-fns-v4/locale/en-GB`)) as unknown;
      break;
    default:
      dateFnsLocaleString = 'enIE';
      localeModule = (await import(`date-fns-v4/locale/en-IE`)) as unknown;
      break;
  }

  /*
    try {
        dateFnsLocaleString = localization;
        localeModule = await import(`date-fns-v4/locale/${dateFnsLocaleString}`);
    } catch (error) {
        try {
            dateFnsLocaleString = localization.split("-")[0];
            // localeModule = await import(`date-fns-v4/locale/${dateFnsLocaleString}`);
            localeModule = await modules[`/date-fns-v4/locale/${dateFnsLocaleString}.js`] as unknown;
            if (!localeModule) {
                throw new Error(`Failed to import the locale: ${dateFnsLocaleString}`);
            }
        } catch (error) {
            try {
                dateFnsLocaleString = defaultLocale;
                // localeModule = await import(`date-fns-v4/locale/${dateFnsLocaleString}`);
                localeModule = await modules[`/date-fns-v4/locale/${dateFnsLocaleString}.js`] as unknown;
                if (!localeModule) {
                    throw new Error(`Failed to import the default locale: ${dateFnsLocaleString}`);
                }
            } catch (error) {
                throw new Error(`Failed to import the default locale: ${dateFnsLocaleString}`);
            }
        }
    }
    // remove the - from the dateFnsLocaleString in order to use it as a key
    dateFnsLocaleString = dateFnsLocaleString.replace("-", "");
    */

  // localeModule is an object with the locale string as key and the locale object as value
  return (localeModule as Record<string, Locale>)[dateFnsLocaleString];
}
