import { PropsWithChildren } from 'react';
import BackHandOutlinedIcon from '@mui/icons-material/BackHandOutlined';
import BoltIcon from '@mui/icons-material/Bolt';
import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function MenuCreateStep1({
  pickOption,
}: {
  pickOption: (index: number) => void;
}) {
  const { t } = useTranslation('');
  return (
    <>
      <Typography variant="subtitle2">{t('menu.createMenuStep1')}</Typography>
      <Option icon={<BoltIcon />} pickOption={pickOption} comingSoon>
        <>
          <Typography variant="body1">{t('menu.uploadMenu')}</Typography>
          <Typography variant="subtitle2">
            {t('menu.uploadMenuDescription')}
          </Typography>
        </>
      </Option>

      <Option
        icon={<LibraryBooksOutlinedIcon />}
        pickOption={pickOption}
        comingSoon
      >
        <>
          <Typography variant="body1">{t('menu.importMenu')}</Typography>
          <Typography variant="subtitle2">
            {t('menu.importMenuDescription')}
          </Typography>
        </>
      </Option>

      <Option icon={<BackHandOutlinedIcon />} pickOption={pickOption}>
        <>
          <Typography variant="body1">{t('menu.buildManually')}</Typography>
        </>
      </Option>
    </>
  );
}

interface OptionProps extends PropsWithChildren {
  icon: any;
  comingSoon?: boolean;
  pickOption: (index: number) => void;
}

const ComingSoon = () => {
  const { t } = useTranslation('');
  return (
    <Typography
      display={'inline-block'}
      sx={{ bgcolor: 'error.main', px: '5px', whiteSpace: 'nowrap' }}
      color="white"
      fontSize="10px"
    >
      {t('menu.comingSoon')}
    </Typography>
  );
};

const Option = ({ icon, pickOption, comingSoon, children }: OptionProps) => {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <Box
      onClick={() => pickOption(1)}
      sx={{
        p: 2,
        borderRadius: 2,
        border: '1px solid',
        borderColor: 'custom.gray400',
        width: '100%',
        cursor: 'pointer',
        pointerEvents: comingSoon ? 'none' : 'all',
        '&:hover': {
          borderColor: 'custom.gray600',
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: {
            xs: 'flex-start',
            md: 'center',
          },
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            bgcolor: 'custom.gray200',
            borderRadius: 2,
            p: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {icon}
        </Box>
        <Box
          sx={{
            minHeight: '40px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            flexDirection: 'column',
          }}
        >
          {comingSoon && isXSmall && <ComingSoon />}
          <Box sx={{ height: '5px' }} />
          {children}
        </Box>
        {comingSoon && !isXSmall && <ComingSoon />}
      </Box>
    </Box>
  );
};
