import React, { createContext, useContext, useState } from 'react';

type FilterContextType = {
  activeFilters: string[];
  filterValues: Record<string, string>;
  toggleFilter: (filter: string) => void;
  setFilterValue: (filter: string, value: string) => void;
};

const FilterContext = createContext<FilterContextType | null>(null);

export const FilterProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, string>>({});

  const toggleFilter = (filter: string) => {
    setActiveFilters(prev =>
      prev.includes(filter) ? prev.filter(f => f !== filter) : [...prev, filter]
    );

    setFilterValues(prev => {
      const newValues = { ...prev };
      delete newValues[filter];
      return newValues;
    });
  };

  const setFilterValue = (filter: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [filter]: value }));
  };

  return (
    <FilterContext.Provider
      value={{
        activeFilters,
        toggleFilter,
        filterValues,
        setFilterValue,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
};

export const useFilters = (): FilterContextType => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};
