import { useMemo } from 'react';
import { Box, capitalize, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import HorizontaBarChart from '~/components/atoms/charts/HorizontalBarChart';
import { CurrencyType, formatNumber } from '~/utils/formatNumber';

export default function PaymentMethodsGraph({
  tableData,
  currency,
}: {
  tableData: Array<{
    type: string;
    value: number;
  }>;
  currency?: CurrencyType;
}) {
  const { t } = useTranslation();
  const paymentMethodsGraphData = useMemo(() => {
    if (tableData && tableData.length > 0) {
      return tableData
        ?.map((data: any) => ({
          label: capitalize(data.type),
          value: data.value / 10000,
        }))
        .slice(0, 5);
    }
    return [{ label: '', value: 0 }];
  }, [tableData]);

  return (
    <Box>
      <Typography
        sx={{
          display: { xs: 'none', md: 'block' },
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        fontWeight="500"
        mb={1.5}
      >
        {paymentMethodsGraphData.length >= 3 &&
          `Top ${paymentMethodsGraphData.length} ${t('paymentMethods.paymentMethod')}`}
      </Typography>
      <HorizontaBarChart
        colorSet={3}
        chartData={paymentMethodsGraphData}
        withTable
        tableStyle="tiny"
        formatData={data => formatNumber(data, currency)}
      />
    </Box>
  );
}
