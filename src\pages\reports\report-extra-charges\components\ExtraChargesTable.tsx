import { useMemo } from 'react';
import { Box, capitalize } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { groupReport } from '~/fake-provider/reports/groupReport';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';

type TableRow = {
  vat: string | undefined;
  promotionsValue: number;
  price: number;
  quantity: number;
  couponsValue: number;
  discountsValue: number;
  netValue: number;
  name: string;
  value: number;
  subItems: [];
};

export default function ExtraChargesTable({
  tableData,
  groupingItems,
  fields,
  onChangeGrouping,
  setFields,
}: {
  groupingItems: string[];
  fields: FieldOption[];
  onChangeGrouping?: (items: any[]) => void;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
}) {
  const { t } = useTranslation();
  const extraChargesData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as TableRow[];
      mappedTableData.sort((a, b) => Number(b.value) - Number(a.value));
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.name = 'Total';
      totalItemsData.vat = undefined;
      totalItemsData.subItems = [];

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }
    return [];
  }, [tableData]);

  const extraChargesConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'name',
      label: t('shared.name'),
      textAlign: 'start',
      render: (row: TableRow) => {
        return <>{row.name}</>;
      },
    },
    {
      id: 'vat',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'end',
    },

    {
      id: 'price',
      label: t('shared.price'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.price)}</>;
      },
    },
    {
      id: 'quantity',
      label: t('giftCards.quantity'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row?.quantity, true)}</>;
      },
    },
    {
      id: 'value',
      label: t('itemSales.grossSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },

    {
      id: 'promotionsValue',
      label: t('categorySales.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'netValue',
      label: t('shared.netSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.netValue)}</>;
      },
    },
  ];

  const columnsToFilter = useMemo(() => {
    const columns = [
      'vat',
      'quantity',
      'price',
      'value',
      'promotionsValue',
      'promotionName',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [{ value: 'vat', label: 'VAT' }];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          config={extraChargesConfig}
          data={extraChargesData || []}
          separateFirstColumn={true}
          fields={fields}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          fixedFirstColumn={true}
        />
      </Box>
    </>
  );
}
