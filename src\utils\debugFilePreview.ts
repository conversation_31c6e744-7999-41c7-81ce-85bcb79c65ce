/**
 * Debug utility to check file preview blocking
 * Add this to any component to debug why temp files might still be showing
 */

import { UploadedFile } from '~/types/fileUpload';

export const debugFilePreviewBlocking = (file: UploadedFile | null) => {
    if (!file) {
        console.log('🐛 [FilePreview Debug] No file provided');
        return;
    }

    console.log('🐛 [FilePreview Debug] File details:', {
        filename: `${file.fn}.${file.e}`,
        fileType: file.t,
        isTemporary: file.x,
        shouldBeBlocked: file.x === true,
        bucketType: file.t === 'i' ? 'images (public)' :
            file.t === 'v' ? 'videos (public)' :
                file.t === 's' ? 'storage (public)' :
                    file.t === 'p' ? 'private' :
                        'unknown',
        fullFile: file
    });

    // Check what the blocking logic would do
    if (file.x === true) {
        console.log('🚫 [FilePreview Debug] File SHOULD BE BLOCKED (temp file)');
    } else if (file.t === 'p') {
        console.log('🔒 [FilePreview Debug] File is private - should use secure cache');
    } else if (file.t === 'i' || file.t === 'v' || file.t === 's') {
        console.log('🌍 [FilePreview Debug] File is public - should use CDN/direct access');
    } else {
        console.log('❓ [FilePreview Debug] Unknown file type - might cause errors');
    }
};

// Quick test function you can call from browser console
(window as any).debugFilePreview = debugFilePreviewBlocking;
