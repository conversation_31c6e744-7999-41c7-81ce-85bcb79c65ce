import { Box } from '@mui/material';

import Subsection from '~/components/molecules/Subsection';

export default function KitchenDisplaysPage() {
  return (
    <>
      <Box sx={{ display: { xs: 'none', lg: 'block' } }}>
        <img
          src="/assets/kitchen-displays/cover.png"
          style={{
            width: '100%',
            height: '500px',
            objectFit: 'cover',
            objectPosition: 'top',
          }}
        />
      </Box>
      <Box sx={{ display: { xs: 'block', lg: 'none' } }}>
        <img
          src="/assets/kitchen-displays/cover_phone.png"
          style={{
            width: '100%',
            objectFit: 'cover',
            objectPosition: 'center',
          }}
        />
      </Box>
      <Box
        my={{ xs: 5, lg: 10 }}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: { xs: 5, lg: 10 },
        }}
      >
        <Subsection
          title="Your orders in one place"
          subtitle="View all your orders within a single device, no matter where they’re placed."
          containerSx={{ maxWidth: '700px', width: '100%' }}
        >
          <Box
            sx={{
              maxWidth: '630px',
              margin: 'auto',
            }}
          >
            <img
              src="/assets/kitchen-displays/details.png"
              style={{ width: '100%' }}
            />
          </Box>
        </Subsection>

        <Subsection
          title="All about the details"
          subtitle="Our reliable tickets have all the information needed by the kitchen, including timers."
          containerSx={{ maxWidth: '700px', width: '100%' }}
        >
          <Box
            pt={3}
            sx={{
              maxWidth: '550px',
              margin: 'auto',
            }}
          >
            <img
              src="/assets/kitchen-displays/ticket.png"
              style={{ width: '100%' }}
            />
          </Box>
        </Subsection>
      </Box>
    </>
  );
}
