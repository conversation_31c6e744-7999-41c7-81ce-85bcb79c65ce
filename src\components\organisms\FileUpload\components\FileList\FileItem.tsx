import React, { useCallback } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Close as CloseIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
} from '@mui/icons-material';
import {
  Box,
  Chip,
  CircularProgress,
  IconButton,
  Paper,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { FilePreviewThumbnail } from '~/components/atoms/FilePreviewThumbnail';
import { PrivateFileContext, UploadedFile } from '~/types/fileUpload';
import { canPreviewFile } from '~/utils/fileUtils';
import { shouldUsePlaceholder } from '../../utils/filePlaceholders';
import { getFileIcon } from '../../utils/fileUploadHelpers';
import { PlaceholderThumbnail } from '../PlaceholderThumbnail';

/**
 * Individual file item component
 */
interface FileItemProps {
  file: UploadedFile;
  index: number;
  onRemove: (index: number) => void;
  onPreview?: (file: UploadedFile) => void;
  disabled?: boolean;
  readOnly?: boolean;
  variant?: 'default' | 'compact';
  privateFileContext?: PrivateFileContext;
  sortable?: boolean;
  sortableId?: string;
  uploading?: boolean;
}

export const FileItem: React.FC<FileItemProps> = ({
  file,
  index,
  onRemove,
  onPreview,
  disabled = false,
  readOnly = false,
  variant = 'default',
  sortable = false,
  sortableId,
  uploading = false,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  // Sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: sortableId || `${file.rn || file.fn}-${index}`,
    disabled: !sortable || disabled || readOnly,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get file display name
  const getDisplayName = useCallback(() => {
    const fileName = file.rn || file.fn;
    const lastDotIndex = fileName.lastIndexOf('.');

    if (lastDotIndex === -1) return fileName;

    const name = fileName.substring(0, lastDotIndex);
    const extension = fileName.substring(lastDotIndex);

    // Truncate long filenames
    const maxLength = variant === 'compact' ? 20 : 40;
    if (name.length > maxLength) {
      return `${name.substring(0, maxLength - 3)}...${extension}`;
    }

    return fileName;
  }, [file, variant]);

  // Handle chip click for preview
  const handleChipClick = useCallback(() => {
    if (canPreviewFile(file) && !disabled && onPreview) {
      onPreview(file);
    }
  }, [file, disabled, onPreview]);

  // Handle remove file
  const handleRemove = useCallback(() => {
    if (!disabled && !readOnly) {
      onRemove(index);
    }
  }, [disabled, readOnly, onRemove, index]);

  // Get chip color based on file type
  const getChipColor = useCallback(() => {
    if (file.x) return 'warning'; // Temporary file

    switch (file.t) {
      case 'i':
        return 'primary'; // Images
      case 'v':
        return 'secondary'; // Videos
      case 's':
        return 'info'; // Public files
      case 'p':
        return 'default'; // Private files
      default:
        return 'default';
    }
  }, [file]);

  // Get file status indicator
  const getStatusIndicator = useCallback(() => {
    if (uploading) {
      return <CircularProgress size={16} sx={{ ml: 1 }} />;
    }

    if (file.x) {
      return (
        <Tooltip title={t('fileUpload.temporaryFile', 'Temporary file')}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: theme.palette.warning.main,
              ml: 1,
            }}
          />
        </Tooltip>
      );
    }

    return null;
  }, [uploading, file.x, theme, t]);

  // Render compact variant as chip
  if (variant === 'compact') {
    return (
      <Box
        ref={setNodeRef}
        style={style}
        sx={{
          display: 'flex',
          alignItems: 'center',
          opacity: isDragging ? 0.5 : 1,
        }}
      >
        {/* Drag handle for sortable items */}
        {sortable && !disabled && !readOnly && (
          <Box
            {...attributes}
            {...listeners}
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
              mr: 1,
              '&:active': {
                cursor: 'grabbing',
              },
            }}
          >
            <DragIndicatorIcon fontSize="small" color="action" />
          </Box>
        )}

        <Chip
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <span>{getFileIcon(file as any)}</span>
              <span style={{ marginLeft: 4 }}>{getDisplayName()}</span>
              {getStatusIndicator()}
            </Box>
          }
          color={getChipColor()}
          variant={file.x ? 'outlined' : 'filled'}
          onClick={
            canPreviewFile(file) && !disabled ? handleChipClick : undefined
          }
          onDelete={!disabled && !readOnly ? handleRemove : undefined}
          deleteIcon={
            !disabled && !readOnly ? (
              <Tooltip title={t('fileUpload.removeFile', 'Remove file')}>
                <CloseIcon />
              </Tooltip>
            ) : undefined
          }
          sx={{
            maxWidth: 200,
            cursor: canPreviewFile(file) && !disabled ? 'pointer' : 'default',
            '& .MuiChip-label': {
              display: 'flex',
              alignItems: 'center',
              maxWidth: '100%',
              overflow: 'hidden',
            },
            '& .MuiChip-deleteIcon': {
              color: 'inherit',
              '&:hover': {
                color: theme.palette.error.main,
              },
            },
          }}
        />
      </Box>
    );
  }

  // Render default variant as Paper (matching old MuiFileUploadInput design)
  return (
    <Paper
      ref={setNodeRef}
      style={style}
      variant="outlined"
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: isDragging ? 0.8 : 1,
        backgroundColor:
          disabled || readOnly
            ? theme.palette.action.hover
            : isDragging
              ? theme.palette.action.hover
              : 'transparent',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          flex: 1,
          minWidth: 0,
        }}
      >
        {/* Drag handle for sortable items */}
        {sortable && !disabled && !readOnly && (
          <Box
            {...attributes}
            {...listeners}
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
              color: theme.palette.text.secondary,
              '&:active': {
                cursor: 'grabbing',
              },
            }}
          >
            <DragIndicatorIcon fontSize="small" />
          </Box>
        )}

        {/* Smart thumbnail - real thumbnail or placeholder */}
        {shouldUsePlaceholder(file) ? (
          <PlaceholderThumbnail
            file={file}
            size={40}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
          />
        ) : (
          <FilePreviewThumbnail
            file={file}
            size={40}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
            theme={theme}
          />
        )}

        {/* File info */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {file.rn || file.fn}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {file.e.toUpperCase()} file
            {uploading && file.x && ' • Uploading...'}
          </Typography>
        </Box>
      </Box>

      {/* Delete button - Hidden in disabled and readOnly modes */}
      {!disabled && !readOnly && (
        <IconButton
          size="small"
          onClick={handleRemove}
          disabled={uploading}
          color="error"
          sx={{ ml: 1 }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
};
