import React, { useCallback } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
} from '@mui/icons-material';
import {
  Box,
  Chip,
  IconButton,
  Paper,
  Typography,
  useTheme,
} from '@mui/material';

import { FilePreviewThumbnail } from '~/components/atoms/FilePreviewThumbnail';
import { PrivateFileContext, UploadedFile } from '~/types/fileUpload';
import { canPreviewFile } from '~/utils/fileUtils';
import { shouldUsePlaceholder } from '../../utils/filePlaceholders';
import { PlaceholderThumbnail } from '../PlaceholderThumbnail';

/**
 * Individual file item component
 */
interface FileItemProps {
  file: UploadedFile;
  index: number;
  onRemove: (index: number) => void;
  onPreview?: (file: UploadedFile) => void;
  disabled?: boolean;
  readOnly?: boolean;
  variant?: 'default' | 'compact';
  privateFileContext?: PrivateFileContext;
  sortable?: boolean;
  sortableId?: string;
  uploading?: boolean;
}

export const FileItem: React.FC<FileItemProps> = ({
  file,
  index,
  onRemove,
  onPreview,
  disabled = false,
  readOnly = false,
  variant = 'default',
  sortable = false,
  sortableId,
  uploading = false,
}) => {
  const theme = useTheme();

  // Sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: sortableId || `${file.rn || file.fn}-${index}`,
    disabled: !sortable || disabled || readOnly,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get file display name with proper truncation logic for compact variant
  const getDisplayName = useCallback(() => {
    const fileName = file.rn || file.fn;

    if (variant === 'compact') {
      const extension = file.e ? `.${file.e}` : '';

      // Function to truncate from middle if needed (for dropdown context)
      const getTruncatedLabel = (name: string, ext: string) => {
        const fullName = `${name}${ext}`;
        // If the full name is short enough, return as is
        if (fullName.length <= 25) {
          // Slightly longer for dropdown
          return fullName;
        }

        // For longer names, show first part + ... + extension
        const maxStartLength = Math.max(10, 25 - ext.length - 3); // 3 for "..."
        const truncatedName =
          name.length > maxStartLength
            ? `${name.substring(0, maxStartLength)}...${ext}`
            : fullName;

        return truncatedName;
      };

      return getTruncatedLabel(fileName, extension);
    }

    // Default variant truncation
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) return fileName;

    const name = fileName.substring(0, lastDotIndex);
    const extension = fileName.substring(lastDotIndex);

    // Truncate long filenames
    const maxLength = 40;
    if (name.length > maxLength) {
      return `${name.substring(0, maxLength - 3)}...${extension}`;
    }

    return fileName;
  }, [file, variant]);

  // Handle chip click for preview
  const handleChipClick = useCallback(() => {
    if (canPreviewFile(file) && !disabled && onPreview) {
      onPreview(file);
    }
  }, [file, disabled, onPreview]);

  // Handle remove file
  const handleRemove = useCallback(() => {
    if (!disabled && !readOnly) {
      onRemove(index);
    }
  }, [disabled, readOnly, onRemove, index]);

  // Render compact variant as chip (matching old SortableFileChip)
  if (variant === 'compact') {
    const fileName = file.rn || file.fn;
    const extension = file.e ? `.${file.e}` : '';
    const fullName = `${fileName}${extension}`;

    return (
      <Chip
        ref={setNodeRef}
        style={style}
        label={getDisplayName()}
        title={fullName} // Show full name on hover
        icon={
          sortable && !disabled && !readOnly ? (
            <Box
              {...attributes}
              {...listeners}
              sx={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'grab',
                '&:active': {
                  cursor: 'grabbing',
                },
              }}
            >
              <DragIndicatorIcon fontSize="small" />
            </Box>
          ) : undefined
        }
        onDelete={!disabled && !readOnly ? handleRemove : undefined}
        onClick={
          canPreviewFile(file) && !disabled ? handleChipClick : undefined
        }
        size="small"
        variant="outlined"
        sx={{
          opacity: isDragging ? 0.8 : 1,
          cursor: canPreviewFile(file) && !disabled ? 'pointer' : 'default',
          backgroundColor: 'background.paper',
          border: '1px solid',
          borderColor: 'divider',
          width: '100%',
          justifyContent: 'flex-start',
          height: '36px',
          '& .MuiChip-icon': {
            cursor: 'grab',
            marginLeft: '4px',
            '&:active': {
              cursor: 'grabbing',
            },
          },
          '& .MuiChip-label': {
            fontWeight: 500,
            fontSize: '0.875rem',
            textAlign: 'left',
            paddingLeft: '8px',
            paddingRight: '8px',
            flex: 1,
            justifyContent: 'flex-start',
          },
          '&:hover': !disabled
            ? {
                backgroundColor: 'action.hover',
                borderColor: 'primary.main',
              }
            : {},
        }}
      />
    );
  }

  // Render default variant as Paper (matching old MuiFileUploadInput design)
  return (
    <Paper
      ref={setNodeRef}
      style={style}
      variant="outlined"
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: isDragging ? 0.8 : 1,
        backgroundColor:
          disabled || readOnly
            ? theme.palette.action.hover
            : isDragging
              ? theme.palette.action.hover
              : 'transparent',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          flex: 1,
          minWidth: 0,
        }}
      >
        {/* Drag handle for sortable items */}
        {sortable && !disabled && !readOnly && (
          <Box
            {...attributes}
            {...listeners}
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
              color: theme.palette.text.secondary,
              '&:active': {
                cursor: 'grabbing',
              },
            }}
          >
            <DragIndicatorIcon fontSize="small" />
          </Box>
        )}

        {/* Smart thumbnail - real thumbnail or placeholder */}
        {shouldUsePlaceholder(file) ? (
          <PlaceholderThumbnail
            file={file}
            size={40}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
          />
        ) : (
          <FilePreviewThumbnail
            file={file}
            size={40}
            onClick={() => !disabled && onPreview?.(file)}
            disabled={disabled}
            theme={theme}
          />
        )}

        {/* File info */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {file.rn || file.fn}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {file.e.toUpperCase()} file
            {uploading && file.x && ' • Uploading...'}
          </Typography>
        </Box>
      </Box>

      {/* Delete button - Hidden in disabled and readOnly modes */}
      {!disabled && !readOnly && (
        <IconButton
          size="small"
          onClick={handleRemove}
          disabled={uploading}
          color="error"
          sx={{ ml: 1 }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
};
