@import url('https://fonts.googleapis.com/css2?family=Geologica:wght@100;200;300;400;500;600;700;800;900&display=swap');
/* @import url('https://fonts.googleapis.com/css2?family=Geologica:wght@100;200;300;400;500;600;700;800;900&family=Roboto:wght@100;300;400;500;700;900&display=swap'); */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

#cc-main {
  --cc-btn-primary-bg: #0169ff;
  --cc-btn-primary-border-color: #0169ff;
  --cc-btn-primary-color: white;
  --cc-btn-primary-hover-bg: #004ab3;
  --cc-btn-primary-hover-color: white;
  --cc-btn-primary-hover-border-color: #004ab3;

  --cc-toggle-on-bg: var(--cc-btn-primary-bg);

  --cc-btn-border-radius: 10px;
}

/* ONLY IF WE WANT TO TARGET EACH COOKIE BUTTON */
/* POPUP REJECT BUTTON */
.cm__btn[data-role='necessary'] {
  background-color: white !important;
  border: 1.5px solid #0051c2 !important;
  color: #0051c2 !important;
}

.cm__btn[data-role='necessary']:hover {
  background-color: #ebebeb !important;
}

/* MODAL REJECT BUTTON */
.pm__btn[data-role='necessary'] {
  background-color: white !important;
  border: 1.5px solid #0051c2 !important;
  color: #0051c2 !important;
}

.pm__btn[data-role='necessary']:hover {
  background-color: #ebebeb !important;
}

.MuiDialog-paper {
  overflow-x: hidden;
}

.RaLayout-appFrame {
  margin-top: 60px !important;
}

#edit-dialog-title {
  display: none;
}

.MuiList-root.MuiList-padding.RaMenu-closed {
  width: 0px;
  overflow: hidden;
}

.MuiList-root.MuiList-padding.RaMenu-open {
  /* width: 300px; */
  overflow: hidden;
}

.RaList-main .RaList-content {
  overflow: visible !important;
}

.RaList-main
  .RaList-content
  .RaBulkActionsToolbar-toolbar:not(.RaBulkActionsToolbar-collapsed) {
  min-height: 73px;
  transform: translateY(-73px);
}

.RaList-main {
  max-width: 100%;
  overflow: auto;
}

/* nu mai stiu de ce facusem asta, -bosti : Buna Cristina, este okay, nici eu nu-mi dau seama... :) */
/* #main-content {
  padding-left: 0;
} */

.MuiAutocomplete-paper {
  background-image: linear-gradient(
    rgba(255, 255, 255, 0.12),
    rgba(255, 255, 255, 0.12)
  ) !important;
}

@media screen and (max-width: 600px) {
  #main-content {
    width: 100vw;
    padding-left: 8px;
  }

  .MuiList-root.MuiList-padding.RaMenu-open,
  .MuiDrawer-paper.RaSidebar-paper {
    width: 100vw !important;
    overflow: hidden;
  }

  .filter-field,
  .MuiAutocomplete-root {
    width: 100%;
  }
}

/* styles for "Save query" and "Confirm delete" modals */
.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing {
  justify-content: space-between;
  padding: 0px 24px 15px;
  width: calc(100vw - 112px);
  max-width: 350px;
}

.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing {
  justify-content: space-between;
  padding: 0px 24px 15px;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button:last-of-type,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button:last-of-type {
  background: #0069ff;
  color: white;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button
  > span,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button
  > span {
  display: none;
}

.MuiDialog-paper[aria-labelledby='form-dialog-title']
  .MuiDialogActions-spacing
  button:first-of-type,
.MuiDialog-paper[aria-labelledby='alert-dialog-title']
  .MuiDialogActions-spacing
  button:first-of-type {
  border: 1px solid #0069ff;
}

.MuiTableRow-root {
  height: 51px;
}

.MuiToolbar-root > form {
  width: 100%;
}

.MuiMenu-list > .Mui-selected {
  background-color: rgba(0, 105, 255, 0.28) !important;
}

.RaCreateButton-floating {
  bottom: 20px !important;
}

@media only screen and (max-width: 900px) {
  .MuiToolbar-regular {
    min-height: auto !important;
  }
}

/* sparkline chart */
.sparkline--cursor {
  stroke: rgba(0, 0, 0, 0.2);
}

.sparkline--spot {
  stroke: transparent;
}

.RaAppBar-menuButton {
  display: none !important;
}

/* Time picker styles */
.MuiMultiSectionDigitalClockSection-root {
  width: auto !important;
  &:after {
    height: 0 !important;
  }

  > li {
    width: 70px;
  }
}
/* End of time picker styles */

.print-body {
  .do-not-print {
    display: none;
  }
}
@media screen and (max-width: 600px) {
  .MuiPickersLayout-contentWrapper {
    display: none !important;
  }
}

.css-l1hos6-MuiFormLabel-root-MuiInputLabel-root {
  top: -14px !important;
}
