import { Datagrid, DatagridProps, List, ListProps } from 'react-admin';

interface SimpleRADatagridProps extends ListProps {
  datagridProps?: DatagridProps;
}

export default function SimpleRADatagrid({
  datagridProps,
  children,
  ...listProps
}: SimpleRADatagridProps) {
  return (
    <List
      component="div"
      exporter={false}
      pagination={false}
      sx={{
        '& .MuiToolbar-gutters, .RaList-actions': {
          minHeight: 0,
          height: 0,
        },
      }}
      {...listProps}
    >
      <Datagrid bulkActionButtons={false} {...datagridProps}>
        {children}
      </Datagrid>
    </List>
  );
}
