import { Box } from '@mui/material';

import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';

type TableRow = {
  vat: string;
  promotionsValue: number;
  price: number;
  quantity: number;
  couponsValue: number;
  discountsValue: number;
  netValue: number;
  type: string;
  value: number;
};

export default function GiftCards({ tableData }: { tableData: any }) {
  const calculateCardValues = (itemsData: TableRow[]) => {
    const physicalSold = mergeAndSumObjects(
      itemsData.filter(item => item.type === '@physical')
    );
    const digitalSold = mergeAndSumObjects(
      itemsData.filter(item => item.type === '@digital')
    );

    const totalItems = mergeAndSumObjects(tableData);

    return {
      physicalSold,
      digitalSold,
      totalItems,
    };
  };

  const cardValues = calculateCardValues(tableData);

  const cardsConfig: { title: string; value: string | number }[] = [
    {
      title: 'Physical Sold',
      value: cardValues.physicalSold.quantity
        ? formatNumberIntl(cardValues.physicalSold.quantity / 1000)
        : '-',
    },
    {
      title: 'Digital Sold',
      value: cardValues.digitalSold.quantity
        ? formatNumberIntl(cardValues.digitalSold.quantity / 1000)
        : '-',
    },
    {
      title: 'Gross Sales',
      value: cardValues.totalItems.value
        ? formatAndDivideNumber(cardValues.totalItems.value)
        : '-',
    },
    {
      title: 'Net Sales',
      value: cardValues.totalItems.netValue
        ? formatAndDivideNumber(cardValues.totalItems.netValue)
        : '-',
    },
  ];

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: { xs: 'grid', sm: 'flex' },
          gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'unset' },
          gap: 8,
          mt: 4,
          alignItems: 'center',
          justifyContent: 'space-around',
          pb: 2,
          borderBottom: '2px solid #F2F2F2',
          '@media print': {
            borderBottom: '2px solid black',
          },
        }}
      >
        {cardsConfig.map(
          (item: { title: string; value: string | number }, index: number) => {
            return (
              <Box
                key={index}
                sx={{
                  gridColumn: 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ExtraDataCard item={item} />
              </Box>
            );
          }
        )}
      </Box>
    </>
  );
}
