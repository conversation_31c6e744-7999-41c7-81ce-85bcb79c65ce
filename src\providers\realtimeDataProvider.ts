import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Provider,
  DeleteManyParams,
  DeleteManyResult,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetListResult,
  GetManyParams,
  GetManyReferenceParams,
  GetManyReferenceResult,
  GetManyResult,
  GetOneParams,
  GetOneResult,
  RaRecord,
  UpdateManyParams,
  UpdateManyResult,
  UpdateParams,
  UpdateResult,
} from 'react-admin';

/*
filters :
    _ninc_any - does not include any
    _inc - includes value
    _inc_any - include any
    _eq - equal
    _eq_any - equal any
    _neq - not equal
    _neq_any - not equal any
    _gt - greater than
    _gte - greater than or equal
    _lt - less than
    _lte - less than or equal
    _q - text search
*/

export const getRealtimeDataProvider = (accountId: string): DataProvider => {
  const getList = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetListParams
  ): Promise<GetListResult<RecordType>> => {
    return { data: [], total: 0 };
  };

  const getOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetOneParams
  ): Promise<GetOneResult<RecordType>> => {
    return { data: {} as RecordType };
  };

  const getMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyParams
  ): Promise<GetManyResult<RecordType>> => {
    return { data: [] };
  };

  const getManyReference = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyReferenceParams
  ): Promise<GetManyReferenceResult<RecordType>> => {
    return { data: [], total: 0 };
  };

  const create = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: CreateParams
  ): Promise<CreateResult<RecordType>> => {
    return { data: {} as RecordType };
  };

  const update = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateParams
  ): Promise<UpdateResult<RecordType>> => {
    return { data: {} as RecordType };
  };

  const updateMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateManyParams
  ): Promise<UpdateManyResult<RecordType>> => {
    return { data: [] };
  };

  const deleteOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteParams
  ): Promise<DeleteResult<RecordType>> => {
    return { data: {} as RecordType };
  };

  const deleteMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteManyParams
  ): Promise<DeleteManyResult<RecordType>> => {
    return { data: [] };
  };

  return {
    getList: getList,
    getOne: getOne,
    getMany: getMany,
    getManyReference: getManyReference,
    create: create,
    update: update,
    updateMany: updateMany,
    delete: deleteOne,
    deleteMany: deleteMany,
  };
};
