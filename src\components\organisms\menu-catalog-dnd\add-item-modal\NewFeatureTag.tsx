import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function NewFeatureTag() {
  const { t } = useTranslation();
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        bgcolor: 'primary.veryLight',
        borderRadius: 4,
        py: 0.5,
        px: 1,
      }}
    >
      <img src="/assets/icons/star.svg" alt="star" />
      <Typography variant="body2" fontWeight={400} color="primary">
        {t('menu.newFeature')}
      </Typography>
    </Box>
  );
}
