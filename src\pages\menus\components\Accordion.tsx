import { ReactNode, useState } from 'react';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  Box,
  Collapse,
  Fade,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import ConfigurableTable, { TableColumn } from './ConfigurableTable';

export type AccordionSection<T> = {
  displayName: string;
  color: string;
  items: (T & { type: string })[];
};

type ConfigurableAccordionProps<T extends { displayName: string }> = {
  section: AccordionSection<T>;
  columns: TableColumn<T>[];
  renderAddItem: () => ReactNode;
  removeSelectedItems: (
    selectedRows: {
      itemIndex: number;
      pageIndex?: number;
    }[]
  ) => void;
  editItem: (index: number, pageIndex?: number) => void;
  showAddGroup?: boolean;
  addGroup?: () => void;
  editGroup?: () => void;
  removeGroup?: () => void;
  itemId: string;
  expanded: boolean;
  onExpand: (id: string) => void;
  isNested?: boolean;
  onReorder?: (reorderedRows: T[]) => void;
  saveReorderedRows: (rows: T[], result: any, itemId: string, pageIndex: number) => void;
  id: string;
  pageIndex: number;
};

export default function ConfigurableAccordion<
  T extends { displayName: string },
>({
  section,
  columns,
  renderAddItem,
  removeSelectedItems,
  removeGroup,
  editItem,
  showAddGroup,
  addGroup,
  editGroup,
  itemId,
  expanded,
  onExpand,
  isNested,
  saveReorderedRows,
  id,
  pageIndex,
}: ConfigurableAccordionProps<T>) {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const { t } = useTranslation('');
  const { theme } = useTheme();

  const totalItems = (items: (T & { type: string })[]): number => {
    return items.reduce((count, item) => {
      if (item.type === 'displayGroup') {
        return count + totalItems((item as any).items || []);
      }
      return count + 1;
    }, 0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  return (
    <Box
      px={1}
      py={1}
      sx={{
        width: '100%',
        border: `2px solid ${theme.palette.mode === 'dark' ? '#515151' : '#EBEBEB'}`,
        borderRadius: '13px',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        backgroundColor: isNested
          ? '#F1F1F1'
          : theme.palette.mode === 'dark'
            ? '#26262B'
            : 'white',
      }}
    >
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        sx={{ cursor: 'pointer' }}
        onClick={() => {
          onExpand(id);
        }}
      >
        <Box
          sx={{
            backgroundColor: expanded
              ? theme.palette.mode === 'dark'
                ? '#26262B'
                : isNested
                  ? '#F1F1F1'
                  : 'white'
              : theme.palette.mode === 'dark'
                ? '#26262B'
                : isNested
                  ? '#F1F1F1'
                  : 'white',
            px: 1,
            py: 1,
            width: '100%',
            borderTopLeftRadius: '10px',
            borderTopRightRadius: '10px',
            justifyContent: 'space-between',
          }}
          display="flex"
          alignItems="center"
          gap={1}
        >
          <Box gap={1} display="flex" alignItems="center">
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'grab',
                '&:active': {
                  cursor: 'grabbing',
                },
              }}
            >
              <DragIndicatorIcon
                sx={{
                  color: 'text.secondary',
                }}
              />
            </Box>
            <Typography
              variant="subtitle1"
              fontWeight={600}
              sx={{ userSelect: 'none' }}
            >
              {section.displayName}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ userSelect: 'none' }}
            >
              {t('shared.item', { count: totalItems(section.items) })}
            </Typography>
            {showAddGroup && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', pr: 2 }}>
                <Tooltip title={t('permissions.groupTitles.actions')}>
                  <IconButton
                    onClick={e => {
                      e.stopPropagation();
                      handleMenuOpen(e);
                    }}
                  >
                    <MoreVertIcon
                      fontSize="small"
                      sx={{ transform: 'rotate(90deg)', color: '#0064F0' }}
                    />
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={menuAnchorEl}
                  open={Boolean(menuAnchorEl)}
                  onClose={handleMenuClose}
                  TransitionComponent={Fade}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                  transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                  onClick={e => e.stopPropagation()}
                >
                  <MenuItem
                    onClick={e => {
                      e.stopPropagation();
                      editGroup?.();
                      handleMenuClose();
                    }}
                  >
                    {t('menu.editGroup')}
                  </MenuItem>
                  <MenuItem
                    onClick={e => {
                      e.stopPropagation();
                      addGroup?.();
                      handleMenuClose();
                    }}
                  >
                    {t('menu.addGroupInside') + section.displayName}
                  </MenuItem>
                  <MenuItem
                    sx={{ color: 'error.main' }}
                    onClick={e => {
                      e.stopPropagation();
                      handleMenuClose();
                      removeGroup?.();
                    }}
                  >
                    {t('menu.removeGroup', { group: section.displayName })}
                  </MenuItem>
                </Menu>
              </Box>
            )}
          </Box>

          <ExpandMoreIcon
            sx={{
              transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease',
            }}
          />
        </Box>
      </Box>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        {section?.items?.some(item => item.type === 'displayGroup') && (
          <Box
            display="flex"
            justifyContent="start"
            sx={{ p: 1, borderRadius: '10px', ml: 1, mt: -1 }}
          >
            {/* <Box display="flex" alignItems="center" gap={1}>
              <InfoOutlinedIcon sx={{ color: 'red' }} />
              <Typography variant="body2" color="red">
                {t('menu.nestedGroupsInfo')}
              </Typography>
            </Box> */}
          </Box>
        )}

        <ConfigurableTable<T>
          itemId={itemId}
          pageIndex={pageIndex}
          columns={columns}
          rows={section.items
            .filter(item => item.type !== 'displayGroup')}
          renderAddItem={renderAddItem}
          removeSelectedItems={removeSelectedItems}
          editItem={editItem}
          saveReorderedRows={saveReorderedRows}
        />
      </Collapse>
    </Box>
  );
}
