// recursive function that traverses the input object that simulates a table in json.
// this object is a nested object where each nested level represents a value
// that acts like a key in a table, so multiple nested levels represent a group of keys.
// this function takes that object and the levels name that represent the cloumns in the table
// and transform the object into an array of objects where each object represents a row in the table
// if the remainingDataFieldName is provided, the remaining data will be added to the object with that field name
// if not, the remaining data will be added to the object as it is
export function transformHierarchicalKeyedObjectToFlatArray(
  input: Record<string, unknown>,
  levelIndex: number,
  currentOutput: Record<string, unknown>,
  levels: string[],
  results: unknown[],
  remainingDataFieldName?: string
): void {
  if (levelIndex === levels.length) {
    // Base case: we've reached the last level
    let newOutput = {};
    if (
      typeof remainingDataFieldName === 'string' &&
      remainingDataFieldName.length > 0
    ) {
      newOutput = { ...currentOutput, [remainingDataFieldName]: input };
    } else {
      newOutput = { ...currentOutput, ...input };
    }
    results.push(newOutput);
    return;
  } else if (levelIndex < levels.length) {
    const level = levels[levelIndex];
    for (const key in input) {
      if (Object.prototype.hasOwnProperty.call(input, key)) {
        // if level contains @ it means that we need to split the key by @
        // and the first part is the level and the second part is the type
        // the key need to be transformed into
        let newOutput = { ...currentOutput, [level]: key };
        if (level.includes('@')) {
          const [levelName, type] = level.split('@');
          if (type === 'number') {
            // we need to remove the first character from the key which is @
            // and convert the key to a number
            const keyValue = Number(key.slice(1));
            // create another newOutput object with the key as a number
            newOutput = { ...currentOutput, [levelName]: keyValue };
          }
        }
        transformHierarchicalKeyedObjectToFlatArray(
          input[key] as Record<string, unknown>,
          levelIndex + 1,
          newOutput,
          levels,
          results,
          remainingDataFieldName
        );
      }
    }
  }
}
