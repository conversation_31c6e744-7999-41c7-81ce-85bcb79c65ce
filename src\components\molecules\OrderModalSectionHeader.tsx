import { Box, Typography, useMediaQuery } from '@mui/material';
import { NumberField } from 'react-admin';

export default function OrderModalSectionHeader({ el }: { el: any }) {
  const isXSmall = useMediaQuery('(max-width: 768px)');
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'sticky',
        top: isXSmall ? '67px' : '75px',
        bgcolor: theme =>
          theme.palette.mode === 'dark' ? '#26262B' : '#F2F2F2',
        padding: '12px 22px',
        textAlign: 'center',
      }}
    >
      <Typography component="p" sx={{ fontSize: '14px', fontWeight: 'bold' }}>
        {el.name}
      </Typography>
      <NumberField
        defaultValue={
          el.orders.reduce((acc: number, el: any) => acc + el.value, 0) / 10000
        }
        sx={{
          fontWeight: 'bold',
        }}
        source=""
        locales={'ro-RO'}
        options={{
          style: 'currency',
          currency: 'RON',
          currencyDisplay: 'narrowSymbol',
        }}
      />
    </Box>
  );
}
