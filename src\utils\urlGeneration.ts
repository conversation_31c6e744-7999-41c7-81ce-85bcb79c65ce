import { getDownloadURL, ref } from 'firebase/storage';
import {
    UploadedFile,
    PrivateFileContext,
    UrlGenerationOptions,
    ImageVariant
} from '~/types/fileUpload';
import { getStorageInstanceForFile } from './bucketManager';
import { generateTempFilePlaceholder, generateVariantPlaceholder, shouldShowPlaceholder } from './placeholderGenerator';

/**
 * CDN base URL
 */
const CDN_BASE_URL = 'https://cdn.selio.io';

/**
 * Check if we're in production environment
 */
const isProduction = (): boolean => {
    return import.meta.env.VITE_NODE_ENV === 'production';
};

/**
 * Check if a file should use direct Firebase URLs (public files in dev)
 */
const shouldUseDirectFirebaseUrl = (file: UploadedFile): boolean => {
    // In development, public files should use direct Firebase URLs for caching
    // In production, they use CDN
    return !isProduction() && (file.t === 'i' || file.t === 'v' || file.t === 's') && !file.x;
};

/**
 * Generate URL for public images (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generatePublicImageUrl = async (file: UploadedFile, variant?: ImageVariant, format?: string): Promise<string> => {
    // Determine the correct format based on variant
    let actualFormat = format;
    if (!actualFormat) {
        // Original keeps the original extension, others default to webp
        actualFormat = variant === 'original' ? file.e : 'webp';
    }

    // In production, use CDN URLs
    if (isProduction()) {
        if (variant) {
            // Image variants are stored in folder structure: /i/uniqueId/variant.{format}
            return `${CDN_BASE_URL}/i/${file.fn}/${variant}.${actualFormat}`;
        }

        // All public images have variants - default to thumbnail
        return `${CDN_BASE_URL}/i/${file.fn}/thumbnail.webp`;
    }

    // In development, use Firebase Storage URLs for caching
    const storage = getStorageInstanceForFile('i', false);
    let filePath: string;

    if (variant) {
        // Image variants are stored in folder structure: i/uniqueId/variant.{format}
        filePath = `i/${file.fn}/${variant}.${actualFormat}`;
    } else {
        // For images without specified variant, default to thumbnail
        filePath = `i/${file.fn}/thumbnail.webp`;
    }

    const fileRef = ref(storage, filePath);

    try {
        return await getDownloadURL(fileRef);
    } catch (error: any) {
        // If thumbnail doesn't exist, try to fallback to original
        if (!variant && error?.code === 'storage/object-not-found') {
            // Try to fallback to original image
            try {
                const originalPath = `i/${file.fn}/original.${file.e}`;
                const originalRef = ref(storage, originalPath);
                const originalUrl = await getDownloadURL(originalRef);
                return originalUrl;
            } catch (originalError) {
                throw error; // Re-throw original error
            }
        }

        throw error;
    }
};

/**
 * Generate URL for videos (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generateVideoUrl = async (file: UploadedFile): Promise<string> => {
    // In production, use CDN URLs
    if (isProduction()) {
        return `${CDN_BASE_URL}/v/${file.fn}.${file.e}`;
    }

    // In development, use Firebase Storage URLs for caching
    const storage = getStorageInstanceForFile('v', false);
    const filePath = `v/${file.fn}.${file.e}`;
    const fileRef = ref(storage, filePath);
    return await getDownloadURL(fileRef);
};

/**
 * Generate URL for public files (permanent)
 * In production: Uses CDN URLs
 * In development: Uses Firebase Storage URLs with getDownloadURL for caching
 */
export const generatePublicFileUrl = async (file: UploadedFile): Promise<string> => {
    // In production, use CDN URLs
    if (isProduction()) {
        return `${CDN_BASE_URL}/s/${file.fn}.${file.e}`;
    }

    // In development, use Firebase Storage URLs for caching
    const storage = getStorageInstanceForFile('s', false);
    const filePath = `s/${file.fn}.${file.e}`;
    const fileRef = ref(storage, filePath);
    return await getDownloadURL(fileRef);
};

/**
 * Generate Firebase Storage URL for private files
 * Private files use organized paths and require authentication
 */
export const generatePrivateFileUrl = async (
    file: UploadedFile,
    context: PrivateFileContext
): Promise<string> => {
    const pathParts = ['a', context.accountId];

    if (context.sellpointId) {
        pathParts.push(context.sellpointId);
    }

    if (context.customPath) {
        pathParts.push(context.customPath);
    }

    pathParts.push(`${file.fn}.${file.e}`);

    const filePath = pathParts.join('/');
    const storage = getStorageInstanceForFile('p', false);
    const fileRef = ref(storage, filePath);

    return await getDownloadURL(fileRef);
};

/**
 * Generate Firebase Storage URL for temporary files
 * All temporary files use blob URLs for security
 * For public images (t: 'i'), automatically use folder structure with variants
 * Original temp files show placeholder until moved to permanent
 */
export const generateTempFileUrl = async (file: UploadedFile, variant?: ImageVariant): Promise<string> => {
    // Return placeholder for original temp files to prevent preview until moved to permanent
    if (variant === 'original') {
        return generateVariantPlaceholder(file, 'original');
    }

    // If no variant specified for temp image files, return placeholder (this is the original file case)
    if (!variant && file.t === 'i') {
        return generateTempFilePlaceholder(file);
    }

    // For all other cases, we need to return blob URLs (handled by SecureFileManager)
    // This will be processed by the bucket manager's TempFileManager
    return 'blob-url-placeholder'; // This will be replaced by actual blob URL in SecureFileManager
};

/**
 * Main URL generation function that routes to appropriate method
 */
export const generateFileUrl = async (
    file: UploadedFile,
    options?: UrlGenerationOptions
): Promise<string> => {
    // If file has cached URL and we're not forcing refresh, use it
    if (file.url && !options?.forceRefresh) {
        return file.url;
    }

    // Handle temporary files - delegate to SecureFileManager for blob handling
    if (file.x) {
        const { getSecureFileUrl } = await import('./bucketManager');
        return getSecureFileUrl(file, options);
    }

    // Handle permanent files based on type
    switch (file.t) {
        case 'i':
            return await generatePublicImageUrl(file, options?.imageVariant as ImageVariant);

        case 'v':
            return await generateVideoUrl(file);

        case 's':
            return await generatePublicFileUrl(file);

        case 'p':
            // Private files also delegate to SecureFileManager for blob handling
            const { getSecureFileUrl } = await import('./bucketManager');
            return getSecureFileUrl(file, options);

        default:
            throw new Error(`Unknown file type: ${file.t}`);
    }
};

/**
 * Get the destination path for file movement based on file type
 */
export const getDestinationPath = (
    file: UploadedFile,
    context?: PrivateFileContext
): string => {
    const filename = `${file.fn}.${file.e}`;

    switch (file.t) {
        case 'i':
            // For images, check if it uses the new folder structure (all images now use this)
            // Images with variants are stored in folder structure: i/{uniqueId}/original.ext
            // For deletion, we need to target the specific file within the folder
            return `i/${file.fn}/original.${file.e}`;

        case 'v':
            // Videos go to /v/ folder
            return `v/${filename}`;

        case 's':
            // Public files go to /s/ folder
            return `s/${filename}`;

        case 'p':
            // Private files use organized paths
            if (!context) {
                throw new Error('Private file context required for destination path');
            }

            const pathParts = ['a', context.accountId];
            if (context.sellpointId) pathParts.push(context.sellpointId);
            if (context.customPath) pathParts.push(context.customPath);
            pathParts.push(filename);

            return pathParts.join('/');

        default:
            throw new Error(`Unknown file type: ${file.t}`);
    }
};

/**
 * Check if a file should have image variants (legacy system)
 */
export const shouldHaveImageVariants = (file: UploadedFile): boolean => {
    // Only public permanent images should use legacy system
    return file.t === 'i' && !file.x;
};

/**
 * Get all available image variants for a public image (updated to use semantic variants)
 */
export const getAvailableImageVariants = (file: UploadedFile): ImageVariant[] => {
    if (!shouldHaveImageVariants(file)) {
        return []; // Only legacy public permanent images have these variants
    }

    return ['thumbnail', 'card', 'detail', 'profile', 'banner_small', 'banner_medium', 'banner_large', 'hero_small'];
};

/**
 * Get optimal image variant based on display context
 */
export const getOptimalImageVariant = (
    displayWidth: number,
    displayHeight: number
): ImageVariant => {
    // Simple logic to choose optimal variant based on display size
    const maxDimension = Math.max(displayWidth, displayHeight);

    if (maxDimension <= 150) return 'thumbnail';
    if (maxDimension <= 300) return 'card';
    if (maxDimension <= 600) return 'detail';
    if (maxDimension <= 1200) return 'hero_medium';

    return 'hero_large'; // Default to largest for high-res displays
};
