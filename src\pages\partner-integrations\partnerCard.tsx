import { Box, Typography } from "@mui/material";
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts';

export default function PartnerCard({ partner, setPartnerModal, setPartnerExtraData }: { partner: any, setPartnerModal: (value: boolean) => void, setPartnerExtraData: (value: any) => void }) {
    const { theme } = useTheme();
    const { t } = useTranslation('');
    return (
    <Box
      key={partner.name}
      maxWidth={'440px'}
      sx={{ cursor: 'pointer' }}
      onClick={() => {
        setPartnerModal(true);
        setPartnerExtraData(partner);
      }}
    >
      <Box display="flex" alignItems={'center'} gap={2}>
        <img
          src={partner.image}
          alt={partner.name}
          style={{
            width: '85px',
            height: '85px',
            border: '1px solid rgba(227, 227, 227, 1)',
            padding: '10px',
            borderRadius: '12px',
            boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.25)',
            backgroundColor: theme.palette.mode === 'dark' ? 'white' : '#fff',
          }}
        />
        <Box>
          <Typography
            variant="h4"
            sx={{ fontSize: { xs: '16px', md: '17px' } }}
          >
            {partner.name}
          </Typography>
          <Typography
            variant="subtitle2"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {t(partner.description)}
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            gap={2}
          >
            <Typography variant="h6">
              {partner.price === 'free'
                ? t('partnerIntegrations.free')
                : partner.price}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
