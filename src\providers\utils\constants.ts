import { ResourcesInfo } from '../resources';

export const LOCAL_STORAGE_SELECTED_ACCOUNT_KEY = 'selectedAccountId';
export const FIREBASE_RTDB_EUROPE_WEST1_DOMAIN =
  'europe-west1.firebasedatabase.app';
export const LOCAL_FORAGE_DATABASE_NAME = 'selio';
export const BROADCAST_CHANNEL_NAME = `selio-broadcast-channel-${import.meta.env.MODE}`;
export const BROWSER_TAB_ID = Math.random().toString(36).substring(2, 15);
