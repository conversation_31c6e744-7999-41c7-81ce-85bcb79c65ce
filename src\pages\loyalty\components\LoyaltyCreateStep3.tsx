import { useEffect, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, TextField, Typography } from '@mui/material';
import { ReferenceInput, required, SelectInput } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';
import UnitsNeeded from './UnitsNeeded';

export default function LoyaltyCreateStep3() {
  const { getValues, setValue } = useFormContext();
  const selectedUnit = getValues('units')?.values?.plural || 'Units';

  const [couponAmount, setCouponAmount] = useState('');

  useEffect(() => {
    const initialValue = getValues('coupon-amount');
    if (initialValue) {
      setCouponAmount(initialValue);
    }
  }, [getValues]);

  const handleCouponAmountChange = (event: any) => {
    const value = event.target.value.replace(/\D/g, '');
    setCouponAmount(value);
    setValue('coupon-amount', value);
  };

  const handleBlur = () => {
    if (couponAmount.trim() !== '') {
      setValue('coupon-amount', couponAmount);
    }
  };

  return (
    <>
      <Subsection
        title="Set up your rewards"
        titleSx={{
          fontSize: '32px',
          textShadow: '1px 5px 2px rgba(0, 0, 0, 0.17)',
        }}
        subtitle="Create rewards that your customers can earn."
      >
        <Box
          sx={{
            p: 3,
            py: 6,
            boxShadow: 'rgba(99, 99, 99, 0.3) 0px 2px 8px 0px',
            borderRadius: 1.5,
          }}
        >
          <Subsection title="Reward">
            <ReferenceInput source="rewardType" reference="rewardType">
              <SelectInput
                defaultValue={'0'}
                type="select"
                sx={{ mb: 1 }}
                label="Reward Type"
                optionText={record => {
                  return <>{`${record.value}`}</>;
                }}
                size="medium"
                optionValue="id"
                validate={required()}
              />
            </ReferenceInput>
            <TextField
              label="Coupon amount"
              size="medium"
              fullWidth
              value={couponAmount}
              onChange={handleCouponAmountChange}
              onBlur={handleBlur}
              InputProps={{
                endAdornment: couponAmount && <span>&nbsp;RON</span>,
              }}
            />
          </Subsection>
          <Box sx={{ py: 6 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', py: 2, gap: 2 }}>
              <Typography fontWeight={500} fontSize={13} variant="caption">
                {selectedUnit} needed to earn this reward
              </Typography>
              <InfoOutlinedIcon
                sx={{
                  color: 'gray',
                }}
              />
            </Box>
            <UnitsNeeded />
            <Typography fontWeight={300} fontSize={13} variant="caption">
              Customers need to spend at least $50.00 to earn this reward.
            </Typography>
          </Box>
          <img
            style={{ width: '100%' }}
            src="/assets/loyalty/step1/food.png"
            alt=""
          />
        </Box>
      </Subsection>
    </>
  );
}
