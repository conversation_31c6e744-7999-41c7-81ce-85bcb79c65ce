import { Box } from '@mui/material';
import { required, useInput } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useGetListLocationsLive } from '~/providers/resources';
import CustomInput from '../../../atoms/inputs/CustomInput';
import RadioInputGroup from '~/components/molecules/input-groups/RadioInputGroup';
import ControlledRadioInputGroup from '~/components/molecules/controlled-input-groups/ControlledRadioInputGroup';

export default function MenuCreateStep2() {
  const { data: sellPoints } = useGetListLocationsLive();
  const { t } = useTranslation();
  const { field } = useInput({ source: 'type', defaultValue: 'pos' });

  return (
    <>
      <Box>
        <CustomInput
          source="name"
          label={t('shared.name')}
          validate={[required()]}
        />
        <CustomInput
          source="sellPointIds"
          choices={sellPoints}
          type="select-array"
          label={t('shared.location')}
          optionText="name"
          optionValue="id"
          placeholder={t('shared.none', { context: 'female' })}
          validate={[required()]}
        />
       <ControlledRadioInputGroup
          title={t('menu.menuType')}
          value={field.value}
          setValue={val => {
            field.onChange(val);
          }}
          choices={[
            {
              id: 'pos',
              name: t('menu.editPOSLayout'),
              description: t('menu.posLayoutDescription'),
            },
            {
              id: 'list',
              name: t('menu.menuList'),
              description: t('menu.listViewDescription'),
            },
          ]}
        />
      </Box>
    </>
  );
}
