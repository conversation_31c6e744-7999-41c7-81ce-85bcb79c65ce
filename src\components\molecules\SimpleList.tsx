import { Box, Divider, Typography } from '@mui/material';

interface SimpleListProps {
  title: string;
  data: string[];
  onClick: (idx: number) => void;
  hideHeader?: boolean;
  hideRows?: boolean;
  hideDivider?: boolean;
}

export default function SimpleList({
  title,
  data,
  onClick,
  hideHeader,
  hideDivider,
}: SimpleListProps) {
  return (
    <>
      <Divider />
      {!hideHeader && (
        <Box
          sx={{
            py: 2,
            px: 1,
            borderBottom: 'solid 1px',
            borderColor: 'custom.gray600',
          }}
        >
          <Typography variant="body2">{title}</Typography>
        </Box>
      )}

      {!data.length && (
        <Typography variant="caption" color="custom.gray600">
          No entries
        </Typography>
      )}

      {data.map((el: string, idx) => {
        return (
          <>
            <Box
              key={idx}
              onClick={() => onClick(idx)}
              sx={{
                ':hover': {
                  bgcolor: 'primary.light',
                },
                cursor: 'pointer',
                py: 2,
                px: 1,
              }}
            >
              <Typography variant="body2" fontWeight={500} color="primary">
                {el}
              </Typography>
            </Box>
            {!hideDivider && <Divider />}
          </>
        );
      })}
    </>
  );
}
