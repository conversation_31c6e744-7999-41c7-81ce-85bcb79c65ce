import { useState } from 'react';
import CampaignIcon from '@mui/icons-material/Campaign';
import CloseIcon from '@mui/icons-material/Close';
import NotificationsOutlinedIcon from '@mui/icons-material/NotificationsOutlined';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  IconButton,
  Typography,
} from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import getSideModalProps from '../../../utils/getSideModalProps';

export default function NotificationsCenter() {
  const [open, setOpen] = useState<boolean>(false);
  const { t } = useTranslation('');

  const { data, total, isLoading } = useGetList('notifications', {
    pagination: { page: 1, perPage: 10 },
    sort: { field: 'unread', order: 'DESC' },
  });

  const handleClick = () => {
    setOpen(true);
  };

  return (
    <>
      <IconButton onClick={handleClick} sx={{ position: 'relative' }}>
        <NotificationsOutlinedIcon />
        {total ? (
          <Box
            sx={{
              position: 'absolute',
              top: '8px',
              right: '6px',
              height: '8px',
              width: '8px',
              borderRadius: '50%',
              bgcolor: 'primary.main',
            }}
          />
        ) : (
          <></>
        )}
      </IconButton>

      <Dialog
        open={open}
        {...getSideModalProps({})}
        onClose={() => setOpen(false)}
      >
        <Box sx={{ position: 'relative', p: 3 }}>
          <Typography variant="h4" textAlign={'center'}>
            {t('dashboard.notificationCenterTitle')}
          </Typography>
          <Button
            onClick={() => setOpen(false)}
            // @ts-ignore
            variant="close-btn"
            aria-label="close"
            sx={{
              '& span': { mr: 0 },
              position: 'absolute',
              top: '16px',
              left: '20px',
            }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        </Box>
        {isLoading ? (
          <CircularProgress sx={{ mx: 'auto', mt: 3 }} />
        ) : data?.length ? (
          data?.map(el => {
            return (
              <Box
                key={el.id}
                sx={{
                  display: 'flex',
                  py: 3,
                  px: 2,
                  mx: 2,
                  borderRadius: 3,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  ':hover': {
                    bgcolor: 'primary.light',
                  },
                }}
                onClick={() => window.open(el.href)}
              >
                <Box
                  sx={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    bgcolor: el.unread ? 'primary.main' : 'transparent',
                    mt: 1,
                    mr: 2,
                  }}
                />
                <Box>
                  <Typography variant="h5">{el.title}</Typography>
                  <Typography variant="subtitle2">{el.description}</Typography>
                </Box>
              </Box>
            );
          })
        ) : (
          <Box sx={{ margin: 'auto', mt: '50px', textAlign: 'center' }}>
            <CampaignIcon fontSize="large" color="disabled" />
            <Typography variant="subtitle1" color={'custom.gray600'}>
              {t('dashboard.upToDate')}
            </Typography>
          </Box>
        )}
      </Dialog>
    </>
  );
}
