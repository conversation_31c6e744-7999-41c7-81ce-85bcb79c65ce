import { ReportFiltersState } from '../components/ReportFilters';

export const getStartEndHours = (
  filters: ReportFiltersState,
  sellpoint: any
) => {
  const { dateRange } = filters;
  const isOneDay = dateRange[0]?.isSame(dateRange[1], 'day');

  let startHour = parseInt(filters?.timeRange.start?.format('HH') ?? '9');
  let endHour = parseInt(filters?.timeRange.end?.format('HH') ?? '24');

  if (!!filters?.timeRange.allDay) {
    const parseHour = (hour: string) => parseInt(hour.split(':')[0]);
    const { businessHoursSchedule, businessHoursExceptions } = sellpoint;

    if (!isOneDay) {
      startHour = parseHour(businessHoursSchedule.startAt);
      endHour = parseHour(businessHoursSchedule.startAt) - 1;
    } else {
      const dayIndex = ((dateRange[0]?.day() || 0) + 6) % 7;
      startHour = parseHour(
        (businessHoursExceptions?.[dayIndex] ?? businessHoursSchedule).startAt
      );
      const nextDayIndex = (dayIndex + 1) % 7;
      endHour = !!(
        businessHoursExceptions?.[nextDayIndex] ?? businessHoursSchedule
      ).startAt
        ? parseHour(
            (businessHoursExceptions?.[nextDayIndex] ?? businessHoursSchedule)
              .startAt
          )
        : startHour;
    }
  }

  return { startHour, endHour };
};
