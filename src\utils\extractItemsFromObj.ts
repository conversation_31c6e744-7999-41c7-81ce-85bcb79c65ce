const extractItemsRecursive = (
  page: any[],
  type: 'product' | 'displayGroup'
): any => {
  const result = [];

  for (const item of page) {
    if (
      (type === 'product' && (item.type === 'product' || !item.type)) ||
      (type === 'displayGroup' && item.type === 'displayGroup')
    ) {
      result.push(item);
    }

    if (item.items && Array.isArray(item.items)) {
      const nestedItems = extractItemsRecursive(item.items, type);
      result.push(...nestedItems);
    }
  }

  return result;
};

const extractItems = (pages: any[], type: 'product' | 'displayGroup'): any => {
  let tmp = [];

  for (const page of pages) {
    const res = extractItemsRecursive(page, type);
    tmp.push(...res);
  }

  // remove duplicates
  tmp = tmp.filter((v, i, a) => a.findIndex(v2 => v2.id === v.id) === i);

  return { items: tmp, length: tmp.length };
};

export default extractItems;
