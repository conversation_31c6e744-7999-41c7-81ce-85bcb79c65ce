import React, { useEffect, useState } from 'react';
import { Box, Button } from '@mui/material';
import { ReferenceInput, SimpleForm, useDataProvider } from 'react-admin';

import CustomInput from '../../components/atoms/inputs/CustomInput';
import Subsection from '../../components/molecules/Subsection';
import { useTranslation } from 'react-i18next';

const AboutMyBusiness: React.FC = () => {
  const { t } = useTranslation();
  const [accountInfo, setAccountInfo] = useState<any>('');
  const dataProvider = useDataProvider();
  const [editBusinessName, setEditBusinessName] = useState(false);
  const [businessName, setBusinessName] = useState('');

  useEffect(() => {
    const fetchAccountInfo = async () => {
      try {
        const { data } = await dataProvider.getOne('accountInfo', { id: '1' });
        setAccountInfo(data);
        setBusinessName(data.businessName || '');
      } catch (error: any) {
        console.error(error.message);
      }
    };

    fetchAccountInfo();
  }, [dataProvider]);

  const updateAccountInfo = async (updatedInfo: any) => {
    try {
      const { data } = await dataProvider.update('accountInfo', {
        id: '1',
        data: updatedInfo,
        previousData: accountInfo,
      });
      setAccountInfo(data);
    } catch (error: any) {
      console.error(error.message);
    }
  };

  const handleSave = async () => {
    const updatedInfo = { ...accountInfo, businessName };
    await updateAccountInfo(updatedInfo);
    setEditBusinessName(false);
  };

  return (
    <>
      <Box
        p={2}
        sx={{
          mt: 3,
          width: '100%',
          maxWidth: '600px',
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
        }}
      >
        <Subsection
          title={t('aboutMyBusiness.title')}
          subtitle={t('aboutMyBusiness.subtitle')}
        >
          <SimpleForm toolbar={false} sx={{ p: 0 }}>
            <Box
              sx={{
                width: '100%',
                display: 'flex',
              }}
            >
              <Box sx={{ width: '100%' }}>
                <Box sx={{ position: 'relative', display: 'flex' }}>
                  <ReferenceInput source="id" reference="accountInfo">
                    <CustomInput
                      // sx={{ pr: 2 }}
                      type="text"
                      label={t('aboutMyBusiness.businessName')}
                      InputProps={{
                        sx: {
                          pr: { xs: 6, sm: 8 },
                        },
                      }}
                      source="businessName"
                      disabled={!editBusinessName}
                      value={businessName}
                      defaultValue={"X SRL"}
                      onChange={e => setBusinessName(e.target.value)}
                    />
                  </ReferenceInput>
                  <Button
                    onClick={() => {
                      if (editBusinessName) {
                        handleSave();
                      } else {
                        setEditBusinessName(true);
                      }
                    }}
                    sx={{
                      position: 'absolute',
                      right: 0,
                      top: { xs: 38, sm: 0 },
                      zIndex: 10,
                      borderRadius: 0,
                    }}
                  >
                    {editBusinessName ? t('shared.save') : t('shared.edit')}
                  </Button>
                </Box>
                <ReferenceInput source="id2" reference="accountInfo">
                  <CustomInput
                    type="text"
                    label={t('aboutMyBusiness.businessOwner')}
                    source="businessOwner"
                    defaultValue={accountInfo.businessOwner}
                    disabled
                  />
                </ReferenceInput>
              </Box>
            </Box>
          </SimpleForm>
        </Subsection>
      </Box>
    </>
  );
};

export default AboutMyBusiness;
