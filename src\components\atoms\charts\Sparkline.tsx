import { createRef, useEffect, useRef, useState } from 'react';
import sparkline from '@fnando/sparkline';
import { Theme, useMediaQuery } from '@mui/material';
import { Instance } from '@popperjs/core';
import dayjs from 'dayjs';

import { formatNumber } from '~/utils/formatNumber';
import { DarkTooltip } from '../DarkTooltip';

type ChartItem = {
  value: number;
  date: string;
};

interface SparklineProps {
  chartData: Array<ChartItem>;
  formatData?: (el: string | number) => string | number;
}

export default function Sparkline({
  chartData,
  formatData = el => el,
}: SparklineProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const [crtDatapointIdx, setCrtDatapointIdx] = useState<number | null>(null);
  const sparklineRef = createRef<SVGSVGElement>();
  const tooltipAreaRef = useRef<HTMLDivElement>(null);
  const tooltipPosRef = useRef<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const popperRef = useRef<Instance>(null);

  // handle mouse move for tooltip (so we show it right above cursor)
  const handleMouseMove = (event: React.MouseEvent) => {
    tooltipPosRef.current = { x: event.clientX, y: event.clientY };

    if (popperRef.current != null) {
      popperRef.current.update();
    }
  };

  // sparkline chart options (so we get value we're hovering on)
  const options = {
    onmousemove: (_: any, datapoint: any) => {
      if (crtDatapointIdx !== datapoint.index) {
        setCrtDatapointIdx(datapoint.index);
      }
    },
    onmouseout: () => {
      setCrtDatapointIdx(null);
    },
  };

  // initialize sparkline on mount
  useEffect(() => {
    const data = chartData.map(el => el.value);
    sparkline(sparklineRef.current as SVGSVGElement, data, options);
  }, [chartData]);

  return (
    <DarkTooltip
      bigFont
      text={
        crtDatapointIdx !== null
          ? formatData(chartData[crtDatapointIdx].value)
          : undefined
      }
      label={
        crtDatapointIdx !== null ? chartData[crtDatapointIdx].date : undefined
      }
      PopperProps={{
        popperRef,
        anchorEl: {
          getBoundingClientRect: () => {
            return new DOMRect(
              tooltipPosRef.current.x,
              tooltipAreaRef.current!.getBoundingClientRect().y,
              0,
              0
            );
          },
        },
      }}
    >
      <div
        ref={tooltipAreaRef}
        style={{ display: 'flex', flexDirection: 'column' }}
        onMouseMove={handleMouseMove}
      >
        <svg
          ref={sparklineRef}
          width={isXSmall ? '280' : '385'}
          height="55"
          strokeWidth="2"
          stroke="#0069FF"
          fill="rgba(0, 0, 0, 0)"
        />
      </div>
    </DarkTooltip>
  );
}
