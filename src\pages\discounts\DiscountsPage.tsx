import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import DiscountsCreate from './DiscountsCreate';
import DiscountsEdit from './DiscountsEdit';
import DiscountsList from './DiscountsList';

export default function DiscountsPage() {
  const { t } = useTranslation();
  const { sellPointId, isLoading } = useGlobalResourceFilters();

  if (isLoading || !sellPointId) {
    return null;
  }

  return (
    <Box sx={{ height: '100%' }} p={2}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('discountsPage.title')}
        description={
          <>
            {t('discountsPage.description')}
            <br /> {t('discountsPage.description2')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <DiscountsList sellPointId={sellPointId} />
      <DiscountsEdit sellPointId={sellPointId} />
      <DiscountsCreate sellPointId={sellPointId} />
    </Box>
  );
}
