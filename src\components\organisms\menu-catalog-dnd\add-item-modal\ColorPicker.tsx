import { useState } from 'react';
import { Box, Button, Popover, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import ColorSelectInputGroup from '~/components/molecules/input-groups/ColorSelectInputGroup';
import { useTheme } from '~/contexts/ThemeContext';
import { menuColors } from '~/data/menu-colors';

interface ColorPickerProps {
  color?: string;
  setColor: (color: string) => void;
  disabled?: boolean;
  disabledColorPicker?: boolean;
}

export default function ColorPicker({
  color,
  setColor,
  disabled,
  disabledColorPicker,
}: ColorPickerProps) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const { t } = useTranslation();
  const { theme } = useTheme();

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'color-picker-popover' : undefined;

  return (
    <>
      <Button
        aria-describedby={id}
        variant="text"
        onClick={handleClick}
        disabled={disabled}
        sx={{
          p: 0,
          width: '100%',
          overflow: 'hidden',
          flexDirection: 'column',
          border: 'solid 1px',
          borderColor: 'custom.gray400',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '100px',
            backgroundColor: color,
          }}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor:
              theme.palette.mode === 'dark'
                ? theme.palette.custom.gray400
                : 'white',
            p: 1,
            width: '100%',
          }}
        >
          <Typography variant="body2" color="primary">
            {disabled
              ? t('menu.menuGroupColor')
              : open
                ? t('shared.save')
                : t('shared.change')}
          </Typography>
        </Box>
      </Button>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Box sx={{ maxWidth: '362px', p: 3 }}>
          <Typography variant="h5" mb={1}>
            {t('menu.color')}
          </Typography>
          <ColorSelectInputGroup
            onChange={value => {
              setColor(value);
            }}
            value={color}
            choices={menuColors}
            disabled={disabledColorPicker}
            //   disabled={!type}
          />
        </Box>
      </Popover>
    </>
  );
}
