import {
  DeleteWithConfirmIconButton,
  DeleteWithConfirmIconButtonProps,
} from '@react-admin/ra-editable-datagrid';
import { useRecordContext } from 'react-admin';
import { useTranslation } from 'react-i18next';

interface CustomDeleteWithConfirmIconButtonProps
  extends DeleteWithConfirmIconButtonProps {
  field?: string;
}

export default function CustomDeleteWithConfirmIconButton({
  field,
  confirmTitle,
  ...rest
}: CustomDeleteWithConfirmIconButtonProps) {
  const record = useRecordContext();
  const { t } = useTranslation('');
  return (
    <DeleteWithConfirmIconButton
      confirmTitle={
        field ? `${t('menu.delete?')} ${record?.[field]}?` : confirmTitle
      }
      confirmContent={t('menu.deleteConfirmation')}
      translateOptions={{
        cancel: t('shared.cancel'),
        confirm: t('shared.confirm'),
      }}
      label={t('shared.delete')}
      {...rest}
    />
  );
}
