import { useEffect, useMemo } from 'react';
import dayjs from 'dayjs';
import { useStore } from 'react-admin';

import { useGetListLocationsLive } from '~/providers/resources';

import type { DateRange } from '@mui/x-date-pickers-pro';
import type { Dayjs } from 'dayjs';

// Helper to stringify Dayjs date range to ISO string array for storage
const dayjsDateRangeToISOStringArray = (
  dateRange: DateRange<Dayjs> | null
): [string, string] | null => {
  if (!dateRange || !dateRange[0] || !dateRange[1]) {
    return null;
  }
  return [dateRange[0].toISOString(), dateRange[1].toISOString()];
};

// Helper to parse ISO string array back to Dayjs date range
const isoStringArrayToDayjsDateRange = (
  value: [string, string] | null | undefined
): DateRange<Dayjs> | null => {
  if (!value || value.length !== 2 || !value[0] || !value[1]) return null;
  try {
    const startDate = dayjs(value[0]);
    const endDate = dayjs(value[1]);
    if (startDate.isValid() && endDate.isValid()) {
      return [startDate, endDate];
    }
    console.warn('Stored date strings are invalid:', value);
    return null;
  } catch (error) {
    console.error('Error parsing date range from store:', error);
    return null;
  }
};

interface UseGlobalResourceFiltersParams {
  initialSellPointId?: string;
  initialDateRange?: DateRange<Dayjs>;
  initialTimeRange?: DateRange<Dayjs> | null; // Added initialTimeRange
}

interface GlobalResourceFiltersHookValue {
  sellPoints?: Array<{ id: string; [key: string]: any }>;
  isLoading: boolean;
  sellPointId: string;
  setSellPointId: (sellPointId: string) => void;
  dateRange: DateRange<Dayjs>;
  setDateRange: (range: DateRange<Dayjs>) => void;
  timeRange: DateRange<Dayjs> | null; // Added timeRange
  setTimeRange: (range: DateRange<Dayjs> | null) => void; // Added setTimeRange
}

const GLOBAL_SELLPOINT_ID_KEY = 'globalSellPointId';
const GLOBAL_DATE_RANGE_KEY = 'globalDateRange';
const GLOBAL_TIME_RANGE_KEY = 'globalTimeRange'; // New store key for timeRange

export const useGlobalResourceFilters = ({
  initialSellPointId = '',
  initialDateRange: providedInitialDateRange,
  initialTimeRange: providedInitialTimeRange = null, // Default to null
}: UseGlobalResourceFiltersParams = {}): GlobalResourceFiltersHookValue => {
  const { data: sellPoints, isLoading } = useGetListLocationsLive();

  const defaultInitialDateRange = useMemo(
    () => [dayjs().startOf('day'), dayjs().endOf('day')] as DateRange<Dayjs>,
    []
  );
  const actualInitialDateRange =
    providedInitialDateRange ?? defaultInitialDateRange;

  // Date Range State
  const [storedDateRangeISO, setStoredDateRangeISO] = useStore<
    [string, string] | null
  >(
    GLOBAL_DATE_RANGE_KEY,
    dayjsDateRangeToISOStringArray(actualInitialDateRange)
  );

  const dateRange = useMemo(() => {
    const parsedRange = isoStringArrayToDayjsDateRange(storedDateRangeISO);
    return parsedRange ?? actualInitialDateRange;
  }, [storedDateRangeISO, actualInitialDateRange]);

  const setDateRange = (newDateRange: DateRange<Dayjs>) => {
    if (
      newDateRange &&
      newDateRange[0] &&
      newDateRange[1] &&
      newDateRange[0].isValid() &&
      newDateRange[1].isValid()
    ) {
      setStoredDateRangeISO(dayjsDateRangeToISOStringArray(newDateRange));
    } else {
      setStoredDateRangeISO(
        dayjsDateRangeToISOStringArray(actualInitialDateRange)
      );
    }
  };

  // Time Range State
  const [storedTimeRangeISO, setStoredTimeRangeISO] = useStore<
    [string, string] | null
  >(
    GLOBAL_TIME_RANGE_KEY,
    dayjsDateRangeToISOStringArray(providedInitialTimeRange) // Use providedInitialTimeRange
  );

  const timeRange = useMemo(() => {
    return isoStringArrayToDayjsDateRange(storedTimeRangeISO); // If null, remains null
  }, [storedTimeRangeISO]);

  const setTimeRange = (newTimeRange: DateRange<Dayjs> | null) => {
    if (newTimeRange === null) {
      setStoredTimeRangeISO(null);
    } else if (
      newTimeRange &&
      newTimeRange[0] &&
      newTimeRange[1] &&
      newTimeRange[0].isValid() &&
      newTimeRange[1].isValid()
    ) {
      setStoredTimeRangeISO(dayjsDateRangeToISOStringArray(newTimeRange));
    } else {
      // Optionally, handle invalid non-null ranges, e.g., by setting to null or a default
      // For now, if it's not explicitly null but invalid, we'll store null
      setStoredTimeRangeISO(
        dayjsDateRangeToISOStringArray(providedInitialTimeRange)
      );
    }
  };

  // SellPoint ID State
  const [storedSellPointId, setStoredSellPointId] = useStore<string>(
    GLOBAL_SELLPOINT_ID_KEY,
    initialSellPointId
  );

  useEffect(() => {
    if (sellPoints && sellPoints.length > 0) {
      const currentIdIsValidSellpoint = sellPoints.some(
        sp => sp.id === storedSellPointId
      );
      if (
        !storedSellPointId ||
        (initialSellPointId && storedSellPointId === initialSellPointId) ||
        !currentIdIsValidSellpoint
      ) {
        const firstSellPointId = sellPoints[0].id;
        if (firstSellPointId && storedSellPointId !== firstSellPointId) {
          setStoredSellPointId(firstSellPointId);
        }
      }
    }
  }, [sellPoints, storedSellPointId, setStoredSellPointId, initialSellPointId]);

  return {
    sellPointId: storedSellPointId,
    setSellPointId: setStoredSellPointId,
    dateRange,
    setDateRange,
    timeRange,
    setTimeRange,
    sellPoints,
    isLoading,
  };
};
