import styled from '@emotion/styled';
import { Autocomplete } from '@mui/material';

export const CustomAutocompleteMui = styled(Autocomplete)(({ theme }) => ({
  boxShadow: 'none',
  fontSize: '16px',
  borderRadius: 4,
  '.MuiOutlinedInput-notchedOutline': {
    border:
      (theme as any).palette.mode == 'light'
        ? '1px solid rgba(0, 0, 0, 0.23)'
        : '1px solid rgba(255, 255, 255, 0.23)',
  },
  '& .MuiInputBase-input': {
    borderRadius: 4,
    position: 'relative',
    '&:focus, &:active, &:focus-visible, &[expanded="true"] ': {
      backgroundColor: 'primary.veryLight',
    },
  },
}));
