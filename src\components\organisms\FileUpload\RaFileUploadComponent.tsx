import React, { useCallback } from 'react';
import { useInput, useNotify } from 'react-admin';

import { UploadedFile } from '~/types/fileUpload';
import FileUploadComponent from './FileUploadComponent';
import { RaFileUploadComponentProps } from './types';
import { mergeWithDefaults } from './utils/fileUploadConfig';

/**
 * React-Admin wrapper for FileUploadComponent - replaces FileUploadInput
 */
const RaFileUploadComponent: React.FC<RaFileUploadComponentProps> = ({
  source,
  config: userConfig,
  label,
  helperText,
  infoText,
  validate,
  required,
  ...props
}) => {
  const notify = useNotify();

  const {
    field: { onChange, value },
    fieldState: { error, invalid },
    formState: { isSubmitting },
  } = useInput({
    source,
    validate,
    ...props,
  });

  // Get current files from form value (not record)
  const currentFiles: UploadedFile[] = Array.isArray(value)
    ? value
    : value
      ? [value]
      : [];

  // Merge user config with defaults
  const config = React.useMemo(
    () => mergeWithDefaults(userConfig),
    [userConfig]
  );

  // Handle files change
  const handleFilesChange = useCallback(
    (files: UploadedFile[]) => {
      onChange(files);
    },
    [onChange]
  );

  // Handle file uploaded callback
  const handleFileUploaded = useCallback(
    (file: UploadedFile) => {
      // The main FileUploadComponent handles adding files to the list
      // We just need to call the original callback if provided
      config.callbacks?.onFileUploaded?.(file);
    },
    [config.callbacks]
  );

  // Handle upload error
  const handleUploadError = useCallback(
    (uploadError: any) => {
      let errorMessage = 'Failed to upload file. ';

      if (uploadError?.message) {
        errorMessage += uploadError.message;
      } else {
        errorMessage +=
          'Please try again or contact support if the problem persists.';
      }

      notify(errorMessage, { type: 'error' });
    },
    [notify]
  );

  // Handle validation error
  const handleValidationError = useCallback(
    (errors: any[]) => {
      const message =
        errors.length === 1
          ? errors[0].message
          : `${errors.length} validation errors occurred`;
      notify(message, { type: 'warning' });
    },
    [notify]
  );

  // Handle upload success
  const handleUploadSuccess = useCallback(
    (files: UploadedFile[]) => {
      const count = files.length;
      const message =
        count === 1
          ? 'File uploaded successfully'
          : `${count} files uploaded successfully`;
      notify(message, { type: 'success' });
    },
    [notify]
  );

  // Enhanced config with callbacks
  const enhancedConfig = React.useMemo(
    () => ({
      ...config,
      callbacks: {
        ...config.callbacks,
        onFileUploaded: handleFileUploaded,
        onUploadError: handleUploadError,
        onValidationError: handleValidationError,
        onUploadSuccess: handleUploadSuccess,
      },
      ui: {
        ...config.ui,
        disabled: isSubmitting || config.ui?.disabled,
      },
    }),
    [
      config,
      handleFileUploaded,
      handleUploadError,
      handleValidationError,
      handleUploadSuccess,
      isSubmitting,
    ]
  );

  return (
    <FileUploadComponent
      value={currentFiles}
      onChange={handleFilesChange}
      config={enhancedConfig}
      label={label}
      helperText={helperText}
      error={invalid}
      errorText={error?.message}
    />
  );
};

export default RaFileUploadComponent;
