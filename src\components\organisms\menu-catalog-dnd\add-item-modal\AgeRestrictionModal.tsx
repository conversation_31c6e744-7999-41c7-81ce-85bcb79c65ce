import { useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import ModalHeader from '~/components/molecules/ModalHeader';

interface AgeRestrictionModalProps {
  age?: number;
  onClose: (e?: any) => void;
  onSave: (age: number) => void;
}
export default function AgeRestrictionModal({
  age: initialAge,
  onClose,
  onSave,
}: AgeRestrictionModalProps) {
  const [age, setAge] = useState(initialAge);
  const [isSelectingCustom, setIsSelectingCustom] = useState(false);
  const { t } = useTranslation('');

  return (
    <Dialog
      open
      disablePortal
      onClose={(e: any, reason: any) => {
        if (reason === 'backdropClick') {
          e.stopPropagation();
          return;
        }
        onClose(e);
      }}
      fullWidth={true}
      maxWidth={'sm'}
      onClick={e => e.stopPropagation()}
    >
      <ModalHeader handleClose={onClose} title="" noBorder>
        <Button
          disabled={isSelectingCustom && !age ? true : false}
          variant="contained"
          onClick={() => onSave(age!)}
        >
          {t('shared.set')}
        </Button>
      </ModalHeader>

      <Box p={2} mb={2}>
        <Typography variant="h3" mb={5}>
          {t('menu.ageVerification')}
        </Typography>
        <Typography variant="subtitle2" color="textPrimary" mb={4}>
          {t('menu.ageVerificationTooltip')}
          <a href="">{t('shared.learnMore')}</a>
        </Typography>

        <Typography variant="h5" mb={2}>
          {t('menu.allLocations')}
        </Typography>
        <FormControl fullWidth>
          {/* <InputLabel id='age-restriction-label'>{t("menu.ageRestriction")}</InputLabel> */}
          <Select
            // fullWidth
            labelId="age-restriction-label"
            value={
              age != 0 && age != 18
                ? 'custom'
                : isSelectingCustom
                  ? 'custom'
                  : age
            }
            onChange={event => {
              if (event.target.value === 'custom') {
                setIsSelectingCustom(true);
                setAge(undefined);
                return;
              }
              setIsSelectingCustom(false);
              setAge(event.target.value as number);
            }}
            // label={t("menu.ageRestriction")}
          >
            <MenuItem value={0}>{t('menu.noRestriction')}</MenuItem>
            <MenuItem value={18}>{t('menu.ageRestriction18')}</MenuItem>
            <MenuItem value={'custom'}>
              {t('menu.customAgeRestriction')}
            </MenuItem>
          </Select>
        </FormControl>

        {(isSelectingCustom || (age != 0 && age != 18)) && (
          <TextField
            type="number"
            fullWidth
            value={age}
            onChange={event => setAge(parseInt(event.target.value))}
            label={t('menu.customAgeRestriction')}
            sx={{ marginTop: 2 }}
          />
        )}
        <Typography variant="subtitle2" mt={2}>
          {t('menu.ageRestrictionTooltip2')}
        </Typography>
      </Box>
    </Dialog>
  );
}
