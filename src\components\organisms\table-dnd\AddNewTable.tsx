import { useState, useEffect } from 'react';
import { Box, Button, Dialog, TextField } from '@mui/material';
import { useTranslation } from 'react-i18next';

import ModalHeader from '../../molecules/ModalHeader';

interface AddNewTableProps {
  open: boolean;
  handleClose: () => void;
  addTable: (name: number, tag?: string) => boolean;
  nextTableNumber: number;
}

export default function AddNewTable({
  open,
  addTable,
  handleClose,
  nextTableNumber,
}: AddNewTableProps) {
  const [name, setName] = useState<string>(nextTableNumber.toString());
  const [tag, setTag] = useState<string>('');
  const { t } = useTranslation();
  const [error, setError] = useState<string>('');
  const [errorTag, setErrorTag] = useState<string>('');

  useEffect(() => {
    if (open) {
      setName(nextTableNumber.toString());
    }
  }, [open, nextTableNumber]);

  const handleAddTable = () => {
    if (!name) {
      setError(t('floorPlansPage.numberRequired'));
      return;
    } else {
      setError('');
    }

    const added = addTable(parseInt(name), tag);

    if (!added) {
      setError(t('floorPlansPage.tableAlreadyExists'));
      return;
    }

    handleCloseHelper();
  };

  const handleCloseHelper = () => {
    setName('');
    setTag('');
    setError('');
    handleClose();
  };

  return (
    <Dialog open={open} fullWidth maxWidth="sm" onClose={handleCloseHelper}>
      <ModalHeader
        handleClose={handleCloseHelper}
        title={t('floorPlansPage.addNewTable')}
      >
        <Button
          // @ts-ignore
          variant="contained"
          onClick={handleAddTable}
        >
          {t('floorPlansPage.addTable')}
        </Button>
      </ModalHeader>
      <Box sx={{ p: 2 }}>
        <TextField
          error={!!error}
          helperText={error}
          label={t('floorPlansPage.number')}
          type="number"
          value={name}
          onChange={e => setName(e.target.value)}
          sx={{
            '& .MuiFormHelperText-root': {
              display: 'block',
            },
          }}
        />
        <TextField
          error={!!errorTag}
          helperText={errorTag}
          label="Tag"
          type="text"
          value={tag}
          onChange={e => setTag(e.target.value)}
          sx={{
            '& .MuiFormHelperText-root': {
              display: 'block',
            },
          }}
        />
      </Box>
    </Dialog>
  );
}
