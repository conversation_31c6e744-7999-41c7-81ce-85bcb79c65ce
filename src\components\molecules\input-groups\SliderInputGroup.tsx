import {
  Box,
  Slider,
  SliderProps,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';

interface SliderInputGroupProps extends SliderProps {
  title?: string;
  values: {
    min: number;
    max: number;
  };
  changeValues: (value: { min: number; max: number }) => void;
}
export default function SliderInputGroup({
  values,
  changeValues,
  title,
  ...props
}: SliderInputGroupProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const handleChange = (_: Event, newValue: number | number[]) => {
    if (Array.isArray(newValue)) {
      changeValues({
        min: newValue[0],
        max: newValue[1],
      });
    }
  };

  return (
    <Box
      sx={{
        border: `1px solid`,
        borderColor: 'custom.gray400',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        marginTop: '-1px',
        bgcolor: 'custom.fieldBg',
      }}
    >
      {title && (
        <Box
          sx={{
            width: isXSmall ? '100%' : '200px',
            bgcolor: 'background.tinted',
            minWidth: '200px',
            display: 'flex',
            p: 2,
          }}
        >
          {/* @ts-ignore */}
          <Typography variant="label">{title}</Typography>
        </Box>
      )}
      <Box
        sx={{
          px: 3.5,
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          minHeight: '45px',
        }}
      >
        <Slider
          getAriaLabel={() => 'Temperature range'}
          value={[values.min, values.max]}
          onChange={handleChange}
          valueLabelDisplay="auto"
          marks
          min={1}
          max={9}
          {...props}
        />
      </Box>
    </Box>
  );
}
