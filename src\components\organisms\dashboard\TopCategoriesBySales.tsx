import { useCallback, useEffect, useState } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import BarChart from '~/components/atoms/charts/BarChart';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import { groupReport } from '~/fake-provider/reports/groupReport';
import {
  OmitKeysWithTypeTransform,
  Report,
} from '~/fake-provider/reports/types';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListHospitalityCategoriesLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';

const REPORT_TYPE = 'topGroups';

export default function TopItemsBySales() {
  const { details: fbDetails } = useFirebase();
  const [data, setData] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { t } = useTranslation();
  const { dateRange, sellPointId } = useGlobalResourceFilters();
  const { data: categories, isPending } = useGetListHospitalityCategoriesLive();

  const fetchItemNames = useCallback(
    (items: { value: number; label: string }[]) => {
      try {
        if (categories?.length === 0) return items;
        const resolvedData = items.map(item => {
          try {
            // find item.label in categories array by filtering by id
            const category = categories?.find(
              category => category.id === item.label
            );
            return {
              ...item,
              label: capitalize(category.name.toLowerCase()),
            };
          } catch (error) {
            console.error(`Failed to fetch item with id: ${item.label}`, error);
            return { ...item, label: item.label };
          }
        });
        setData(resolvedData);
      } catch (error) {
        console.error('Error fetching item names:', error);
      }
    },
    [categories]
  );

  useEffect(() => {
    if (isPending) return;
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        setData([]);
        setIsLoading(false);
        return;
      }
      try {
        if (!dateRange[0] || !dateRange[1]) return;

        const rawData = (await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          endDate: dateRange[1].format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        })) as OmitKeysWithTypeTransform<Report<'topItems'>>[];

        if (rawData.length === 0) {
          setData([]);
          setIsLoading(false);
          return;
        }

        const grouped = groupReport(REPORT_TYPE, rawData, [], ['id']);
        const sortedCategories = grouped[0].report
          .sort((a, b) => b.scoreValue - a.scoreValue)
          .slice(0, 4)
          .map(el => ({
            value: el.scoreValue,
            label: el.id,
          }));

        fetchItemNames(sortedCategories);
        setIsLoading(false);
      } catch (err) {
        console.error(err);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fetchItemNames, fbDetails, isPending]);

  return (
    <Box>
      <Typography variant="h4">
        {t('dashboard.topPopularCategories')}
      </Typography>
      <Typography variant="body2" color="custom.gray600">
        {t('dashboard.byWeightInSales')}
      </Typography>
      {isLoading || isPending ? (
        <CircularProgress sx={{ mx: 'auto', mt: 3 }} />
      ) : data.length > 0 ? (
        <BarChart chartData={data} type="number" withTable />
      ) : (
        <Typography
          variant="body2"
          color="custom.gray600"
          mt={2}
          fontStyle="italic"
        >
          {t('dashboard.noData')}
        </Typography>
      )}
    </Box>
  );
}
