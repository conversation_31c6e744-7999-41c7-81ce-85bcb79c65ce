import path from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import svgr from 'vite-plugin-svgr';

import { APP_VERSION } from './version';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, isSsrBuild, isPreview }) => {
  return {
    plugins: [
      svgr(),
      react(),
      {
        name: 'html-transform',
        transformIndexHtml(html) {
          return html.replace(
            /<\/head>/,
            `  <meta name="app-version" content="${APP_VERSION}" />\n  </head>`
          );
        },
      },
    ],
    define: {
      //      'process.env': process.env,
      __APP_VERSION__: JSON.stringify(APP_VERSION),
    },
    esbuild: {
      keepNames: true,
    },
    build: {
      //sourcemap: true,
      //outDir: 'public',
    },
    resolve: {
      preserveSymlinks: true,
      alias: {
        '~': path.resolve(__dirname, 'src'),
      },
    },
    //envPrefix: "SELIO_",
    worker: {
      format: 'es',
    },
  };
});
