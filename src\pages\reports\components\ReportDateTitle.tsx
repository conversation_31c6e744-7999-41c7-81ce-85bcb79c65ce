import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';

export default function ReportDateTitle() {
  const { dateRange, timeRange } = useGlobalResourceFilters();
  const [startDate, endDate] = dateRange;
  const [startTime, endTime] = timeRange ?? [];
  const { t } = useTranslation();

  return (
    <Box py={3}>
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="h2"
      >
        {startDate?.format('MMM D, YYYY')}{' '}
        {!startDate?.isSame(endDate, 'date') &&
          ` - ${endDate?.format('MMM D, YYYY')}`}
      </Typography>
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        mt={0.5}
        color="custom.gray800"
      >
        {!timeRange
          ? t('transactionsPage.allDay')
          : `(${startTime?.format('HH:mm')} - ${endTime?.format('HH.mm')})`}
      </Typography>
    </Box>
  );
}
