import { Box, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';

type Unit = {
  id: number;
  title: string;
  values: { singular: string; plural: string };
};

const units: Unit[] = [
  {
    id: 0,
    title: 'Points',
    values: { singular: 'Point', plural: 'Points' },
  },
  {
    id: 1,
    title: 'Stars',
    values: { singular: 'Star', plural: 'Stars' },
  },
];

interface UnitsSelectProps {
  setShowCustomInputs: (value: boolean) => void;
}

export default function UnitsSelect({ setShowCustomInputs }: UnitsSelectProps) {
  const { setValue, watch } = useFormContext();

  const selectedUnit = watch('units');

  const handleUnitSelection = (unit: Unit) => {
    if (selectedUnit?.id === unit.id) {
      setValue('units', null);
    } else {
      setValue('units', unit);
      setShowCustomInputs(false);
    }
  };

  return (
    <Box>
      <Subsection
        title="Customize your program"
        titleSx={{
          fontSize: '32px',
          textShadow: '1px 5px 2px rgba(0, 0, 0, 0.17)',
        }}
        subtitle="Specify if you want customers to earn Points or Stars. Or choose a custom name."
      >
        {units.map(unit => (
          <Box
            key={unit.id}
            onClick={() => handleUnitSelection(unit)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '15px',
              border:
                selectedUnit?.id === unit.id ? 'white' : 'solid 1px #D9D9D9',
              color: selectedUnit?.id === unit.id ? 'white' : 'black',
              backgroundColor:
                selectedUnit?.id === unit.id ? 'primary.main' : '',
              borderRadius: '6px',
              px: 2.5,
              py: 2,
              cursor: 'pointer',
              ':hover': {
                bgcolor:
                  selectedUnit?.id === unit.id ? '' : 'primary.veryLight',
              },
            }}
          >
            <Typography variant="h6">{unit.title}</Typography>
          </Box>
        ))}
      </Subsection>
    </Box>
  );
}
