import { DateRange } from '@mui/x-date-pickers-pro';
import { Dayjs } from 'dayjs';
import { Database } from 'firebase/database';
import { DataProvider } from 'react-admin';

import { storage } from '~/configs/firebaseConfig';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReport } from '~/fake-provider/reports/getReport';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { subtractReports } from '~/fake-provider/reports/subtractReports';
import { getReportsDataFromRealtime } from '~/fake-provider/reports/utils/getReportsDataFromRealtime';
import { getReportsDataFromStorage } from '~/fake-provider/reports/utils/getReportsDataFromStorage';
import { isNumberPrimitive } from '~/fake-provider/reports/utils/isNumber';
import { RESOURCES } from '~/providers/resources';

type WinMentorMonetar = {
  anLucru: number;
  lunaLucru: number;
  monetare: {
    [dataLucru: string]: {
      nrDoc: string;
      simbolCarnet: string;
      numarBonuri: number;
      data: string;
      case: Record<string, string>;
      tipuriPlata: Record<string, number>;
      totalArticole: number;
      observatii: string;
      discount: number;
      tvaDiscount: number;
      articole: Array<{
        codExtern: string;
        unitateMasura: string;
        cantitate: number;
        pret: number;
        simbolGestiune: string;
        tva: number;
        discount: number;
      }>;
      detaliiDiscount: {
        [tva: string]: {
          tva: number;
          valoareCuTva: number;
          dinCareTva: number;
        };
      };
      facturi?: {
        totalFacturi: number;
        facturi: Array<{
          codExtern: string;
          numarFactura: string;
          serieFactura: string;
        }>;
      };
    };
  };
};

type WinMentorBonConsum = {
  anLucru: number;
  lunaLucru: number;
  bonuri: {
    [dataLucruSiGestConsumSiObservatii: string]: {
      nrDoc: string;
      data: string;
      gestConsum: string;
      observatii: string;
      articole: Array<{
        codExtern: string;
        unitateMasura: string;
        cantitate: number;
        pret: number;
        simbolGestiune: string;
      }>;
    };
  };
};

export async function getWinMentorExport(
  dataProvider: DataProvider,
  rtdbInstance: Database,
  accountId: string,
  sellPointId: string,
  dateRange: DateRange<Dayjs>
) {
  if (
    !dataProvider ||
    !rtdbInstance ||
    !accountId ||
    !sellPointId ||
    !dateRange[0] ||
    !dateRange[1]
  )
    throw new Error('Missing parameters');

  const startDate = dateRange[0].format('YYYY-MM-DD');
  // we add 1 second because end date is always 23:59:59 and we want it to be next day
  const endDate = dateRange[1].add(1, 'seconds').format('YYYY-MM-DD');

  const startDateYear = Number(dateRange[0].format('YYYY'));
  const startDateMonth = Number(dateRange[0].format('MM'));
  const endDateYear = Number(dateRange[1].format('YYYY'));
  const endDateMonth = Number(dateRange[1].format('MM'));

  if (startDateYear !== endDateYear || startDateMonth !== endDateMonth) {
    throw new Error('Cannot export for multiple months');
  }

  // get the winmentor integration configuration
  const winMentorRequest = await dataProvider.getOne(
    RESOURCES.MY_INTEGRATIONS,
    {
      id: 'winMentor',
    }
  );
  const winMentorData = winMentorRequest.data ?? {};
  const winMentorIntegrationIsActive = winMentorData.active ?? false;
  if (!winMentorIntegrationIsActive) {
    throw new Error('WinMentor integration is not active');
  }
  const winMentorMonetaryBookSymbol =
    winMentorData.sellPoints?.[sellPointId]?.monetaryBookSymbol ?? '?';
  const winMentorMonetaryNumberPrefix =
    winMentorData.sellPoints?.[sellPointId]?.monetaryNumberPrefix ?? '?';
  const winMentorCompCtStockSymbol =
    winMentorData.sellPoints?.[sellPointId]?.compCtStockSymbol ?? '';
  const winMentorPmsCtStockSymbol =
    winMentorData.sellPoints?.[sellPointId]?.pmsCtStockSymbol ?? '';
  const winMentorUsesEnterprise =
    winMentorData.sellPoints?.[sellPointId]?.usesWMEnterprise ?? false;
  const winMentorPaymentTypeCollectors =
    winMentorData.sellPoints?.[sellPointId]?.paymentTypeCollectors ?? {};
  const winMentorPrepStations =
    winMentorData.sellPoints?.[sellPointId]?.prepStations ?? {};
  const winMentorGiftCards = winMentorData.giftCards ?? {};
  const winMentorExtraCharges = winMentorData.extraCharges ?? {};
  const winMentorTips = winMentorData.tips ?? {};
  const winMentorItemsCodeField = winMentorData.itemsCodeField ?? 'sku';
  const winMentorModifiersCodeField = winMentorData.modifiersCodeField ?? 'sku';

  // get the sell point data
  const sellPointRequest = await dataProvider.getOne(RESOURCES.LOCATIONS, {
    id: sellPointId,
  });
  const sellPointData = sellPointRequest.data;

  const locale = sellPointData.localization || 'en-IE';
  const language = locale.split('-')[0];

  const salesReportType = 'sales';
  const salesRawReport = await getReport(
    accountId,
    sellPointId,
    salesReportType,
    startDate,
    endDate,
    locale,
    {
      storageBucket: storage,
      database: rtdbInstance,
      getReportFromStorageFn: getReportsDataFromStorage,
      getReportFromRealtimeFn: getReportsDataFromRealtime,
    }
  );
  const salesFilteredReport = filterReport(
    salesReportType,
    salesRawReport,
    [
      {
        field: 'reportType',
        operator: '==',
        value: salesReportType,
      },
    ],
    []
  );
  const salesGroupedReport = groupReport(
    salesReportType,
    salesFilteredReport,
    ['date'],
    []
  );

  let salesReportsExist = false;
  let pmsReportsExist = false;
  let compReportsExist = false;
  if (salesGroupedReport.length > 0) {
    // we are going to mark the existence of the reports (sales, comp and pms)
    // for the bills field includes the pmsBills so we need to loop through the salesGroupedReport
    // and if we find a report with pmsBills higher than 0 we mark the pmsReportsExist as true
    // and also subtract the pmsBills from the bills field
    salesGroupedReport.forEach(item => {
      if (
        item.report.length === 1 &&
        isNumberPrimitive(item.report[0].pmsBills) &&
        item.report[0].pmsBills > 0
      ) {
        pmsReportsExist = true;
        item.report[0].bills! -= item.report[0].pmsBills;
      }
    });
    // loop through the salesGroupedReport and check if there is at least one item with a bills value greater than 0
    salesReportsExist = salesGroupedReport.some(item => {
      return (
        item.report.length === 1 &&
        isNumberPrimitive(item.report[0].bills) &&
        item.report[0].bills > 0
      );
    });
    // loop through the salesGroupedReport and check if there is at least one item with a compValue or compedBills value greater than 0
    compReportsExist = salesGroupedReport.some(item => {
      return (
        item.report.length === 1 &&
        ((isNumberPrimitive(item.report[0].compValue) &&
          item.report[0].compValue > 0) ||
          (isNumberPrimitive(item.report[0].compedBills) &&
            item.report[0].compedBills > 0))
      );
    });
  }

  // check if the sales report has bills
  if (!salesReportsExist && !pmsReportsExist && !compReportsExist) {
    throw new Error('No data found!');
  }

  // get the measure units data
  const measureUnits = await dataProvider
    .getList(RESOURCES.MEASURE_UNITS, {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
    })
    .then(res => res.data)
    .catch(() => []);
  const measureUnitIdToSymbol = measureUnits.reduce(
    (acc: any, measureUnit: any) => {
      acc[measureUnit.id] = measureUnit.symbol[language];
      return acc;
    },
    {}
  );
  const defaultMeasureUnit =
    measureUnitIdToSymbol[sellPointData.defaultMeasureUnit] ?? 'buc';

  // get all items and create a map of id to name and code
  const itemIdToName: { [id: string]: string } = {};
  const itemIdToWinMentorCode: { [id: string]: string } = {};
  const allItems = await dataProvider
    .getList(RESOURCES.HOSPITALITY_ITEMS, {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
    })
    .then(res => res.data)
    .catch(() => []);
  for (const item of allItems) {
    itemIdToName[item.id] = item.name;
    itemIdToWinMentorCode[item.id] = item[winMentorItemsCodeField];
  }
  const modifierIdToName: { [id: string]: string } = {};
  const modifierIdToWinMentorCode: { [id: string]: string } = {};

  const itemsReportType = 'items';
  const modifiersReportType = 'modifiers';
  const giftCardsReportType = 'giftCards';
  const extraChargesReportType = 'extraCharges';
  const tipsReportType = 'tips';

  let itemsWithPmsItemsFilteredReport;
  let modifiersWithPmsModifiersFilteredReport;
  let giftCardsWithPmsGiftCardsFilteredReport;
  let extraChargesWithPmsExtraChargesFilteredReport;
  let tipsWithPmsTipsFilteredReport;

  let itemsGroupedReport;
  let modifiersGroupedReport;
  let giftCardsGroupedReport;
  let extraChargesGroupedReport;
  let tipsGroupedReport;
  if (salesReportsExist) {
    const itemsWithPmsItemsRawReport = await getReport(
      accountId,
      sellPointId,
      itemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    itemsWithPmsItemsFilteredReport = filterReport(
      itemsReportType,
      itemsWithPmsItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: itemsReportType,
        },
      ],
      []
    );

    const modifiersWithPmsModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      modifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    modifiersWithPmsModifiersFilteredReport = filterReport(
      modifiersReportType,
      modifiersWithPmsModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: modifiersReportType,
        },
      ],
      []
    );

    const giftCardsWithPmsGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      giftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    giftCardsWithPmsGiftCardsFilteredReport = filterReport(
      giftCardsReportType,
      giftCardsWithPmsGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: giftCardsReportType,
        },
      ],
      []
    );

    const extraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      extraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    extraChargesWithPmsExtraChargesFilteredReport = filterReport(
      extraChargesReportType,
      extraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: extraChargesReportType,
        },
      ],
      []
    );

    const tipsWithPmsTipsRawReport = await getReport(
      accountId,
      sellPointId,
      tipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    tipsWithPmsTipsFilteredReport = filterReport(
      tipsReportType,
      tipsWithPmsTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: tipsReportType,
        },
      ],
      []
    );
  }

  let pmsItemsGroupedReport;
  let pmsModifiersGroupedReport;
  let pmsGiftCardsGroupedReport;
  let pmsExtraChargesGroupedReport;
  let pmsTipsGroupedReport;
  if (!pmsReportsExist) {
    if (
      salesReportsExist &&
      itemsWithPmsItemsFilteredReport !== undefined &&
      modifiersWithPmsModifiersFilteredReport !== undefined &&
      giftCardsWithPmsGiftCardsFilteredReport !== undefined &&
      extraChargesWithPmsExtraChargesFilteredReport !== undefined &&
      tipsWithPmsTipsFilteredReport !== undefined
    ) {
      itemsGroupedReport = groupReport(
        itemsReportType,
        itemsWithPmsItemsFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );
      modifiersGroupedReport = groupReport(
        modifiersReportType,
        modifiersWithPmsModifiersFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );
      giftCardsGroupedReport = groupReport(
        giftCardsReportType,
        giftCardsWithPmsGiftCardsFilteredReport,
        ['date'],
        ['vat', 'type', 'price']
      );
      extraChargesGroupedReport = groupReport(
        extraChargesReportType,
        extraChargesWithPmsExtraChargesFilteredReport,
        ['date'],
        ['vat', 'name', 'price']
      );
      tipsGroupedReport = groupReport(
        tipsReportType,
        tipsWithPmsTipsFilteredReport,
        ['date'],
        ['vat']
      );
    }
  } else {
    const pmsItemsReportType = 'pmsItems';
    const pmsItemsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsItemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsItemsFilteredReport = filterReport(
      pmsItemsReportType,
      pmsItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsItemsReportType,
        },
      ],
      []
    );
    const pmsItemsBasicGroupedReport = groupReport(
      pmsItemsReportType,
      pmsItemsFilteredReport,
      ['date'],
      ['prepStation', 'id', 'measureUnit', 'price']
    );
    pmsItemsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      pmsItemsReportType,
      pmsItemsBasicGroupedReport,
      ['prepStation']
    );

    const pmsModifiersReportType = 'pmsModifiers';
    const pmsModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      pmsModifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsModifiersFilteredReport = filterReport(
      pmsModifiersReportType,
      pmsModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsModifiersReportType,
        },
      ],
      []
    );
    const pmsModifiersBasicGroupedReport = groupReport(
      pmsModifiersReportType,
      pmsModifiersFilteredReport,
      ['date'],
      ['prepStation', 'id', 'measureUnit', 'price']
    );
    pmsModifiersGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      pmsModifiersReportType,
      pmsModifiersBasicGroupedReport,
      ['prepStation']
    );

    const pmsGiftCardsReportType = 'pmsGiftCards';
    const pmsGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsGiftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsGiftCardsFilteredReport = filterReport(
      pmsGiftCardsReportType,
      pmsGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsGiftCardsReportType,
        },
      ],
      []
    );
    pmsGiftCardsGroupedReport = groupReport(
      pmsGiftCardsReportType,
      pmsGiftCardsFilteredReport,
      ['date'],
      ['type', 'price']
    );

    const pmsExtraChargesReportType = 'pmsExtraCharges';
    const pmsExtraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      pmsExtraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsExtraChargesFilteredReport = filterReport(
      pmsExtraChargesReportType,
      pmsExtraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsExtraChargesReportType,
        },
      ],
      []
    );
    pmsExtraChargesGroupedReport = groupReport(
      pmsExtraChargesReportType,
      pmsExtraChargesFilteredReport,
      ['date'],
      ['name', 'price']
    );

    const pmsTipsReportType = 'pmsTips';
    const pmsTipsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsTipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsTipsFilteredReport = filterReport(
      pmsTipsReportType,
      pmsTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsTipsReportType,
        },
      ],
      []
    );
    pmsTipsGroupedReport = groupReport(
      pmsTipsReportType,
      pmsTipsFilteredReport,
      ['date'],
      []
    );

    if (
      salesReportsExist &&
      itemsWithPmsItemsFilteredReport !== undefined &&
      modifiersWithPmsModifiersFilteredReport !== undefined &&
      giftCardsWithPmsGiftCardsFilteredReport !== undefined &&
      extraChargesWithPmsExtraChargesFilteredReport !== undefined &&
      tipsWithPmsTipsFilteredReport !== undefined
    ) {
      const itemsWithoutPmsItemsFilteredReport = subtractReports(
        itemsWithPmsItemsFilteredReport,
        pmsItemsFilteredReport,
        itemsReportType
      );
      itemsGroupedReport = groupReport(
        itemsReportType,
        itemsWithoutPmsItemsFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );

      const modifiersWithoutPmsModifiersFilteredReport = subtractReports(
        modifiersWithPmsModifiersFilteredReport,
        pmsModifiersFilteredReport,
        modifiersReportType
      );
      modifiersGroupedReport = groupReport(
        modifiersReportType,
        modifiersWithoutPmsModifiersFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );

      const giftCardsWithoutPmsGiftCardsFilteredReport = subtractReports(
        giftCardsWithPmsGiftCardsFilteredReport,
        pmsGiftCardsFilteredReport,
        giftCardsReportType
      );
      giftCardsGroupedReport = groupReport(
        giftCardsReportType,
        giftCardsWithoutPmsGiftCardsFilteredReport,
        ['date'],
        ['vat', 'type', 'price']
      );

      const extraChargesWithoutPmsExtraChargesFilteredReport = subtractReports(
        extraChargesWithPmsExtraChargesFilteredReport,
        pmsExtraChargesFilteredReport,
        extraChargesReportType
      );
      extraChargesGroupedReport = groupReport(
        extraChargesReportType,
        extraChargesWithoutPmsExtraChargesFilteredReport,
        ['date'],
        ['vat', 'name', 'price']
      );

      const tipsWithoutPmsTipsFilteredReport = subtractReports(
        tipsWithPmsTipsFilteredReport,
        pmsTipsFilteredReport,
        tipsReportType
      );
      tipsGroupedReport = groupReport(
        tipsReportType,
        tipsWithoutPmsTipsFilteredReport,
        ['date'],
        ['vat']
      );
    }
  }

  let compedItemsGroupedReport;
  let compedModifiersGroupedReport;
  let compedGiftCardsGroupedReport;
  let compedExtraChargesGroupedReport;
  let compedTipsGroupedReport;
  if (compReportsExist) {
    const compedItemsReportType = 'compedItems';
    const compedItemsRawReport = await getReport(
      accountId,
      sellPointId,
      compedItemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedItemsFilteredReport = filterReport(
      compedItemsReportType,
      compedItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedItemsReportType,
        },
      ],
      []
    );
    const compedItemsBasicGroupedReport = groupReport(
      compedItemsReportType,
      compedItemsFilteredReport,
      ['date'],
      ['reason', 'prepStation', 'id', 'measureUnit', 'price']
    );
    compedItemsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      compedItemsReportType,
      compedItemsBasicGroupedReport,
      ['reason', 'prepStation']
    );

    const compedModifiersReportType = 'compedModifiers';
    const compedModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      compedModifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedModifiersFilteredReport = filterReport(
      compedModifiersReportType,
      compedModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedModifiersReportType,
        },
      ],
      []
    );
    const compedModifiersBasicGroupedReport = groupReport(
      compedModifiersReportType,
      compedModifiersFilteredReport,
      ['date'],
      ['reason', 'prepStation', 'id', 'measureUnit', 'price']
    );
    compedModifiersGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedModifiersReportType,
        compedModifiersBasicGroupedReport,
        ['reason', 'prepStation']
      );

    const compedGiftCardsReportType = 'compedGiftCards';
    const compedGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      compedGiftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedGiftCardsFilteredReport = filterReport(
      compedGiftCardsReportType,
      compedGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedGiftCardsReportType,
        },
      ],
      []
    );
    const compedGiftCardsBasicGroupedReport = groupReport(
      compedGiftCardsReportType,
      compedGiftCardsFilteredReport,
      ['date'],
      ['reason', 'type', 'price']
    );
    compedGiftCardsGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedGiftCardsReportType,
        compedGiftCardsBasicGroupedReport,
        ['reason']
      );

    const compedExtraChargesReportType = 'compedExtraCharges';
    const compedExtraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      compedExtraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedExtraChargesFilteredReport = filterReport(
      compedExtraChargesReportType,
      compedExtraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedExtraChargesReportType,
        },
      ],
      []
    );
    const compedExtraChargesBasicGroupedReport = groupReport(
      compedExtraChargesReportType,
      compedExtraChargesFilteredReport,
      ['date'],
      ['reason', 'name', 'price']
    );
    compedExtraChargesGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedExtraChargesReportType,
        compedExtraChargesBasicGroupedReport,
        ['reason']
      );

    const compedTipsReportType = 'compedTips';
    const compedTipsRawReport = await getReport(
      accountId,
      sellPointId,
      compedTipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedTipsFilteredReport = filterReport(
      compedTipsReportType,
      compedTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedTipsReportType,
        },
      ],
      []
    );
    const compedTipsBasicGroupedReport = groupReport(
      compedTipsReportType,
      compedTipsFilteredReport,
      ['date'],
      ['reason']
    );
    compedTipsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      compedTipsReportType,
      compedTipsBasicGroupedReport,
      ['reason']
    );
  }

  // build the WinMentorMonetar object
  const wmMonetar: WinMentorMonetar = {
    anLucru: startDateYear,
    lunaLucru: startDateMonth,
    monetare: {},
  };
  const wmMonetarOutput: Array<string> = [];
  if (
    salesReportsExist &&
    itemsGroupedReport !== undefined &&
    modifiersGroupedReport !== undefined &&
    giftCardsGroupedReport !== undefined &&
    extraChargesGroupedReport !== undefined &&
    tipsGroupedReport !== undefined
  ) {
    // add the sales report
    for (const item of salesGroupedReport) {
      if (
        item.report.length === 1 &&
        typeof item.report[0].bills === 'number' &&
        item.report[0].bills > 0
      ) {
        const dataLucru = item.date;
        const dataLucruDate = new Date(dataLucru);
        wmMonetar.monetare[dataLucru] = {
          nrDoc: `${winMentorMonetaryNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
          simbolCarnet: winMentorMonetaryBookSymbol,
          numarBonuri: item.report[0].bills ?? 1,
          data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
          case: {},
          tipuriPlata: {},
          totalArticole: 0,
          observatii: '',
          discount: 0,
          tvaDiscount: 0,
          articole: [],
          detaliiDiscount: {},
        };
        if (winMentorUsesEnterprise) {
          wmMonetar.monetare[dataLucru].case = {
            CasaCash: winMentorPaymentTypeCollectors['Cash']?.name
              ? winMentorPaymentTypeCollectors['Cash'].name
              : '???_Casa Numerar',
            CasaCard: winMentorPaymentTypeCollectors['Card']?.name
              ? winMentorPaymentTypeCollectors['Card'].name
              : '',
            CasaCec: winMentorPaymentTypeCollectors['Cec']?.name
              ? winMentorPaymentTypeCollectors['Cec'].name
              : '',
            CasaBonValoric: winMentorPaymentTypeCollectors['BonValoric']?.name
              ? winMentorPaymentTypeCollectors['BonValoric'].name
              : '',
          };
          wmMonetar.monetare[dataLucru].tipuriPlata = {
            CEC: 0,
            CARD: 0,
            BonValoric: 0,
          };
          if (item.report[0].payments) {
            for (const paymentType of Object.keys(item.report[0].payments)) {
              if (paymentType !== 'cash') {
                const paymentValue = Number(
                  (item.report[0].payments[paymentType] / 10000).toFixed(2)
                );
                let paymentSymbol = `???_${paymentType}`;
                let paymentCasaName = `???_Casa ${paymentType}`;
                let paymentCasaFieldName = `Casa???_${paymentType}`;
                for (const casaFieldName of Object.keys(
                  winMentorPaymentTypeCollectors
                )) {
                  if (
                    Array.isArray(
                      winMentorPaymentTypeCollectors[casaFieldName].paymentTypes
                    ) &&
                    winMentorPaymentTypeCollectors[
                      casaFieldName
                    ].paymentTypes.includes(paymentType)
                  ) {
                    if (
                      winMentorPaymentTypeCollectors[casaFieldName].symbol &&
                      winMentorPaymentTypeCollectors[casaFieldName].name
                    ) {
                      paymentSymbol =
                        winMentorPaymentTypeCollectors[casaFieldName].symbol;
                      paymentCasaName =
                        winMentorPaymentTypeCollectors[casaFieldName].name;
                      paymentCasaFieldName = casaFieldName;
                      break;
                    }
                  }
                }
                if (wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol]) {
                  wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol] +=
                    paymentValue;
                } else {
                  wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol] =
                    paymentValue;
                }
                wmMonetar.monetare[dataLucru].case[paymentCasaFieldName] =
                  paymentCasaName;
              }
            }
          }
        } else {
          wmMonetar.monetare[dataLucru].case['Casa'] =
            winMentorPaymentTypeCollectors['Cash']?.name
              ? winMentorPaymentTypeCollectors['Cash'].name
              : '???_Casa Numerar';
          wmMonetar.monetare[dataLucru].tipuriPlata = {
            CEC: 0,
            CARD: 0,
            BONVALORIC: 0,
          };
          if (item.report[0].payments) {
            for (const paymentType of Object.keys(item.report[0].payments)) {
              if (paymentType !== 'cash') {
                const paymentValue = Number(
                  (item.report[0].payments[paymentType] / 10000).toFixed(2)
                );
                let paymentSymbol = `???_${paymentType}`;
                for (const casaFieldName of Object.keys(
                  winMentorPaymentTypeCollectors
                )) {
                  if (
                    Array.isArray(
                      winMentorPaymentTypeCollectors[casaFieldName].paymentTypes
                    ) &&
                    winMentorPaymentTypeCollectors[
                      casaFieldName
                    ].paymentTypes.includes(paymentType)
                  ) {
                    if (winMentorPaymentTypeCollectors[casaFieldName].symbol) {
                      paymentSymbol =
                        winMentorPaymentTypeCollectors[casaFieldName].symbol;
                      break;
                    }
                  }
                }
                if (wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol]) {
                  wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol] +=
                    paymentValue;
                } else {
                  wmMonetar.monetare[dataLucru].tipuriPlata[paymentSymbol] =
                    paymentValue;
                }
              }
            }
          }
        }
      }
    }
    // add the items report
    for (const item of itemsGroupedReport) {
      const dataLucru = item.date;
      if (wmMonetar.monetare[dataLucru]) {
        for (const article of item.report) {
          wmMonetar.monetare[dataLucru].articole.push({
            codExtern: itemIdToWinMentorCode[article.id]
              ? itemIdToWinMentorCode[article.id]
              : itemIdToName[article.id]
                ? `???_${itemIdToName[article.id]}`
                : `???_${article.id}`,
            unitateMasura:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            simbolGestiune:
              winMentorPrepStations[article.prepStation] ??
              `???_${article.prepStation}`,
            tva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
          const tva = `TVA_${article.vat}`;
          if (!wmMonetar.monetare[dataLucru].detaliiDiscount[tva]) {
            wmMonetar.monetare[dataLucru].detaliiDiscount[tva] = {
              tva: article.vat,
              valoareCuTva: 0,
              dinCareTva: 0,
            };
          }
          wmMonetar.monetare[dataLucru].detaliiDiscount[tva].valoareCuTva +=
            (article.discountsValue ?? 0) +
            (article.couponsValue ?? 0) +
            (article.promotionsValue ?? 0);
        }
      }
    }
    // add the modifiers report
    for (const item of modifiersGroupedReport) {
      const dataLucru = item.date;
      if (wmMonetar.monetare[dataLucru]) {
        for (const article of item.report) {
          wmMonetar.monetare[dataLucru].articole.push({
            codExtern: modifierIdToWinMentorCode[article.id]
              ? modifierIdToWinMentorCode[article.id]
              : modifierIdToName[article.id]
                ? `???_${modifierIdToName[article.id]}`
                : `???_${article.id}`,
            unitateMasura:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            simbolGestiune:
              winMentorPrepStations[article.prepStation] ??
              `???_${article.prepStation}`,
            tva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
          const tva = `TVA_${article.vat}`;
          if (!wmMonetar.monetare[dataLucru].detaliiDiscount[tva]) {
            wmMonetar.monetare[dataLucru].detaliiDiscount[tva] = {
              tva: article.vat,
              valoareCuTva: 0,
              dinCareTva: 0,
            };
          }
          wmMonetar.monetare[dataLucru].detaliiDiscount[tva].valoareCuTva +=
            (article.discountsValue ?? 0) +
            (article.couponsValue ?? 0) +
            (article.promotionsValue ?? 0);
        }
      }
    }
    // add the gift cards report
    for (const item of giftCardsGroupedReport) {
      const dataLucru = item.date;
      if (wmMonetar.monetare[dataLucru]) {
        for (const article of item.report) {
          wmMonetar.monetare[dataLucru].articole.push({
            codExtern: winMentorGiftCards[article.type]?.code
              ? winMentorGiftCards[article.type].code
              : article.type == '@digital'
                ? '???_GiftCardDigital'
                : '???_GiftCardFizic',
            unitateMasura: defaultMeasureUnit,
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            simbolGestiune: winMentorGiftCards[article.type]?.symbol
              ? winMentorGiftCards[article.type].symbol
              : article.type == '@digital'
                ? '???_GiftCardDigital'
                : '???_GiftCardFizic',
            tva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
          const tva = `TVA_${article.vat}`;
          if (!wmMonetar.monetare[dataLucru].detaliiDiscount[tva]) {
            wmMonetar.monetare[dataLucru].detaliiDiscount[tva] = {
              tva: article.vat,
              valoareCuTva: 0,
              dinCareTva: 0,
            };
          }
          wmMonetar.monetare[dataLucru].detaliiDiscount[tva].valoareCuTva +=
            (article.discountsValue ?? 0) +
            (article.couponsValue ?? 0) +
            (article.promotionsValue ?? 0);
        }
      }
    }
    // add the extra charges report
    for (const item of extraChargesGroupedReport) {
      const dataLucru = item.date;
      if (wmMonetar.monetare[dataLucru]) {
        for (const article of item.report) {
          wmMonetar.monetare[dataLucru].articole.push({
            codExtern: winMentorExtraCharges[article.name]?.code
              ? winMentorExtraCharges[article.name].code
              : `???_${article.name}`,
            unitateMasura: defaultMeasureUnit,
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            simbolGestiune: winMentorExtraCharges[article.name]?.symbol
              ? winMentorExtraCharges[article.name].symbol
              : `???_${article.name}`,
            tva: article.vat,
            discount: Number(
              ((article.promotionsValue ?? 0) / 10000).toFixed(2)
            ),
          });
          const tva = `TVA_${article.vat}`;
          if (!wmMonetar.monetare[dataLucru].detaliiDiscount[tva]) {
            wmMonetar.monetare[dataLucru].detaliiDiscount[tva] = {
              tva: article.vat,
              valoareCuTva: 0,
              dinCareTva: 0,
            };
          }
          wmMonetar.monetare[dataLucru].detaliiDiscount[tva].valoareCuTva +=
            article.promotionsValue ?? 0;
        }
      }
    }
    // add the tips report
    for (const item of tipsGroupedReport) {
      const dataLucru = item.date;
      if (wmMonetar.monetare[dataLucru]) {
        for (const article of item.report) {
          wmMonetar.monetare[dataLucru].articole.push({
            codExtern: winMentorTips.code ?? '???_Bacsis',
            unitateMasura: defaultMeasureUnit,
            cantitate: Number((1).toFixed(3)),
            pret: Number((article.value / 10000).toFixed(2)),
            simbolGestiune: winMentorTips.symbol ?? '???_Bacsis',
            tva: article.vat,
            discount: 0,
          });
          const tva = `TVA_${article.vat}`;
          if (!wmMonetar.monetare[dataLucru].detaliiDiscount[tva]) {
            wmMonetar.monetare[dataLucru].detaliiDiscount[tva] = {
              tva: article.vat,
              valoareCuTva: 0,
              dinCareTva: 0,
            };
          }
        }
      }
    }

    // now we are going to get totalArticole, discount, tvaDiscount and detaliiDiscount
    for (const dataLucru of Object.keys(wmMonetar.monetare)) {
      const monetar = wmMonetar.monetare[dataLucru];
      monetar.totalArticole = monetar.articole.length;
      for (const tvaKey of Object.keys(monetar.detaliiDiscount)) {
        const tvaDiscountDetalii = monetar.detaliiDiscount[tvaKey];
        const tva = tvaDiscountDetalii.tva;
        tvaDiscountDetalii.dinCareTva = Math.round(
          tvaDiscountDetalii.valoareCuTva / ((100 + tva) / 100)
        );
        tvaDiscountDetalii.dinCareTva = Number(
          (tvaDiscountDetalii.dinCareTva / 10000).toFixed(2)
        );
        tvaDiscountDetalii.valoareCuTva = Number(
          (tvaDiscountDetalii.valoareCuTva / 10000).toFixed(2)
        );
        monetar.discount += tvaDiscountDetalii.valoareCuTva;
        monetar.tvaDiscount += tvaDiscountDetalii.dinCareTva;
      }
    }

    // check if the wmMonetar.monetare is empty
    if (Object.keys(wmMonetar.monetare).length > 0) {
      // now we are going to process wmMonetar and output the needed format
      if (winMentorUsesEnterprise) {
        wmMonetarOutput.push('[InfoPachet]');
        wmMonetarOutput.push(`AnLucru=${wmMonetar.anLucru}`);
        wmMonetarOutput.push(`LunaLucru=${wmMonetar.lunaLucru}`);
        wmMonetarOutput.push('TipDocument=MONETAR');
        wmMonetarOutput.push(
          `TotalMonetare=${Object.keys(wmMonetar.monetare).length}`
        );
        let indexMonetar = 1;
        for (const dataLucru of Object.keys(wmMonetar.monetare)) {
          const monetar = wmMonetar.monetare[dataLucru];
          wmMonetarOutput.push(`[Monetar_${indexMonetar}]`);
          wmMonetarOutput.push(`NumarBonuri=${monetar.numarBonuri}`);
          wmMonetarOutput.push(`NrDoc=${monetar.nrDoc}`);
          wmMonetarOutput.push(`Data=${monetar.data}`);
          wmMonetarOutput.push(`Scadenta=${monetar.data}`);
          wmMonetarOutput.push('Majorari=');
          wmMonetarOutput.push(`Observatii=${monetar.observatii}`);
          wmMonetarOutput.push('Locatie=');
          for (const tipuriPlataFieldName of Object.keys(monetar.tipuriPlata)) {
            wmMonetarOutput.push(
              `${tipuriPlataFieldName}=${monetar.tipuriPlata[tipuriPlataFieldName]}`
            );
          }
          for (const caseFieldName of Object.keys(monetar.case)) {
            wmMonetarOutput.push(
              `${caseFieldName}=${monetar.case[caseFieldName]}`
            );
          }
          wmMonetarOutput.push(`TotalArticole=${monetar.totalArticole}`);
          wmMonetarOutput.push(`Discount=${monetar.discount.toFixed(2)}`);
          wmMonetarOutput.push(`TvaDiscount=${monetar.tvaDiscount.toFixed(2)}`);
          wmMonetarOutput.push(`[Monetar_${indexMonetar}_RepartizareDiscount]`);
          for (const tvaKey of Object.keys(monetar.detaliiDiscount)) {
            const tvaDiscountDetalii = monetar.detaliiDiscount[tvaKey];
            wmMonetarOutput.push(
              `${tvaDiscountDetalii.tva}=${tvaDiscountDetalii.valoareCuTva}`
            );
          }
          wmMonetarOutput.push(`[Items_${indexMonetar}]`);
          let indexArticol = 1;
          for (const articol of monetar.articole) {
            wmMonetarOutput.push(
              `Item_${indexArticol}=${articol.codExtern};${articol.unitateMasura};${articol.cantitate};${articol.pret};${articol.simbolGestiune};;;`
            );
            wmMonetarOutput.push(
              `Item_${indexArticol}_ValDiscount=${articol.discount ? `-${articol.discount}` : 0}`
            );
            const articolValoareCuTva =
              articol.cantitate * articol.pret - articol.discount;
            wmMonetarOutput.push(
              `Item_${indexArticol}_TVA=${articol.tva ? ((articolValoareCuTva * articol.tva) / (100 + articol.tva)).toFixed(4) : 0}`
            );
            indexArticol++;
          }
          indexMonetar++;
        }
      } else {
        wmMonetarOutput.push('[InfoPachet]');
        wmMonetarOutput.push(`AnLucru=${wmMonetar.anLucru}`);
        wmMonetarOutput.push(`LunaLucru=${wmMonetar.lunaLucru}`);
        wmMonetarOutput.push('TipDocument=MONETAR');
        wmMonetarOutput.push(
          `TotalMonetare=${Object.keys(wmMonetar.monetare).length}`
        );
        let indexMonetar = 1;
        for (const dataLucru of Object.keys(wmMonetar.monetare)) {
          const monetar = wmMonetar.monetare[dataLucru];
          wmMonetarOutput.push(`[Monetar_${indexMonetar}]`);
          wmMonetarOutput.push('Operat=D');
          wmMonetarOutput.push(`NrDoc=${monetar.nrDoc}`);
          wmMonetarOutput.push(`SimbolCarnet=${monetar.simbolCarnet}`);
          wmMonetarOutput.push('Operatie=A');
          wmMonetarOutput.push('CasaDeMarcat=D');
          wmMonetarOutput.push(`NumarBonuri=${monetar.numarBonuri}`);
          wmMonetarOutput.push(`Data=${monetar.data}`);
          for (const caseFieldName of Object.keys(monetar.case)) {
            wmMonetarOutput.push(
              `${caseFieldName}=${monetar.case[caseFieldName]}`
            );
          }
          wmMonetarOutput.push(`TotalArticole=${monetar.totalArticole}`);
          for (const tipuriPlataFieldName of Object.keys(monetar.tipuriPlata)) {
            wmMonetarOutput.push(
              `${tipuriPlataFieldName}=${monetar.tipuriPlata[tipuriPlataFieldName]}`
            );
          }
          wmMonetarOutput.push(`Observatii=${monetar.observatii}`);
          wmMonetarOutput.push(`Discount=${monetar.discount}`);
          wmMonetarOutput.push(`TvaDiscount=${monetar.tvaDiscount.toFixed(2)}`);
          wmMonetarOutput.push(`[Items_${indexMonetar}]`);
          let indexArticol = 1;
          for (const articol of monetar.articole) {
            wmMonetarOutput.push(
              `Item_${indexArticol}=${articol.codExtern};${articol.unitateMasura};${articol.cantitate};${articol.pret};${articol.simbolGestiune};`
            );
            indexArticol++;
          }
          wmMonetarOutput.push(`[MONETAR_${indexMonetar}_DetaliiDiscount]`);
          for (const tvaKey of Object.keys(monetar.detaliiDiscount)) {
            const tvaDiscountDetalii = monetar.detaliiDiscount[tvaKey];
            wmMonetarOutput.push(
              `${tvaKey}=${tvaDiscountDetalii.valoareCuTva};${tvaDiscountDetalii.dinCareTva}`
            );
          }
          indexMonetar++;
        }
      }
    }
  }
  // build the WinMentorBonConsum object
  const wmBonConsum: WinMentorBonConsum = {
    anLucru: startDateYear,
    lunaLucru: startDateMonth,
    bonuri: {},
  };
  const wmBonConsumOutput: Array<string> = [];
  if (
    compReportsExist &&
    compedItemsGroupedReport !== undefined &&
    compedModifiersGroupedReport !== undefined &&
    compedGiftCardsGroupedReport !== undefined &&
    compedExtraChargesGroupedReport !== undefined &&
    compedTipsGroupedReport !== undefined
  ) {
    // add the comped items report
    for (const pDate of compedItemsGroupedReport) {
      const dataLucru = pDate.date;
      const dataLucruDate = new Date(dataLucru!);
      for (const pReason of pDate.report) {
        const currentReason = pReason.groupedBy?.value;
        for (const pPrep of pReason.subReport!) {
          const currentPrep = pPrep.groupedBy?.value;
          // Use compCvStockSymbol if available, otherwise use the original logic
          const currentBonKey = winMentorCompCtStockSymbol
            ? `${dataLucru}-Comp-${winMentorCompCtStockSymbol}`
            : `${dataLucru}-${currentReason}-${currentPrep}`;
          const gestConsumValue = winMentorCompCtStockSymbol
            ? winMentorCompCtStockSymbol
            : (winMentorPrepStations[currentPrep!] ?? `???_${currentPrep!}`);
          const observatiiValue = winMentorCompCtStockSymbol
            ? 'Gratuitati'
            : currentReason!;

          if (!wmBonConsum.bonuri[currentBonKey]) {
            wmBonConsum.bonuri[currentBonKey] = {
              nrDoc: `${winMentorMonetaryNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
              data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
              gestConsum: gestConsumValue,
              observatii: observatiiValue,
              articole: [],
            };
          }
          for (const article of pPrep.subReport!) {
            wmBonConsum.bonuri[currentBonKey].articole.push({
              codExtern: itemIdToWinMentorCode[article.id!]
                ? itemIdToWinMentorCode[article.id!]
                : itemIdToName[article.id!]
                  ? `???_${itemIdToName[article.id!]}`
                  : `???_${article.id}`,
              unitateMasura:
                article.measureUnit === 'undefined'
                  ? defaultMeasureUnit
                  : (measureUnitIdToSymbol[article.measureUnit!] ??
                    defaultMeasureUnit),
              cantitate: Number((article.quantity / 1000).toFixed(3)),
              pret: Number((article.price! / 10000).toFixed(2)),
              simbolGestiune:
                winMentorPrepStations[article.prepStation!] ??
                `???_${article.prepStation}`,
            });
          }
        }
      }
    }
    // add the comped modifiers report
    for (const pDate of compedModifiersGroupedReport) {
      const dataLucru = pDate.date;
      const dataLucruDate = new Date(dataLucru!);
      for (const pReason of pDate.report) {
        const currentReason = pReason.groupedBy?.value;
        for (const pPrep of pReason.subReport!) {
          const currentPrep = pPrep.groupedBy?.value;
          // Use compCvStockSymbol if available, otherwise use the original logic
          const currentBonKey = winMentorCompCtStockSymbol
            ? `${dataLucru}-Comp-${winMentorCompCtStockSymbol}`
            : `${dataLucru}-${currentReason}-${currentPrep}`;
          const gestConsumValue = winMentorCompCtStockSymbol
            ? winMentorCompCtStockSymbol
            : (winMentorPrepStations[currentPrep!] ?? `???_${currentPrep!}`);
          const observatiiValue = winMentorCompCtStockSymbol
            ? 'Gratuitati'
            : currentReason!;

          if (!wmBonConsum.bonuri[currentBonKey]) {
            wmBonConsum.bonuri[currentBonKey] = {
              nrDoc: `${winMentorMonetaryNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
              data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
              gestConsum: gestConsumValue,
              observatii: observatiiValue,
              articole: [],
            };
          }
          for (const article of pPrep.subReport!) {
            wmBonConsum.bonuri[currentBonKey].articole.push({
              codExtern: modifierIdToWinMentorCode[article.id!]
                ? modifierIdToWinMentorCode[article.id!]
                : modifierIdToName[article.id!]
                  ? `???_${modifierIdToName[article.id!]}`
                  : `???_${article.id}`,
              unitateMasura:
                article.measureUnit === 'undefined'
                  ? defaultMeasureUnit
                  : (measureUnitIdToSymbol[article.measureUnit!] ??
                    defaultMeasureUnit),
              cantitate: Number((article.quantity / 1000).toFixed(3)),
              pret: Number((article.price! / 10000).toFixed(2)),
              simbolGestiune:
                winMentorPrepStations[article.prepStation!] ??
                `???_${article.prepStation}`,
            });
          }
        }
      }
    }
    // TODO! what to do with gift cards? extra charges? tips?
  }
  if (
    pmsReportsExist &&
    pmsItemsGroupedReport !== undefined &&
    pmsModifiersGroupedReport !== undefined &&
    pmsGiftCardsGroupedReport !== undefined &&
    pmsExtraChargesGroupedReport !== undefined &&
    pmsTipsGroupedReport !== undefined
  ) {
    // add the pms items report
    const currentReason = 'Cont Hotel';
    for (const pDate of pmsItemsGroupedReport) {
      const dataLucru = pDate.date;
      const dataLucruDate = new Date(dataLucru!);
      for (const pPrep of pDate.report) {
        const currentPrep = pPrep.groupedBy?.value;
        // Use pmsCtStockSymbol if available, otherwise use the original logic
        const currentBonKey = winMentorPmsCtStockSymbol
          ? `${dataLucru}-PMS-${winMentorPmsCtStockSymbol}`
          : `${dataLucru}-${currentReason}-${currentPrep}`;
        const gestConsumValue = winMentorPmsCtStockSymbol
          ? winMentorPmsCtStockSymbol
          : (winMentorPrepStations[currentPrep!] ?? `???_${currentPrep!}`);
        const observatiiValue = winMentorPmsCtStockSymbol
          ? 'Cont Hotel'
          : currentReason!;

        if (!wmBonConsum.bonuri[currentBonKey]) {
          wmBonConsum.bonuri[currentBonKey] = {
            nrDoc: `${winMentorMonetaryNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
            data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
            gestConsum: gestConsumValue,
            observatii: observatiiValue,
            articole: [],
          };
        }
        for (const article of pPrep.subReport!) {
          wmBonConsum.bonuri[currentBonKey].articole.push({
            codExtern: itemIdToWinMentorCode[article.id!]
              ? itemIdToWinMentorCode[article.id!]
              : itemIdToName[article.id!]
                ? `???_${itemIdToName[article.id!]}`
                : `???_${article.id}`,
            unitateMasura:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit!] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price! / 10000).toFixed(2)),
            simbolGestiune:
              winMentorPrepStations[article.prepStation!] ??
              `???_${article.prepStation}`,
          });
        }
      }
    }
    for (const pDate of pmsModifiersGroupedReport) {
      const dataLucru = pDate.date;
      const dataLucruDate = new Date(dataLucru!);
      for (const pPrep of pDate.report) {
        const currentPrep = pPrep.groupedBy?.value;
        // Use pmsCtStockSymbol if available, otherwise use the original logic
        const currentBonKey = winMentorPmsCtStockSymbol
          ? `${dataLucru}-PMS-${winMentorPmsCtStockSymbol}`
          : `${dataLucru}-${currentReason}-${currentPrep}`;
        const gestConsumValue = winMentorPmsCtStockSymbol
          ? winMentorPmsCtStockSymbol
          : (winMentorPrepStations[currentPrep!] ?? `???_${currentPrep!}`);
        const observatiiValue = winMentorPmsCtStockSymbol
          ? 'Cont Hotel'
          : currentReason!;

        if (!wmBonConsum.bonuri[currentBonKey]) {
          wmBonConsum.bonuri[currentBonKey] = {
            nrDoc: `${winMentorMonetaryNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
            data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
            gestConsum: gestConsumValue,
            observatii: observatiiValue,
            articole: [],
          };
        }
        for (const article of pPrep.subReport!) {
          wmBonConsum.bonuri[currentBonKey].articole.push({
            codExtern: modifierIdToWinMentorCode[article.id!]
              ? modifierIdToWinMentorCode[article.id!]
              : modifierIdToName[article.id!]
                ? `???_${modifierIdToName[article.id!]}`
                : `???_${article.id}`,
            unitateMasura:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit!] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price! / 10000).toFixed(2)),
            simbolGestiune:
              winMentorPrepStations[article.prepStation!] ??
              `???_${article.prepStation}`,
          });
        }
      }
    }
    // TODO! what to do with gift cards? extra charges? tips?
  }

  if (Object.keys(wmBonConsum.bonuri).length > 0) {
    // give unique number to bon consum but first we need to sort the bonuri by key
    const sortedBonuri = Object.keys(wmBonConsum.bonuri)
      .sort()
      .reduce(
        (obj, key) => {
          obj[key] = wmBonConsum.bonuri[key];
          return obj;
        },
        {} as (typeof wmBonConsum)['bonuri']
      );
    wmBonConsum.bonuri = sortedBonuri;
    let bonConsumNumber = 1;
    for (const bonConsumKey of Object.keys(wmBonConsum.bonuri)) {
      wmBonConsum.bonuri[bonConsumKey].nrDoc =
        `${wmBonConsum.bonuri[bonConsumKey].nrDoc}${bonConsumNumber.toString().padStart(2, '0')}`;
      bonConsumNumber++;
    }
    // create the winmentor format output
    wmBonConsumOutput.push('[InfoPachet]');
    wmBonConsumOutput.push(`AnLucru=${wmBonConsum.anLucru}`);
    wmBonConsumOutput.push(`LunaLucru=${wmBonConsum.lunaLucru}`);
    wmBonConsumOutput.push('TipDocument=BON DE CONSUM');
    wmBonConsumOutput.push(
      `TotalBonuri=${Object.keys(wmBonConsum.bonuri).length}`
    );
    let indexBon = 1;
    for (const currentBonKey of Object.keys(wmBonConsum.bonuri)) {
      const bon = wmBonConsum.bonuri[currentBonKey];
      wmBonConsumOutput.push(`[Bon_${indexBon}]`);
      wmBonConsumOutput.push(`NrDoc=${bon.nrDoc}`);
      wmBonConsumOutput.push(`Data=${bon.data}`);
      wmBonConsumOutput.push(`GestConsum=${bon.gestConsum}`);
      wmBonConsumOutput.push(`Observatii=${bon.observatii}`);
      wmBonConsumOutput.push(`TotalArticole=${bon.articole.length}`);
      wmBonConsumOutput.push(`[Items_${indexBon}]`);
      let indexArticol = 1;
      for (const articol of bon.articole) {
        wmBonConsumOutput.push(
          `Item_${indexArticol}=${articol.codExtern};${articol.unitateMasura};${articol.cantitate};${articol.pret};${articol.simbolGestiune};`
        );
        indexArticol++;
      }
      indexBon++;
    }
  }

  return {
    monetar: wmMonetarOutput,
    bonConsum: wmBonConsumOutput,
  };
}
