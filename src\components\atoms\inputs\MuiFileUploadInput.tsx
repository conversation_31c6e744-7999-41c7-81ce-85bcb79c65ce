import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  horizontalListSortingStrategy,
  rectSortingStrategy,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Box,
  Chip,
  CircularProgress,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { useNotify } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { FilePreviewImage } from '~/components/atoms/FilePreviewImage';
import { FilePreviewModal } from '~/components/atoms/FilePreviewModal';
import { FilePreviewThumbnail } from '~/components/atoms/FilePreviewThumbnail';
import { ImageEditorModal } from '~/components/molecules/ImageEditorModal';
import { STANDARD_IMAGE_SIZES } from '~/configs/imageSize';
import { useFirebase } from '~/contexts/FirebaseContext';
import { useFileSize } from '~/hooks/useFileSize';
import { useTempFileCleanup } from '~/hooks/useTempFileCleanup';
import {
  DEFAULT_FILE_UPLOAD_CONFIG,
  ImageEditorConfig,
  PrivateFileContext,
  UploadedFile,
  validateFileTypeConfiguration,
} from '~/types/fileUpload';
import { formatFileSize } from '~/utils/fileSizeUtils';
import { fileUploadManager } from '~/utils/FileUploadManager';
import {
  canPreviewFile,
  ensureFilesFlags,
  filterValidFiles,
  isImageFile,
  isVideoFile,
  mapFileTypeToCode,
} from '~/utils/fileUtils';
import {
  getAspectRatioGroups,
  getCropSessionCount,
  hasMatchingAspectRatio,
  isMultiAspectRatioConfig,
  shouldEditImage,
  shouldEditPublicImage,
} from '~/utils/imageEditorUtils';
import {
  getBestVariantForContext,
  getThumbnailUrl,
  hasImageVariants,
} from '~/utils/imageVariants';
import { resolveImageEditorConfig } from '~/utils/standardImageEditor';

// Sortable File Item Component for @dnd-kit
interface SortableFileItemProps {
  file: UploadedFile;
  index: number;
  disabled: boolean;
  readOnly: boolean;
  uploading: boolean;
  multiple: boolean;
  files: UploadedFile[];
  theme: any;
  handleFilePreview: (file: UploadedFile) => void;
  handleRemoveFile: (index: number) => void;
  formatFileSize: (size: number) => string;
  privateFileContext?: PrivateFileContext;
}

// File size display component with runtime loading
interface FileSizeDisplayProps {
  file: UploadedFile;
  formatFileSize: (size: number) => string;
  privateFileContext?: PrivateFileContext;
}

function FileSizeDisplay({
  file,
  formatFileSize,
  privateFileContext,
}: FileSizeDisplayProps) {
  const { formattedSize, loading } = useFileSize(file, {
    formatted: true,
    precision: 1,
    context: privateFileContext,
  });

  // Handle legacy files that might still have stored size field
  const legacyFile = file as UploadedFile & { s?: number };
  const displaySize =
    formattedSize || (legacyFile.s ? formatFileSize(legacyFile.s * 1024) : '');

  return (
    <>
      {displaySize || (loading ? 'Loading...' : '')} •{' '}
      {file.e || 'Unknown type'}
    </>
  );
}

function SortableFileItem({
  file,
  index,
  disabled,
  readOnly,
  uploading,
  multiple,
  files,
  theme,
  handleFilePreview,
  handleRemoveFile,
  formatFileSize,
  privateFileContext,
}: SortableFileItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `${file.rn || file.fn}-${index}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Paper
      ref={setNodeRef}
      style={style}
      variant="outlined"
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: isDragging ? 0.8 : 1,
        backgroundColor:
          disabled || readOnly
            ? theme.palette.action.hover
            : isDragging
              ? theme.palette.action.hover
              : 'transparent',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          flex: 1,
          minWidth: 0,
        }}
      >
        {!disabled && !readOnly && multiple && files.length > 1 && (
          <Box
            {...attributes}
            {...listeners}
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
              color: theme.palette.text.secondary,
              '&:active': {
                cursor: 'grabbing',
              },
            }}
          >
            <DragIndicatorIcon fontSize="small" />
          </Box>
        )}

        {(isImageFile(file) || isVideoFile(file)) && (
          <FilePreviewThumbnail
            file={file}
            size={40}
            onClick={() => !disabled && handleFilePreview(file)}
            disabled={disabled}
            theme={theme}
          />
        )}

        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {file.rn}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            <FileSizeDisplay
              file={file}
              formatFileSize={formatFileSize}
              privateFileContext={privateFileContext}
            />
            {uploading && file.x && ' • Uploading...'}
          </Typography>
        </Box>
      </Box>

      {/* Delete button - Hidden in disabled and readOnly modes */}
      {!disabled && !readOnly && (
        <IconButton
          size="small"
          onClick={() => handleRemoveFile(index)}
          disabled={uploading}
          color="error"
          sx={{ ml: 1 }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </Paper>
  );
}

// Sortable File Chip Component for compact variant
interface SortableFileChipProps {
  file: UploadedFile;
  index: number;
  disabled: boolean;
  readOnly: boolean;
  uploading: boolean;
  multiple: boolean;
  files: UploadedFile[];
  theme: any;
  handleFilePreview: (file: UploadedFile) => void;
  handleRemoveFile: (index: number) => void;
}

function SortableFileChip({
  file,
  index,
  disabled,
  readOnly,
  uploading,
  multiple,
  files,
  theme,
  handleFilePreview,
  handleRemoveFile,
}: SortableFileChipProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `${file.rn || file.fn}-${index}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleChipClick = useCallback(() => {
    if (canPreviewFile(file) && !disabled) {
      handleFilePreview(file);
    }
  }, [file, disabled, handleFilePreview]);

  const handleChipDelete = useCallback(() => {
    if (!disabled && !readOnly) {
      handleRemoveFile(index);
    }
  }, [disabled, readOnly, handleRemoveFile, index]);

  const fileName = file.rn || file.fn;
  const extension = file.e ? `.${file.e}` : '';

  // Function to truncate from middle if needed (for dropdown context)
  const getTruncatedLabel = (name: string, ext: string) => {
    const fullName = `${name}${ext}`;
    // If the full name is short enough, return as is
    if (fullName.length <= 25) {
      // Slightly longer for dropdown
      return fullName;
    }

    // For longer names, show first part + ... + extension
    const maxStartLength = Math.max(10, 25 - ext.length - 3); // 3 for "..."
    const truncatedName =
      name.length > maxStartLength
        ? `${name.substring(0, maxStartLength)}...${ext}`
        : fullName;

    return truncatedName;
  };

  const displayLabel = getTruncatedLabel(fileName, extension);
  const fullName = `${fileName}${extension}`;

  return (
    <Chip
      ref={setNodeRef}
      style={style}
      label={displayLabel}
      title={fullName} // Show full name on hover
      icon={
        !disabled && !readOnly && multiple && files.length > 1 ? (
          <Box
            {...attributes}
            {...listeners}
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'grab',
              '&:active': {
                cursor: 'grabbing',
              },
            }}
          >
            <DragIndicatorIcon fontSize="small" />
          </Box>
        ) : undefined
      }
      onDelete={!disabled && !readOnly ? handleChipDelete : undefined}
      onClick={canPreviewFile(file) ? handleChipClick : undefined}
      size="small"
      variant="outlined"
      sx={{
        opacity: isDragging ? 0.8 : 1,
        cursor: canPreviewFile(file) && !disabled ? 'pointer' : 'default',
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        width: '100%',
        justifyContent: 'flex-start',
        height: '36px',
        '& .MuiChip-icon': {
          cursor: 'grab',
          marginLeft: '4px',
          '&:active': {
            cursor: 'grabbing',
          },
        },
        '& .MuiChip-label': {
          fontWeight: 500,
          fontSize: '0.875rem',
          textAlign: 'left',
          paddingLeft: '8px',
          paddingRight: '8px',
          flex: 1,
          justifyContent: 'flex-start',
        },
        '&:hover': !disabled
          ? {
              backgroundColor: 'action.hover',
              borderColor: 'primary.main',
            }
          : {},
      }}
    />
  );
}

interface ValidationError {
  id: string;
  message: string;
  severity: 'error' | 'warning';
  fileName?: string;
}

interface MuiFileUploadInputProps {
  value: UploadedFile[];
  onChange: (files: UploadedFile[]) => void;
  variant?: 'default' | 'compact';
  label?: string;
  placeholder?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[]; // MIME types
  helperText?: string;
  infoText?: string; // New prop for info icon tooltip/content
  disabled?: boolean;
  readOnly?: boolean;
  fileType?: 'images' | 'videos' | 'public' | 'private';

  // Private file context (required for private files)
  privateFileContext?: PrivateFileContext;

  // Enhanced validation props
  showValidationErrors?: boolean; // Default: true - deprecated, validation errors now always shown via notifications
  validationDisplay?: 'inline' | 'toast' | 'both'; // Default: 'both' - deprecated, now always uses notifications
  allowInvalidFiles?: boolean; // Default: false

  // Simple validation rules (optional)
  minFileCount?: number;
  minFileSize?: number; // in bytes
  requiredFileTypes?: string[]; // MIME types that are required (stricter than acceptedTypes)

  // Custom validation function
  customValidation?: (file: File) => string | null; // return error message or null if valid

  // Error state management
  error?: boolean;
  errorText?: string;

  // Image editor props (optional)
  enableImageEditor?: boolean;
  imageEditorConfig?: ImageEditorConfig;
  onImageEdit?: (originalFile: File, editedFiles: File[]) => void; // Updated to handle multiple files
  onImageEditCancel?: (file: File) => void;
  onImageEditStart?: (file: File) => void;

  // Existing callbacks (maintain backward compatibility)
  onValidationError?: (message: string) => void;
  onUploadSuccess?: (count: number) => void;
  onUploadError?: (error: any) => void;
  onFileUploaded?: (file: UploadedFile) => void;
  onFileMoved?: (file: UploadedFile) => void;
  onFileDeleted?: (file: UploadedFile) => void;
  /**
   * Callback to handle when files are updated (useful for refreshing form data
   * after files are moved from temporary to permanent storage)
   */
  onFilesUpdated?: (files: UploadedFile[]) => void;
}

/**
 * Generate dynamic helper text based on file upload configuration
 */
const generateHelperText = (
  fileType: 'images' | 'videos' | 'public' | 'private',
  acceptedTypes: string[],
  enableImageEditor: boolean,
  imageEditorConfig: ImageEditorConfig | undefined,
  providedHelperText: string | undefined,
  t: (key: string, options?: any) => string,
  multiple: boolean,
  maxFiles: number,
  maxSize: number
): string => {
  // If helperText prop is provided, use it instead of generating one
  if (providedHelperText) return providedHelperText;

  // Determine supported file types
  const hasImages = acceptedTypes.some(type => type.startsWith('image/'));
  const hasVideos = acceptedTypes.some(type => type.startsWith('video/'));

  // Determine if single or multiple files
  const isSingle = !multiple || maxFiles === 1;

  // Build the base upload text
  let baseText = '';
  if (hasImages && hasVideos) {
    baseText = isSingle
      ? t('menu.helperText.uploadSingleImageOrVideo')
      : t('menu.helperText.uploadMultipleImagesOrVideos', { count: maxFiles });
  } else if (hasImages) {
    baseText = isSingle
      ? t('menu.helperText.uploadSingleImage')
      : t('menu.helperText.uploadMultipleImages', { count: maxFiles });
  } else if (hasVideos) {
    baseText = isSingle
      ? t('menu.helperText.uploadSingleVideo')
      : t('menu.helperText.uploadMultipleVideos', { count: maxFiles });
  } else {
    baseText = isSingle
      ? t('menu.helperText.uploadSingleFile')
      : t('menu.helperText.uploadMultipleFiles', { count: maxFiles });
  }

  // Add file size information
  const formattedSize = formatFileSize(maxSize);
  const sizeText = isSingle
    ? t('menu.helperText.maxSize', { size: formattedSize })
    : t('menu.helperText.maxSize', { size: formattedSize });

  // Build supported formats text
  const formatMap: { [key: string]: string } = {
    'image/jpeg': 'JPEG',
    'image/png': 'PNG',
    'image/webp': 'WebP',
    'video/mp4': 'MP4',
    'video/webm': 'WebM',
    'video/quicktime': 'MOV',
  };

  const supportedFormats = acceptedTypes
    .map(type => formatMap[type] || type.split('/')[1]?.toUpperCase())
    .filter(Boolean)
    .join(', ');

  const formatsText = supportedFormats
    ? t('menu.helperText.supportedFormats', { formats: supportedFormats })
    : '';

  // Add context-specific enhancement information
  let enhancementText = '';
  if (enableImageEditor && hasImages) {
    enhancementText = t('menu.helperText.withImageEditor');
  } else if (hasVideos) {
    enhancementText = t('menu.helperText.withVideoPreview');
  }

  // Combine all parts with appropriate separators
  const parts = [baseText, sizeText, formatsText, enhancementText].filter(
    Boolean
  );
  return parts.join('. ') + '.';
};

export default function MuiFileUploadInput({
  value = [],
  onChange,
  variant = 'default',
  label,
  placeholder,
  multiple = DEFAULT_FILE_UPLOAD_CONFIG.multiple,
  maxFiles = DEFAULT_FILE_UPLOAD_CONFIG.maxFiles,
  maxSize = DEFAULT_FILE_UPLOAD_CONFIG.maxSize,
  acceptedTypes = DEFAULT_FILE_UPLOAD_CONFIG.acceptedTypes,
  helperText,
  infoText,
  disabled = false,
  readOnly = false,
  fileType = 'images',

  // Private file context
  privateFileContext,

  // Enhanced validation props with defaults (deprecated props kept for backward compatibility)
  showValidationErrors = true, // deprecated
  validationDisplay = 'both', // deprecated
  allowInvalidFiles = false,

  // Simple validation rules (optional)
  minFileCount,
  minFileSize,
  requiredFileTypes,

  // Custom validation function
  customValidation,

  // Error state management
  error = false,
  errorText,

  // Image editor props (optional)
  enableImageEditor = false,
  imageEditorConfig,
  onImageEdit,
  onImageEditCancel,
  onImageEditStart,

  // Existing callbacks
  onValidationError,
  onUploadSuccess,
  onUploadError,
  onFileUploaded,
  onFileMoved,
  onFileDeleted,
  onFilesUpdated,
}: MuiFileUploadInputProps) {
  const theme = useTheme();
  const { details } = useFirebase();
  const notify = useNotify();

  // Cleanup temp files on component unmount to prevent memory leaks
  useTempFileCleanup();

  const [uploading, setUploading] = useState<boolean>(false);
  const [dragOver, setDragOver] = useState<boolean>(false);
  const [previewFile, setPreviewFile] = useState<UploadedFile | null>(null);
  const [inputKey, setInputKey] = useState<number>(0);
  const [uniqueId] = useState<string>(
    () => `file-input-${Math.random().toString(36).substr(2, 9)}`
  );

  // Validation state
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>(
    []
  );

  // State for expandable file list in compact variant
  const [isFileListExpanded, setIsFileListExpanded] = useState<boolean>(false);

  // State for info tooltip visibility (for mobile)
  const [isInfoTooltipOpen, setIsInfoTooltipOpen] = useState<boolean>(false);

  // Image editor state
  const [editingFile, setEditingFile] = useState<File | null>(null);
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);
  const [currentEditIndex, setCurrentEditIndex] = useState<number>(0);
  const [isImageEditorOpen, setIsImageEditorOpen] = useState<boolean>(false);

  // Multi-aspect-ratio cropping state
  const [currentAspectRatioGroup, setCurrentAspectRatioGroup] =
    useState<number>(0);
  const [aspectRatioGroups, setAspectRatioGroups] = useState<any[]>([]);
  const [allCroppedFiles, setAllCroppedFiles] = useState<File[]>([]);

  // Ensure value is always an array for consistency and has proper flags
  const files: UploadedFile[] = Array.isArray(value)
    ? ensureFilesFlags(value)
    : value
      ? ensureFilesFlags([value])
      : [];

  // Validate fileType configuration on component mount and when props change
  useEffect(() => {
    const validation = validateFileTypeConfiguration(fileType, acceptedTypes);
    if (!validation.isValid) {
      // Show warning to developer in development mode
      if (import.meta.env.VITE_NODE_ENV === 'dev') {
        notify(
          `File upload configuration issue: ${validation.errors.join(', ')}`,
          {
            type: 'warning',
          }
        );
      }
    }

    // Validate private file context
    if (fileType === 'private' && !privateFileContext?.accountId) {
      if (import.meta.env.VITE_NODE_ENV === 'dev') {
        notify(
          'Private file context with accountId is required for private files',
          { type: 'warning' }
        );
      }
    }
  }, [fileType, acceptedTypes, privateFileContext, notify]);

  // Validation functions
  const validateFileSize = useCallback(
    (file: File): ValidationError | null => {
      if (minFileSize && file.size < minFileSize) {
        return {
          id: `${file.name}-minSize`,
          message: `File too small: ${file.name} (min ${formatFileSize(minFileSize)})`,
          severity: 'error',
          fileName: file.name,
        };
      }
      if (maxSize && file.size > maxSize) {
        return {
          id: `${file.name}-maxSize`,
          message: `File too large: ${file.name} (max ${formatFileSize(maxSize)})`,
          severity: 'error',
          fileName: file.name,
        };
      }
      return null;
    },
    [minFileSize, maxSize]
  );

  const validateFileType = useCallback(
    (file: File): ValidationError | null => {
      // Check required file types first (stricter)
      if (requiredFileTypes && requiredFileTypes.length > 0) {
        if (!requiredFileTypes.includes(file.type)) {
          return {
            id: `${file.name}-requiredType`,
            message: `Invalid file type: ${file.name}. Required types: ${requiredFileTypes.join(', ')}`,
            severity: 'error',
            fileName: file.name,
          };
        }
      }
      // Check accepted types
      else if (acceptedTypes && acceptedTypes.length > 0) {
        if (!acceptedTypes.includes(file.type)) {
          return {
            id: `${file.name}-acceptedType`,
            message: `Invalid file type: ${file.name}. Accepted types: ${acceptedTypes.join(', ')}`,
            severity: 'error',
            fileName: file.name,
          };
        }
      }
      return null;
    },
    [requiredFileTypes, acceptedTypes]
  );

  const validateFileCount = useCallback(
    (newFiles: File[]): ValidationError | null => {
      const totalFiles = files.length + newFiles.length;

      if (minFileCount && totalFiles < minFileCount) {
        return {
          id: 'fileCount-min',
          message: `Minimum ${minFileCount} files required (currently ${totalFiles})`,
          severity: 'warning',
        };
      }

      if (maxFiles && totalFiles > maxFiles) {
        return {
          id: 'fileCount-max',
          message: `Maximum ${maxFiles} files allowed (trying to add ${totalFiles})`,
          severity: 'error',
        };
      }

      if (!multiple && newFiles.length > 1) {
        return {
          id: 'fileCount-single',
          message: 'Only one file allowed',
          severity: 'error',
        };
      }

      return null;
    },
    [files.length, minFileCount, maxFiles, multiple]
  );

  const validateCustomRules = useCallback(
    (file: File): ValidationError | null => {
      if (customValidation) {
        const errorMessage = customValidation(file);
        if (errorMessage) {
          return {
            id: `${file.name}-custom`,
            message: errorMessage,
            severity: 'error',
            fileName: file.name,
          };
        }
      }
      return null;
    },
    [customValidation]
  );

  const validateFiles = useCallback(
    (newFiles: File[]): ValidationError[] => {
      const errors: ValidationError[] = [];

      // Validate file count
      const countError = validateFileCount(newFiles);
      if (countError) errors.push(countError);

      // Validate individual files
      newFiles.forEach(file => {
        const sizeError = validateFileSize(file);
        if (sizeError) errors.push(sizeError);

        const typeError = validateFileType(file);
        if (typeError) errors.push(typeError);

        const customError = validateCustomRules(file);
        if (customError) errors.push(customError);
      });

      return errors;
    },
    [validateFileCount, validateFileSize, validateFileType, validateCustomRules]
  );

  const handleValidationDisplay = useCallback(
    (errors: ValidationError[]) => {
      const errorMessages = errors.filter(e => e.severity === 'error');
      const warningMessages = errors.filter(e => e.severity === 'warning');

      // Show errors as notifications
      if (errorMessages.length > 0) {
        notify(errorMessages[0].message, { type: 'error' });
      } else if (warningMessages.length > 0) {
        notify(warningMessages[0].message, { type: 'warning' });
      }

      // Maintain backward compatibility with onValidationError callback
      if (onValidationError) {
        if (errorMessages.length > 0) {
          onValidationError(errorMessages[0].message);
        } else if (warningMessages.length > 0) {
          onValidationError(warningMessages[0].message);
        }
      }
    },
    [notify, onValidationError]
  );

  // Notify parent when files are updated (useful for refreshing after temp->permanent moves)
  useEffect(() => {
    onFilesUpdated?.(files);
  }, [files, onFilesUpdated]);

  // Close expanded file list when files count becomes 1 or 0
  useEffect(() => {
    if (files.length <= 1) {
      setIsFileListExpanded(false);
    }
  }, [files.length]);

  // Set up sensors for @dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle file preview
  const handleFilePreview = useCallback((file: UploadedFile) => {
    if (canPreviewFile(file)) {
      setPreviewFile(file);
    }
  }, []);

  const handleClosePreview = useCallback(() => {
    setPreviewFile(null);
  }, []);

  // Handle keyboard events for modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && previewFile) {
        handleClosePreview();
      }
      if (event.key === 'Escape' && isFileListExpanded) {
        setIsFileListExpanded(false);
      }
      if (event.key === 'Escape' && isInfoTooltipOpen) {
        setIsInfoTooltipOpen(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (isFileListExpanded) {
        const target = event.target as Element;
        if (!target.closest('[data-file-dropdown]')) {
          setIsFileListExpanded(false);
        }
      }
      // Close info tooltip when clicking outside
      if (isInfoTooltipOpen) {
        const target = event.target as Element;
        if (!target.closest('[data-info-tooltip]')) {
          setIsInfoTooltipOpen(false);
        }
      }
    };

    const handleCloseAllTooltips = (event: CustomEvent) => {
      // Close this tooltip if it's not the one that initiated the close
      if (event.detail?.excludeId !== uniqueId && isInfoTooltipOpen) {
        setIsInfoTooltipOpen(false);
      }
    };

    if (previewFile || isFileListExpanded || isInfoTooltipOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Always listen for the custom event to close tooltips
    document.addEventListener(
      'closeAllTooltips',
      handleCloseAllTooltips as EventListener
    );

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener(
        'closeAllTooltips',
        handleCloseAllTooltips as EventListener
      );
    };
  }, [
    previewFile,
    handleClosePreview,
    isFileListExpanded,
    isInfoTooltipOpen,
    uniqueId,
  ]);

  // Helper function to upload files
  const uploadFiles = useCallback(
    async (filesToUpload: File[]) => {
      if (filesToUpload.length === 0) return [];

      setUploading(true);

      try {
        // Check if this is public images that should use client-side processing
        const isPublicImages = fileType === 'images';
        const shouldProcessVariants =
          isPublicImages && imageEditorConfig?.targetSizes?.length;

        if (isPublicImages) {
          // Always use client-side processing for public images to ensure thumbnail generation
          const targetSizes = imageEditorConfig?.targetSizes || [];
          const standardSizes = targetSizes.map((ts: any) => ({
            key: ts.name || `${ts.width}x${ts.height}`,
            name: ts.name || `${ts.width}×${ts.height}`,
            width: ts.width,
            height: ts.height,
            aspectRatio: ts.width / ts.height,
            description: `Generated variant ${ts.width}×${ts.height}`,
          }));

          const uploadPromises = filesToUpload.map(async (file, index) => {
            const onProgress = (progress: number) => {
              // Progress tracking can be added here if needed
            };

            return fileUploadManager.uploadPublicImageWithVariants(
              file,
              standardSizes,
              0.9, // quality
              onProgress
            );
          });

          const uploadedFiles = await Promise.all(uploadPromises);

          // Call lifecycle callback for each uploaded file
          uploadedFiles.forEach((file: UploadedFile) => {
            onFileUploaded?.(file);
          });

          if (multiple) {
            onChange([...files, ...uploadedFiles]);
          } else {
            onChange(uploadedFiles);
          }

          onUploadSuccess?.(uploadedFiles.length);

          // Clear validation errors on successful upload
          setValidationErrors([]);

          return uploadedFiles;
        } else {
          // Use traditional upload for non-public images or images without variants
          const uploadPromises = filesToUpload.map(file =>
            fileUploadManager.uploadToTemp(file, mapFileTypeToCode(fileType))
          );

          const uploadedFiles = await Promise.all(uploadPromises);

          // Call lifecycle callback for each uploaded file
          uploadedFiles.forEach((file: UploadedFile) => {
            onFileUploaded?.(file);
          });

          if (multiple) {
            onChange([...files, ...uploadedFiles]);
          } else {
            onChange(uploadedFiles);
          }

          onUploadSuccess?.(uploadedFiles.length);

          // Clear validation errors on successful upload
          setValidationErrors([]);

          return uploadedFiles;
        }
      } catch (error) {
        // Show user-friendly error notification
        let errorMessage = 'An error occurred during file upload';

        if (error instanceof Error) {
          if (
            error.message.includes('FileUploadManager not properly initialized')
          ) {
            errorMessage =
              'Upload service is not ready. Please refresh the page and try again.';
          } else {
            errorMessage = error.message;
          }
        }

        notify(errorMessage, { type: 'error' });

        onUploadError?.(error);
        return [];
      } finally {
        setUploading(false);
      }
    },
    [
      files,
      multiple,
      onChange,
      onFileUploaded,
      onUploadSuccess,
      onUploadError,
      fileType,
      imageEditorConfig,
    ]
  );

  // Helper function to upload an original image with pre-cropped variants
  const uploadImageWithCroppedVariants = useCallback(
    async (originalFile: File, croppedFiles: File[]) => {
      if (!originalFile) return;

      setUploading(true);

      try {
        // Create a single upload using the new method in FileUploadManager
        const uploadedFile =
          await fileUploadManager.uploadImageWithPreCroppedVariants(
            originalFile,
            croppedFiles,
            0.9, // quality
            (progress: number) => {
              // Note: Progress is logged but could be used for UI progress bars in the future
            }
          );

        // Call lifecycle callback for the uploaded file
        onFileUploaded?.(uploadedFile);

        // Add only the single file record to the list (representing original + all variants)
        if (multiple) {
          onChange([...files, uploadedFile]);
        } else {
          onChange([uploadedFile]);
        }

        onUploadSuccess?.(1); // Only one file record created

        // Clear validation errors on successful upload
        setValidationErrors([]);
      } catch (error) {
        // Show user-friendly error notification
        let errorMessage = 'An error occurred during file upload';

        if (error instanceof Error) {
          if (
            error.message.includes('FileUploadManager not properly initialized')
          ) {
            errorMessage =
              'Upload service is not ready. Please refresh the page and try again.';
          } else {
            errorMessage = error.message;
          }
        }

        notify(errorMessage, { type: 'error' });

        onUploadError?.(error);
      } finally {
        setUploading(false);
      }
    },
    [files, multiple, onChange, onFileUploaded, onUploadSuccess, onUploadError]
  );

  // Helper function to calculate enhanced batch information for multi-file, multi-aspect-ratio workflow
  const getEnhancedBatchInfo = useCallback(() => {
    if (!editingFile || !aspectRatioGroups || aspectRatioGroups.length === 0) {
      // Fallback to simple batch info
      return pendingFiles.length > 1
        ? {
            current: currentEditIndex + 1,
            total: pendingFiles.length,
          }
        : undefined;
    }

    const totalFiles = pendingFiles.length;
    const totalAspectRatios = aspectRatioGroups.length;
    const fileIndex = currentEditIndex + 1;
    const aspectRatioIndex = currentAspectRatioGroup + 1;
    const aspectRatioName =
      aspectRatioGroups[currentAspectRatioGroup]?.name ||
      `Aspect Ratio ${aspectRatioIndex}`;

    // Calculate overall progress position
    const overallCurrent =
      currentEditIndex * totalAspectRatios + aspectRatioIndex;
    const overallTotal = totalFiles * totalAspectRatios;

    return {
      current: overallCurrent,
      total: overallTotal,
      fileIndex,
      totalFiles,
      aspectRatioIndex,
      totalAspectRatios,
      aspectRatioName,
    };
  }, [
    editingFile,
    aspectRatioGroups,
    pendingFiles.length,
    currentEditIndex,
    currentAspectRatioGroup,
  ]);

  // Handle image editor save
  const handleImageEditorSave = useCallback(
    async (editedFiles: File[]) => {
      if (!editingFile || editedFiles.length === 0) return;

      // The image editor now includes the original file as the first item
      // Separate the original file from the cropped variants
      const [originalFile, ...croppedVariants] = editedFiles;

      // Verify the original file matches our editing file
      if (originalFile.name !== editingFile.name) {
        // File mismatch detected - continuing with processing
      }

      // Add only the cropped variants to accumulated files (not the original)
      setAllCroppedFiles(prev => [...prev, ...croppedVariants]);

      // Check if we're doing multi-aspect-ratio cropping
      const isMultiAspectRatio = isMultiAspectRatioConfig(
        imageEditorConfig || {},
        fileType
      );

      if (isMultiAspectRatio) {
        const groups = getAspectRatioGroups(imageEditorConfig || {}, fileType);
        const nextGroupIndex = currentAspectRatioGroup + 1;

        if (nextGroupIndex < groups.length) {
          // Move to next aspect ratio group for the same image
          setCurrentAspectRatioGroup(nextGroupIndex);
          return; // Keep editor open for next edit
        } else {
          // Finished all aspect ratios for current image
          const allFilesForThisImage = [...allCroppedFiles, ...croppedVariants];

          // Call the onImageEdit callback with all files from all aspect ratios
          onImageEdit?.(editingFile, allFilesForThisImage);

          // Upload the original file and all cropped variants
          // Use a special upload function that handles the folder structure properly
          await uploadImageWithCroppedVariants(
            editingFile, // Use the original editing file, not the one from editor
            allFilesForThisImage
          );

          // Reset aspect ratio state
          setCurrentAspectRatioGroup(0);
          setAllCroppedFiles([]);
        }
      } else {
        // Single aspect ratio - process normally
        onImageEdit?.(editingFile, croppedVariants);

        // Upload the original file and the cropped variants
        await uploadImageWithCroppedVariants(editingFile, croppedVariants);
      }

      // Move to next image or close editor
      const nextIndex = currentEditIndex + 1;
      if (nextIndex < pendingFiles.length) {
        setCurrentEditIndex(nextIndex);
        setEditingFile(pendingFiles[nextIndex]);

        // Reset aspect ratio state for the new file
        const isMultiAspectRatio = isMultiAspectRatioConfig(
          imageEditorConfig || {},
          fileType
        );
        if (isMultiAspectRatio) {
          setCurrentAspectRatioGroup(0);
          setAllCroppedFiles([]);
        }

        onImageEditStart?.(pendingFiles[nextIndex]);
      } else {
        // All images processed
        setIsImageEditorOpen(false);
        setEditingFile(null);
        setPendingFiles([]);
        setCurrentEditIndex(0);
      }
    },
    [
      editingFile,
      currentEditIndex,
      pendingFiles,
      currentAspectRatioGroup,
      allCroppedFiles,
      imageEditorConfig,
      onImageEdit,
      onImageEditStart,
      uploadFiles,
    ]
  );

  // Handle image editor cancel
  const handleImageEditorCancel = useCallback(() => {
    if (editingFile) {
      onImageEditCancel?.(editingFile);
    }

    // Reset multi-aspect-ratio state for current file
    setCurrentAspectRatioGroup(0);
    setAspectRatioGroups([]);
    setAllCroppedFiles([]);

    // Move to next image or close editor
    const nextIndex = currentEditIndex + 1;
    if (nextIndex < pendingFiles.length) {
      setCurrentEditIndex(nextIndex);
      setEditingFile(pendingFiles[nextIndex]);
      onImageEditStart?.(pendingFiles[nextIndex]);
    } else {
      // All images processed (or skipped)
      setIsImageEditorOpen(false);
      setEditingFile(null);
      setPendingFiles([]);
      setCurrentEditIndex(0);
    }
  }, [
    editingFile,
    currentEditIndex,
    pendingFiles,
    onImageEditCancel,
    onImageEditStart,
  ]);

  // Handle image editor close
  const handleImageEditorClose = useCallback(() => {
    // Cancel the entire editing process
    setIsImageEditorOpen(false);
    setEditingFile(null);
    setPendingFiles([]);
    setCurrentEditIndex(0);
  }, []);

  const handleFileSelect = useCallback(
    async (selectedFiles: FileList | null) => {
      if (!selectedFiles || !details.selectedAccount) return;

      const fileArray = Array.from(selectedFiles);

      // Run validation
      const errors = validateFiles(fileArray);
      setValidationErrors(errors);

      // Handle validation display
      handleValidationDisplay(errors);

      // Check if we should proceed with upload
      const hasErrors = errors.some(e => e.severity === 'error');
      if (!allowInvalidFiles && hasErrors) {
        return; // Stop here if not allowing invalid files
      }

      // Filter valid files if allowInvalidFiles is false, or use all files if true
      const validFiles = allowInvalidFiles
        ? fileArray
        : fileArray.filter(file => {
            // Check if this specific file has any errors
            const fileErrors = errors.filter(
              e => e.fileName === file.name && e.severity === 'error'
            );
            return fileErrors.length === 0;
          });

      if (validFiles.length === 0) return;

      // For public images, we need async aspect ratio checking
      const imagesToEdit: File[] = [];
      const filesToUploadDirectly: File[] = [];

      if (fileType === 'public' || fileType === 'images') {
        // Use async checking for public images
        const checkPromises = validFiles.map(async file => {
          if (!file.type.startsWith('image/')) {
            filesToUploadDirectly.push(file);
            return;
          }

          if (!enableImageEditor) {
            filesToUploadDirectly.push(file);
            return;
          }

          // Import the async function
          const needsEditing = await shouldEditPublicImage(
            file,
            fileType,
            imageEditorConfig
          );

          if (needsEditing) {
            imagesToEdit.push(file);
          } else {
            filesToUploadDirectly.push(file);
          }
        });

        await Promise.all(checkPromises);
      } else {
        // Use synchronous checking for other file types
        validFiles.forEach(file => {
          if (
            shouldEditImage(
              file,
              enableImageEditor,
              imageEditorConfig,
              fileType
            )
          ) {
            imagesToEdit.push(file);
          } else {
            filesToUploadDirectly.push(file);
          }
        });
      }

      // If we have images to edit, start the editing process
      if (imagesToEdit.length > 0) {
        // Initialize aspect ratio groups for multi-aspect-ratio workflow
        const isMultiAspectRatio = isMultiAspectRatioConfig(
          imageEditorConfig || {},
          fileType
        );
        if (isMultiAspectRatio) {
          const groups = getAspectRatioGroups(
            imageEditorConfig || {},
            fileType
          );
          setAspectRatioGroups(groups);
          setCurrentAspectRatioGroup(0);
          setAllCroppedFiles([]);
        }

        setPendingFiles(imagesToEdit);
        setCurrentEditIndex(0);
        setEditingFile(imagesToEdit[0]);
        setIsImageEditorOpen(true);
        onImageEditStart?.(imagesToEdit[0]);
      }

      // Upload files that don't need editing immediately
      if (filesToUploadDirectly.length > 0) {
        await uploadFiles(filesToUploadDirectly);
      }
    },
    [
      files,
      details.selectedAccount,
      validateFiles,
      handleValidationDisplay,
      allowInvalidFiles,
      enableImageEditor,
      imageEditorConfig,
      onImageEditStart,
    ]
  );

  const handleRemoveFile = useCallback(
    (index: number) => {
      const fileToRemove = files[index];

      if (multiple) {
        const newFiles = files.filter((_, i) => i !== index);
        onChange(newFiles);

        // Close expanded list if only one file or no files remain
        if (newFiles.length <= 1) {
          setIsFileListExpanded(false);
        }
      } else {
        onChange([]);
      }

      // Call lifecycle callback for deleted file
      if (fileToRemove) {
        onFileDeleted?.(fileToRemove);
      }
    },
    [files, multiple, onChange, onFileDeleted]
  );

  const handleFileReorder = useCallback(
    (fromIndex: number, toIndex: number) => {
      if (!multiple || fromIndex === toIndex) return;

      const newFiles = arrayMove(files, fromIndex, toIndex);
      onChange(newFiles);
    },
    [files, multiple, onChange]
  );

  const handleDragEnd = useCallback(
    (event: any) => {
      const { active, over } = event;

      if (active.id !== over?.id) {
        // Extract the index from the ID (format: "filename-index")
        const getIndexFromId = (id: string) => {
          const match = id.match(/-(\d+)$/);
          return match ? parseInt(match[1], 10) : -1;
        };

        const oldIndex = getIndexFromId(active.id);
        const newIndex = getIndexFromId(over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          handleFileReorder(oldIndex, newIndex);
        }
      }
    },
    [handleFileReorder]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragOver(false);

      if (disabled) return;

      handleFileSelect(e.dataTransfer.files);
    },
    [disabled, handleFileSelect]
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleFileSelect(e.target.files);
      // Reset input by changing the key to force re-render
      setInputKey(prev => prev + 1);
    },
    [handleFileSelect]
  );

  // Handle toggle of file list expansion
  const handleToggleFileList = useCallback(() => {
    setIsFileListExpanded(prev => !prev);
  }, []);

  // Handle info tooltip toggle for mobile
  const handleInfoTooltipToggle = useCallback(() => {
    setIsInfoTooltipOpen(prev => {
      const newState = !prev;

      // If opening this tooltip, close any other tooltips by dispatching a custom event
      if (newState) {
        const event = new CustomEvent('closeAllTooltips', {
          detail: { excludeId: uniqueId },
        });
        document.dispatchEvent(event);
      }

      return newState;
    });
  }, [uniqueId]);

  const { t } = useTranslation();

  // Generate dynamic helper text if not provided
  const computedHelperText = useMemo(() => {
    return generateHelperText(
      fileType,
      acceptedTypes,
      enableImageEditor,
      imageEditorConfig,
      helperText,
      t,
      multiple,
      maxFiles,
      maxSize
    );
  }, [
    fileType,
    acceptedTypes,
    enableImageEditor,
    imageEditorConfig,
    helperText,
    t,
    multiple,
    maxFiles,
    maxSize,
  ]);

  // Render compact variant
  if (variant === 'compact') {
    return (
      <Box sx={{ width: '100%', position: 'relative' }}>
        <TextField
          fullWidth
          variant="outlined"
          label={label}
          data-file-dropdown
          placeholder={
            files.length === 0
              ? placeholder ||
                (multiple ? t('menu.selectFiles') : t('menu.selectFile'))
              : undefined
          }
          value=""
          slotProps={{
            input: {
              readOnly: true,
              startAdornment:
                files.length > 0 ? (
                  <InputAdornment
                    position="start"
                    sx={{
                      maxWidth: 'calc(100% - 60px)',
                      width: 'calc(100% - 60px)',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        overflow: 'hidden',
                        minHeight: 36,
                        py: 0.5,
                      }}
                    >
                      {files.length === 1 ? (
                        // Single file: Show file chip (regardless of multiple setting)
                        files.map((file, index) => {
                          const fileName = file.rn || file.fn;
                          const extension = file.e ? `.${file.e}` : '';

                          // Function to truncate from middle if needed
                          const getTruncatedLabel = (
                            name: string,
                            ext: string
                          ) => {
                            const fullName = `${name}${ext}`;
                            // If the full name is short enough, return as is
                            if (fullName.length <= 20) {
                              return fullName;
                            }

                            // For longer names, show first part + ... + extension
                            const maxStartLength = Math.max(
                              8,
                              20 - ext.length - 3
                            ); // 3 for "..."
                            const truncatedName =
                              name.length > maxStartLength
                                ? `${name.substring(0, maxStartLength)}...${ext}`
                                : fullName;

                            return truncatedName;
                          };

                          const displayLabel = getTruncatedLabel(
                            fileName,
                            extension
                          );
                          const fullName = `${fileName}${extension}`;

                          return (
                            <Chip
                              key={`${file.fn || file.rn}-${index}`}
                              label={displayLabel}
                              title={fullName} // Show full name on hover
                              onDelete={
                                !disabled && !readOnly
                                  ? () => handleRemoveFile(index)
                                  : undefined
                              }
                              onClick={
                                canPreviewFile(file) && !disabled
                                  ? () => handleFilePreview(file)
                                  : undefined
                              }
                              size="small"
                              variant="outlined"
                              sx={{
                                cursor:
                                  canPreviewFile(file) && !disabled
                                    ? 'pointer'
                                    : 'default',
                                backgroundColor: 'background.paper',
                                border: '1px solid',
                                borderColor: 'divider',
                                maxWidth: '100%',
                                height: '32px',
                                '& .MuiChip-label': {
                                  fontWeight: 500,
                                  fontSize: '0.875rem',
                                  textAlign: 'left',
                                  paddingLeft: '8px',
                                  paddingRight: '8px',
                                },
                                '&:hover': !disabled
                                  ? {
                                      backgroundColor: 'action.hover',
                                      borderColor: 'primary.main',
                                    }
                                  : {},
                              }}
                            />
                          );
                        })
                      ) : (
                        // Multiple files: Show summary chip with count
                        <Chip
                          label={`${files.length} file${files.length !== 1 ? 's' : ''} uploaded`}
                          onClick={
                            !disabled && files.length > 1
                              ? handleToggleFileList
                              : undefined
                          }
                          size="medium"
                          variant="outlined"
                          sx={{
                            cursor:
                              !disabled && files.length > 1
                                ? 'pointer'
                                : 'default',
                            minHeight: 32,
                            height: 32,
                            fontSize: '0.875rem',
                            backgroundColor: 'background.paper',
                            border: '1px solid',
                            borderColor: 'divider',
                            '& .MuiChip-label': {
                              fontWeight: 500,
                              paddingX: 1.5,
                              fontSize: '0.875rem',
                            },
                            '&:hover':
                              !disabled && files.length > 1
                                ? {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'primary.main',
                                  }
                                : {},
                          }}
                        />
                      )}
                    </Box>
                  </InputAdornment>
                ) : undefined,
              endAdornment: !readOnly ? (
                <InputAdornment position="end">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {/* Info icon - positioned near the browse files icon */}
                    {infoText && (
                      <Box sx={{ position: 'relative' }} data-info-tooltip>
                        <Tooltip
                          title={infoText}
                          placement="top"
                          arrow
                          open={isInfoTooltipOpen}
                          disableHoverListener={true}
                          disableFocusListener={true}
                          disableTouchListener={true}
                        >
                          <IconButton
                            size="small"
                            onClick={handleInfoTooltipToggle}
                            sx={{
                              color: 'text.secondary',
                              cursor: 'pointer',
                              padding: '4px',
                              '&:hover': {
                                color: 'primary.main',
                                backgroundColor: 'action.hover',
                              },
                            }}
                          >
                            <InfoOutlinedIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    )}

                    {/* File input and browse icon */}
                    <input
                      key={inputKey}
                      type="file"
                      multiple={multiple}
                      accept={acceptedTypes.join(',')}
                      onChange={handleFileInputChange}
                      disabled={disabled || uploading}
                      style={{ display: 'none' }}
                      id={`${uniqueId}-compact`}
                    />
                    <label
                      htmlFor={`${uniqueId}-compact`}
                      style={{
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      {uploading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <AttachFileIcon
                          fontSize="small"
                          sx={{
                            cursor:
                              disabled || uploading ? 'not-allowed' : 'pointer',
                            color: disabled
                              ? 'action.disabled'
                              : 'action.active',
                          }}
                        />
                      )}
                    </label>
                  </Box>
                </InputAdornment>
              ) : undefined,
            },
            inputLabel: {
              shrink: files.length > 0 || undefined,
            },
          }}
          disabled={disabled}
          error={error}
          helperText={computedHelperText}
          sx={{
            cursor: 'default',
            '& .MuiOutlinedInput-input': {
              cursor: 'default',
              display: 'none', // Hide the actual input since we're using chips
            },
            '& .MuiOutlinedInput-root': {
              cursor: 'default',
              paddingRight: '14px',
              paddingLeft: '14px',
              // Add focus styling when dropdown is open
              ...(isFileListExpanded && {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '2px',
                },
              }),
            },
            '& .MuiInputAdornment-positionStart': {
              maxHeight: 'none',
              alignItems: 'stretch',
            },
            '& .MuiInputAdornment-positionEnd': {
              position: 'absolute',
              right: '9px',
              top: '50%',
              transform: 'translateY(-50%)',
              pointerEvents: 'none',
              '& label': {
                pointerEvents: 'auto',
              },
              '& .MuiIconButton-root': {
                pointerEvents: 'auto',
              },
            },
            // Style the label when dropdown is open (like focused state)
            ...(isFileListExpanded && {
              '& .MuiInputLabel-root': {
                color: theme.palette.primary.main,
              },
            }),
          }}
        />

        {/* Expandable file list for multiple files - Overlay dropdown style */}
        {multiple && files.length > 1 && isFileListExpanded && (
          <Box
            data-file-dropdown
            sx={{
              position: 'absolute',
              top: 'calc(100% - 3px)', // Move up by 1px to overlap the input border
              left: 0,
              right: 0,
              zIndex: 1300, // Higher than most components
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5,
              maxHeight: '200px',
              overflowY: 'auto',
              overflowX: 'hidden',
              p: 1,
              border: `1px solid ${theme.palette.divider}`,
              borderTop: 'none', // Remove top border to connect with input
              borderRadius: '4px',
              backgroundColor: theme.palette.background.paper,
              boxShadow: theme.shadows[8], // Add elevation like a dropdown
              animation: 'fadeIn 0.2s ease-in-out',
              '@keyframes fadeIn': {
                from: {
                  opacity: 0,
                  transform: 'translateY(-10px)',
                },
                to: {
                  opacity: 1,
                  transform: 'translateY(0)',
                },
              },
            }}
          >
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={disabled || readOnly ? () => {} : handleDragEnd}
            >
              <SortableContext
                items={files.map(
                  (file, index) => `${file.rn || file.fn}-${index}`
                )}
                strategy={verticalListSortingStrategy}
              >
                {files.map((file, index) => {
                  const fileName = file.rn || file.fn;
                  const extension = file.e ? `.${file.e}` : '';

                  // Function to truncate from middle if needed (same as single file)
                  const getTruncatedLabel = (name: string, ext: string) => {
                    const fullName = `${name}${ext}`;
                    // If the full name is short enough, return as is
                    if (fullName.length <= 25) {
                      // Slightly longer for dropdown
                      return fullName;
                    }

                    // For longer names, show first part + ... + extension
                    const maxStartLength = Math.max(10, 25 - ext.length - 3); // 3 for "..."
                    const truncatedName =
                      name.length > maxStartLength
                        ? `${name.substring(0, maxStartLength)}...${ext}`
                        : fullName;

                    return truncatedName;
                  };

                  const displayLabel = getTruncatedLabel(fileName, extension);
                  const fullName = `${fileName}${extension}`;

                  return (
                    <SortableFileChip
                      key={`${file.rn || file.fn}-${index}`}
                      file={file}
                      index={index}
                      disabled={disabled}
                      readOnly={readOnly}
                      uploading={uploading}
                      multiple={multiple}
                      files={files}
                      theme={theme}
                      handleFilePreview={handleFilePreview}
                      handleRemoveFile={handleRemoveFile}
                    />
                  );
                })}
              </SortableContext>
            </DndContext>
          </Box>
        )}

        {/* Show external error text */}
        {errorText && (
          <Typography
            variant="caption"
            color="error"
            sx={{ mt: '3px', ml: '14px', mr: '14px', display: 'block' }}
          >
            {errorText}
          </Typography>
        )}

        {/* File Preview Modal */}
        <FilePreviewModal file={previewFile} onClose={handleClosePreview} />

        {/* Image Editor Modal */}
        <ImageEditorModal
          file={editingFile}
          isOpen={isImageEditorOpen}
          onClose={handleImageEditorClose}
          onSave={handleImageEditorSave}
          onCancel={handleImageEditorCancel}
          config={imageEditorConfig || {}}
          fileType={fileType}
          uploading={uploading}
          batchInfo={getEnhancedBatchInfo()}
          aspectRatioInfo={
            aspectRatioGroups && aspectRatioGroups.length > 1
              ? {
                  currentGroup: currentAspectRatioGroup + 1,
                  totalGroups: aspectRatioGroups.length,
                  groupName:
                    aspectRatioGroups[currentAspectRatioGroup]?.name ||
                    'Unknown',
                }
              : undefined
          }
        />
      </Box>
    );
  }

  // Render default variant (existing code)
  return (
    <Box sx={{ width: '100%' }}>
      {/* Upload Area - Hidden in disabled and readOnly modes */}
      {!disabled && !readOnly && (
        <Paper
          variant="outlined"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          sx={{
            p: 2,
            textAlign: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            border: error
              ? `2px dashed ${theme.palette.error.main}`
              : dragOver
                ? `2px dashed ${theme.palette.primary.main}`
                : `2px dashed ${theme.palette.divider}`,
            backgroundColor: error
              ? theme.palette.error.light
              : dragOver
                ? theme.palette.primary.light
                : 'transparent',
            transition: 'border-color 0.2s, background-color 0.2s',
            opacity: disabled ? 0.6 : 1,
            '&:hover': disabled
              ? {}
              : {
                  borderColor: error
                    ? theme.palette.error.main
                    : theme.palette.primary.main,
                  backgroundColor: error
                    ? theme.palette.error.light
                    : theme.palette.primary.light,
                },
          }}
        >
          <input
            key={inputKey}
            type="file"
            multiple={multiple}
            accept={acceptedTypes.join(',')}
            onChange={handleFileInputChange}
            disabled={disabled || uploading}
            style={{ display: 'none' }}
            id={`${uniqueId}-standalone`}
          />

          <label
            htmlFor={`${uniqueId}-standalone`}
            style={{ cursor: 'inherit', display: 'block' }}
          >
            {uploading ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <CircularProgress size={40} />
                <Typography variant="body2" color="textSecondary">
                  {t('menu.uploading')}
                </Typography>
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <CloudUploadIcon
                  sx={{
                    fontSize: 32,
                    color: dragOver
                      ? theme.palette.primary.main
                      : theme.palette.text.secondary,
                  }}
                />
                <Typography
                  variant="body1"
                  color={dragOver ? 'primary' : 'textPrimary'}
                >
                  {dragOver
                    ? t('menu.dropFilesHere')
                    : multiple
                      ? t('menu.dragAndDropOrClickToUploadFiles')
                      : t('menu.dragAndDropOrClickToUploadFile')}
                </Typography>
                {computedHelperText && (
                  <Typography variant="body2" color="textSecondary">
                    {computedHelperText}
                  </Typography>
                )}
              </Box>
            )}
          </label>
        </Paper>
      )}

      {/* File List */}
      {files.length > 0 ? (
        <Box sx={{ mt: disabled || readOnly ? 0 : 2 }}>
          {/* Show summary only in normal mode */}
          {!disabled && !readOnly && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {multiple
                  ? `${files.length} file(s) uploaded:`
                  : t('menu.uploadedFile')}
              </Typography>
            </Box>
          )}

          {multiple ? (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={disabled || readOnly ? () => {} : handleDragEnd}
            >
              <SortableContext
                items={files.map(
                  (file, index) => `${file.rn || file.fn}-${index}`
                )}
                strategy={verticalListSortingStrategy}
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {files.map((file, index) => (
                    <SortableFileItem
                      key={`${file.rn || file.fn}-${index}`}
                      file={file}
                      index={index}
                      disabled={disabled}
                      readOnly={readOnly}
                      uploading={uploading}
                      multiple={multiple}
                      files={files}
                      theme={theme}
                      handleFilePreview={handleFilePreview}
                      handleRemoveFile={handleRemoveFile}
                      formatFileSize={formatFileSize}
                      privateFileContext={privateFileContext}
                    />
                  ))}
                </Box>
              </SortableContext>
            </DndContext>
          ) : (
            // Single file mode - no drag and drop
            files.map((file, index) => (
              <Paper
                key={`${file.rn || file.fn}-${index}`}
                variant="outlined"
                sx={{
                  p: 2,
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  opacity: disabled ? 0.6 : 1,
                  backgroundColor:
                    disabled || readOnly
                      ? theme.palette.action.hover
                      : 'transparent',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    flex: 1,
                    minWidth: 0,
                  }}
                >
                  {(isImageFile(file) || isVideoFile(file)) && (
                    <FilePreviewThumbnail
                      file={file}
                      size={40}
                      onClick={() => !disabled && handleFilePreview(file)}
                      disabled={disabled}
                      theme={theme}
                    />
                  )}

                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 500,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {file.rn}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      <FileSizeDisplay
                        file={file}
                        formatFileSize={formatFileSize}
                        privateFileContext={privateFileContext}
                      />
                      {uploading && file.x && ' • Uploading...'}
                    </Typography>
                  </Box>
                </Box>

                {/* Delete button - Hidden in disabled and readOnly modes */}
                {!disabled && !readOnly && (
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveFile(index)}
                    disabled={uploading}
                    color="error"
                    sx={{ ml: 1 }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                )}
              </Paper>
            ))
          )}
        </Box>
      ) : (
        // Show "No files uploaded" message in disabled and readOnly modes
        (disabled || readOnly) && (
          <Box sx={{ mt: 0 }}>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: disabled ? 0.6 : 1,
                backgroundColor:
                  disabled || readOnly
                    ? theme.palette.action.hover
                    : 'transparent',
                minHeight: 56,
              }}
            >
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ fontStyle: 'italic' }}
              >
                {t('menu.noFilesUploaded')}
              </Typography>
            </Paper>
          </Box>
        )
      )}

      {/* Show external error text */}
      {errorText && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 1, display: 'block' }}
        >
          {errorText}
        </Typography>
      )}

      {/* File Preview Modal */}
      <FilePreviewModal file={previewFile} onClose={handleClosePreview} />

      {/* Image Editor Modal */}
      <ImageEditorModal
        file={editingFile}
        isOpen={isImageEditorOpen}
        onClose={handleImageEditorClose}
        onSave={handleImageEditorSave}
        onCancel={handleImageEditorCancel}
        config={imageEditorConfig || {}}
        fileType={fileType}
        batchInfo={getEnhancedBatchInfo()}
        aspectRatioInfo={
          aspectRatioGroups && aspectRatioGroups.length > 1
            ? {
                currentGroup: currentAspectRatioGroup + 1,
                totalGroups: aspectRatioGroups.length,
                groupName:
                  aspectRatioGroups[currentAspectRatioGroup]?.name || 'Unknown',
              }
            : undefined
        }
      />
    </Box>
  );
}
