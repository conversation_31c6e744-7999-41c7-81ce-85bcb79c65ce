import {
  reportCalculatedSummableFields,
  reportSummableFields,
} from './constants';
import {
  OmitKeysWithTypeTransform,
  ReportCommonFields,
  ReportType,
  ReportTypeCalculatedSummableFields,
  ReportTypeNonSummableFields,
  ReportTypeSpecificFields,
  ReportTypeSummableFields,
} from './types';
import { isNumberPrimitive } from './utils/isNumber';

type ReportInternal<K extends keyof ReportType> = Partial<
  OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>
> &
  ReportTypeSummableFields[K] &
  OmitKeysWithTypeTransform<ReportTypeCalculatedSummableFields[K]> &
  ReportTypeNonSummableFields[K];

type GroupedReport<K extends keyof ReportType> = Partial<
  OmitKeysWithTypeTransform<ReportCommonFields<K>>
> & {
  report: Array<ReportInternal<K>>;
};

type ReportGroupedBySpecificFields<
  K extends keyof ReportType,
  G extends keyof OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>,
> = {
  groupedBy?: {
    field: G;
    value: ReportTypeSpecificFields[K][G];
  };
  subReport?: Array<ReportGroupedBySpecificFields<K, G>>;
} & ReportInternal<K>;

function groupReportBySpecificFieldsHierarchicalRecursive<
  K extends keyof ReportType,
  G extends keyof OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>,
>(
  reportType: K,
  report: Array<ReportInternal<K>>,
  groupBySpecificFields: Array<G>
): Array<ReportGroupedBySpecificFields<K, G>> {
  if (
    groupBySpecificFields.length === 0 ||
    report.length === 0 ||
    reportSummableFields[reportType].length === 0
  ) {
    return report as Array<ReportGroupedBySpecificFields<K, G>>;
  }

  // get the summable fields for the report type
  const summableFields = reportSummableFields[reportType];
  // get the calculated summable fields for the report type.
  // These need to be splited with @ symbol and the field name will be the first part
  const calculatedSummableFields = reportCalculatedSummableFields[
    reportType
  ].map(field => (field as string).split('@')[0]);

  const groupByField = groupBySpecificFields[0];
  const grouped: Record<string, Array<ReportInternal<K>>> = {};

  report.forEach(item => {
    const groupKey = String(item[groupByField] ?? '');
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push(item);
  });

  const result: Array<ReportGroupedBySpecificFields<K, G>> = Object.entries(
    grouped
  ).map(([groupKey, groupItems]) => {
    const subReport = groupReportBySpecificFieldsHierarchicalRecursive(
      reportType,
      groupItems,
      groupBySpecificFields.slice(1)
    );

    // Sum up summable fields
    // set the initial value to undefined
    const summableFieldsSum = {} as Record<string, number | undefined>;
    // it is possible that the field is not present in all items in the group or is not present at all
    // in that case we need to set the value to undefined
    summableFields.forEach(field => {
      summableFieldsSum[field as string] = sumFieldValues(
        groupItems,
        field as string
      );
    });

    // Sum up calculated summable fields
    // set the initial value to undefined
    const calculatedSummableFieldsSum = {} as Record<
      string,
      number | undefined
    >;
    // it is possible that the field is not present in all items in the group or is not present at all
    // in that case we need to set the value to undefined
    calculatedSummableFields.forEach(field => {
      calculatedSummableFieldsSum[field] = sumFieldValues(groupItems, field);
    });

    return {
      groupedBy: {
        field: groupByField,
        value: groupKey,
      },
      subReport: subReport,
      ...summableFieldsSum,
      ...calculatedSummableFieldsSum,
    } as ReportGroupedBySpecificFields<K, G>;
  });

  return result;
}

function sumFieldValues<K extends keyof ReportType>(
  items: Array<ReportInternal<K>>,
  field: string
): number | undefined {
  let sum = 0;
  let hasValidValues = false;

  for (const item of items) {
    const value = item[field as keyof typeof item];
    if (isNumberPrimitive(value)) {
      sum += value;
      hasValidValues = true;
    }
  }

  return hasValidValues ? sum : undefined;
}

export function groupGroupedReportBySpecificFieldsHierarchical<
  K extends keyof ReportType,
  G extends keyof OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>,
>(
  reportType: K,
  groupedReport: Array<GroupedReport<K>>,
  groupBySpecificFields: Array<G>
): Array<
  Partial<OmitKeysWithTypeTransform<ReportCommonFields<K>>> & {
    report: Array<ReportGroupedBySpecificFields<K, G>>;
  }
> {
  if (groupedReport.length === 0 || groupBySpecificFields.length === 0) {
    return groupedReport;
  }

  return groupedReport.map(group => {
    const result = groupReportBySpecificFieldsHierarchicalRecursive(
      reportType,
      group.report,
      groupBySpecificFields
    );

    return {
      ...group,
      report: result,
    };
  });
}
