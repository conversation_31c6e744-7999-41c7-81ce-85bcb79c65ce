import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { TeamMemberWorkingAreaCreate } from './TeamMemberWorkingAreaCreate';
import { TeamMemberWorkingAreaEdit } from './TeamMemberWorkingAreaEdit';
import { TeamMemberWorkingAreaList } from './TeamMemberWorkingAreaList';

export default function TeamMemberWorkingAreaPage() {
  const { t } = useTranslation('');
  const { sellPointId, isLoading } = useGlobalResourceFilters();

  if (isLoading || !sellPointId) {
    return null;
  }

  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
          mt: 2,
        }}
        title={t('workingArea.title')}
        description={
          <>
            {t('workingArea.description')} {" "}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <TeamMemberWorkingAreaList sellPointId={sellPointId} />
      <TeamMemberWorkingAreaCreate sellPointId={sellPointId} />
      <TeamMemberWorkingAreaEdit sellPointId={sellPointId} />
    </Box>
  );
}
