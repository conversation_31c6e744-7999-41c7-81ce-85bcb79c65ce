import { Box } from '@mui/material';

import { SubFilterSelect } from '~/components/organisms/MainFilterSelect/SubFilterSelect';
import { useFilters } from '~/contexts/FilterContext';

const SubFiltersDisplay = () => {
  const { activeFilters } = useFilters();

  const SUB_OPTIONS: Record<string, string[]> = {
    closedWith: ['any', 'comp', 'void'],
    hasDiscounts: ['any', 'yes', 'no'],
    hasTransfers: ['any', 'yes', 'no'],
    hasBillsIssued: ['any', 'yes', 'no'],
  };

  return (
    <Box
      sx={{ width: '100%', display: 'flex', gap: 2, flexWrap: 'wrap' }}
      mt={3}
    >
      {activeFilters.map(key => (
        <SubFilterSelect subOptions={SUB_OPTIONS} key={key} filterKey={key} />
      ))}
    </Box>
  );
};

export default SubFiltersDisplay;
