import { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import { groupReport } from '~/fake-provider/reports/groupReport';
import {
  OmitKeysWithTypeTransform,
  Report,
} from '~/fake-provider/reports/types';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import HorizontaBarChart from '../../atoms/charts/HorizontalBarChart';

const REPORT_TYPE = 'sales';

const formatString = (input: string) => {
  return input
    .replace(/([a-z])([A-Z])/g, '$1 $2') // Add space before capital letters
    .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2') // Handle all-caps words
    .replace(/\b\w/g, char => char.toUpperCase()); // Capitalize first letters
};

export default function PaymentTypes() {
  const { details: fbDetails } = useFirebase();
  const [data, setData] = useState<{ label: string; value: number }[]>([]);
  const [currency, setCurrency] = useState<CurrencyType>();

  const { dateRange, sellPointId } = useGlobalResourceFilters();
  const { t } = useTranslation();

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        setData([]);
        return;
      }
      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const rawData = (await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          endDate: dateRange[1].format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        })) as OmitKeysWithTypeTransform<Report<'sales'>>[];
        if (rawData.length === 0) {
          setData([]);
          return;
        }

        const grouped = groupReport(REPORT_TYPE, rawData, [], []);
        const paymentsObj = grouped[0].report[0].payments ?? {};
        const paymentsArr = [];
        for (let key in paymentsObj) {
          paymentsArr.push({
            label: formatString(key),
            value: paymentsObj[key] / 10000,
          });
        }

        setData(paymentsArr.sort((a, b) => b.value - a.value).slice(0, 4));

        const currency = (rawData[0]?.currency || 'RON') as CurrencyType;
        setCurrency(currency);
      } catch (err) {
        console.error(err);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  return (
    <Box>
      <Typography variant="h4">{t('dashboard.paymentTypes')}</Typography>
      <Typography variant="body2" color="custom.gray600">
        {t('dashboard.byGross')}
      </Typography>
      {data.length > 0 ? (
        <HorizontaBarChart
          colorSet={3}
          chartData={data}
          withTable
          chartHeight="120px"
          formatData={data => formatNumber(data, currency)}
        />
      ) : (
        <Typography
          variant="body2"
          color="custom.gray600"
          mt={2}
          fontStyle="italic"
        >
          {t('dashboard.noData')}
        </Typography>
      )}
    </Box>
  );
}
