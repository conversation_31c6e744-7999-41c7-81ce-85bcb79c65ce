export default function mergeAndSumObjects(array: any[]) {
  return array.reduce((acc, obj) => {
    for (let key in obj) {
      // Check if the value is a number and sum it
      if (typeof obj[key] === 'number') {
        acc[key] = (acc[key] || 0) + obj[key];
      }
      // if it s not a number, take the first occurrence and keep it
      else if (!(key in acc)) {
        acc[key] = obj[key];
      }
    }
    return acc;
  }, {});
}
