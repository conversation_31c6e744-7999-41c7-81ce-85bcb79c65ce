import { Box, Typography } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  SimpleForm,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomInput from '~/components/atoms/inputs/CustomInput';
import { useAvailableCategories } from '~/hooks';
import { validateName } from '~/utils/validateName';
import ModalHeader from '../../components/molecules/ModalHeader';

const PrepStationsEditInner = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');

  // Use the current prep station ID so its categories remain available
  const { availableCategories, isLoading } = useAvailableCategories(
    record?.id as string
  );

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('prepStations.editPrepStation')}
      >
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <CustomInput
            source="name"
            label={t('shared.name')}
            validate={[required(), validateName]}
          />
          {availableCategories.length > 0 ? (
            <CustomInput
              source="groups"
              choices={availableCategories}
              type="select-array"
              label={t('prepStations.tags')}
              optionText="name"
              optionValue="id"
              placeholder={t('shared.none')}
              validate={[required()]}
              disabled={isLoading}
            />
          ) : (
            <>
              <CustomInput
                source="groups"
                choices={[]}
                type="select-array"
                label={t('prepStations.tags')}
                optionText="name"
                optionValue="id"
                placeholder={
                  isLoading
                    ? t('shared.loading')
                    : t('prepStations.noAvailableCategories')
                }
                validate={[required()]}
                disabled={true}
              />
              {!isLoading && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: 'block' }}
                >
                  {t('prepStations.allCategoriesAssigned')}
                </Typography>
              )}
            </>
          )}
        </Box>
      </Box>
    </>
  );
};

export const PrepStationEdit = () => {
  const transform = (data: any) => {
    return {
      ...data,
      groupIds: data.groups,
    };
  };

  return (
    <EditDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="optimistic"
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <PrepStationsEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
