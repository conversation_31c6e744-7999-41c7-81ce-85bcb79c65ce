import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import {
  RadioButtonGroupInput,
  RadioButtonGroupInputProps,
  useChoicesContext,
  useInput,
  useRecordContext,
} from 'react-admin';

export interface RadioInputGroupProps
  extends Omit<RadioButtonGroupInputProps, 'choices'> {
  title?: string;
  choices?: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
}

const OptionField = () => {
  const record = useRecordContext();
  return (
    <Box sx={{ mt: '20px', ml: '5px' }}>
      {/* @ts-ignore */}
      <Typography variant="label" fontWeight={300}>
        {record?.name}
      </Typography>
      {record?.description && (
        <Typography variant="subtitle2">{record.description}</Typography>
      )}
    </Box>
  );
};

export default function RadioInputGroup({
  source: sourceProp,
  choices,
  disabled,
  title,
  ...props
}: RadioInputGroupProps) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const { source } = useChoicesContext({
    source: sourceProp,
  });

  if (source === undefined) {
    throw new Error(
      `If you're not wrapping the RadioGroup inside a ReferenceInput, you must provide the source prop`
    );
  }

  const { fieldState } = useInput({ source });

  return (
    <Box
      sx={{
        border: `1px solid`,
        borderColor: 'custom.gray400',
        display: 'flex',
        flexDirection: isXSmall ? 'column' : 'row',
        marginTop: '-1px',
        bgcolor: 'custom.fieldBg',
      }}
      onClick={event => {
        if (disabled) {
          event.preventDefault();
          event.stopPropagation();
        }
      }}
    >
      {title && (
        <Box
          sx={{
            width: isXSmall ? '100%' : '200px',
            minWidth: '200px',
            bgcolor: !!fieldState.error ? 'error.light' : 'background.tinted',
            display: 'flex',
            p: 2,
          }}
        >
          {/* @ts-ignore */}
          <Typography variant="label">{title}</Typography>
        </Box>
      )}
      <Box
        sx={{
          px: 3.5,
        }}
      >
        <RadioButtonGroupInput
          disabled={disabled}
          row={false}
          source={source}
          choices={choices}
          label=""
          optionText={<OptionField />}
          {...props}
        />
      </Box>
    </Box>
  );
}
