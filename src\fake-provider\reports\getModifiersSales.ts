import { number } from 'react-admin';

import { OmitKeysWithTypeTransform } from './types';

export default function getModifiersSales(
  report: Array<OmitKeysWithTypeTransform<Report>>
): Array<{
  name: string;
  category: string;
  unit: string;
  itemsSold: number;
  grossSales: number;
  netValue: number;
  discountsValue: number;
  couponssValue: number;
  prepStation: string;
  promotionssValue: number;
  subItems: Array<{
    variant: string;
    vat: number;
    unit: string;
    itemSold: number;
    grossSales: number;
  }>;
}> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result: Record<string, any> = {};

  report.forEach((group: any) => {
    group.report.forEach((item: any) => {
      const groupKey =
        item.id + item.groupId + (item.measureUnit ?? '@default');
      if (!result[groupKey]) {
        result[groupKey] = {
          name: item.id,
          category: item.groupId,
          unit: item.measureUnit ?? '@default',
          itemsSold: item.quantity,
          discountsValue: item.discountsValue,
          couponsValue: item.couponsValue,
          promotionsValue: item.promotionsValue,
          prepStation: item.prepStation,
          netValue: item.netValue,
          grossSales: item.value,
          subItems: {
            [item.variant]: {
              variant: item.variant,
              vat: item.vat,
              prepStation: item.prepStation,
              unit: item.measureUnit ?? '',
              itemsSold: item.quantity,
              grossSales: item.value,
            },
          },
        };
      } else {
        result[groupKey].itemsSold += item.quantity;
        result[groupKey].grossSales += item.value;
        const variantKey = item.variant + item.vat.toString();
        if (!result[groupKey].subItems[variantKey]) {
          result[groupKey].subItems[variantKey] = {
            variant: item.variant,
            vat: item.vat,
            prepStation: item.prepStation,
            unit: item.measureUnit ?? '@default',
            itemsSold: item.quantity,
            grossSales: item.value,
          };
        } else {
          result[groupKey].subItems[variantKey].itemsSold += item.quantity;
          result[groupKey].subItems[variantKey].grossSales += item.value;
        }
      }
    });
  });

  return Object.values(result).map(item => ({
    ...item,
    subItems: Object.values(item.subItems),
  }));
}
