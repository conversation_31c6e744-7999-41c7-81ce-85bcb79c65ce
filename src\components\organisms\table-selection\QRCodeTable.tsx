import { Table } from '../table-dnd/types';
import FloorPlanTable from './FloorPlanTable';

interface QRCodeTableProps {
  table: Table;
  floorPlanLabel: string;
  onViewQR: () => void;
}

export default function QRCodeTable({
  table,
  floorPlanLabel,
  onViewQR,
}: QRCodeTableProps) {
  return (
    <FloorPlanTable
      table={table}
      floorPlanLabel={floorPlanLabel}
      mode="qr"
      showLabel={true}
      labelSize={14}
      onClick={onViewQR}
    />
  );
}
