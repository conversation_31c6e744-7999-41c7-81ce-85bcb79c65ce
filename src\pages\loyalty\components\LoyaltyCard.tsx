import { Box, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';

export default function LoyaltyCard({
  title,
  description,
  icon,
}: {
  title: string;
  description: string;
  icon: string;
}) {
  const { theme } = useTheme();
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        height: '100%',
        maxWidth: '162px',
        alignItems: { xs: 'center', sm: 'start' },
      }}
    >
      <img
        src={icon}
        style={{
          width: '46px',
          height: '46px',
          filter: theme.palette.mode === 'light' ? '' : 'invert(1)',
        }}
      />
      <Typography sx={{}} fontWeight={500} fontSize={15} variant="caption">
        {title}
      </Typography>
      <Typography
        sx={{ color: '', textAlign: { xs: 'center', sm: 'start' } }}
        fontWeight={300}
        fontSize={13}
        variant="caption"
      >
        {description}
      </Typography>
    </Box>
  );
}
