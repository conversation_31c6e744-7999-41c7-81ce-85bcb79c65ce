import { Box } from '@mui/material';
import { Datagrid, List, TextField } from 'react-admin';
import { useTheme } from '~/contexts';
import Subsection from '../../components/molecules/Subsection';
import { useTranslation } from 'react-i18next';
const SalesTaxesListInner = () => {
  const {theme} = useTheme()
  const { t } = useTranslation();
  return (
    <Datagrid
      bulkActionButtons={false}
      sx={{
        '& .MuiTableCell-root:last-of-type': {
          textAlign: 'right',
          '& button': {
            visibility: 'visible',
          },
        },
        '& .MuiFormControl-root': {
          margin: 0,
          height: '30px',
        },
        '& .MuiTableCell-root': {
          width: '33%',
        },
        '& .RaDatagrid-headerCell': {
            backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
            borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
          },
      }}
    >
      <TextField source="taxName" label={t('salesTaxes.taxName')} />
      <TextField source="status" label={t('salesTaxes.status')} />
      <TextField source="taxRate" label={t('salesTaxes.taxRate')} />
    </Datagrid>
  );
};

export default function SalesTaxesList() {
  const { t } = useTranslation();
  return (
    <Box
      sx={{
        mt: { xs: 3, sm: 0 },
      }}
    >
      <List
        resource="salesTaxes"
        component="div"
        exporter={false}
        pagination={false}
        sx={{ mx: 'auto', width: '95%' }}
      >
        <Subsection
          title={t('salesTaxes.title2')}
          subtitle={t('salesTaxes.description3')}
        />
        <Box sx={{ mt: 7 }}>
          <SalesTaxesListInner />
        </Box>
      </List>
    </Box>
  );
}
