import { Box } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  SimpleForm,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import CustomInput from '~/components/atoms/inputs/CustomInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { validateName } from '~/utils/validateName';

const CategoryCreateInner = () => {
  const resource = useResourceContext();
  const redirect = useRedirect();
  const { t } = useTranslation('');

  const handleClose = () => {
    redirect('list', resource, undefined, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('categoryLibrary.createCategory')}
      >
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <CustomInput
            source="name"
            label={t('shared.name')}
            validate={[required(), validateName]}
          />
          <CustomInput
            source="straightFire"
            type="switch"
            label="Straight fire"
          />
        </Box>
      </Box>
    </>
  );
};

export const CategoryCreate = () => {
  const transform = (data: any) => {
    return {
      ...data,
    };
  };

  return (
    <CreateDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="optimistic"
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <CategoryCreateInner />
      </SimpleForm>
    </CreateDialog>
  );
};
