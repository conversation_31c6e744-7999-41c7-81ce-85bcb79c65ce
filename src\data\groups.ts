export const groupsObject = {
  groups: [
    {
      id: '5TdXIenoYao8QEq7IYa6',
      name: '<PERSON><PERSON>',
      straightFire: true,
    },
    {
      id: '7pg7q36z84LmjCkb7wUG',
      modifiers: [
        'LyfVK9cTPJmU1zpxVt4c',
        'BswvIHu84fxcKeLE6R1D',
        'FsCsZDWCc94YzzVs1WGN',
        'cDQ4ol5MVl4izCKzGwuL',
      ],
      name: '<PERSON>ap<PERSON>',
      straightFire: true,
    },
    {
      id: 'C6m2fdcW2N0pdgbx6cvo',
      modifiers: [
        'nrZzmLPbbwnXjPYfGlEf',
        'NLeJUPKG0EMcPLxHsjtA',
        'BswvIHu84fxcKeLE6R1D',
      ],
      name: '<PERSON>ai',
      straightFire: true,
    },
    {
      id: 'Fa9XzZdZphWYlNbvS78v',
      name: 'Limona<PERSON>',
      straightFire: true,
    },
    {
      id: 'Ne4tvYWCbaMTIXIYH8lB',
      name: 'Antipaste',
    },
    {
      id: 'OetLvqQtm7PGYvbZd008',
      modifiers: [
        'g3NWzpbI0PWMtxKpqA3o',
        'veCfhI29yOUF5Eiy99iZ',
        'VLJDvc00dNba0acvJLye',
        'kFg2C9NNODUGXo0PqNI5',
        'XiQEdbNDKjZrbppbBA7y',
        'tuKo1PuqjNkyBDg7e0VO',
        'sNKybXF0KLqSPi4myzCz',
        'xSBjie1LkrlrT4GjZPGV',
        'OGJeDzoHyackX8NY3Jtm',
        'KpQrt5oNst2tYBzfizwH',
        'vFUTSTeqTHabx36PeS66',
        'FQai3kSPHD82t6UQc85G',
        '29SiLPSaUDnsiTdRpfmT',
        'bg2bKpQe8Am3NY8ayIMv',
        'srOtGIiJk1eyHXgqwFMl',
        'zBVNFompgrjlH2STQh2g',
        'sq3Go86RihFqvaCvuEzV',
        '01sdERTixoiEZr9WdwGy',
        'ZjMavDv3o28tV2dczqeA',
        'Jxv0bjEyZMYuHWNnGLkT',
        '5SkuvuV9AgBbZ8Y93aq2',
        'PNVYBe99iQUVIc1YZwXT',
        'JSufrpnHPKGbwvIKb3z3',
        'JJ3hlZnFP2F5QdPJWJQg',
        'IVQ4RvlI0uCmCJECJiEm',
        'b2jzW14M1tMiLYiJaTSP',
        'bG81qGSZy6eNJIzauNa8',
        'eNDx6h6fHXTQ5p8SAEUZ',
        'gOZRGwctv4oyfJxionwc',
        'C6YERXR9kVkbF8XI0rlH',
        'Lq6y53Nkeka6dUyapom7',
        'Az5ddPHhQhjhoSEe8XbU',
        'Bb4QjFou8Q4rBH3EWZqQ',
        'ArTPZwro3aQmuEGWCzxi',
        'I3kaihMX944mR2asf3NS',
        'DKtOn40iOjsB7IMiLjjk',
        'SHHLdnim6UlwwfqWBuNb',
        'Vwo0n2CGJMIXdYUwxtQy',
        '3PpYS4gS0meO2wvvr34U',
        'dwX4sdxVtS3diOQtYeK7',
        'P0KVSRUb6NAct9eS9pj9',
        'h4eidDotP6nLmmwcF1wj',
        'rnnyLYMajnbejfn1KUge',
        'amL5mCu2FEZXPuIVqL0Q',
        'AHPfRhXqKpUjk2RazvJn',
      ],
      name: 'Pizza',
    },
    {
      id: 'YcPO7fINyVIyjji0CY8x',
      modifiers: [
        '6N6F1u6y8ZRWqQGsa1ta',
        '2UMoHx3hUUm7LwOJ7Zy6',
        'eLiQc733fK411iqMuuba',
        'axfziCkoYIaodf3HG6zC',
        'jtcHT7dcnIuGi2U664ym',
        'lMJLFByEuapBAwZAg6OQ',
        'hC93VW5pX9OPsm2QngID',
        'ArVs7Tkxd6odRzudeLTJ',
        'XC73MCG9Rwp90NWz54bv',
        'LNXg64HSkinUMmLAEUy1',
      ],
      name: 'Salate',
    },
    {
      id: 'ZlwptBsuqMQGjsXh3eSL',
      modifiers: [
        'bnYkoIWA6khA5MZImCD1',
        'lFve55kJsT42zmx2kgL1',
        'O3j7o9Q20vuEeN0ByAky',
      ],
      name: 'Paste',
    },
    {
      id: 'o3Kef6w1UfSudZGafquR',
      name: 'Cafea',
      straightFire: true,
    },
    {
      id: 'qwlGXr4Mcy2cwIDUMIVa',
      name: 'Desert',
    },
    {
      id: 'uweslytLYgHOlz1LI6g5',
      modifiers: ['g3NWzpbI0PWMtxKpqA3o', 'BswvIHu84fxcKeLE6R1D'],
      name: 'Combos',
    },
    {
      id: 'w5urPSPkj9M1qlNk05hG',
      name: 'Vinuri',
      straightFire: true,
    },
    {
      id: 'zgJiHwi5HIaMVQphMlvq',
      name: 'Orez',
    },
  ],
};
