import React from 'react';
import { Chip, Grid, Slider, Typography } from '@mui/material';

import { isValidOrderingRange } from '../../utils/googleMapsUtils';

interface RangeInputProps {
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  label?: string;
  min?: number;
  max?: number;
  showSlider?: boolean;
  showChip?: boolean;
}

// Predefined steps for ordering range with 0 (disabled) and specific breakpoints
const ORDERING_RANGE_STEPS = [0, 15, 50, 100, 200];

export const RangeInput: React.FC<RangeInputProps> = ({
  value,
  onChange,
  disabled = false,
  error = false,
  helperText,
  label = 'Ordering Range',
  min = 0,
  max = 200,
  showSlider = true,
  showChip = true,
}) => {
  const handleSliderChange = (_event: Event, newValue: number | number[]) => {
    const sliderValue = Array.isArray(newValue) ? newValue[0] : newValue;
    onChange(sliderValue);
  };

  const formatDistance = (meters: number): string => {
    if (meters === 0) {
      return 'Disabled';
    }
    if (meters >= 1000) {
      const km = (meters / 1000).toFixed(1);
      return `${km} km`;
    }
    return `${meters} m`;
  };

  const isValid =
    value === 0 ||
    (isValidOrderingRange(value) && value >= min && value <= max);

  // Create marks only for the predefined breakpoints that are within range
  const marks = ORDERING_RANGE_STEPS.filter(
    step => step >= min && step <= max
  ).map(step => ({
    value: step, // Use actual value for marks
    label: formatDistance(step),
  }));

  // For the slider, find the closest step value to snap to
  const snapToClosestStep = (inputValue: number): number => {
    // If exactly 0 or very close to 0 (within 2.5), snap to 0
    if (inputValue <= 2.5) {
      return 0;
    }

    // If less than 15 but not 0, snap to minimum 15
    if (inputValue < 15) {
      return 15;
    }

    // Round to nearest 5m increment, but ensure it's at least 15
    const rounded = Math.round(inputValue / 5) * 5;
    const result = Math.max(15, Math.min(200, rounded));
    return result;
  };

  const handleSliderChangeWithSnap = (
    _event: Event,
    newValue: number | number[]
  ) => {
    const sliderValue = Array.isArray(newValue) ? newValue[0] : newValue;

    // Allow 0 to be selected directly without any interference
    if (sliderValue === 0) {
      onChange(0);
      return;
    }

    // Allow any value during dragging - no snapping
    onChange(sliderValue);
  };

  const handleSliderChangeCommitted = (
    _event: Event | React.SyntheticEvent,
    newValue: number | number[]
  ) => {
    const sliderValue = Array.isArray(newValue) ? newValue[0] : newValue;

    const snappedValue = snapToClosestStep(sliderValue);

    onChange(snappedValue);
  };

  // If only chip is needed (no slider, no label)
  if (!showSlider && !label && showChip) {
    return (
      <Chip
        label={formatDistance(value)}
        size="small"
        color={isValid ? 'primary' : 'error'}
        variant="outlined"
      />
    );
  }

  return (
    <Grid container spacing={1}>
      {label && (
        <Grid item xs={12}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 4,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {label}
            </Typography>
            {showChip && (
              <Chip
                label={formatDistance(value)}
                size="small"
                color={isValid ? 'primary' : 'error'}
                variant="outlined"
              />
            )}
          </div>
        </Grid>
      )}

      {!label && showChip && showSlider && (
        <Grid item xs={12}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              marginBottom: 4,
            }}
          >
            <Chip
              label={formatDistance(value)}
              size="small"
              color={isValid ? 'primary' : 'error'}
              variant="outlined"
            />
          </div>
        </Grid>
      )}

      {showSlider && (
        <Grid item xs={12}>
          <Slider
            value={value}
            onChange={handleSliderChangeWithSnap}
            onChangeCommitted={handleSliderChangeCommitted}
            disabled={disabled}
            min={min}
            max={max}
            step={5} // 5m step for precise control
            marks={marks}
            valueLabelDisplay="auto"
            valueLabelFormat={formatDistance}
            sx={{
              mt: 0,
              mb: 0.5,
              '& .MuiSlider-markLabel': {
                fontSize: '0.7rem',
                transform: 'translateX(-50%) rotate(-35deg)',
                transformOrigin: 'center top',
                marginTop: '6px',
              },
              '& .MuiSlider-mark': {
                height: '8px',
                width: '2px',
              },
              '& .MuiSlider-thumb': {
                height: 20,
                width: 20,
                '&:focus, &:hover, &.Mui-active': {
                  boxShadow: '0px 0px 0px 8px rgba(25, 118, 210, 0.16)',
                },
              },
            }}
          />
        </Grid>
      )}
    </Grid>
  );
};
