import { useMemo } from 'react';
import { Box } from '@mui/material';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../../../types/globals';
import ExtraDataModal from './ExtraDataModifierModal';
import { useTranslation } from 'react-i18next';
import { downloadCSV } from 'react-admin';
import { useGetListHospitalityCategoriesLive } from '~/providers/resources';
import { useGetListLive } from '@react-admin/ra-realtime';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListLocationsLive } from '~/providers/resources';

interface TableRow {
  id: string;
  variant: string;
  groupId: string;
  discountsValue: number;
  couponsValue: number;
  promotionsValue: number;
  netValue: number;
  measureUnit: string;
  vat: number | undefined;
  prepStation: string;
  name: string;
  groupName: string;
  reason: string;
  quantity: string;
  value: string;
  items?: TableRow[];
  subItems?: TableRow[];
  extraData?: { [key: string]: any };
}



export default function ModifierSalesTable({
  tableData,
  fields,
  formattedFilters,
  groupingItems,
  setFields,
  onChangeGrouping,
  reportType,
  rawData,
  composedFilters,
  filters,
  updateCompsData,
}: {
  tableData: any;
  updateCompsData?: any;
  formattedFilters: any;
  groupingItems: string[];
  fields: FieldOption[];
  onChangeGrouping?: (items: any[]) => void;
  reportType: string;
  rawData: any;
  composedFilters: any;
  filters: any;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
}) {
  const { t } = useTranslation();
  const modifiersTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.id = 'Total';
      totalItemsData.name = 'Total';
      totalItemsData.groupName = '';
      totalItemsData.subItems = [];
      totalItemsData.vat = undefined;
      totalItemsData.reason = '';
      totalItemsData.groupId = '';
      totalItemsData.prepStation = '';
      totalItemsData.measureUnit = '';

      mappedTableData = [...mappedTableData, totalItemsData];
      updateCompsData('totalModifier', totalItemsData);
      return mappedTableData;
    }
    updateCompsData('totalModifier', {});
    return [];
  }, [tableData]);

  const modifierSalesTableConfig: ColumnConfig<TableRow>[] = useMemo(() => [
    {
      id: 'id',
      textAlign: 'start',
      label: t('reportsPage.modifier'),
      render: (row: TableRow) => {
        if (row.subItems && row.subItems.length > 0) {
          return (
            <span
              style={{
                fontSize: '14px',
                whiteSpace: 'nowrap',
              }}
            >
              {row.id.includes('@none')
                ? 'Regular'
                : row?.id.includes('Vat')
                  ? capitalize(row?.id?.toLowerCase())
                  : row.id}
            </span>
          );
        } else {
          return (
            <div
              style={{
                fontSize: '14px',
                minWidth: '230px',
                whiteSpace: 'normal',
              }}
            >
              {row.name === '@none'
                ? 'Regular'
                : capitalize(row?.name?.toLowerCase()) || row.id}{' '}
            </div>
          );
        }
      },
    },
    {
      id: 'reason',
      textAlign: 'end',
      label: t('reportsPage.reason'),
      render: (row: TableRow) => {
        return <>{row.reason === '@na' ? 'N/A' : row.reason}</>;
      },
    },
    {
      id: 'vat',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'end',
    },
    {
      id: 'prepStation',
      textAlign: 'end',
      label: t('reportsPage.prepStation'),
    },
    {
      id: 'groupId',
      textAlign: 'end',
      label: t('shared.category_capitalize'),
      render: (row: TableRow) => {
        return <>{row.groupName}</>;
      },
    },
    {
      id: 'measureUnit',
      label: t('reportsPage.unit'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.measureUnit === 'undefined' ? '' : row.measureUnit} </>;
      },
    },
    {
      id: 'quantity',
      textAlign: 'end',
      label: t('reportsPage.itemsComped'),
      render: (row: TableRow) => {
        return (
          <>{Intl.NumberFormat('ro-RO').format(Number(row.quantity) / 1000)} </>
        );
      },
    },
  
    {
      id: 'value',
      textAlign: 'end',
      label: t('reportsPage.grossComps'),
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(Number(row.value))}</>;
      },
    },
    {
      id: 'discountsValue',
      label: t('reportsPage.discounts'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.discountsValue
              ? '- ' + formatAndDivideNumber(row?.discountsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'couponsValue',
      label: t('reportsPage.coupons'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.couponsValue
              ? '- ' + formatAndDivideNumber(row?.couponsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'promotionsValue',
      label: t('reportsPage.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'netValue',
      label: t('reportsPage.netComps'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.netValue)}</>;
      },
    },
  ], [t]);

  const columnsToFilter = useMemo(() => {
    const columns = [
      'groupId',
      'measureUnit',
      'variant',
      'vat',
      'quantity',
      'couponsValue',
      'discountsValue',
      'promotionsValue',
      'prepStation',
      'value',
      'reason',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'vat', label: t('shared.tva') },
    { value: 'groupId', label: t('shared.category_capitalize') },
    { value: 'prepStation', label: t('reportsPage.prepStation') },
    { value: 'reason', label: t('reportsPage.reason') },
    { value: 'measureUnit', label: t('reportsPage.unit') },
  ];

  const { dateRange, timeRange } = useGlobalResourceFilters();
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();
  const { data: modifiers } = useGetListLive('modifiers');
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const handleExport = () => {
    const title = 'Report comped modifiers';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Modifier',
        'Reason',
        'VAT',
        'Prep Station',
        'Category',
        'Unit',
        'Items Comped',
        'Gross Comps',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Comps',
      ].join(','),
      ...modifiersTableData?.map((el: any) => el.subItems?.map((report: any) =>
             [
              modifiers?.find(modifier => modifier.id === report.id)?.name || report.id,
              el.name,
              report.vat,
              report.prepStation,
              categories?.find(category => category.id === report.groupId)?.name || report.groupId,
              report.measureUnit,
              report.quantity / 1000,
              report.value / 10000,
              -report.discountsValue / 10000 || 0,
              -report.couponsValue / 10000 || 0,
              report.promotionsValue / 10000 || 0,
              report.netValue / 10000 || 0,
            ].join(',')
          )
        ).flat(),
        [
          'Total',
          '',
          '',
          '',
          '',
          Number(modifiersTableData[modifiersTableData.length - 1].quantity ) / 1000 || 0,
          Number(modifiersTableData[modifiersTableData.length - 1].value) / 10000 || 0,
          -Number(modifiersTableData[modifiersTableData.length - 1].discountsValue) / 10000 || 0,
          -Number(modifiersTableData[modifiersTableData.length - 1].couponsValue) / 10000 || 0,
          Number(modifiersTableData[modifiersTableData.length - 1].promotionsValue) / 10000 || 0,
          Number(modifiersTableData[modifiersTableData.length - 1].netValue) / 10000 || 0,
        ].join(','),
    ].join('\n');
   
    downloadCSV(csvContent, 'comped-modifiers');
  };


  return (
    <>
      <Box sx={{ py: 2 }}>
        <GroupingTable
          config={modifierSalesTableConfig}
          data={modifiersTableData}
          fields={fields}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          separateFirstColumn={true}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          enableInfoModal={true}
          fixedFirstColumn={true}
          exportCSV={true}
          handleExport={handleExport}
          renderModalContent={rowData => (
            <ExtraDataModal
              extraData={{
                composedFilters,
                rawData,
                reportType,
                filters,
                rowData,
                formattedFilters,
              }}
            />
          )}
        />
      </Box>
    </>
  );
}
