import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { MuiSwitchInput } from '../../../components/atoms/inputs/SwitchInput';
import { PermissionPages } from './constants';

const PermissionsPageHeader = ({
  pageLabel,
  isPageActive,
  togglePageActive,
  disableToggle,
}: {
  pageLabel: PermissionPages;
  isPageActive: boolean;
  togglePageActive: (pageLabel: PermissionPages) => void;
  disableToggle: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        borderBottom: 'solid 1px',
        borderColor: 'custom.gray400',
        paddingBottom: 2,
      }}
    >
      <Box>
        <Typography variant="h4" mb={1}>
          {t(`permissions.pages.${pageLabel}.title`)}
        </Typography>
        <Typography variant="subtitle2" color="inherit">
          {t(`permissions.pages.${pageLabel}.description`)}
        </Typography>
      </Box>
      <MuiSwitchInput
        sx={{ marginRight: '0 !important' }}
        checked={isPageActive}
        onChange={() => {
          togglePageActive(pageLabel);
        }}
        disabled={disableToggle}
      />
    </Box>
  );
};

export { PermissionsPageHeader };
