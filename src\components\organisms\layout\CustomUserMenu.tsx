import { Box, Divider, ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import { Logout, UserMenu, useUserMenu } from 'react-admin';
// import SettingsIcon from '@mui/icons-material/Settings';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useCookiesConsent } from '~/contexts/CookiesConsentContext';

import { useFirebase } from '~/contexts/FirebaseContext';

const UserMenuInner = () => {
  const { onClose } = useUserMenu() ?? {};
  const { t } = useTranslation();
  const { handleInsideButtonClick } = useCookiesConsent();
  const userMenu = [
    {
      label: t('profile.accountSettings'),
      href: '/sign-in-security',
      //   logo: <SettingsIcon fontSize='small' />,
    },
    {
      label: t('profile.termsOfService'),
      href: 'https://selio.io/terms-of-service/',
    },
    {
      label: t('profile.cookiePreferences'),
      href: '#',
      onClick: () => {
        handleInsideButtonClick();
        onClose?.();
      },
    },
    {
      label: t('profile.orderSelio'),
      href: 'https://selio.io/shop-main/',
    },
  ];

  return (
    <>
      {userMenu.map(item => (
        <Box key={item.label}>
          <MenuItem
            component={Link}
            to={item.href}
            onClick={item.onClick || onClose}
          >
            {/* <ListItemIcon>
            {item.logo}
            </ListItemIcon> */}
            <ListItemText>{item.label}</ListItemText>
          </MenuItem>
          <Divider sx={{ mx: 2 }} />
        </Box>
      ))}
    </>
  );
};
export default function CustomUserMenu() {
  const { onClose } = useUserMenu() ?? {};
  const { details: fbDetails } = useFirebase();
  const { t } = useTranslation();
  return (
    <UserMenu>
      {fbDetails.availableAccounts.length > 1 && (
        <>
          <MenuItem component={Link} to={'/select-account'} onClick={onClose}>
            {t('dashboard.switchAccount')}
          </MenuItem>
          <Divider sx={{ mx: 2 }} />
        </>
      )}

      <UserMenuInner />
      <Logout />
    </UserMenu>
  );
}
