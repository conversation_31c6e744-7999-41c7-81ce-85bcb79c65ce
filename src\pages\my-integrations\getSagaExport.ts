import { DateRange } from '@mui/x-date-pickers-pro';
import { Dayjs } from 'dayjs';
import { XMLBuilder } from 'fast-xml-parser';
import { Database } from 'firebase/database';
import { get, set } from 'lodash';
import { DataProvider } from 'react-admin';

import { storage } from '~/configs/firebaseConfig';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReport } from '~/fake-provider/reports/getReport';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { subtractReports } from '~/fake-provider/reports/subtractReports';
import { getReportsDataFromRealtime } from '~/fake-provider/reports/utils/getReportsDataFromRealtime';
import { getReportsDataFromStorage } from '~/fake-provider/reports/utils/getReportsDataFromStorage';
import { isNumberPrimitive } from '~/fake-provider/reports/utils/isNumber';
import { RESOURCES } from '~/providers/resources';

type SagaExportPackage = {
  [dataLucru: string]: {
    sales?: {
      nrDoc: string;
      serie: string;
      data: string;
      tip: string;
      informatiiSuplimentare: string;
      moneda: string;
      articole: Array<{
        gestiune: string;
        descriere: string;
        codArticolClient: string;
        guidCodArticol: string;
        um: string;
        cantitate: number;
        pret: number;
        procTva: number;
        discount: number;
      }>;
    };
    comps?: {
      [reason: string]: {
        nrDoc: string;
        serie: string;
        data: string;
        tip: string;
        moneda: string;
        articole: Array<{
          gestiune: string;
          descriere: string;
          codArticolClient: string;
          guidCodArticol: string;
          um: string;
          cantitate: number;
          pret: number;
          procTva: number;
          discount: number;
        }>;
      };
    };
    pms?: {
      nrDoc: string;
      serie: string;
      data: string;
      tip: string;
      informatiiSuplimentare: string;
      moneda: string;
      articole: Array<{
        gestiune: string;
        descriere: string;
        codArticolClient: string;
        guidCodArticol: string;
        um: string;
        cantitate: number;
        pret: number;
        procTva: number;
        discount: number;
      }>;
    };
  };
};

// Helper function to build Saga XML structure
function buildSagaXmlObject(
  sagaExport: SagaExportPackage,
  sellPointData: any,
  accountData: any
) {
  const facturi = [];

  for (const [dataLucru, dayData] of Object.entries(sagaExport)) {
    // Add sales invoice if it exists
    if (dayData.sales && dayData.sales.articole.length > 0) {
      const factura = {
        Antet: {
          FurnizorNume: accountData?.company?.name || '???_NumeCompanie',
          FurnizorCIF: accountData?.company?.identification || '???_CIF',
          FurnizorNrRegCom:
            accountData?.company?.registration || '???_NrRegCom',
          FurnizorTara: accountData?.company?.address?.country || '???_Tara',
          FurnizorLocalitate: accountData?.company?.address?.city || '???_Oras',
          FurnizorJudet: accountData?.company?.address?.state || '???_Judet',
          FurnizorAdresa: accountData?.company?.address?.line1 || '???_Adresa',
          ClientNume: 'Clienti BF',
          ClientCIF: '*************',
          //ClientNrRegCom: '',
          //ClientJudet: '',
          ClientTara: 'RO',
          //ClientLocalitate: '',
          //ClientAdresa: '',
          //ClientBanca: '',
          FacturaNumar: `${dayData.sales.serie}${dayData.sales.nrDoc}`,
          FacturaData: dayData.sales.data,
          //FacturaScadenta: '',
          //FacturaTaxareInversa: '',
          //FacturaTVAIncasare: '',
          FacturaTip: dayData.sales.tip,
          FacturaInformatiiSuplimentare: dayData.sales.informatiiSuplimentare,
          FacturaMoneda: dayData.sales.moneda,
          //Cod: '',
        },
        Detalii: {
          Continut: {
            Linie: dayData.sales.articole.map((articol, index) => ({
              LinieNrCrt: index + 1,
              Gestiune: articol.gestiune,
              Descriere: articol.descriere,
              CodArticolClient: articol.codArticolClient,
              GUID_cod_articol: articol.guidCodArticol,
              UM: articol.um,
              Cantitate: articol.cantitate,
              Pret: articol.pret,
              Valoare: Number(
                (
                  Number((articol.cantitate * articol.pret).toFixed(2)) -
                  articol.discount
                ).toFixed(2)
              ),
              ProcTVA: articol.procTva,
              TVA: Number(
                (
                  (Number(
                    (
                      Number((articol.cantitate * articol.pret).toFixed(2)) -
                      articol.discount
                    ).toFixed(2)
                  ) *
                    articol.procTva) /
                  (100 + articol.procTva)
                ).toFixed(2)
              ),
            })),
          },
        },
        GUID_factura: `${dayData.sales.serie}${dayData.sales.nrDoc}`,
      };

      facturi.push(factura);
    }

    // Add comps invoices if they exist (each reason gets its own invoice)
    if (dayData.comps) {
      for (const [reason, compData] of Object.entries(dayData.comps)) {
        if (compData.articole.length > 0) {
          const facturaComps = {
            Antet: {
              FurnizorNume: accountData?.company?.name || '???_NumeCompanie',
              FurnizorCIF: accountData?.company?.identification || '???_CIF',
              FurnizorNrRegCom:
                accountData?.company?.registration || '???_NrRegCom',
              FurnizorTara:
                accountData?.company?.address?.country || '???_Tara',
              FurnizorLocalitate:
                accountData?.company?.address?.city || '???_Oras',
              FurnizorJudet:
                accountData?.company?.address?.state || '???_Judet',
              FurnizorAdresa:
                accountData?.company?.address?.line1 || '???_Adresa',
              ClientNume: 'Gratuitati',
              ClientCIF: '*************',
              //ClientNrRegCom: '',
              //ClientJudet: '',
              ClientTara: 'RO',
              //ClientLocalitate: '',
              //ClientAdresa: '',
              //ClientBanca: '',
              FacturaNumar: `${compData.serie}${compData.nrDoc}`,
              FacturaData: compData.data,
              //FacturaScadenta: '',
              //FacturaTaxareInversa: '',
              //FacturaTVAIncasare: '',
              FacturaTip: compData.tip,
              FacturaInformatiiSuplimentare: `${reason} - ${dataLucru}`,
              FacturaMoneda: compData.moneda,
              //Cod: '',
            },
            Detalii: {
              Continut: {
                Linie: compData.articole.map((articol, index) => ({
                  LinieNrCrt: index + 1,
                  Gestiune: articol.gestiune,
                  Descriere: articol.descriere,
                  CodArticolClient: articol.codArticolClient,
                  GUID_cod_articol: articol.guidCodArticol,
                  UM: articol.um,
                  Cantitate: articol.cantitate,
                  Pret: articol.pret,
                  Valoare: Number(
                    (
                      Number((articol.cantitate * articol.pret).toFixed(2)) -
                      articol.discount
                    ).toFixed(2)
                  ),
                  ProcTVA: articol.procTva,
                  TVA: Number(
                    (
                      (Number(
                        (
                          Number(
                            (articol.cantitate * articol.pret).toFixed(2)
                          ) - articol.discount
                        ).toFixed(2)
                      ) *
                        articol.procTva) /
                      (100 + articol.procTva)
                    ).toFixed(2)
                  ),
                })),
              },
            },
            GUID_factura: `${compData.serie}${compData.nrDoc}`,
          };

          facturi.push(facturaComps);
        }
      }
    }

    // Add PMS invoice if it exists
    if (dayData.pms && dayData.pms.articole.length > 0) {
      const facturaPms = {
        Antet: {
          FurnizorNume: sellPointData.companyName || 'Company Name',
          FurnizorCIF: sellPointData.companyCIF || '',
          FurnizorNrRegCom: '',
          FurnizorCapital: '',
          FurnizorTara: 'RO',
          FurnizorLocalitate: sellPointData.companyCity || '',
          FurnizorJudet: sellPointData.companyCounty || '',
          FurnizorAdresa: sellPointData.companyAddress || '',
          FurnizorTelefon: sellPointData.companyPhone || '',
          FurnizorMail: sellPointData.companyEmail || '',
          FurnizorBanca: '',
          FurnizorIBAN: '',
          FurnizorInformatiiSuplimentare: '',
          GUID_cod_client: '',
          ClientNume: 'Client Casa - Cont Hotel',
          ClientInformatiiSuplimentare: '',
          ClientCIF: '',
          ClientNrRegCom: '',
          ClientJudet: '',
          ClientTara: 'RO',
          ClientLocalitate: '',
          ClientAdresa: '',
          ClientBanca: '',
          ClientIBAN: '',
          ClientTelefon: '',
          ClientMail: '',
          FacturaNumar: dayData.pms.nrDoc,
          FacturaData: dayData.pms.data,
          FacturaScadenta: dayData.pms.data,
          FacturaTaxareInversa: 'Nu',
          FacturaTVAIncasare: 'Nu',
          FacturaTip: dayData.pms.tip,
          FacturaInformatiiSuplimentare: dayData.pms.informatiiSuplimentare,
          FacturaMoneda: dayData.pms.moneda,
          FacturaGreutate: '',
          FacturaAccize: '',
          FacturaIndexSPV: '',
          FacturaIndexDescarcareSPV: '',
          Cod: '',
        },
        Detalii: {
          Continut: {
            Linie: dayData.pms.articole.map((articol, index) => ({
              LinieNrCrt: index + 1,
              Gestiune: articol.gestiune,
              Activitate: '',
              Descriere: articol.descriere,
              CodArticolFurnizor: '',
              CodArticolClient: articol.codArticolClient,
              GUID_cod_articol: articol.guidCodArticol,
              CodBare: '',
              InformatiiSuplimentare: '',
              UM: articol.um,
              Cantitate: articol.cantitate,
              Pret: articol.pret,
              Valoare: Number(
                (articol.cantitate * articol.pret - articol.discount).toFixed(2)
              ),
              ProcTVA: articol.procTva,
              TVA: Number(
                (
                  ((articol.cantitate * articol.pret - articol.discount) *
                    articol.procTva) /
                  (100 + articol.procTva)
                ).toFixed(2)
              ),
              Cont: '',
              TipDeducere: '',
              PretVanzare: '',
            })),
          },
        },
        GUID_factura: `${dayData.pms.nrDoc}_${dataLucru}`,
      };

      facturi.push(facturaPms);
    }
  }

  return { Facturi: { Factura: facturi } };
}

export async function getSagaExport(
  dataProvider: DataProvider,
  rtdbInstance: Database,
  accountId: string,
  sellPointId: string,
  dateRange: DateRange<Dayjs>
) {
  if (
    !dataProvider ||
    !rtdbInstance ||
    !accountId ||
    !sellPointId ||
    !dateRange[0] ||
    !dateRange[1]
  )
    throw new Error('Missing parameters');

  const startDate = dateRange[0].format('YYYY-MM-DD');
  // we add 1 second because end date is always 23:59:59 and we want it to be next day
  const endDate = dateRange[1].add(1, 'seconds').format('YYYY-MM-DD');

  const startDateYear = Number(dateRange[0].format('YYYY'));
  const startDateMonth = Number(dateRange[0].format('MM'));
  const endDateYear = Number(dateRange[1].format('YYYY'));
  const endDateMonth = Number(dateRange[1].format('MM'));

  if (startDateYear !== endDateYear || startDateMonth !== endDateMonth) {
    throw new Error('Cannot export for multiple months');
  }

  // get the saga integration configuration
  const sagaRequest = await dataProvider.getOne(RESOURCES.MY_INTEGRATIONS, {
    id: 'saga',
  });
  const sagaData = sagaRequest.data ?? {};
  const sagaIntegrationIsActive = sagaData.active ?? false;
  if (!sagaIntegrationIsActive) {
    throw new Error('Saga integration is not active');
  }
  const sagaLocationNumberPrefix =
    sagaData.sellPoints?.[sellPointId]?.locationNumberPrefix ?? '?';
  const sagaGroupAllCompsInSameDocument =
    sagaData.sellPoints?.[sellPointId]?.groupAllCompsInSameDocument ?? false;
  const sagaPrepStations =
    sagaData.sellPoints?.[sellPointId]?.prepStations ?? {};
  const sagaGiftCards = sagaData.giftCards ?? {};
  const sagaExtraCharges = sagaData.extraCharges ?? {};
  const sagaTips = sagaData.tips ?? {};
  const sagaItemsCodeField = sagaData.itemsCodeField ?? 'sku';
  const sagaModifiersCodeField = sagaData.modifiersCodeField ?? 'sku';

  // get the sell point data
  const sellPointRequest = await dataProvider.getOne(RESOURCES.LOCATIONS, {
    id: sellPointId,
  });
  const sellPointData = sellPointRequest.data;

  const accountRequest = await dataProvider.getOne(RESOURCES.ACCOUNTS, {
    id: accountId,
  });
  const accountData = accountRequest.data;

  const locale = sellPointData.localization || 'en-IE';
  const language = locale.split('-')[0];

  const salesReportType = 'sales';
  const salesRawReport = await getReport(
    accountId,
    sellPointId,
    salesReportType,
    startDate,
    endDate,
    locale,
    {
      storageBucket: storage,
      database: rtdbInstance,
      getReportFromStorageFn: getReportsDataFromStorage,
      getReportFromRealtimeFn: getReportsDataFromRealtime,
    }
  );
  const salesFilteredReport = filterReport(
    salesReportType,
    salesRawReport,
    [
      {
        field: 'reportType',
        operator: '==',
        value: salesReportType,
      },
    ],
    []
  );
  const salesGroupedReport = groupReport(
    salesReportType,
    salesFilteredReport,
    ['date'],
    []
  );

  let salesReportsExist = false;
  let pmsReportsExist = false;
  let compReportsExist = false;
  if (salesGroupedReport.length > 0) {
    // we are going to mark the existence of the reports (sales, comp and pms)
    // for the bills field includes the pmsBills so we need to loop through the salesGroupedReport
    // and if we find a report with pmsBills higher than 0 we mark the pmsReportsExist as true
    // and also subtract the pmsBills from the bills field
    salesGroupedReport.forEach(item => {
      if (
        item.report.length === 1 &&
        isNumberPrimitive(item.report[0].pmsBills) &&
        item.report[0].pmsBills > 0
      ) {
        pmsReportsExist = true;
        item.report[0].bills! -= item.report[0].pmsBills;
      }
    });
    // loop through the salesGroupedReport and check if there is at least one item with a bills value greater than 0
    salesReportsExist = salesGroupedReport.some(item => {
      return (
        item.report.length === 1 &&
        isNumberPrimitive(item.report[0].bills) &&
        item.report[0].bills > 0
      );
    });
    // loop through the salesGroupedReport and check if there is at least one item with a compValue or compedBills value greater than 0
    compReportsExist = salesGroupedReport.some(item => {
      return (
        item.report.length === 1 &&
        ((isNumberPrimitive(item.report[0].compValue) &&
          item.report[0].compValue > 0) ||
          (isNumberPrimitive(item.report[0].compedBills) &&
            item.report[0].compedBills > 0))
      );
    });
  }

  // check if the sales report has bills
  if (!salesReportsExist && !pmsReportsExist && !compReportsExist) {
    throw new Error('No data found!');
  }

  // get the measure units data
  const measureUnits = await dataProvider
    .getList(RESOURCES.MEASURE_UNITS, {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
    })
    .then(res => res.data)
    .catch(() => []);
  const measureUnitIdToSymbol = measureUnits.reduce(
    (acc: any, measureUnit: any) => {
      acc[measureUnit.id] = measureUnit.symbol[language];
      return acc;
    },
    {}
  );
  const defaultMeasureUnit =
    measureUnitIdToSymbol[sellPointData.defaultMeasureUnit] ?? 'buc';

  // get all items and create a map of id to name and code
  const itemIdToName: { [id: string]: string } = {};
  const itemIdToSagaCode: { [id: string]: string } = {};
  const allItems = await dataProvider
    .getList(RESOURCES.HOSPITALITY_ITEMS, {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
    })
    .then(res => res.data)
    .catch(() => []);
  for (const item of allItems) {
    itemIdToName[item.id] = item.name;
    itemIdToSagaCode[item.id] = item[sagaItemsCodeField];
  }
  const modifierIdToName: { [id: string]: string } = {};
  const modifierIdToSagaCode: { [id: string]: string } = {};

  const itemsReportType = 'items';
  const modifiersReportType = 'modifiers';
  const giftCardsReportType = 'giftCards';
  const extraChargesReportType = 'extraCharges';
  const tipsReportType = 'tips';

  let itemsWithPmsItemsFilteredReport;
  let modifiersWithPmsModifiersFilteredReport;
  let giftCardsWithPmsGiftCardsFilteredReport;
  let extraChargesWithPmsExtraChargesFilteredReport;
  let tipsWithPmsTipsFilteredReport;

  let itemsGroupedReport;
  let modifiersGroupedReport;
  let giftCardsGroupedReport;
  let extraChargesGroupedReport;
  let tipsGroupedReport;
  if (salesReportsExist) {
    const itemsWithPmsItemsRawReport = await getReport(
      accountId,
      sellPointId,
      itemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    itemsWithPmsItemsFilteredReport = filterReport(
      itemsReportType,
      itemsWithPmsItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: itemsReportType,
        },
      ],
      []
    );

    const modifiersWithPmsModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      modifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    modifiersWithPmsModifiersFilteredReport = filterReport(
      modifiersReportType,
      modifiersWithPmsModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: modifiersReportType,
        },
      ],
      []
    );

    const giftCardsWithPmsGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      giftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    giftCardsWithPmsGiftCardsFilteredReport = filterReport(
      giftCardsReportType,
      giftCardsWithPmsGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: giftCardsReportType,
        },
      ],
      []
    );

    const extraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      extraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    extraChargesWithPmsExtraChargesFilteredReport = filterReport(
      extraChargesReportType,
      extraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: extraChargesReportType,
        },
      ],
      []
    );

    const tipsWithPmsTipsRawReport = await getReport(
      accountId,
      sellPointId,
      tipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    tipsWithPmsTipsFilteredReport = filterReport(
      tipsReportType,
      tipsWithPmsTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: tipsReportType,
        },
      ],
      []
    );
  }

  let pmsItemsGroupedReport;
  let pmsModifiersGroupedReport;
  let pmsGiftCardsGroupedReport;
  let pmsExtraChargesGroupedReport;
  let pmsTipsGroupedReport;
  if (!pmsReportsExist) {
    if (
      salesReportsExist &&
      itemsWithPmsItemsFilteredReport !== undefined &&
      modifiersWithPmsModifiersFilteredReport !== undefined &&
      giftCardsWithPmsGiftCardsFilteredReport !== undefined &&
      extraChargesWithPmsExtraChargesFilteredReport !== undefined &&
      tipsWithPmsTipsFilteredReport !== undefined
    ) {
      itemsGroupedReport = groupReport(
        itemsReportType,
        itemsWithPmsItemsFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );
      modifiersGroupedReport = groupReport(
        modifiersReportType,
        modifiersWithPmsModifiersFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );
      giftCardsGroupedReport = groupReport(
        giftCardsReportType,
        giftCardsWithPmsGiftCardsFilteredReport,
        ['date'],
        ['vat', 'type', 'price']
      );
      extraChargesGroupedReport = groupReport(
        extraChargesReportType,
        extraChargesWithPmsExtraChargesFilteredReport,
        ['date'],
        ['vat', 'name', 'price']
      );
      tipsGroupedReport = groupReport(
        tipsReportType,
        tipsWithPmsTipsFilteredReport,
        ['date'],
        ['vat']
      );
    }
  } else {
    const pmsItemsReportType = 'pmsItems';
    const pmsItemsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsItemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsItemsFilteredReport = filterReport(
      pmsItemsReportType,
      pmsItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsItemsReportType,
        },
      ],
      []
    );
    const pmsItemsBasicGroupedReport = groupReport(
      pmsItemsReportType,
      pmsItemsFilteredReport,
      ['date'],
      ['vat', 'prepStation', 'id', 'measureUnit', 'price']
    );
    pmsItemsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      pmsItemsReportType,
      pmsItemsBasicGroupedReport,
      ['prepStation']
    );

    const pmsModifiersReportType = 'pmsModifiers';
    const pmsModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      pmsModifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsModifiersFilteredReport = filterReport(
      pmsModifiersReportType,
      pmsModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsModifiersReportType,
        },
      ],
      []
    );
    const pmsModifiersBasicGroupedReport = groupReport(
      pmsModifiersReportType,
      pmsModifiersFilteredReport,
      ['date'],
      ['vat', 'prepStation', 'id', 'measureUnit', 'price']
    );
    pmsModifiersGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      pmsModifiersReportType,
      pmsModifiersBasicGroupedReport,
      ['prepStation']
    );

    const pmsGiftCardsReportType = 'pmsGiftCards';
    const pmsGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsGiftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsGiftCardsFilteredReport = filterReport(
      pmsGiftCardsReportType,
      pmsGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsGiftCardsReportType,
        },
      ],
      []
    );
    pmsGiftCardsGroupedReport = groupReport(
      pmsGiftCardsReportType,
      pmsGiftCardsFilteredReport,
      ['date'],
      ['type', 'price']
    );

    const pmsExtraChargesReportType = 'pmsExtraCharges';
    const pmsExtraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      pmsExtraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsExtraChargesFilteredReport = filterReport(
      pmsExtraChargesReportType,
      pmsExtraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsExtraChargesReportType,
        },
      ],
      []
    );
    pmsExtraChargesGroupedReport = groupReport(
      pmsExtraChargesReportType,
      pmsExtraChargesFilteredReport,
      ['date'],
      ['name', 'price']
    );

    const pmsTipsReportType = 'pmsTips';
    const pmsTipsRawReport = await getReport(
      accountId,
      sellPointId,
      pmsTipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const pmsTipsFilteredReport = filterReport(
      pmsTipsReportType,
      pmsTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: pmsTipsReportType,
        },
      ],
      []
    );
    pmsTipsGroupedReport = groupReport(
      pmsTipsReportType,
      pmsTipsFilteredReport,
      ['date'],
      []
    );

    if (
      salesReportsExist &&
      itemsWithPmsItemsFilteredReport !== undefined &&
      modifiersWithPmsModifiersFilteredReport !== undefined &&
      giftCardsWithPmsGiftCardsFilteredReport !== undefined &&
      extraChargesWithPmsExtraChargesFilteredReport !== undefined &&
      tipsWithPmsTipsFilteredReport !== undefined
    ) {
      const itemsWithoutPmsItemsFilteredReport = subtractReports(
        itemsWithPmsItemsFilteredReport,
        pmsItemsFilteredReport,
        itemsReportType
      );
      itemsGroupedReport = groupReport(
        itemsReportType,
        itemsWithoutPmsItemsFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );

      const modifiersWithoutPmsModifiersFilteredReport = subtractReports(
        modifiersWithPmsModifiersFilteredReport,
        pmsModifiersFilteredReport,
        modifiersReportType
      );
      modifiersGroupedReport = groupReport(
        modifiersReportType,
        modifiersWithoutPmsModifiersFilteredReport,
        ['date'],
        ['vat', 'prepStation', 'id', 'measureUnit', 'price']
      );

      const giftCardsWithoutPmsGiftCardsFilteredReport = subtractReports(
        giftCardsWithPmsGiftCardsFilteredReport,
        pmsGiftCardsFilteredReport,
        giftCardsReportType
      );
      giftCardsGroupedReport = groupReport(
        giftCardsReportType,
        giftCardsWithoutPmsGiftCardsFilteredReport,
        ['date'],
        ['vat', 'type', 'price']
      );

      const extraChargesWithoutPmsExtraChargesFilteredReport = subtractReports(
        extraChargesWithPmsExtraChargesFilteredReport,
        pmsExtraChargesFilteredReport,
        extraChargesReportType
      );
      extraChargesGroupedReport = groupReport(
        extraChargesReportType,
        extraChargesWithoutPmsExtraChargesFilteredReport,
        ['date'],
        ['vat', 'name', 'price']
      );

      const tipsWithoutPmsTipsFilteredReport = subtractReports(
        tipsWithPmsTipsFilteredReport,
        pmsTipsFilteredReport,
        tipsReportType
      );
      tipsGroupedReport = groupReport(
        tipsReportType,
        tipsWithoutPmsTipsFilteredReport,
        ['date'],
        ['vat']
      );
    }
  }

  let compedItemsGroupedReport;
  let compedModifiersGroupedReport;
  let compedGiftCardsGroupedReport;
  let compedExtraChargesGroupedReport;
  let compedTipsGroupedReport;
  if (compReportsExist) {
    const compedItemsReportType = 'compedItems';
    const compedItemsRawReport = await getReport(
      accountId,
      sellPointId,
      compedItemsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedItemsFilteredReport = filterReport(
      compedItemsReportType,
      compedItemsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedItemsReportType,
        },
      ],
      []
    );
    const compedItemsBasicGroupedReport = groupReport(
      compedItemsReportType,
      compedItemsFilteredReport,
      ['date'],
      ['reason', 'vat', 'prepStation', 'id', 'measureUnit', 'price']
    );
    compedItemsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      compedItemsReportType,
      compedItemsBasicGroupedReport,
      ['reason', 'prepStation']
    );

    const compedModifiersReportType = 'compedModifiers';
    const compedModifiersRawReport = await getReport(
      accountId,
      sellPointId,
      compedModifiersReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedModifiersFilteredReport = filterReport(
      compedModifiersReportType,
      compedModifiersRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedModifiersReportType,
        },
      ],
      []
    );
    const compedModifiersBasicGroupedReport = groupReport(
      compedModifiersReportType,
      compedModifiersFilteredReport,
      ['date'],
      ['reason', 'vat', 'prepStation', 'id', 'measureUnit', 'price']
    );
    compedModifiersGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedModifiersReportType,
        compedModifiersBasicGroupedReport,
        ['reason', 'prepStation']
      );

    const compedGiftCardsReportType = 'compedGiftCards';
    const compedGiftCardsRawReport = await getReport(
      accountId,
      sellPointId,
      compedGiftCardsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedGiftCardsFilteredReport = filterReport(
      compedGiftCardsReportType,
      compedGiftCardsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedGiftCardsReportType,
        },
      ],
      []
    );
    const compedGiftCardsBasicGroupedReport = groupReport(
      compedGiftCardsReportType,
      compedGiftCardsFilteredReport,
      ['date'],
      ['reason', 'type', 'price']
    );
    compedGiftCardsGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedGiftCardsReportType,
        compedGiftCardsBasicGroupedReport,
        ['reason']
      );

    const compedExtraChargesReportType = 'compedExtraCharges';
    const compedExtraChargesRawReport = await getReport(
      accountId,
      sellPointId,
      compedExtraChargesReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedExtraChargesFilteredReport = filterReport(
      compedExtraChargesReportType,
      compedExtraChargesRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedExtraChargesReportType,
        },
      ],
      []
    );
    const compedExtraChargesBasicGroupedReport = groupReport(
      compedExtraChargesReportType,
      compedExtraChargesFilteredReport,
      ['date'],
      ['reason', 'name', 'price']
    );
    compedExtraChargesGroupedReport =
      groupGroupedReportBySpecificFieldsHierarchical(
        compedExtraChargesReportType,
        compedExtraChargesBasicGroupedReport,
        ['reason']
      );

    const compedTipsReportType = 'compedTips';
    const compedTipsRawReport = await getReport(
      accountId,
      sellPointId,
      compedTipsReportType,
      startDate,
      endDate,
      locale,
      {
        storageBucket: storage,
        database: rtdbInstance,
        getReportFromStorageFn: getReportsDataFromStorage,
        getReportFromRealtimeFn: getReportsDataFromRealtime,
      }
    );
    const compedTipsFilteredReport = filterReport(
      compedTipsReportType,
      compedTipsRawReport,
      [
        {
          field: 'reportType',
          operator: '==',
          value: compedTipsReportType,
        },
      ],
      []
    );
    const compedTipsBasicGroupedReport = groupReport(
      compedTipsReportType,
      compedTipsFilteredReport,
      ['date'],
      ['reason']
    );
    compedTipsGroupedReport = groupGroupedReportBySpecificFieldsHierarchical(
      compedTipsReportType,
      compedTipsBasicGroupedReport,
      ['reason']
    );
  }

  // build the WinMentorMonetar object
  const sagaExport: SagaExportPackage = {};
  if (
    salesReportsExist &&
    itemsGroupedReport !== undefined &&
    modifiersGroupedReport !== undefined &&
    giftCardsGroupedReport !== undefined &&
    extraChargesGroupedReport !== undefined &&
    tipsGroupedReport !== undefined
  ) {
    // add the sales report
    for (const item of salesGroupedReport) {
      if (
        item.report.length === 1 &&
        typeof item.report[0].bills === 'number' &&
        item.report[0].bills > 0
      ) {
        const dataLucru = item.date;
        const dataLucruDate = new Date(dataLucru);
        sagaExport[dataLucru] = {
          sales: {
            nrDoc: `${sagaLocationNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
            serie: 'VANZARE',
            data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
            tip: 'B',
            informatiiSuplimentare: `Vanzare - ${dataLucru}`,
            moneda: sellPointData.currency,
            articole: [],
          },
        };
      }
    }
    // add the items report
    for (const item of itemsGroupedReport) {
      const dataLucru = item.date;
      if (sagaExport[dataLucru].sales) {
        for (const article of item.report) {
          sagaExport[dataLucru].sales.articole.push({
            gestiune:
              sagaPrepStations[article.prepStation] ??
              `???_${article.prepStation}`,
            descriere: itemIdToName[article.id] ?? `???_${article.id}`,
            codArticolClient: itemIdToSagaCode[article.id]
              ? itemIdToSagaCode[article.id]
              : itemIdToName[article.id]
                ? `???_${itemIdToName[article.id]}`
                : `???_${article.id}`,
            guidCodArticol: article.id,
            um:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            procTva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
        }
      }
    }
    // add the modifiers report
    for (const item of modifiersGroupedReport) {
      const dataLucru = item.date;
      if (sagaExport[dataLucru].sales) {
        for (const article of item.report) {
          sagaExport[dataLucru].sales.articole.push({
            gestiune:
              sagaPrepStations[article.prepStation] ??
              `???_${article.prepStation}`,
            descriere: modifierIdToName[article.id] ?? `???_${article.id}`,
            codArticolClient: modifierIdToSagaCode[article.id]
              ? modifierIdToSagaCode[article.id]
              : modifierIdToName[article.id]
                ? `???_${modifierIdToName[article.id]}`
                : `???_${article.id}`,
            guidCodArticol: article.id,
            um:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            procTva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
        }
      }
    }
    // add the gift cards report
    for (const item of giftCardsGroupedReport) {
      const dataLucru = item.date;
      if (sagaExport[dataLucru].sales) {
        for (const article of item.report) {
          sagaExport[dataLucru].sales.articole.push({
            gestiune: sagaGiftCards[article.type]?.symbol
              ? sagaGiftCards[article.type].symbol
              : article.type == '@digital'
                ? '???_GiftCardDigital'
                : '???_GiftCardFizic',
            descriere:
              article.type == '@digital'
                ? 'Gift Card Digital'
                : 'Gift Card Fizic',
            codArticolClient: sagaGiftCards[article.type]?.code
              ? sagaGiftCards[article.type].code
              : article.type == '@digital'
                ? '???_GiftCardDigital'
                : '???_GiftCardFizic',
            guidCodArticol: article.type,
            um: defaultMeasureUnit,
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            procTva: article.vat,
            discount: Number(
              (
                ((article.discountsValue ?? 0) +
                  (article.couponsValue ?? 0) +
                  (article.promotionsValue ?? 0)) /
                10000
              ).toFixed(2)
            ),
          });
        }
      }
    }
    // add the extra charges report
    for (const item of extraChargesGroupedReport) {
      const dataLucru = item.date;
      if (sagaExport[dataLucru].sales) {
        for (const article of item.report) {
          sagaExport[dataLucru].sales.articole.push({
            gestiune: sagaExtraCharges[article.name]?.symbol
              ? sagaExtraCharges[article.name].symbol
              : `???_${article.name}`,
            descriere: article.name,
            codArticolClient: sagaExtraCharges[article.name]?.code
              ? sagaExtraCharges[article.name].code
              : `???_${article.name}`,
            guidCodArticol: article.name,
            um: defaultMeasureUnit,
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: Number((article.price / 10000).toFixed(2)),
            procTva: article.vat,
            discount: Number(
              ((article.promotionsValue ?? 0) / 10000).toFixed(2)
            ),
          });
        }
      }
    }
    // add the tips report
    for (const item of tipsGroupedReport) {
      const dataLucru = item.date;
      if (sagaExport[dataLucru].sales) {
        for (const article of item.report) {
          sagaExport[dataLucru].sales.articole.push({
            gestiune: sagaTips.symbol ?? '???_Bacsis',
            descriere: 'Bacsis',
            codArticolClient: sagaTips.code ?? '???_Bacsis',
            guidCodArticol: 'bacsis',
            um: defaultMeasureUnit,
            cantitate: Number((1).toFixed(3)),
            pret: Number((article.value / 10000).toFixed(2)),
            procTva: article.vat,
            discount: 0,
          });
        }
      }
    }
  }

  if (
    compReportsExist &&
    compedItemsGroupedReport !== undefined &&
    compedModifiersGroupedReport !== undefined &&
    compedGiftCardsGroupedReport !== undefined &&
    compedExtraChargesGroupedReport !== undefined &&
    compedTipsGroupedReport !== undefined
  ) {
    // add the comped items report
    for (const pDate of compedItemsGroupedReport) {
      const dataLucru = pDate.date as string;
      const dataLucruDate = new Date(dataLucru!);
      for (const pReason of pDate.report) {
        const currentReason = pReason.groupedBy?.value as string;
        for (const pPrep of pReason.subReport!) {
          const currentPrep = pPrep.groupedBy?.value;
          // Use compCvStockSymbol if available, otherwise use the original logic
          const currentCompKey = sagaGroupAllCompsInSameDocument
            ? 'Gratuitati'
            : currentReason;

          if (!get(sagaExport, [dataLucru, 'comps', currentCompKey])) {
            set(sagaExport, [dataLucru, 'comps', currentCompKey], {
              nrDoc: `${sagaLocationNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
              serie: 'GRATUITATI',
              data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
              tip: 'B',
              informatiiSuplimentare: `${currentCompKey} - ${dataLucru}`,
              moneda: sellPointData.currency,
              articole: [],
            });
          }
          for (const article of pPrep.subReport!) {
            sagaExport?.[dataLucru]?.comps?.[currentCompKey]?.articole?.push({
              gestiune:
                sagaPrepStations[article.prepStation!] ??
                `???_${article.prepStation!}`,
              descriere: itemIdToName[article.id!] ?? `???_${article.id}`,
              codArticolClient: itemIdToSagaCode[article.id!]
                ? itemIdToSagaCode[article.id!]
                : itemIdToName[article.id!]
                  ? `???_${itemIdToName[article.id!]}`
                  : `???_${article.id}`,
              guidCodArticol: article.id!,
              um:
                article.measureUnit === 'undefined'
                  ? defaultMeasureUnit
                  : (measureUnitIdToSymbol[article.measureUnit!] ??
                    defaultMeasureUnit),
              cantitate: Number((article.quantity / 1000).toFixed(3)),
              pret: 0,
              procTva: article.vat!,
              discount: 0,
            });
          }
        }
      }
    }
    // add the comped modifiers report
    for (const pDate of compedModifiersGroupedReport) {
      const dataLucru = pDate.date as string;
      const dataLucruDate = new Date(dataLucru!);
      for (const pReason of pDate.report) {
        const currentReason = pReason.groupedBy?.value as string;
        for (const pPrep of pReason.subReport!) {
          const currentPrep = pPrep.groupedBy?.value;
          // Use compCvStockSymbol if available, otherwise use the original logic
          const currentCompKey = sagaGroupAllCompsInSameDocument
            ? 'Gratuitati'
            : currentReason;
          if (!get(sagaExport, [dataLucru, 'comps', currentCompKey])) {
            set(sagaExport, [dataLucru, 'comps', currentCompKey], {
              nrDoc: `${sagaLocationNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
              serie: 'GRATUITATI',
              data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
              tip: 'B',
              informatiiSuplimentare: `${currentCompKey} - ${dataLucru}`,
              moneda: sellPointData.currency,
              articole: [],
            });
          }
          for (const article of pPrep.subReport!) {
            sagaExport?.[dataLucru]?.comps?.[currentCompKey]?.articole?.push({
              gestiune:
                sagaPrepStations[article.prepStation!] ??
                `???_${article.prepStation!}`,
              descriere: modifierIdToName[article.id!] ?? `???_${article.id}`,
              codArticolClient: modifierIdToSagaCode[article.id!]
                ? modifierIdToSagaCode[article.id!]
                : modifierIdToName[article.id!]
                  ? `???_${modifierIdToName[article.id!]}`
                  : `???_${article.id}`,
              guidCodArticol: article.id!,
              um:
                article.measureUnit === 'undefined'
                  ? defaultMeasureUnit
                  : (measureUnitIdToSymbol[article.measureUnit!] ??
                    defaultMeasureUnit),
              cantitate: Number((article.quantity / 1000).toFixed(3)),
              pret: 0,
              procTva: article.vat!,
              discount: 0,
            });
          }
        }
      }
    }
    // TODO! what to do with gift cards? extra charges? tips?
  }
  if (
    pmsReportsExist &&
    pmsItemsGroupedReport !== undefined &&
    pmsModifiersGroupedReport !== undefined &&
    pmsGiftCardsGroupedReport !== undefined &&
    pmsExtraChargesGroupedReport !== undefined &&
    pmsTipsGroupedReport !== undefined
  ) {
    // add the pms items report
    for (const pDate of pmsItemsGroupedReport) {
      const dataLucru = pDate.date as string;
      const dataLucruDate = new Date(dataLucru!);
      for (const pPrep of pDate.report) {
        const currentPrep = pPrep.groupedBy?.value;
        if (!get(sagaExport, [dataLucru, 'pms'])) {
          set(sagaExport, [dataLucru, 'pms'], {
            nrDoc: `${sagaLocationNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
            serie: 'HOTEL',
            data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
            tip: 'B',
            informatiiSuplimentare: `Cont Hotel - ${dataLucru}`,
            moneda: sellPointData.currency,
            articole: [],
          });
        }
        for (const article of pPrep.subReport!) {
          sagaExport?.[dataLucru]?.pms?.articole?.push({
            gestiune:
              sagaPrepStations[article.prepStation!] ??
              `???_${article.prepStation!}`,
            descriere: itemIdToName[article.id!] ?? `???_${article.id}`,
            codArticolClient: itemIdToSagaCode[article.id!]
              ? itemIdToSagaCode[article.id!]
              : itemIdToName[article.id!]
                ? `???_${itemIdToName[article.id!]}`
                : `???_${article.id}`,
            guidCodArticol: article.id!,
            um:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit!] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: 0,
            procTva: article.vat!,
            discount: 0,
          });
        }
      }
    }

    for (const pDate of pmsModifiersGroupedReport) {
      const dataLucru = pDate.date as string;
      const dataLucruDate = new Date(dataLucru!);
      for (const pPrep of pDate.report) {
        const currentPrep = pPrep.groupedBy?.value;
        if (!get(sagaExport, [dataLucru, 'pms'])) {
          set(sagaExport, [dataLucru, 'pms'], {
            nrDoc: `${sagaLocationNumberPrefix}${dataLucruDate.getFullYear().toString().slice(-2)}${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}${dataLucruDate.getDate().toString().padStart(2, '0')}`,
            serie: 'HOTEL',
            data: `${dataLucruDate.getDate().toString().padStart(2, '0')}.${(dataLucruDate.getMonth() + 1).toString().padStart(2, '0')}.${dataLucruDate.getFullYear()}`,
            tip: 'B',
            informatiiSuplimentare: `Cont Hotel - ${dataLucru}`,
            moneda: sellPointData.currency,
            articole: [],
          });
        }
        for (const article of pPrep.subReport!) {
          sagaExport?.[dataLucru]?.pms?.articole?.push({
            gestiune:
              sagaPrepStations[article.prepStation!] ??
              `???_${article.prepStation!}`,
            descriere: modifierIdToName[article.id!] ?? `???_${article.id}`,
            codArticolClient: modifierIdToSagaCode[article.id!]
              ? modifierIdToSagaCode[article.id!]
              : modifierIdToName[article.id!]
                ? `???_${modifierIdToName[article.id!]}`
                : `???_${article.id}`,
            guidCodArticol: article.id!,
            um:
              article.measureUnit === 'undefined'
                ? defaultMeasureUnit
                : (measureUnitIdToSymbol[article.measureUnit!] ??
                  defaultMeasureUnit),
            cantitate: Number((article.quantity / 1000).toFixed(3)),
            pret: 0,
            procTva: article.vat!,
            discount: 0,
          });
        }
      }
    }
    // TODO! what to do with gift cards? extra charges? tips?
  }

  // Generate XML using fast-xml-parser
  const xmlObject = buildSagaXmlObject(sagaExport, sellPointData, accountData);
  const builder = new XMLBuilder({
    ignoreAttributes: false,
    format: true,
    indentBy: '  ',
    suppressEmptyNode: true,
  });

  return builder.build(xmlObject);
}
