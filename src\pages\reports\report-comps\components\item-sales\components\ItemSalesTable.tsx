import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListHospitalityCategoriesLive, useGetListLocationsLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../../../types/globals';
import ExtraDataModal from './ExtraDataItemsModal';

type TableRow = {
  id: string;
  variant: string;
  groupId: string;
  discountsValue: number;
  couponsValue: number;
  promotionsValue: number;
  netValue: number;
  name: string;
  reason: string;
  measureUnit: string;
  vat: number | undefined;
  groupName: string;
  prepStation: string;
  quantity: string;
  value: string;
  items?: TableRow[];
  subItems?: TableRow[];
  extraData?: { [key: string]: any };
};

export default function ItemSalesTable({
  tableData,
  fields,
  setFields,
  onChangeGrouping,
  reportType,
  formattedFilters,
  rawData,
  composedFilters,
  filters,
  groupingItems,
  updateCompsData,
}: {
  reportType: string;
  rawData: any;
  formattedFilters: any;
  composedFilters: any;
  filters: any;
  tableData: any;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  fields: FieldOption[];
  groupingItems: string[];
  updateCompsData?: any;
  onChangeGrouping?: (items: any[]) => void;
}) {
  const { t } = useTranslation();
  const itemsTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.id = 'Total';
      totalItemsData.name = 'Total';
      totalItemsData.reason = '';
      totalItemsData.groupName = '';
      totalItemsData.subItems = [];
      totalItemsData.vat = undefined;
      totalItemsData.groupId = '';
      totalItemsData.prepStation = '';
      totalItemsData.measureUnit = '';

      mappedTableData = [...mappedTableData, totalItemsData];
      updateCompsData('totalItems', totalItemsData);
      return mappedTableData;
    }
    updateCompsData('totalItems', {});
    return [];
  }, [tableData]);

  const itemSalesTablConfig: ColumnConfig<TableRow>[] = useMemo(
    () => [
      {
        id: 'id',
        textAlign: 'start',
        label: t('shared.item_capitalize'),
        render: (row: TableRow) => {
          if (row.subItems && row.subItems.length > 0) {
            return (
              <span
                style={{
                  fontSize: '14px',
                  whiteSpace: 'nowrap',
                }}
              >
                {row.id.includes('@none')
                  ? 'Regular'
                  : row?.id.includes('vat')
                    ? capitalize(row?.id?.toLowerCase())
                    : row.id}
              </span>
            );
          }
          return (
            <div
              style={{
                fontSize: '14px',
                minWidth: '230px',
                whiteSpace: 'normal',
              }}
            >
              {row.name === '@none'
                ? 'Regular'
                : capitalize(row?.name?.toLowerCase()) || row.id}{' '}
            </div>
          );
        },
      },
      {
        id: 'reason',
        textAlign: 'end',
        label: t('reportsPage.reason'),
        render: (row: TableRow) => {
          return <>{row.reason === '@na' ? 'N/A' : row.reason}</>;
        },
      },
      {
        id: 'vat',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
            </div>
          );
        },
        label: t('shared.tva'),
        textAlign: 'end',
      },
      {
        id: 'prepStation',
        textAlign: 'end',
        label: t('reportsPage.prepStation'),
      },
      {
        id: 'groupId',
        textAlign: 'end',
        label: t('shared.category_capitalize'),
        render: (row: TableRow) => {
          return <>{row.groupName}</>;
        },
      },
      {
        id: 'measureUnit',
        label: t('reportsPage.unit'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{row.measureUnit === 'undefined' ? '' : row.measureUnit} </>;
        },
      },
      {
        id: 'quantity',
        textAlign: 'end',
        label: t('reportsPage.itemsComped'),
        render: (row: TableRow) => {
          return (
            <>
              {Intl.NumberFormat('ro-RO').format(Number(row.quantity) / 1000)}{' '}
            </>
          );
        },
      },

      {
        id: 'value',
        textAlign: 'end',
        label: t('reportsPage.grossComps'),
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.value))}</>;
        },
      },
      {
        id: 'discountsValue',
        label: t('reportsPage.discounts'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {row?.discountsValue
                ? '- ' + formatAndDivideNumber(row?.discountsValue)
                : ''}
            </div>
          );
        },
      },
      {
        id: 'couponsValue',
        label: t('reportsPage.coupons'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {row?.couponsValue
                ? '- ' + formatAndDivideNumber(row?.couponsValue)
                : ''}
            </div>
          );
        },
      },
      {
        id: 'promotionsValue',
        label: t('reportsPage.promotions'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {row?.promotionsValue
                ? '- ' + formatAndDivideNumber(row?.promotionsValue)
                : ''}
            </div>
          );
        },
      },
      {
        id: 'netValue',
        label: t('reportsPage.netComps'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(row.netValue)}</>;
        },
      },
    ],
    [t]
  );

  const columnsToFilter = useMemo(() => {
    const columns = [
      'groupId',
      'measureUnit',
      'variant',
      'quantity',
      'vat',
      'couponsValue',
      'discountsValue',
      'promotionsValue',
      'prepStation',
      'value',
      'reason',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'vat', label: t('shared.tva') },
    { value: 'groupId', label: t('shared.category_capitalize') },
    { value: 'reason', label: t('reportsPage.reason') },
    { value: 'prepStation', label: t('reportsPage.prepStation') },
    { value: 'measureUnit', label: t('reportsPage.unit') },
  ];

  const { dateRange, timeRange } = useGlobalResourceFilters();
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();
  const { data: items } = useGetListLive('items');
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const handleExport = () => {
    const title = 'Report comped items';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Item',
        'Reason',
        'VAT',
        'Prep Station',
        'Category',
        'Unit',
        'Items Comped',
        'Gross Comps',
        'Discounts',
        'Coupons',
        'Promotions',
        'Net Comps',
      ].join(','),
      ...itemsTableData?.map((el: any) => el.subItems?.map((report: any) =>
             [
              items?.find(item => item.id === report.id)?.name || report.id,
              el.name,
              report.vat,
              report.prepStation,
              categories?.find(category => category.id === report.groupId)?.name || report.groupId,
              report.measureUnit,
              report.quantity / 1000,
              report.value / 10000,
              -report.discountsValue / 10000 || 0,
              -report.couponsValue / 10000 || 0,
              report.promotionsValue / 10000 || 0,
              report.netValue / 10000 || 0,
            ].join(',')
          )
        ).flat(),
        [
          'Total',
          '',
          '',
          '',
          '',
          Number(itemsTableData[itemsTableData.length - 1].quantity ) / 1000 || 0,
          Number(itemsTableData[itemsTableData.length - 1].value) / 10000 || 0,
          -Number(itemsTableData[itemsTableData.length - 1].discountsValue) / 10000 || 0,
          -Number(itemsTableData[itemsTableData.length - 1].couponsValue) / 10000 || 0,
          Number(itemsTableData[itemsTableData.length - 1].promotionsValue) / 10000 || 0,
          Number(itemsTableData[itemsTableData.length - 1].netValue) / 10000 || 0,
        ].join(','),
    ].join('\n');
   
    downloadCSV(csvContent, 'comped-items');
  };

  return (
    <>
      <Box sx={{ py: 2, width: '100%' }}>
        <GroupingTable
          config={itemSalesTablConfig}
          data={itemsTableData}
          fields={fields}
          separateFirstColumn={true}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          enableInfoModal={true}
          fixedFirstColumn={true}
          exportCSV={true}
          handleExport={handleExport}
          renderModalContent={rowData => (
            <ExtraDataModal
              extraData={{
                composedFilters,
                rawData,
                reportType,
                filters,
                rowData,
                formattedFilters,
              }}
            />
          )}
        />
      </Box>
    </>
  );
}
