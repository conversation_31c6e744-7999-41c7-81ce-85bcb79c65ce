export type CurrencyType = 'USD' | 'RON' | 'EUR';
export type FormatNumberType =
  | 'currency'
  | 'number'
  | 'percentage'
  | CurrencyType;

const USDollar = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

const RONFormatter = new Intl.NumberFormat('ro-RO', {
  style: 'currency',
  currency: 'LEI',
});

export const formatNumber = (
  number: number | string | undefined = 0,
  type: FormatNumberType = 'number'
) => {
  if (!number) {
    number = 0;
  }

  if (type === 'percentage') return `${(Number(number) * 100).toFixed(2)}%`;
  if (type === 'RON') return RONFormatter.format(Number(number));
  if (type === 'USD') return USDollar.format(Number(number));
  if (type === 'currency') return RONFormatter.format(Number(number));

  return number.toLocaleString('ro-RO');
};
