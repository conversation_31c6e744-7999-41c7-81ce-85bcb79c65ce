import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { AttachFile as AttachFileIcon } from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  Collapse,
  InputAdornment,
  Paper,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { FilePreviewModal } from '~/components/atoms/FilePreviewModal';
import { ImageEditorModal } from '~/components/molecules/ImageEditorModal';
import { FileDropzone } from './components/FileDropzone/FileDropzone';
import { FileList } from './components/FileList/FileList';
import { ValidationDisplay } from './components/FileValidation/ValidationDisplay';
import { useFileList } from './hooks/useFileList';
import { useFileUpload } from './hooks/useFileUpload';
import { useFileValidation } from './hooks/useFileValidation';
import { useImageEditor } from './hooks/useImageEditor';
import { FileUploadComponentProps } from './types';
import { mergeWithDefaults, validateConfig } from './utils/fileUploadConfig';

/**
 * Main FileUploadComponent - replaces MuiFileUploadInput with modular architecture
 */
const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  value = [],
  onChange,
  config: userConfig,
  label,
  helperText,
  error = false,
  errorText,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  // Merge user config with defaults
  const config = useMemo(() => mergeWithDefaults(userConfig), [userConfig]);

  // Validate configuration
  const configErrors = useMemo(() => validateConfig(config), [config]);

  // State
  const [previewFile, setPreviewFile] = useState<any>(null);
  const [isFileListExpanded, setIsFileListExpanded] = useState(false);

  // Initialize hooks
  const fileList = useFileList(value, onChange);
  const validation = useFileValidation(config);
  const upload = useFileUpload(config);

  // Update image editor with correct callback
  const imageEditorWithCallback = useImageEditor(config, {
    onImageEdit: config.callbacks?.onImageEdit,
    onImageEditCancel: config.callbacks?.onImageEditCancel,
    onImageEditStart: config.callbacks?.onImageEditStart,
    onUploadImageWithCroppedVariants: async (
      originalFile: File,
      croppedFiles: File[]
    ): Promise<void> => {
      const uploadedFile = await upload.uploadImageWithCroppedVariants(
        originalFile,
        croppedFiles
      );
      fileList.addFile(uploadedFile);
    },
  });

  // Handle file selection
  const handleFilesSelected = useCallback(
    async (selectedFiles: File[]) => {
      if (selectedFiles.length === 0) return;

      // Validate files
      validation.validate(
        selectedFiles,
        fileList.files.map(() => ({}) as File)
      );

      if (validation.hasErrors && !config.validation?.allowInvalidFiles) {
        return; // Stop if there are errors and invalid files are not allowed
      }

      // Get valid files
      const validFiles = validation.getValidFiles(selectedFiles);
      if (validFiles.length === 0) return;

      try {
        // Check if any files need image editing
        const filesToEdit = await Promise.all(
          validFiles.map(async file => ({
            file,
            needsEditing:
              await imageEditorWithCallback.shouldFileBeEdited(file),
          }))
        );

        const imagesToEdit = filesToEdit
          .filter(f => f.needsEditing)
          .map(f => f.file);
        const filesToUploadDirectly = filesToEdit
          .filter(f => !f.needsEditing)
          .map(f => f.file);

        // Upload files that don't need editing
        if (filesToUploadDirectly.length > 0) {
          const uploadedFiles = await upload.upload(filesToUploadDirectly);
          fileList.addFiles(uploadedFiles);
        }

        // Start editing process for images that need editing
        if (imagesToEdit.length > 0) {
          await imageEditorWithCallback.startEditingProcess(imagesToEdit);
        }

        // Input will be reset automatically by the FileDropzone component
      } catch (error) {
        console.error('Error handling file selection:', error);
      }
    },
    [config, validation, fileList, upload, imageEditorWithCallback]
  );

  // Handle file removal
  const handleRemoveFile = useCallback(
    (index: number) => {
      const fileToRemove = fileList.files[index];
      fileList.removeFile(index);

      // Call lifecycle callback
      if (fileToRemove) {
        config.callbacks?.onFileDeleted?.(fileToRemove);
      }

      // Close expanded list if only one file or no files remain
      if (fileList.files.length <= 2) {
        // Will be 1 after removal
        setIsFileListExpanded(false);
      }
    },
    [fileList, config.callbacks]
  );

  // Handle file preview
  const handleFilePreview = useCallback((file: any) => {
    setPreviewFile(file);
  }, []);

  // Handle close preview
  const handleClosePreview = useCallback(() => {
    setPreviewFile(null);
  }, []);

  // Generate computed helper text
  const computedHelperText = useMemo(() => {
    if (helperText) return helperText;

    // Simple helper text generation
    const fileTypeText = config.fileType === 'images' ? 'images' : 'files';
    const multipleText = config.multiple
      ? `up to ${config.maxFiles} ${fileTypeText}`
      : `a ${fileTypeText.slice(0, -1)}`;
    return `Select ${multipleText}`;
  }, [config, helperText]);

  // Show configuration errors in development
  useEffect(() => {
    if (configErrors.length > 0 && process.env.NODE_ENV === 'development') {
      console.warn('FileUploadComponent configuration errors:', configErrors);
    }
  }, [configErrors]);

  // Close expanded file list when files count becomes 1 or 0
  useEffect(() => {
    if (fileList.fileCount <= 1) {
      setIsFileListExpanded(false);
    }
  }, [fileList.fileCount]);

  // Render compact variant
  if (config.ui?.variant === 'compact') {
    return (
      <Box sx={{ width: '100%', position: 'relative' }}>
        <TextField
          fullWidth
          variant="outlined"
          label={label}
          placeholder={
            fileList.isEmpty
              ? config.ui?.placeholder ||
                (config.multiple
                  ? t('menu.selectFiles', 'Select files')
                  : t('menu.selectFile', 'Select file'))
              : undefined
          }
          value={
            fileList.isEmpty ? '' : `${fileList.fileCount} file(s) selected`
          }
          disabled={config.ui?.disabled || upload.uploading}
          error={error}
          helperText={errorText || computedHelperText}
          slotProps={{
            input: {
              readOnly: true,
              endAdornment:
                !config.ui?.disabled && !config.ui?.readOnly ? (
                  <InputAdornment position="end">
                    <FileDropzone
                      variant="compact"
                      onFilesSelected={handleFilesSelected}
                      disabled={config.ui?.disabled || upload.uploading}
                      multiple={config.multiple}
                      acceptedTypes={config.acceptedTypes}
                      maxFiles={config.maxFiles}
                      uploading={upload.uploading}
                    >
                      {upload.uploading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <AttachFileIcon
                          fontSize="small"
                          sx={{
                            cursor:
                              config.ui?.disabled || upload.uploading
                                ? 'not-allowed'
                                : 'pointer',
                            color: config.ui?.disabled
                              ? 'action.disabled'
                              : 'action.active',
                          }}
                        />
                      )}
                    </FileDropzone>
                  </InputAdornment>
                ) : undefined,
            },
          }}
        />

        {/* Expandable file list for compact variant */}
        {fileList.hasFiles && (
          <Collapse in={isFileListExpanded}>
            <Box
              sx={{
                mt: 1,
                p: 2,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
              }}
            >
              <FileList
                files={fileList.files}
                onRemoveFile={handleRemoveFile}
                onReorderFiles={
                  config.multiple ? fileList.reorderFiles : undefined
                }
                onFilePreview={handleFilePreview}
                disabled={config.ui?.disabled}
                readOnly={config.ui?.readOnly}
                multiple={config.multiple}
                variant="compact"
                privateFileContext={config.privateContext}
                showSummary={false}
              />
            </Box>
          </Collapse>
        )}

        {/* Validation display */}
        <ValidationDisplay
          errors={validation.errors}
          warnings={validation.warnings}
          onDismissError={validation.removeValidationError}
        />

        {/* Modals */}
        <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
        <ImageEditorModal
          file={imageEditorWithCallback.currentFile}
          isOpen={imageEditorWithCallback.isOpen}
          onClose={imageEditorWithCallback.closeEditor}
          onSave={imageEditorWithCallback.saveEdits}
          onCancel={imageEditorWithCallback.cancelEdits}
          config={imageEditorWithCallback.editorConfig || {}}
          fileType={config.fileType}
          uploading={upload.uploading}
          batchInfo={imageEditorWithCallback.batchInfo}
          aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
        />
      </Box>
    );
  }

  // Render default variant
  return (
    <Box sx={{ width: '100%' }}>
      {/* Upload Area - Hidden in disabled and readOnly modes */}
      {!config.ui?.disabled && !config.ui?.readOnly && (
        <FileDropzone
          onFilesSelected={handleFilesSelected}
          disabled={config.ui?.disabled || upload.uploading}
          multiple={config.multiple}
          acceptedTypes={config.acceptedTypes}
          maxFiles={config.maxFiles}
          placeholder={config.ui?.placeholder}
          error={error}
          uploading={upload.uploading}
        />
      )}

      {/* File List */}
      <FileList
        files={fileList.files}
        onRemoveFile={handleRemoveFile}
        onReorderFiles={config.multiple ? fileList.reorderFiles : undefined}
        onFilePreview={handleFilePreview}
        disabled={config.ui?.disabled}
        readOnly={config.ui?.readOnly}
        multiple={config.multiple}
        variant="default"
        privateFileContext={config.privateContext}
      />

      {/* Show "No files uploaded" message in disabled and readOnly modes */}
      {fileList.isEmpty && (config.ui?.disabled || config.ui?.readOnly) && (
        <Box sx={{ mt: 0 }}>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: config.ui?.disabled ? 0.6 : 1,
              backgroundColor:
                config.ui?.disabled || config.ui?.readOnly
                  ? theme.palette.action.hover
                  : 'transparent',
              minHeight: 56,
            }}
          >
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ fontStyle: 'italic' }}
            >
              {t('menu.noFilesUploaded', 'No files uploaded')}
            </Typography>
          </Paper>
        </Box>
      )}

      {/* Show external error text */}
      {errorText && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 1, display: 'block' }}
        >
          {errorText}
        </Typography>
      )}

      {/* Helper text */}
      {computedHelperText && (
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mt: 1, display: 'block' }}
        >
          {computedHelperText}
        </Typography>
      )}

      {/* Validation display */}
      <ValidationDisplay
        errors={validation.errors}
        warnings={validation.warnings}
        onDismissError={validation.removeValidationError}
      />

      {/* Modals */}
      <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
      <ImageEditorModal
        file={imageEditorWithCallback.currentFile}
        isOpen={imageEditorWithCallback.isOpen}
        onClose={imageEditorWithCallback.closeEditor}
        onSave={imageEditorWithCallback.saveEdits}
        onCancel={imageEditorWithCallback.cancelEdits}
        config={imageEditorWithCallback.editorConfig || {}}
        fileType={config.fileType}
        uploading={upload.uploading}
        batchInfo={imageEditorWithCallback.batchInfo}
        aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
      />
    </Box>
  );
};

export default FileUploadComponent;
