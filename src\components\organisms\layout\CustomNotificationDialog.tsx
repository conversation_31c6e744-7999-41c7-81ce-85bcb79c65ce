import { Button, Typography } from "@mui/material";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box } from "@mui/material";
import { Dialog } from "@mui/material";

export const CustomNotificationDialog = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <Dialog open={isOpen}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexDirection: { xs: 'column', md: 'row' },
          bgcolor: '#F2F2F2',
          padding: '24px',
          borderRadius: '8px',
          gap: { xs: 2, md: 4 },
        }}
      >
        <Box display="flex" alignItems="center" gap={2} flexDirection={{ xs: 'column', md: 'row' }}>
          <InfoOutlinedIcon sx={{ color: '#0064F0', display: { xs: 'none', md: 'block' } }} />
          <Typography sx={{ textAlign: { xs: 'center', md: 'left' } }}>
            A new version of the application is available. <br /> Please update.
          </Typography>
        </Box>

        <Button
          variant="contained"
          color="primary"
          onClick={() => window.location.reload()}
          sx={{ width: { xs: '100%', md: 'auto' } }}
        >
          Update
        </Button>
      </Box>
    </Dialog>
  );
};
