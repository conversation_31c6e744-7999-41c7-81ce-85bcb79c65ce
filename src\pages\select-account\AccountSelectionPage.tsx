import { memo, useCallback, useEffect, useState } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Skeleton,
  Typography,
} from '@mui/material';
import { useLogout, useNotify, useRedirect } from 'react-admin';

import { storage } from '../../configs/firebaseConfig';
import { useFirebase } from '../../contexts/FirebaseContext';
import { getAccountDetails } from '../../utils/getAccountDetails';
import { obfuscateAccountId } from '../../utils/obfuscateAccountId';

// Memoized account list item component
const AccountListItem = memo(
  ({
    accountId,
    accountName,
    loading,
    error,
    onSelect,
  }: {
    accountId: string;
    accountName: string | null;
    loading: boolean;
    error: boolean;
    onSelect: (id: string) => void;
  }) => {
    const getPrimaryText = () => {
      if (loading) {
        return <Skeleton variant="text" width="60%" height={24} />;
      }

      if (error || !accountName) {
        return `Unknown Account Name`;
      }

      return accountName;
    };

    const getSecondaryText = () => {
      if (loading) {
        return <Skeleton variant="text" width="40%" height={16} />;
      }

      return `ID: ${obfuscateAccountId(accountId)}`;
    };

    return (
      <ListItem
        key={accountId}
        disablePadding
        sx={{
          border: '1px solid #F1f1f1',
          borderRadius: '6px',
          boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.2)',
        }}
      >
        <ListItemButton onClick={() => onSelect(accountId)} disabled={loading}>
          <ListItemText
            primary={getPrimaryText()}
            secondary={getSecondaryText()}
            sx={{
              '& .MuiListItemText-primary': {
                display: 'flex',
                alignItems: 'center',
                fontSize: '1.2rem',
              },
              '& .MuiListItemText-secondary': {
                display: 'flex',
                alignItems: 'center',
                fontSize: '0.5rem',
              },
            }}
          />
        </ListItemButton>
      </ListItem>
    );
  }
);

// Memoized loading component
const LoadingView = memo(() => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="100vh"
  >
    <CircularProgress />
  </Box>
));

// Memoized no accounts view
const NoAccountsView = memo(({ onLogout }: { onLogout: () => void }) => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="100vh"
  >
    <Typography variant="h6" color="error">
      No accounts available.
    </Typography>
    <Typography>Please contact support.</Typography>
    <Button variant="outlined" onClick={onLogout} sx={{ mt: 2 }}>
      Logout
    </Button>
  </Box>
));

export const AccountSelectionPage = () => {
  const {
    loading: fbLoading,
    details: fbDetails,
    setSelectedAccount,
  } = useFirebase();
  const notify = useNotify();
  const redirect = useRedirect();
  const logout = useLogout();

  const [accountStates, setAccountStates] = useState<
    Record<string, { name: string | null; loading: boolean; error: boolean }>
  >({});

  // Initialize account states and load account names
  useEffect(() => {
    if (fbDetails.availableAccounts.length === 0) return;

    // Initialize all accounts with loading state
    const initialStates: Record<
      string,
      { name: string | null; loading: boolean; error: boolean }
    > = {};
    fbDetails.availableAccounts.forEach((accountId: string) => {
      initialStates[accountId] = { name: null, loading: true, error: false };
    });
    setAccountStates(initialStates);

    // Load account names
    const loadAccountNames = async () => {
      await Promise.all(
        fbDetails.availableAccounts.map(async (accountId: string) => {
          try {
            const name = await getAccountDetails(storage, accountId);
            setAccountStates(prev => ({
              ...prev,
              [accountId]: { name, loading: false, error: false },
            }));
          } catch (error) {
            console.warn(
              `Failed to load account name for ${accountId}:`,
              error
            );
            setAccountStates(prev => ({
              ...prev,
              [accountId]: { name: null, loading: false, error: true },
            }));
          }
        })
      );
    };

    loadAccountNames();
  }, [fbDetails.availableAccounts]);

  const handleAccountSelect = useCallback(
    async (accountId: string) => {
      try {
        await setSelectedAccount(accountId);
        console.log('Account selected:', accountId, 'redirecting to /');
        redirect('/');
      } catch {
        notify('Error selecting account', { type: 'error' });
      }
    },
    [setSelectedAccount, redirect, notify]
  );

  if (fbLoading) {
    return <LoadingView />;
  }

  if (fbDetails.availableAccounts.length === 0) {
    return <NoAccountsView onLogout={logout} />;
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      p={2}
    >
      <Typography variant="h5" fontSize="1.25rem !important" gutterBottom>
        Select an Account
      </Typography>
      <List
        sx={{
          width: '100%',
          maxWidth: 360,
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
        }}
      >
        {fbDetails.availableAccounts.map((accountId: string) => {
          const accountState = accountStates[accountId] || {
            name: null,
            loading: true,
            error: false,
          };

          return (
            <AccountListItem
              key={accountId}
              accountId={accountId}
              accountName={accountState.name}
              loading={accountState.loading}
              error={accountState.error}
              onSelect={handleAccountSelect}
            />
          );
        })}
      </List>
      <Button
        variant="contained"
        onClick={logout}
        sx={{
          mt: 2,
          width: '358px',
          height: '45px',
          fontSize: '1rem !important',
        }}
      >
        Logout
      </Button>
    </Box>
  );
};
