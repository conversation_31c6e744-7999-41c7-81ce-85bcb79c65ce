import { Box } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { required, SaveButton, SimpleForm, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import CustomInput from '../../components/atoms/inputs/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import { validateName } from '~/utils/validateName';

export default function DiscountsCreate({
  sellPointId,
}: {
  sellPointId: string;
}) {
  const redirect = useRedirect();
  const { t } = useTranslation();
  const handleClose = () => {
    redirect('list', RESOURCES.DISCOUNTS);
  };

  return (
    <CreateDialog
      fullWidth={true}
      maxWidth={'sm'}
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <SimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={{ _isJustAValue: true }}
      >
        <ModalHeader
          handleClose={handleClose}
          title={t('discountsPage.createDiscount')}
        >
          <SaveButton type="button" icon={<></>} label={t('shared.save')} />
        </ModalHeader>

        <Box p={2} width="100%">
          <CustomInput
            source="id"
            type="text"
            label={t('shared.name')}
            validate={[required(), validateName]}
          />

          <CustomInput
            source="value"
            type="number"
            label={t('tipsPage.percentage')}
            locale="ro-RO"
            format={v => v / 100}
            parse={v => Math.floor(v * 100)}
            options={{
              style: 'percent',
              maximumFractionDigits: 2,
            }}
            slotProps={{
              input: {
                endAdornment: '%',
              },
            }}
          />
        </Box>
      </SimpleForm>
    </CreateDialog>
  );
}
