import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { PermissionPages } from './constants';

const PermissionsSideMenu = ({
  activePages,
  currentPage,
  setCurrentPage,
}: {
  activePages: PermissionPages[];
  currentPage: PermissionPages;
  setCurrentPage: (page: PermissionPages) => void;
}) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{ display: 'flex', flexDirection: 'column', pr: { xs: 0, md: 2 } }}
    >
      {Object.values(PermissionPages).map(page => (
        <Box
          key={page}
          onClick={() => setCurrentPage(page)}
          sx={{
            width: '100%',
            bgcolor: page === currentPage ? 'primary.main' : 'transparent',
            borderRadius: '6px',
            py: 1,
            px: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            cursor: 'pointer',
            mb: 1,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <img
              src={`/assets/icons/${page}.svg`}
              width="20px"
              style={{
                filter: page == currentPage ? 'invert(1)' : 'invert(0)',
              }}
            />
            <Typography
              variant="body2"
              color={page == currentPage ? 'white' : 'inherit'}
            >
              {t(`permissions.pages.${page}.title`)}
            </Typography>
          </Box>
          <Typography
            variant="caption"
            color={page == currentPage ? 'white' : 'custom.gray600'}
            fontWeight={600}
          >
            {activePages.includes(page) ? 'Active' : 'Off'}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export { PermissionsSideMenu };
