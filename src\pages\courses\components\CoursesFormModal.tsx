import { useEffect, useState } from 'react';
import { Box, Button, Dialog } from '@mui/material';

import MuiCustomInput from '../../../components/atoms/inputs/MuiCustomInput';
import ModalHeader from '../../../components/molecules/ModalHeader';

interface CoursesFormModalProps {
  open: boolean;
  onClose: () => void;
  initialName: string | null;
  onDelete: () => void;
  onSave: (name: string) => void;
}
export default function CoursesFormModal({
  open,
  onClose,
  initialName,
  onDelete,
  onSave,
}: CoursesFormModalProps) {
  const isEdit = initialName !== null;
  const [name, setName] = useState(initialName ?? '');

  useEffect(() => {
    setName(initialName ?? '');
  }, [initialName]);

  return (
    <Dialog
      fullWidth={true}
      maxWidth={'sm'}
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <ModalHeader
        handleClose={onClose}
        title={isEdit ? 'Edit course' : 'Add course'}
      >
        <>
          {isEdit && (
            <Button
              sx={{ mr: 2 }}
              onClick={onDelete}
              // @ts-ignore
              variant="contained-light"
              color="error"
            >
              Delete
            </Button>
          )}
          <Button
            onClick={() => onSave(name)}
            variant="contained"
            disabled={initialName === name || !name.trim()}
          >
            {isEdit ? 'Save' : 'Create'}
          </Button>
        </>
      </ModalHeader>
      <Box p={2} mb={2}>
        <MuiCustomInput
          fullWidth
          value={name}
          onChange={event => setName(event.target.value)}
          label="Course name"
        />
      </Box>
    </Dialog>
  );
}
