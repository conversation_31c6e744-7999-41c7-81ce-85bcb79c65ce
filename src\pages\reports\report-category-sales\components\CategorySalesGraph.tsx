import { useCallback, useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useGetListHospitalityCategoriesLive } from '~/providers/resources';
import capitalize from '~/utils/capitalize';
import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function CategorySalesGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency?: CurrencyType;
}) {
  const { data: categories } = useGetListHospitalityCategoriesLive();

  const [graphData, setGraphData] = useState<
    { label: string; data: number[] }[]
  >([]);

  const fetchCategorySalesData = useCallback(() => {
    if (!data.datasets) return;

    const controller = new AbortController();

    try {
      const resolvedData = data.datasets.map((item: any) => {
        try {
          const category = categories?.find(
            category => category.id === item.label
          );
          return {
            ...item,
            label: capitalize(category?.name.toLowerCase()),
          };
        } catch (error) {
          console.error(`Failed to fetch category with id: ${item.id}`, error);
          return { ...item, name: item.id };
        }
      });

      setGraphData(resolvedData);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching category sales data:', error);
      }
    }

    return () => controller.abort();
  }, [data.datasets, categories]);

  useEffect(() => {
    fetchCategorySalesData();
  }, [fetchCategorySalesData]);

  const { t } = useTranslation();

  return (
    <>
      {!data.datasets || !data.labels ? (
        <></>
      ) : (
        <>
          {graphData.length >= 0 && (
            <>
              <Typography
                sx={{
                  display: { xs: 'none', md: 'block' },
                  '@media print': {
                    backgroundColor: '#FFFFFF !important',
                    color: 'black !important',
                  },
                }}
                variant="body2"
                fontWeight="500"
                mb={1.5}
              >
                {data.datasets.length >= 3 &&
                  `Top ${data.datasets.length} ${t('categorySales.grossSales2')}`}
              </Typography>
              <ReportMultiLineChart
                datasets={graphData}
                labels={data.labels}
                formatData={data => formatNumber(data, currency)}
              />
            </>
          )}
        </>
      )}
    </>
  );
}
