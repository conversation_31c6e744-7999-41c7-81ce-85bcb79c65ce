import { PropsWithChildren } from 'react';
import { Box, Typography } from '@mui/material';

interface SubsectionI extends PropsWithChildren {
  title: string;
  subtitle?: string | React.ReactNode;
  comingSoon?: boolean;
  titleSx?: any;
  containerSx?: any;
}
export default function Subsection({
  title,
  subtitle,
  children,
  comingSoon,
  titleSx = {},
  containerSx = {},
}: SubsectionI) {
  return (
    <Box sx={containerSx}>
      <Typography sx={titleSx} variant="h4" mb={2} display={'inline-block'}>
        {title}
      </Typography>
      {comingSoon && (
        <Typography
          display={'inline-block'}
          sx={{ bgcolor: 'error.main', ml: 1, px: '5px' }}
          color="white"
          fontSize="10px"
        >
          coming soon
        </Typography>
      )}
      {subtitle && (
        <Typography variant="subtitle2" mb={3} color="inherit">
          {subtitle}
        </Typography>
      )}
      {children}
    </Box>
  );
}
