/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  reportCommonFields,
  reportSpecificFields,
  reportSummableFields,
} from './constants';
import { OmitKeysWithTypeTransform, Report, ReportType } from './types';
import { isNumberPrimitive } from './utils/isNumber';

// Creates a unique key for common fields to enable fast lookups
function createCommonFieldsKey<K extends keyof ReportType>(
  report: OmitKeysWithTypeTransform<Report<K>>
): string {
  const commonFields = reportCommonFields;
  const keyParts: string[] = [];

  commonFields.forEach(field => {
    const actualFieldName =
      typeof field === 'string' && field.includes('@')
        ? field.split('@')[0]
        : field;

    const value = report[actualFieldName as keyof typeof report];
    keyParts.push(`${actualFieldName}:${value}`);
  });

  return keyParts.join('|');
}

// Creates a unique key for specific fields to enable fast lookups
function createSpecificFieldsKey<K extends keyof ReportType>(
  entity: ReportType[K],
  reportType: K
): string {
  const specificFields = reportSpecificFields[reportType];
  const keyParts: string[] = [];

  specificFields.forEach(field => {
    const actualFieldName =
      typeof field === 'string' && field.includes('@')
        ? field.split('@')[0]
        : field;

    const value = (entity as any)[actualFieldName];
    keyParts.push(`${String(actualFieldName)}:${value}`);
  });

  return keyParts.join('|');
}

// Subtracts object field values recursively and removes empty objects
function subtractObjectField(
  mainObj: Record<string, any>,
  pmsObj: Record<string, any>
): Record<string, any> | undefined {
  const result: Record<string, any> = { ...mainObj };

  for (const key in pmsObj) {
    if (result[key] !== undefined) {
      const mainValue = result[key];
      const pmsValue = pmsObj[key];

      if (typeof mainValue === 'number' && typeof pmsValue === 'number') {
        result[key] = mainValue - pmsValue;
        // Remove if value is <= 0
        if (result[key] <= 0) {
          delete result[key];
        }
      } else if (
        typeof mainValue === 'object' &&
        typeof pmsValue === 'object' &&
        mainValue !== null &&
        pmsValue !== null
      ) {
        const subtractedNestedObj = subtractObjectField(mainValue, pmsValue);
        if (
          subtractedNestedObj &&
          Object.keys(subtractedNestedObj).length > 0
        ) {
          result[key] = subtractedNestedObj;
        } else {
          delete result[key];
        }
      }
    }
  }

  // Return undefined if object is empty
  return Object.keys(result).length > 0 ? result : undefined;
}

// Subtracts summable field values from main entity using pms entity values
// Returns a new entity with subtracted values
function subtractSummableFields<K extends keyof ReportType>(
  mainEntity: ReportType[K],
  pmsEntity: ReportType[K],
  reportType: K
): ReportType[K] {
  const summableFields = reportSummableFields[reportType];
  const result = { ...mainEntity } as any;

  summableFields.forEach(field => {
    const mainValue = (mainEntity as any)[field];
    const pmsValue = (pmsEntity as any)[field];

    if (isNumberPrimitive(mainValue) && isNumberPrimitive(pmsValue)) {
      result[field] = mainValue - pmsValue;
    } else if (
      typeof mainValue === 'object' &&
      typeof pmsValue === 'object' &&
      mainValue !== null &&
      pmsValue !== null
    ) {
      // Handle object fields like payments and prepStations
      const subtractedObj = subtractObjectField(mainValue, pmsValue);
      if (subtractedObj && Object.keys(subtractedObj).length > 0) {
        result[field] = subtractedObj;
      } else {
        delete result[field];
      }
    }
  });

  return result;
}

// Checks if all summable fields in an entity are zero or negative
function areAllSummableFieldsZeroOrNegative<K extends keyof ReportType>(
  entity: ReportType[K],
  reportType: K
): boolean {
  const summableFields = reportSummableFields[reportType];

  return summableFields.every(field => {
    const value = (entity as any)[field];
    if (isNumberPrimitive(value)) {
      return value <= 0;
    } else if (typeof value === 'object' && value !== null) {
      // For object fields, check if they are empty (no keys)
      return Object.keys(value).length === 0;
    }
    // If field is undefined or null, consider it as "zero"
    return true;
  });
}

// Subtracts partial report data from main report data
export function subtractReports<K extends keyof ReportType>(
  mainReports: Array<OmitKeysWithTypeTransform<Report<K>>>,
  partialReports: Array<OmitKeysWithTypeTransform<Report<K>>>,
  reportType: K
): Array<OmitKeysWithTypeTransform<Report<K>>> {
  // If no partial reports to subtract, return a copy of main reports
  if (partialReports.length === 0) {
    return mainReports.map(report => ({
      ...report,
      report: [...report.report],
    }));
  }

  // Create a Map for fast main report lookups by common fields
  const mainReportsMap = new Map<
    string,
    OmitKeysWithTypeTransform<Report<K>>
  >();
  mainReports.forEach(report => {
    const key = createCommonFieldsKey(report);
    mainReportsMap.set(key, report);
  });

  // Track which main reports have been processed
  const processedMainReports = new Set<string>();

  // Process partial reports (smaller dataset) first
  const subtractedReportsMap = new Map<
    string,
    OmitKeysWithTypeTransform<Report<K>>
  >();

  for (const partialReport of partialReports) {
    const commonFieldsKey = createCommonFieldsKey(partialReport);
    const matchingMainReport = mainReportsMap.get(commonFieldsKey);

    if (!matchingMainReport) {
      // No matching main report found, skip this partial report
      continue;
    }

    // Mark this main report as processed
    processedMainReports.add(commonFieldsKey);

    // Create entity maps for fast lookups within this report
    const mainEntitiesMap = new Map<
      string,
      { entity: ReportType[K]; index: number }
    >();
    matchingMainReport.report.forEach((entity, index) => {
      const entityKey = createSpecificFieldsKey(entity, reportType);
      mainEntitiesMap.set(entityKey, { entity, index });
    });

    // Process entities within the partial report
    const processedEntities: ReportType[K][] = [...matchingMainReport.report];
    const entitiesToRemove = new Set<number>(); // Track indices of entities to remove

    for (const partialEntity of partialReport.report) {
      const specificFieldsKey = createSpecificFieldsKey(
        partialEntity,
        reportType
      );
      const matchingMainEntityData = mainEntitiesMap.get(specificFieldsKey);

      if (matchingMainEntityData) {
        const { entity: matchingMainEntity, index } = matchingMainEntityData;

        // Subtract partial values from main entity
        const subtractedEntity = subtractSummableFields(
          matchingMainEntity,
          partialEntity,
          reportType
        );

        // Check if all summable fields are zero or negative
        if (areAllSummableFieldsZeroOrNegative(subtractedEntity, reportType)) {
          // Mark this entity for removal
          entitiesToRemove.add(index);
        } else {
          // Update the entity with subtracted values
          processedEntities[index] = subtractedEntity;
        }

        // Remove from map to avoid processing the same entity again
        mainEntitiesMap.delete(specificFieldsKey);
      }
    }

    // Remove entities that have all summable fields zero or negative
    const finalEntities = processedEntities.filter(
      (_, index) => !entitiesToRemove.has(index)
    );

    // Only add the report if it has remaining entities
    // If no entities remain, the entire main report is removed (it was completely covered by partial report)
    if (finalEntities.length > 0) {
      subtractedReportsMap.set(commonFieldsKey, {
        ...matchingMainReport,
        report: finalEntities,
      });
    }
    // If finalEntities.length === 0, we don't add anything to subtractedReportsMap
    // This effectively removes the entire main report from the final result
  }

  // Add main reports that weren't processed (no matching partial data)
  const result: Array<OmitKeysWithTypeTransform<Report<K>>> = [];

  for (const mainReport of mainReports) {
    const commonFieldsKey = createCommonFieldsKey(mainReport);

    if (subtractedReportsMap.has(commonFieldsKey)) {
      // Use the processed version (this report had matching partial data and has remaining entities)
      result.push(subtractedReportsMap.get(commonFieldsKey)!);
    } else if (!processedMainReports.has(commonFieldsKey)) {
      // Main report with no matching partial data, keep as is
      result.push({
        ...mainReport,
        report: [...mainReport.report],
      });
    }
    // If processedMainReports.has(commonFieldsKey) && !subtractedReportsMap.has(commonFieldsKey)
    // This means the main report was processed but resulted in 0 entities, so we don't add it to result
    // This effectively removes the entire main report from the final result
  }

  return result;
}
