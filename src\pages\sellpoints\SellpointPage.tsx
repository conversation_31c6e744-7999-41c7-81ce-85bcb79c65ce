import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PageTitle from '~/components/molecules/PageTitle';
import { SellpointCreate } from './SellpointCreate';
import { SellpointEdit } from './SellpointEdit';
import { SellpointList } from './SellpointList';
import SellpointShow from './SellpointShow';

export default function SellpointPage() {
  const { t } = useTranslation();
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        title={t('sellPointsPage.title')}
        description={
          <>
            {t('sellPointsPage.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
      />
      <SellpointList />
      <SellpointShow />
      <SellpointCreate />
      <SellpointEdit />
    </Box>
  );
}
