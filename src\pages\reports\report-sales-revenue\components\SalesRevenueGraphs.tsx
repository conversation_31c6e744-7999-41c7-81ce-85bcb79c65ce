import { Box, Typography } from '@mui/material';
import { DateRange } from '@mui/x-date-pickers-pro';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import ReportBartChart from '../../components/ReportBarChart';
import ReportLineChart from '../../components/ReportLineChart';

type DatasetType = { label: string; data: number[]; tableLabels: string[] };

export type DatasetsProp = {
  groupedByHour?: DatasetType;
  groupedByDay?: DatasetType;
  groupedByDayInWeek?: DatasetType;
  oneDayComparison?: {
    collectedToday: number;
    lastWeekDifference: number;
  };
};

interface SalesRevenueGraphsProps {
  dateRange: DateRange<dayjs.Dayjs>;
  datasets: DatasetsProp;
  currency?: CurrencyType;
}

export default function SalesRevenueGraphs({
  dateRange,
  datasets,
  currency,
}: SalesRevenueGraphsProps) {
  const { t } = useTranslation();
  const isOneDay = dateRange[0]?.isSame(dateRange[1], 'day');

  return (
    <Box
      sx={{
        display: { xs: 'none', md: 'block' },
        '@media print': {
          display: 'block !important',
        },
      }}
    >
      {isOneDay && (
        <Typography variant="body2" fontWeight="500" mb={2}>
          {t('transactionsPage.timeOfDay')}
        </Typography>
      )}
      {(!!datasets.groupedByHour || !!datasets.groupedByDay) && (
        <ReportLineChart
          datasets={[
            isOneDay ? datasets.groupedByHour! : datasets.groupedByDay!,
          ]}
          labels={
            isOneDay
              ? datasets.groupedByHour!.tableLabels
              : datasets.groupedByDay!.tableLabels
          }
          fill={!isOneDay}
          hidePoints={!isOneDay}
          formatData={data => formatNumber(data, currency)}
        />
      )}

      {isOneDay && datasets.oneDayComparison && (
        <Typography variant="caption">
          {t('reportsPage.totalCollected')} {dateRange[0]?.format('DD MMM YYYY')} {t('reportsPage.were')}{" "}
          <b style={{ fontSize: '14px' }}>
            {formatNumber(datasets.oneDayComparison.collectedToday, currency)}
          </b>
          . {t('reportsPage.thisWas')}{" "}
          <b
            style={{
              color:
                datasets.oneDayComparison.lastWeekDifference > 0
                  ? '#12D252'
                  : '#FF0000',
              fontSize: '14px',
            }}
          >
            {formatNumber(
              datasets.oneDayComparison.lastWeekDifference,
              currency
            )}
          </b>{' '}
          {t('reportsPage.fromTheSameDayOfThePreviousWeek')}
        </Typography>
      )}

      {!isOneDay && datasets.groupedByHour && datasets.groupedByDayInWeek && (
        <Box mt={5} display="flex" gap={'5%'} alignItems="flex-end">
          <Box sx={{ width: '30%' }}>
            <Typography variant="body2" fontWeight="500" mb={2}>
              {t('transactionsPage.dayOfWeek')}
            </Typography>
            <ReportBartChart
              width={'100%'}
              datasets={[datasets.groupedByDayInWeek]}
              labels={datasets.groupedByDayInWeek.tableLabels}
              formatData={data => formatNumber(data, currency)}
            />
          </Box>
          <Box width="65% ">
            <Typography variant="body2" fontWeight="500" mb={2}>
              {t('transactionsPage.timeOfDay')}
            </Typography>
            <ReportLineChart
              width={'100%'}
              datasets={[datasets.groupedByHour]}
              labels={datasets.groupedByHour.tableLabels}
              fill
              hideLegend
              formatData={data => formatNumber(data, currency)}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
}
