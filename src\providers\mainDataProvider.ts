/**
 * Main Data Provider for the Application
 *
 * This module creates a combined data provider that routes different resources
 * to the appropriate underlying data provider:
 *
 * 1. Firestore Data Provider - Direct access to Firestore for some resources
 * 2. Hybrid Data Provider - Combines Firestore (writes) and local cache (reads) for better performance
 * 3. Local Data Provider - Used by the Hybrid provider for local caching
 *
 * The routing logic determines which provider to use based on the resource name.
 */
import { addSearchMethod, SearchDataProvider } from '@react-admin/ra-search';
import { DataProvider, defaultDataProvider } from 'react-admin';

import { dataProvider } from '~/dataProvider';
import { combineDataProviders } from './combineDataProviders';
import {
    FirestoreDataProvider,
    getFirestoreDataProvider,
} from './firestoreDataProvider';
import {
    getHybridDataProvider,
    HybridDataProvider,
} from './hybridDataProvider';
import {
    getLocalCacheDataProvider,
    LocalCacheDataProvider,
} from './localCacheDataProvider';
import { ResourcesInfo, resourcesInfo } from './resources';
import {
    getStorageDataProvider,
    StorageDataProvider,
} from './storageDataProvider';
import cleanupActions from './utils/cleanupActions';
import { fileUploadManager } from '../utils/FileUploadManager';

/**
 * Dummy Data Provider
 *
 * Used when no account ID is selected or when there's an error initializing the real providers.
 * All methods reject with an error message to prevent silent failures.
 */
export const dummyDataProvider: DataProvider = addSearchMethod(
    {
        ...defaultDataProvider,

        // Subscription methods (not implemented in dummy provider)
        subscribe: () => Promise.resolve({ data: null }),
        unsubscribe: () => Promise.resolve({ data: null }),
        publish: () => Promise.resolve({ data: null }),
        getAccountId: () => '',
        generateFirestoreId: () => '',
    },
    []
);

export const getMainDataProvider = async (
    accountId: string | null,
    user?: { uid: string } | null
): Promise<DataProvider> => {
    if (!accountId) {
        return dummyDataProvider;
    }

    try {
        // Initialize the underlying data providers
        const firestoreProvider: FirestoreDataProvider =
            getFirestoreDataProvider(accountId);
        const localCacheProvider: LocalCacheDataProvider =
            await getLocalCacheDataProvider(accountId);
        const hybridProvider: HybridDataProvider = getHybridDataProvider(
            accountId,
            firestoreProvider,
            localCacheProvider
        );
        const storageProvider: StorageDataProvider =
            getStorageDataProvider(accountId);

        // Initialize FileUploadManager with account and user context
        if (user?.uid) {
            try {
                await fileUploadManager.initialize(accountId, user.uid);
                console.log('getMainDataProvider: FileUploadManager initialized successfully');
                // Register cleanup function for FileUploadManager
                cleanupActions.register(() => {
                    console.log('getMainDataProvider: Cleaning up FileUploadManager');
                    // FileUploadManager is a singleton, no explicit cleanup needed
                    // but we could add reset/cleanup methods if needed in the future
                });
            } catch (error) {
                console.error('getMainDataProvider: Failed to initialize FileUploadManager:', error);
                // Continue without failing the entire data provider initialization
            }
        }

        // Register cleanup function for hybrid provider
        // Do this first because it will be called last
        console.log(
            'getAppDataProvider: Registering cleanup function for hybrid provider'
        );
        cleanupActions.register(() => hybridProvider.cleanup());
        // Register cleanup function for local cache provider
        console.log(
            'getAppDataProvider: Registering cleanup function for local cache provider'
        );
        cleanupActions.register(() => localCacheProvider.cleanup());
        // getting resources
        const resources = Object.keys(resourcesInfo);
        // Set up real-time listeners for hybrid resources
        console.log(
            'getAppDataProvider: Setting up real-time listeners for hybrid resources'
        );
        // Open listeners for hybrid resources in parallel but wait for all to complete
        await Promise.allSettled(
            resources.map(async resource => {
                const resourceInfo = resourcesInfo[resource as keyof ResourcesInfo];
                if (
                    resourceInfo.dataProvider !== 'hybrid' ||
                    !resourceInfo.hasLiveListener ||
                    !resourceInfo.hasGlobalListener
                ) {
                    console.log(
                        `getAppDataProvider: Skipping global real-time listener for resource: ${resource}`
                    );
                    return;
                }
                return hybridProvider.ensureSyncListener(resource);
            })
        );

        // Add search methods to the hybrid provider for searchable resources
        console.log(
            'getAppDataProvider: Adding search methods to the hybrid provider for searchable resources'
        );
        const searchableResources = resources
            .filter(
                resource => resourcesInfo[resource as keyof ResourcesInfo].isSearchable
            )
            .reduce(
                (acc, resource) => ({
                    ...acc,
                    [resource]:
                        resourcesInfo[resource as keyof ResourcesInfo].searchOptions ?? {},
                }),
                {}
            );
        const searchableHybridProvider: SearchDataProvider = addSearchMethod(
            hybridProvider,
            searchableResources
        );

        const combinedDataProvider = combineDataProviders(resource => {
            if (resource.startsWith('resource/')) {
                resource = resource.substring('resource/'.length);
            }
            if (resourcesInfo[resource as keyof ResourcesInfo]) {
                switch (resourcesInfo[resource as keyof ResourcesInfo].dataProvider) {
                    case 'local':
                        return localCacheProvider;
                    case 'firestore':
                        return firestoreProvider;
                    case 'hybrid':
                        return searchableHybridProvider;
                    case 'storage':
                        return storageProvider;
                }
            } else {
                return {
                    ...dataProvider,
                    getAccountId: () => accountId,
                    generateFirestoreId: () => firestoreProvider.generateFirestoreId(),
                };
            }
        }, searchableHybridProvider);

        return combinedDataProvider;
    } catch (error) {
        console.error('getAppDataProvider: Error initializing providers:', error);
        cleanupActions.runAll();
        return dummyDataProvider;
    }
};
