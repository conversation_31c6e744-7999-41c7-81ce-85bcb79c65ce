import React, { useEffect } from 'react';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import {
  Box,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
} from '@mui/material';

import { useFilters } from '~/contexts/FilterContext';
import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';

export const SubFilterSelect = ({
  filterKey,
  subOptions,
}: {
  filterKey: string;
  subOptions: Record<string, string[]>;
}) => {
  const { filterValues, setFilterValue, toggleFilter } = useFilters();

  const firstOption = subOptions[filterKey]?.[0] || '';

  useEffect(() => {
    if (!filterValues[filterKey] && firstOption) {
      setFilterValue(filterKey, firstOption);
    }
  }, [filterKey, firstOption, filterValues, setFilterValue]);

  return (
    <Box
      sx={{ width: { xs: '100%', sm: '200px' } }}
      display="flex"
      alignItems="center"
      gap={0.5}
      mb={2}
    >
      <FormControl fullWidth>
        <InputLabel
          sx={{
            fontSize: '0.9rem',
          }}
        >
          {camelCaseToNormalWords(filterKey)}
        </InputLabel>
        <Select
          sx={{
            height: '43px',
            fontSize: '0.9rem',
          }}
          value={filterValues[filterKey] || firstOption}
          label={filterKey}
          onChange={e => setFilterValue(filterKey, e.target.value)}
        >
          {subOptions[filterKey]?.map((val: string) => (
            <MenuItem
              sx={{
                fontSize: '0.9rem',
              }}
              key={val}
              value={val}
            >
              {camelCaseToNormalWords(val)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <IconButton onClick={() => toggleFilter(filterKey)}>
        <RemoveCircleOutlineIcon />
      </IconButton>
    </Box>
  );
};
