import { Box, BoxProps } from '@mui/material';

export default function ListCard({ children, sx, ...props }: BoxProps) {
  return (
    <Box
      sx={{
        ...{
          bgcolor: 'custom.fieldBg',
          py: 1,
          px: 1.5,
          borderRadius: '3px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          cursor: 'pointer',
          boxShadow: '0 1px 2px rgba(0,0,0,.1), 0 0 4px rgba(0,0,0,.1)',
        },
        ...sx,
      }}
      {...props}
    >
      {children}
    </Box>
  );
}
