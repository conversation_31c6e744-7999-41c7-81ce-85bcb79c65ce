import { useState } from 'react';
import DownloadIcon from '@mui/icons-material/Download';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import PrintIcon from '@mui/icons-material/Print';
import { Button, Menu, MenuItem, useMediaQuery } from '@mui/material';
import { ExportButton, WrapperField } from 'react-admin';
import { useReactToPrint } from 'react-to-print';

export const ExportMenuButton = ({
  contentRef,
  handleExport,
  printOnly = false,
}: {
  contentRef?: any;
  handleExport?: () => void;
  printOnly?: boolean;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const isXSmall = useMediaQuery(theme => theme.breakpoints.down('sm'));
  
  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    setAnchorEl(e.currentTarget);
  };
  
  const handlePrint = useReactToPrint({
    contentRef,
    bodyClass: 'print-body',
  });


  return (
    <>
      {!printOnly ? (
        <WrapperField>
          <Button onClick={handleClick} sx={{ color: '#0064F0' }}>
            <DownloadIcon sx={{ marginRight: 1, width: 20, height: 20 }} />
            Export
          </Button>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
            onClick={e => e.stopPropagation()}
          >
            {!handleExport ? (
              <MenuItem
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: '#0064F0',
                  fontSize: 14,
                }}
              >
                <ExportButton label="Export CSV" />
                {isXSmall ? 'Export CSV' : ''}
              </MenuItem>
            ) : (
              <MenuItem>
                <Button
                  className="do-not-print"
                  onClick={() => {
                    handleExport?.();
                    setAnchorEl(null);
                  }}
                >
                  <FileDownloadIcon color="primary" sx={{ marginRight: 1 }} />
                  Export CSV
                </Button>
              </MenuItem>
            )}

            <MenuItem onClick={handlePrint}>
              <Button sx={{ width: '100%' }}>
                <PrintIcon
                  color="primary"
                  sx={{ marginRight: 1, width: 20, height: 20 }}
                />
                Print PDF
              </Button>
            </MenuItem>
          </Menu>
        </WrapperField>
      ) : (
        <Button onClick={handlePrint}>
          <PrintIcon
            color="primary"
            sx={{ marginRight: 1, width: 20, height: 20 }}
          />
        </Button>
      )}
    </>
  );
};
