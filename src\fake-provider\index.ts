import { Database } from 'firebase/database';

import { storage } from '~/configs/firebaseConfig';
import { getReport } from './reports/getReport';
import { getReportsDataFromRealtime } from './reports/utils/getReportsDataFromRealtime';
import { getReportsDataFromStorage } from './reports/utils/getReportsDataFromStorage';

export async function getReportDataHelper({
  database,
  startDate,
  endDate,
  accountId,
  sellPointId,
  reportType,
}: {
  database: Database;
  startDate: string;
  endDate: string;
  accountId: string;
  sellPointId: string;
  reportType:
    | 'items'
    | 'sales'
    | 'payments'
    | 'groups'
    | 'discounts'
    | 'modifiers'
    | 'coupons'
    | 'promotions'
    | 'voids'
    | 'tips'
    | 'vat'
    | 'giftCards'
    | 'extraCharges'
    | 'topGroups'
    | 'topModifiers'
    | 'topItems'
    | 'compedItems'
    | 'compedModifiers'
    | 'compedTips'
    | 'compedExtraCharges'
    | 'compedGiftCards'
    | 'transactions'
    | 'teamRevenue';
}) {
  const report = await getReport(
    accountId,
    sellPointId,
    reportType,
    startDate,
    endDate,
    'ro-RO',
    {
      storageBucket: storage,
      database: database,
      getReportFromStorageFn: getReportsDataFromStorage,
      getReportFromRealtimeFn: getReportsDataFromRealtime,
    }
  );

  return report;
}
