import { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useGetList } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import Subsection from '../../../components/molecules/Subsection';
import { useTranslation } from 'react-i18next';

export default function PricingSubscriptionsCreateStep1() {
  const { setValue } = useFormContext();
  const { data: subscriptions } = useGetList('pricing-subscriptions');
  const [selectedPlanId, setSelectedPlanId] = useState<number | null>(
    subscriptions && subscriptions[0].isPremium ? 1 : 0
  );
  const { t } = useTranslation();

  const subscriptionPlans = [
    {
      id: 0,
      isCurrentPlan: subscriptions && !subscriptions[0].isPremium,
      title: 'Selio for Restaurants Free',
      subtitle: t('pricingSubscriptions.includesBasic'),
      price: 0,
    },
    {
      id: 1,
      isCurrentPlan: subscriptions && subscriptions[0].isPremium,
      title: 'Selio for Restaurants Premium',
      subtitle: t('pricingSubscriptions.includesAllRestaurantFeatures'),
      price: 85,
    },
  ];

  useEffect(() => {
    if (selectedPlanId === 0) {
      setValue('isPremium', false);
    } else {
      setValue('isPremium', true);
    }
  }, [selectedPlanId]);

  return (
    <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
      <Box
        p={2}
        sx={{
          mt: 3,
          maxWidth: '600px',
        }}
      >
        <Subsection title={t('pricingSubscriptions.manageSubscription')}>
          <Subsection
            titleSx={{ fontSize: '15px' }}
            title={t('pricingSubscriptions.youAreSubscribedTo')}
          />
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {subscriptionPlans.map((subscription, index) => {
              return (
                <Box
                  key={index}
                  onClick={() => {
                    setSelectedPlanId(subscription.id);
                    setValue('isPremium', subscription.id === 1);
                  }}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  <SubscriptionCard
                    subscription={subscription}
                    selectedPlanId={selectedPlanId}
                  />
                </Box>
              );
            })}
          </Box>
        </Subsection>
        <Typography sx={{ mt: 2, mb: 10, fontSize: '14px' }}>
            {t('pricingSubscriptions.needMoreAdvancedFeatures')}
          <a
            style={{ textDecoration: 'none' }}
            href="https://selio.io/contact-form/"
            target="_blank"
          >
            <span style={{ cursor: 'pointer', color: '#0064F0' }}>
              {t('pricingSubscriptions.contactOurSales')}
            </span>
          </a>
        </Typography>
        <Subsection title={t('pricingSubscriptions.unsubscribe')}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              justifyContent: 'space-between',
              gap: 2,
              alignItems: { xs: 'start', sm: 'center' },
            }}
          >
            <Typography
              color="textSecondary"
              sx={{ mt: 0.5, maxWidth: '400px', fontSize: '14px' }}
            >
              {t('pricingSubscriptions.unsubscribeDescription')}
            </Typography>
            <Button
              sx={{
                color: 'white',
                backgroundColor: '#BF0120',
                px: 1.2,
                py: 0.5,
                height: '50px',
                borderRadius: '7px',
                '&.MuiButtonBase-root:hover': {
                  backgroundColor: '#850016',
                },
              }}
            >
              {t('pricingSubscriptions.unsubscribe2')}
            </Button>
          </Box>
        </Subsection>
      </Box>
    </Box>
  );
}

function SubscriptionCard({
  subscription,
  selectedPlanId,
}: {
  selectedPlanId: number | null;
  subscription: {
    id: number;
    isCurrentPlan: boolean;
    title: string;
    subtitle: string;
    price: number;
  };
}) {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        borderRadius: 1.5,
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        gap: 3,
        backgroundColor: selectedPlanId === subscription.id ? '#0064F0' : '',
        border: selectedPlanId === subscription.id ? '' : '1.5px #D9D9D9 solid',
        color: selectedPlanId === subscription.id ? 'white' : 'black',
      }}
    >
      {subscription.isCurrentPlan && (
        <Typography
          sx={{
            py: 0.5,
            px: 2,
            backgroundColor:
              selectedPlanId === subscription.id ? 'white' : '#E5F0FF',
            color: 'primary.main',
            width: 'fit-content',
            borderRadius: 7,
          }}
        >
          {t('pricingSubscriptions.currentPlan')}
        </Typography>
      )}

      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography>{subscription.title}</Typography>
        <Typography>
          {subscription.price === 0
            ? t('pricingSubscriptions.free')
            : `${t('pricingSubscriptions.startingAt')} ${subscription.price} €/mo`}
        </Typography>
      </Box>

      <Typography
        sx={{
          color: selectedPlanId === subscription.id ? '#7BB2FF' : '#969696',
          fontSize: '14px',
        }}
      >
        {subscription.subtitle}
      </Typography>
    </Box>
  );
}
