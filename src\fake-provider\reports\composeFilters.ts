import { ReportFiltersState } from '~/pages/reports/components/ReportFilters';
import { ReportFilterCommonFieldsCriterion } from './types';

const composeFilters = (filters: ReportFiltersState, report_type: any) => {
  const composedFilters: ReportFilterCommonFieldsCriterion<
    typeof report_type
  >[] = [];

  if (!filters.timeRange.allDay) {
    composedFilters.push(
      {
        field: 'hourOfDay',
        operator: '>=',
        value: parseInt(filters?.timeRange.start?.format('HH') ?? '0'),
      },
      {
        field: 'hourOfDay',
        operator: '<=',
        value: parseInt(filters?.timeRange.end?.format('HH') ?? '24'),
      }
    );
  }

  if (filters?.member && filters.member !== 'all') {
    composedFilters.push({
      field: 'memberId',
      operator: '==',
      value: filters.member,
    });
  }

  if (filters?.floor && filters.floor !== 'all') {
    composedFilters.push({
      field: 'section',
      operator: '==',
      value: filters.floor,
    });
  }

  if (filters?.diningOption && filters.diningOption !== 'all') {
    composedFilters.push({
      field: 'serviceType',
      operator: '==',
      value: filters.diningOption,
    });
  }

  if (filters?.source && filters.source !== 'all') {
    composedFilters.push({
      field: 'source',
      operator: '==',
      value: filters.source,
    });
  }

  return composedFilters;
};

export default composeFilters;
