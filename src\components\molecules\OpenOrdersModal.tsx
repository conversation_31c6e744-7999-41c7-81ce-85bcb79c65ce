import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Dialog, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useGetListTeamMembersLive } from '~/providers/resources';
import ModalHeader from './ModalHeader';
import OrderModalRow from './OrderModalRow';
import OrderModalSectionHeader from './OrderModalSectionHeader';

interface OpenOrdersModalProps {
  isOpen: boolean;
  handleCloseOrdersModal: () => void;
  sectionsOrders: any;
}

export default function OpenOrdersModal({
  isOpen,
  handleCloseOrdersModal,
  sectionsOrders,
}: OpenOrdersModalProps) {
  const { data: members } = useGetListTeamMembersLive();

  const getMemberName = (memberId: string) => {
    const member = members?.find(el => el.id === memberId);
    return member?.displayName;
  };

  const { t } = useTranslation();
  const isXSmall = useMediaQuery('(max-width: 768px)');

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseOrdersModal}
      fullWidth
      fullScreen={isXSmall}
      maxWidth="xs"
    >
      <ModalHeader
        handleClose={handleCloseOrdersModal}
        title={t('dashboard.openSales')}
      />

      {sectionsOrders?.reduce(
        (acc: number, el: any) => acc + el.orders.length,
        0
      ) === 0 && (
        <Box sx={{ padding: '24px' }}>
          <Typography variant="h6" component="p">
            {t('dashboard.noOpenOrders')}
          </Typography>
        </Box>
      )}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {sectionsOrders
          ?.sort((a: any, b: any) => a.id - b.id)
          ?.map(
            (el: any) =>
              el.orders.length > 0 && (
                <Box
                  key={el.id}
                  sx={{
                    position: 'relative',
                  }}
                >
                  <OrderModalSectionHeader el={el} />
                  <Box>
                    {el.orders.map((el: any) => (
                      <OrderModalRow
                        key={el.billId}
                        el={el}
                        name={getMemberName(el.memberId)}
                      />
                    ))}
                  </Box>
                </Box>
              )
          )}
      </Box>
    </Dialog>
  );
}
