import React, { useCallback, useMemo, useState } from 'react';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import JSZip from 'jszip';
import { QRCodeSVG } from 'qrcode.react';
import {
  useNotify,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import ModalHeader from '~/components/molecules/ModalHeader';
import FloorPlanTabSelector from '~/components/organisms/table-selection/FloorPlanTabSelector';
import { useFirebase } from '~/contexts/FirebaseContext';
import {
  useGetListFloorPlansLive,
  useGetListLocationsLive,
} from '~/providers/resources';
import {
  generateQRCodeFilenameFromModal,
  sanitizeFilename,
} from '~/utils/fileNameUtils';

interface FloorPlanItem {
  number?: string;
  tag?: string;
  shape: string;
  position: {
    startX: string;
    startY: string;
    endX: string;
    endY: string;
  };
}

interface FloorPlan {
  id: string;
  name: string;
  label: string;
  active: boolean;
  sellPointId: string;
  items: FloorPlanItem[];
}

interface Location {
  id: string;
  name: string;
  [key: string]: any;
}

// Custom encoding alphabet (URL-safe, no ambiguous characters)
const ENCODING_ALPHABET =
  '23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz';
const ALPHABET_LENGTH = ENCODING_ALPHABET.length; // 57 characters

// Optimized QR Code configuration for physical materials
// These settings ensure maximum scanning reliability on paper, plastic, wood, metal
const QR_CONFIG = {
  // High error correction (Level H = ~30% recovery) for damaged/worn codes
  errorCorrectionLevel: 'H' as const,

  // Generous margins (quiet zones) for better scanner detection
  // Especially important for engraved/embossed materials
  margin: 4, // 4 modules on each side (standard recommends 4+ for reliability)

  // High contrast colors for optimal scanning
  color: {
    dark: '#000000', // Pure black for maximum contrast
    light: '#FFFFFF', // Pure white background
  },

  // High resolution for printing (minimum 300 DPI equivalent)
  printWidth: 2000, // 2000px for crisp printing at various sizes
  displayWidth: 1000, // 1000px for screen display/clipboard

  // Canvas settings for high-quality output
  canvasSize: 2400, // Large canvas for 300+ DPI printing
  padding: 0.15, // 15% padding around QR code for mounting/borders
} as const;

// Helper functions for encoding - Simple base64 with URL-safe characters
const encodeToCustomBase = (data: Uint8Array): string => {
  // Convert to standard base64
  let binary = '';
  for (let i = 0; i < data.length; i++) {
    binary += String.fromCharCode(data[i]);
  }

  // Create base64 string and make it URL-safe
  const base64 = btoa(binary)
    .replace(/\+/g, '-') // Replace + with -
    .replace(/\//g, '_') // Replace / with _
    .replace(/=+$/, ''); // Remove padding =

  return base64;
};

const decodeFromCustomBase = (encoded: string): Uint8Array => {
  try {
    // Convert back from URL-safe base64
    let base64 = encoded
      .replace(/-/g, '+') // Replace - with +
      .replace(/_/g, '/'); // Replace _ with /

    // Add padding if needed
    while (base64.length % 4 !== 0) {
      base64 += '=';
    }

    // Decode from base64
    const binary = atob(base64);
    const result = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      result[i] = binary.charCodeAt(i);
    }
    return result;
  } catch (error) {
    console.error('Failed to decode base64:', error, 'Input:', encoded);
    throw new Error('Invalid token format');
  }
};

// Simple checksum function
const calculateChecksum = (data: string): number => {
  let checksum = 0;
  for (let i = 0; i < data.length; i++) {
    checksum = (checksum + data.charCodeAt(i)) & 0xff;
  }
  return checksum;
};

// Simple XOR obfuscation - not for security, just basic obfuscation
const simpleXorObfuscate = (data: string, key: string): Uint8Array => {
  if (!data || !key) {
    throw new Error('Data and key must not be empty');
  }

  // Add a simple checksum at the beginning for validation
  const checksum = calculateChecksum(data);
  const fullData = String.fromCharCode(checksum) + data;

  const result = new Uint8Array(fullData.length);

  // Add debug logging in dev mode
  if (import.meta.env.VITE_NODE_ENV === 'dev') {
    console.log('XOR obfuscate debug:', {
      originalData: data,
      dataLength: data.length,
      keyLength: key.length,
      checksum: checksum,
      fullDataLength: fullData.length,
    });
  }

  for (let i = 0; i < fullData.length; i++) {
    const keyChar = key.charCodeAt(i % key.length);
    result[i] = fullData.charCodeAt(i) ^ keyChar;
  }

  if (import.meta.env.VITE_NODE_ENV === 'dev') {
    console.log('XOR obfuscate result:', {
      resultLength: result.length,
      result: Array.from(result),
    });
  }

  return result;
};

const simpleXorDeobfuscate = (data: Uint8Array, key: string): string | null => {
  if (data.length < 2) {
    console.warn('XOR deobfuscate: data too short', data.length);
    return null;
  }

  if (!key) {
    console.warn('XOR deobfuscate: key is empty');
    return null;
  }

  // Deobfuscate the data
  const deobfuscated: number[] = [];
  for (let i = 0; i < data.length; i++) {
    const keyChar = key.charCodeAt(i % key.length);
    deobfuscated.push(data[i] ^ keyChar);
  }

  const fullString = String.fromCharCode(...deobfuscated);

  // Extract checksum and data
  const storedChecksum = fullString.charCodeAt(0);
  const originalData = fullString.substring(1);
  const calculatedChecksum = calculateChecksum(originalData);

  // Add debug logging in dev mode
  if (import.meta.env.VITE_NODE_ENV === 'dev') {
    console.log('XOR deobfuscate debug:', {
      dataLength: data.length,
      deobfuscatedData: Array.from(deobfuscated),
      fullStringLength: fullString.length,
      storedChecksum: storedChecksum,
      calculatedChecksum: calculatedChecksum,
      originalData: originalData,
      checksumMatch: storedChecksum === calculatedChecksum,
    });
  }

  // Verify checksum
  if (storedChecksum !== calculatedChecksum) {
    console.warn('XOR deobfuscate: checksum mismatch', {
      storedChecksum,
      calculatedChecksum,
    });
    return null;
  }

  return originalData;
};

// Function to generate OrderNow URL with simple obfuscation
const generateSecureOrderNowUrl = (params: {
  accountId: string;
  sellPointId: string;
  floorPlanIndex: number;
  itemNumber: string;
}): string => {
  try {
    // Check if the secret key is available
    if (!import.meta.env.VITE_ORDERNOW_URL_SECRET) {
      throw new Error('OrderNow Url key is not set');
    }

    // Debug logging - only in development
    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('URL Secret:', import.meta.env.VITE_ORDERNOW_URL_SECRET);
    }

    // 1. Extract partial IDs for matching
    const accountPartial = params.accountId.substring(0, 12); // 12 chars
    const sellPointPartial = params.sellPointId.substring(0, 8); // 8 chars

    // 2. Validate and format numbers
    const floorPlanIndex = Math.min(Math.max(params.floorPlanIndex, 0), 99); // 0-99
    const itemNumber = Math.min(parseInt(params.itemNumber) || 0, 9999); // 0-9999

    // 3. Create compact string format: account(12) + sellpoint(8) + floor(2) + item(4)
    const floorPadded = floorPlanIndex.toString().padStart(2, '0');
    const itemPadded = itemNumber.toString().padStart(4, '0');

    // 4. Concatenate all parts (total: 12+8+2+4 = 26 characters)
    const tokenString =
      accountPartial + sellPointPartial + floorPadded + itemPadded;

    // Add debug logging in dev mode
    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('Encode debug - token string:', tokenString);
      console.log('Encode debug - token string length:', tokenString.length);
    }

    // 5. Obfuscate with simple XOR (not for security, just basic obfuscation)
    const obfuscatedData = simpleXorObfuscate(
      tokenString,
      import.meta.env.VITE_ORDERNOW_URL_SECRET
    );

    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('Encode debug - obfuscated data:', obfuscatedData);
      console.log(
        'Encode debug - obfuscated data length:',
        obfuscatedData.length
      );
    }

    // 6. Encode with URL-safe base64
    const encodedToken = encodeToCustomBase(obfuscatedData);

    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('Encode debug - encoded token:', encodedToken);
    }

    // 7. Create URL
    const compactUrl = `https://ordernow.selio.io/t/${encodedToken}`;

    return compactUrl;
  } catch (error) {
    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.error('Failed to generate URL:', error);
    }
    throw new Error(
      `Failed to generate OrderNow URL: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

// Fresh decode functions - reverse of the encoding process
const decodeOrderNowToken = (
  token: string
): {
  accountPartial: string;
  sellPointPartial: string;
  floorPlanIndex: number;
  itemNumber: number;
} | null => {
  try {
    // Check if the secret key is available
    if (!import.meta.env.VITE_ORDERNOW_URL_SECRET) {
      throw new Error('OrderNow URL secret is not set');
    }

    // Add debug logging in dev mode
    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('Decode debug - input token:', token);
      console.log(
        'Decode debug - secret key:',
        import.meta.env.VITE_ORDERNOW_URL_SECRET
      );
    }

    // 1. Decode from URL-safe base64 to get obfuscated data
    const obfuscatedData = decodeFromCustomBase(token);

    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      console.log('Decode debug - obfuscated data:', obfuscatedData);
      console.log(
        'Decode debug - obfuscated data length:',
        obfuscatedData.length
      );
    }

    // Check if we got reasonable data length (should be around 27 bytes: 26 for data + 1 for checksum)
    if (obfuscatedData.length < 20 || obfuscatedData.length > 40) {
      console.warn(
        `Unexpected obfuscated data length: ${obfuscatedData.length}, expected around 27`
      );
    }

    // Let's also test round-trip encoding to see if the issue is in encoding or deobfuscation
    if (import.meta.env.VITE_NODE_ENV === 'dev') {
      try {
        const testRoundTrip = encodeToCustomBase(obfuscatedData);
        console.log('Round-trip test - original token:', token);
        console.log('Round-trip test - re-encoded:', testRoundTrip);
        console.log('Round-trip test - match:', token === testRoundTrip);
      } catch (error) {
        console.error('Round-trip test failed:', error);
      }
    }

    // 2. Deobfuscate with simple XOR using the same secret key
    const decryptedString = simpleXorDeobfuscate(
      obfuscatedData,
      import.meta.env.VITE_ORDERNOW_URL_SECRET
    );

    if (!decryptedString) {
      throw new Error(
        'Failed to deobfuscate token - may be corrupted or invalid'
      );
    }

    // 3. Parse the 26-character string: account(12) + sellpoint(8) + floor(2) + item(4)
    if (decryptedString.length !== 26) {
      throw new Error(
        `Invalid token length: expected 26 chars, got ${decryptedString.length}`
      );
    }

    const accountPartial = decryptedString.substring(0, 12); // chars 0-11
    const sellPointPartial = decryptedString.substring(12, 20); // chars 12-19
    const floorPlanIndex = parseInt(decryptedString.substring(20, 22)); // chars 20-21
    const itemNumber = parseInt(decryptedString.substring(22, 26)); // chars 22-25

    // Validate extracted numbers
    if (
      isNaN(floorPlanIndex) ||
      isNaN(itemNumber) ||
      floorPlanIndex < 0 ||
      itemNumber < 0
    ) {
      throw new Error('Invalid numeric values in token');
    }

    return {
      accountPartial,
      sellPointPartial,
      floorPlanIndex,
      itemNumber,
    };
  } catch (error) {
    console.error('Token decode error:', error);
    return null;
  }
};

// Function to decode token from a full URL
const decodeOrderNowUrl = (url: string) => {
  try {
    // Extract token from URL
    const match = url.match(/\/t\/([^/?#]+)/);
    if (!match || !match[1]) {
      throw new Error('No token found in URL');
    }

    const token = match[1];
    return decodeOrderNowToken(token);
  } catch (error) {
    console.error('URL decode error:', error);
    return null;
  }
};

const LocationCard = React.memo(
  ({
    location,
    accountId,
    onQRModalOpen,
    onQRDataSet,
    isLargeScreen,
    isExpanded,
    onToggleExpanded,
    isDownloadingQRs,
    onDownloadingQRsChange,
  }: {
    location: Location;
    accountId: string;
    onQRModalOpen: () => void;
    onQRDataSet: (data: {
      url: string;
      title: string;
      subtitle: string;
    }) => void;
    isLargeScreen?: boolean;
    isExpanded?: boolean;
    onToggleExpanded?: () => void;
    isDownloadingQRs?: boolean;
    onDownloadingQRsChange?: (isDownloading: boolean) => void;
  }) => {
    const notify = useNotify();

    // Get floor plans for this location
    const { data: floorPlans } = useGetListFloorPlansLive({
      meta: {
        sellPointId: location.id,
      },
    });

    const activeFloorPlans = useMemo(() => {
      if (!floorPlans) return [];
      return floorPlans.filter((fp: FloorPlan) => fp.active);
    }, [floorPlans]);

    const generateQRCodeBlob = useCallback(
      async (qrCodeUrl: string): Promise<Blob> => {
        // Create a canvas to render optimized QR code for physical materials
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('Could not get canvas context');
        }

        // Use optimized canvas size for high-quality printing
        const size = QR_CONFIG.canvasSize;
        canvas.width = size;
        canvas.height = size;

        // Fill pure white background for maximum contrast
        ctx.fillStyle = QR_CONFIG.color.light;
        ctx.fillRect(0, 0, size, size);

        try {
          // Generate QR code with optimized settings for physical materials
          const QRCode = await import('qrcode');
          const qrDataURL = await QRCode.toDataURL(qrCodeUrl, {
            width: QR_CONFIG.printWidth,
            margin: QR_CONFIG.margin,
            color: QR_CONFIG.color,
            errorCorrectionLevel: QR_CONFIG.errorCorrectionLevel,
          });

          // Load the QR code image
          const img = new Image();
          await new Promise<void>((resolve, reject) => {
            img.onload = () => resolve();
            img.onerror = () =>
              reject(new Error('Failed to load QR code image'));
            img.src = qrDataURL;
          });

          // Calculate positioning with generous padding for mounting/borders
          const padding = size * QR_CONFIG.padding;
          const qrSize = size - padding * 2;
          const x = padding;
          const y = padding;

          // Draw with crisp pixel rendering for maximum quality
          ctx.imageSmoothingEnabled = false; // Preserve sharp edges
          ctx.drawImage(img, x, y, qrSize, qrSize);

          // Convert canvas to high-quality PNG
          const blob = await new Promise<Blob>((resolve, reject) => {
            canvas.toBlob(
              blob => {
                if (blob) {
                  resolve(blob);
                } else {
                  reject(new Error('Failed to create image blob'));
                }
              },
              'image/png',
              1.0
            ); // Maximum quality
          });

          return blob;
        } catch (error) {
          throw error;
        }
      },
      []
    );

    const generateQRCodeSVGBlob = useCallback(
      async (qrCodeUrl: string): Promise<Blob> => {
        try {
          // Generate SVG QR code
          const QRCode = await import('qrcode');
          const svgString = await QRCode.toString(qrCodeUrl, {
            type: 'svg',
            width: QR_CONFIG.printWidth,
            margin: QR_CONFIG.margin,
            color: QR_CONFIG.color,
            errorCorrectionLevel: QR_CONFIG.errorCorrectionLevel,
          });

          // Create SVG blob
          const blob = new Blob([svgString], { type: 'image/svg+xml' });
          return blob;
        } catch (error) {
          throw error;
        }
      },
      []
    );

    const downloadAllQRCodes = useCallback(async () => {
      if (isDownloadingQRs) return; // Prevent multiple simultaneous downloads

      onDownloadingQRsChange?.(true);
      try {
        notify('Generating QR codes...', { type: 'info' });

        const zip = new JSZip();
        const qrPromises: Array<
          Promise<{
            pngFilename: string;
            svgFilename: string;
            pngBlob: Blob;
            svgBlob: Blob;
          }>
        > = [];

        // Collect all QR generation promises for parallel processing
        for (
          let floorPlanIndex = 0;
          floorPlanIndex < activeFloorPlans.length;
          floorPlanIndex++
        ) {
          const floorPlan = activeFloorPlans[floorPlanIndex];
          if (floorPlan.items && floorPlan.items.length > 0) {
            for (const item of floorPlan.items) {
              // Create display text for table identification
              const displayText =
                item.tag && item.tag.trim()
                  ? item.tag
                  : `${floorPlan.label}${item.number || 'Unknown'}`;

              // Validate display text length
              if (displayText.length > 100) {
                if (import.meta.env.VITE_NODE_ENV === 'dev') {
                  console.warn(
                    'Display text too long for QR code:',
                    displayText
                  );
                }
                continue;
              }

              // Generate secure QR code URL
              try {
                const qrCodeUrl = generateSecureOrderNowUrl({
                  accountId,
                  sellPointId: location.id,
                  floorPlanIndex,
                  itemNumber: item.number || 'Unknown',
                });

                // Generate sanitized filename base
                const rawFilename = `${location.name}-${floorPlan.name}-${displayText}`;
                const filenameBase = sanitizeFilename(rawFilename);
                const pngFilename = `${filenameBase}.png`;
                const svgFilename = `${filenameBase}.svg`;

                // Add promise to array for parallel processing (generate both PNG and SVG)
                qrPromises.push(
                  Promise.all([
                    generateQRCodeBlob(qrCodeUrl),
                    generateQRCodeSVGBlob(qrCodeUrl),
                  ]).then(([pngBlob, svgBlob]) => ({
                    pngFilename,
                    svgFilename,
                    pngBlob,
                    svgBlob,
                  }))
                );
              } catch (error) {
                if (import.meta.env.VITE_NODE_ENV === 'dev') {
                  console.error(
                    `Failed to generate QR URL for ${displayText}:`,
                    error
                  );
                }
                // Skip this item and continue with others
                continue;
              }
            }
          }
        }

        if (qrPromises.length === 0) {
          notify('No QR codes to generate', { type: 'warning' });
          return;
        }

        // Process all QR codes in parallel (much faster)
        const qrResults = await Promise.all(qrPromises);

        // Add all files to zip (both PNG and SVG for each QR code)
        qrResults.forEach(({ pngFilename, svgFilename, pngBlob, svgBlob }) => {
          zip.file(pngFilename, pngBlob);
          zip.file(svgFilename, svgBlob);
        });

        // Generate and download the zip file
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        const url = URL.createObjectURL(zipBlob);

        try {
          const a = document.createElement('a');
          a.href = url;
          a.download = `${sanitizeFilename(location.name)}-QR-Codes.zip`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        } finally {
          URL.revokeObjectURL(url);
        }

        notify(
          `Successfully downloaded ${qrResults.length * 2} QR codes (PNG + SVG for each table)!`,
          {
            type: 'success',
          }
        );
      } catch (error) {
        if (import.meta.env.VITE_NODE_ENV === 'dev') {
          console.error('Failed to download QR codes:', error);
        }
        notify('Failed to download QR codes', { type: 'error' });
      } finally {
        onDownloadingQRsChange?.(false);
      }
    }, [
      isDownloadingQRs,
      onDownloadingQRsChange,
      activeFloorPlans,
      location.name,
      location.id,
      accountId,
      generateQRCodeBlob,
      generateQRCodeSVGBlob,
      notify,
    ]);

    // Generate QR code URL for a table
    const generateTableQRUrl = useCallback(
      (table: any, floorPlan: any) => {
        // Find the floor plan index in the active floor plans array
        const floorPlanIndex = activeFloorPlans.findIndex(
          fp => fp.id === floorPlan.id
        );

        return generateSecureOrderNowUrl({
          accountId,
          sellPointId: location.id,
          floorPlanIndex: Math.max(0, floorPlanIndex), // Ensure non-negative
          itemNumber: table.number || 'Unknown',
        });
      },
      [accountId, location.id, activeFloorPlans]
    );

    // Handle viewing QR code
    const handleViewQR = useCallback(
      (table: any, floorPlan: any) => {
        const qrUrl = generateTableQRUrl(table, floorPlan);
        const tableIdentifier =
          table.tag || `${floorPlan.label}${table.number}`;

        // Set QR data and open modal
        onQRDataSet({
          url: qrUrl,
          title: `${location.name} - ${floorPlan.name}`,
          subtitle: tableIdentifier,
        });
        onQRModalOpen();
      },
      [generateTableQRUrl, location.name, onQRDataSet, onQRModalOpen]
    );

    return isLargeScreen ? (
      // Large screen layout with location-level accordion
      <Accordion
        expanded={isExpanded}
        onChange={onToggleExpanded}
        sx={{ mb: 2 }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            '& .MuiAccordionSummary-content': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              margin: '12px 0',
            },
            '& .MuiAccordionSummary-expandIconWrapper': {
              marginLeft: '16px', // Add more space between button and expand icon
            },
          }}
        >
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
              {location.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              View and download print-ready QR codes for tables in this floor
              plan
            </Typography>
          </Box>
          <Box
            onClick={e => {
              e.stopPropagation(); // Prevent accordion toggle
              downloadAllQRCodes();
            }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 0.75,
              flexShrink: 0,
              bgcolor: isDownloadingQRs
                ? 'action.disabledBackground'
                : 'primary.main',
              color: isDownloadingQRs
                ? 'action.disabled'
                : 'primary.contrastText',
              px: 2,
              py: 1.5,
              minHeight: '42px',
              borderRadius: '4px',
              cursor: isDownloadingQRs ? 'default' : 'pointer',
              userSelect: 'none',
              transition: 'all 0.2s ease-in-out',
              border: '1px solid',
              borderColor: isDownloadingQRs
                ? 'action.disabledBackground'
                : 'primary.main',
              '&:hover': {
                bgcolor: isDownloadingQRs
                  ? 'action.disabledBackground'
                  : 'primary.dark',
                borderColor: isDownloadingQRs
                  ? 'action.disabledBackground'
                  : 'primary.dark',
              },
              '&:active': {
                transform: isDownloadingQRs ? 'none' : 'scale(0.98)',
              },
            }}
            title={isDownloadingQRs ? 'Generating...' : 'Download All QRs'}
          >
            {isDownloadingQRs && <CircularProgress size={16} color="inherit" />}
            <Typography
              variant="button"
              sx={{
                fontWeight: 500,
                fontSize: '0.875rem',
                textTransform: 'none',
                lineHeight: 1.2,
              }}
            >
              {isDownloadingQRs ? 'Generating...' : 'Download All QRs'}
            </Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <FloorPlanTabSelector
            sellPointId={location.id}
            mode="qr"
            title=""
            description=""
            onViewQR={handleViewQR}
          />
        </AccordionDetails>
      </Accordion>
    ) : (
      // Small screen layout - direct display without accordion wrapper
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1,
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            {location.name}
          </Typography>
          <Button
            variant="contained"
            size="small"
            onClick={downloadAllQRCodes}
            disabled={isDownloadingQRs}
            startIcon={
              isDownloadingQRs ? <CircularProgress size={16} /> : undefined
            }
            sx={{ ml: 2 }}
          >
            {isDownloadingQRs ? 'Generating...' : 'Download All QRs'}
          </Button>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          View and download print-ready QR codes for tables in this location
        </Typography>
        <FloorPlanTabSelector
          sellPointId={location.id}
          mode="qr"
          title=""
          description=""
          onViewQR={handleViewQR}
        />
      </Box>
    );
  }
);

// Add display name for better debugging
LocationCard.displayName = 'LocationCard';

export const MyIntegrationsOrderNowShow = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');
  const { details: fbDetails } = useFirebase();
  const notify = useNotify();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));

  // Get all locations
  const {
    data: allLocations,
    isLoading: locationsLoading,
    error: locationsError,
  } = useGetListLocationsLive({
    filter: { _d: false },
  });

  // Get account ID from Firebase context
  const accountId = fbDetails?.selectedAccount || 'unknown-account';

  // State for tracking download loading per location
  const [downloadingStates, setDownloadingStates] = useState<
    Record<string, boolean>
  >({});

  // State for QR code modal
  const [isQRModalOpen, setIsQRModalOpen] = useState(false);
  const [currentQRCode, setCurrentQRCode] = useState<{
    url: string;
    title: string;
    subtitle: string;
  } | null>(null);

  // State for accordion expansion (only one can be expanded at a time)
  const [expandedLocationId, setExpandedLocationId] = useState<string | false>(
    false
  );

  // State for test panel
  const [showTestPanel, setShowTestPanel] = useState(false);
  const [testParams, setTestParams] = useState({
    accountId: '',
    sellPointId: '',
    floorPlanIndex: 0,
    itemNumber: '1',
  });
  const [testResults, setTestResults] = useState<{
    url: string;
    token: string;
    decoded: any;
    error: string | null;
  } | null>(null);

  // Filter to only show active locations (sellPoints) that are configured in the integration
  const activeLocations = useMemo(() => {
    if (!allLocations || !record?.sellPoints) return [];

    return allLocations.filter((location: Location) => {
      const locationConfig = record.sellPoints[location.id];
      return locationConfig?.active === true;
    });
  }, [allLocations, record?.sellPoints]);

  // Handle accordion expansion - only one can be expanded at a time
  const handleAccordionToggle = useCallback((locationId: string) => {
    setExpandedLocationId(prevExpanded =>
      prevExpanded === locationId ? false : locationId
    );
  }, []);

  // Handle download state changes per location
  const handleDownloadingStateChange = useCallback(
    (locationId: string, isDownloading: boolean) => {
      setDownloadingStates(prev => ({
        ...prev,
        [locationId]: isDownloading,
      }));
    },
    []
  );

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={`${record?.name || 'OrderNow Integration'} - QR Codes`}
      >
        <Button
          variant="contained"
          color="primary"
          onClick={() => {
            redirect('edit', resource, record?.id, undefined, {
              _scrollToTop: false,
            });
          }}
        >
          {t('shared.edit')}
        </Button>
      </ModalHeader>

      <Box sx={{ p: 3, width: '100%' }}>
        {/* Debug section - only in development */}
        {import.meta.env.VITE_NODE_ENV === 'dev' && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Debug: Secure URL Encoding/Decoding + Tamper Detection Test
            </Typography>
            <Box
              sx={{ mb: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}
            >
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, fontWeight: 'bold' }}
              >
                🎯 QR Code Optimization for Physical Materials
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>Error Correction:</strong> Level H (~30% recovery) -
                resists damage/wear
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>Margins:</strong> 4 modules on each side - improves
                scanner detection
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>Resolution:</strong> 2400px canvas, 2000px QR - crisp
                printing at any size
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>Contrast:</strong> Pure black/white (#000/#FFF) -
                optimal for all materials
              </Typography>
              <Typography variant="body2">
                • <strong>Padding:</strong> 15% border space - accommodates
                mounting/engraving borders
              </Typography>
            </Box>
            <Button
              variant="outlined"
              onClick={async () => {
                try {
                  const testParams = {
                    accountId: accountId || 'test-account-id',
                    sellPointId: 'test-sellpoint-id',
                    floorPlanIndex: 2,
                    itemNumber: '123',
                  };

                  // Generate the secure URL
                  const testUrl = generateSecureOrderNowUrl(testParams);

                  // Extract token from URL for display
                  const token = testUrl.split('/t/')[1];

                  console.log('=== SECURE URL GENERATION TEST ===');
                  console.log('Original params:', testParams);
                  console.log('Generated URL:', testUrl);
                  console.log('URL length:', testUrl.length, 'characters');
                  console.log('Token:', token);
                  console.log('Token length:', token.length, 'characters');

                  await navigator.clipboard.writeText(testUrl);
                  notify(
                    'Test URL generated and copied! Check console for details.',
                    { type: 'info' }
                  );
                } catch (error) {
                  if (import.meta.env.VITE_NODE_ENV === 'dev') {
                    console.error('Failed to generate test URL:', error);
                  }
                  notify('Failed to generate test URL', { type: 'error' });
                }
              }}
            >
              Test URL Generation
            </Button>
            <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
              Account ID: {accountId || 'Not loaded'} | Check console for
              detailed results
            </Typography>
          </Box>
        )}

        {/* Token Encode/Decode Test Panel - only in development */}
        {import.meta.env.VITE_NODE_ENV === 'dev' && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 2,
              }}
            >
              <Typography variant="h6">
                Token Encode/Decode Test Panel
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setShowTestPanel(!showTestPanel)}
              >
                {showTestPanel ? 'Hide' : 'Show'} Test Panel
              </Button>
            </Box>

            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              Test the encoding and decoding of OrderNow tokens. This panel is
              available only in development mode.
            </Typography>

            {showTestPanel && (
              <Box>
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
                    gap: 3,
                  }}
                >
                  {/* Input Section */}
                  <Box>
                    <Typography
                      variant="subtitle2"
                      sx={{ mb: 1, fontWeight: 'bold' }}
                    >
                      Input Parameters
                    </Typography>
                    <Box
                      sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
                    >
                      <TextField
                        label="Account ID"
                        value={testParams.accountId}
                        onChange={e =>
                          setTestParams(prev => ({
                            ...prev,
                            accountId: e.target.value,
                          }))
                        }
                        placeholder={accountId || 'test-account-id'}
                        size="small"
                        fullWidth
                      />
                      <TextField
                        label="SellPoint ID"
                        value={testParams.sellPointId}
                        onChange={e =>
                          setTestParams(prev => ({
                            ...prev,
                            sellPointId: e.target.value,
                          }))
                        }
                        placeholder="test-sellpoint-id"
                        size="small"
                        fullWidth
                      />
                      <TextField
                        label="Floor Plan Index"
                        type="number"
                        value={testParams.floorPlanIndex}
                        onChange={e =>
                          setTestParams(prev => ({
                            ...prev,
                            floorPlanIndex: parseInt(e.target.value) || 0,
                          }))
                        }
                        size="small"
                        fullWidth
                        inputProps={{ min: 0, max: 99 }}
                      />
                      <TextField
                        label="Item Number"
                        value={testParams.itemNumber}
                        onChange={e =>
                          setTestParams(prev => ({
                            ...prev,
                            itemNumber: e.target.value,
                          }))
                        }
                        size="small"
                        fullWidth
                      />
                      <Button
                        variant="contained"
                        onClick={() => {
                          try {
                            const params = {
                              accountId:
                                testParams.accountId ||
                                accountId ||
                                'test-account-id',
                              sellPointId:
                                testParams.sellPointId || 'test-sellpoint-id',
                              floorPlanIndex: testParams.floorPlanIndex,
                              itemNumber: testParams.itemNumber,
                            };

                            // Generate URL
                            const url = generateSecureOrderNowUrl(params);
                            const token = url.split('/t/')[1];

                            // Try to decode
                            const decoded = decodeOrderNowToken(token);

                            setTestResults({
                              url,
                              token,
                              decoded,
                              error: decoded ? null : 'Failed to decode token',
                            });

                            console.log('=== TOKEN TEST RESULTS ===');
                            console.log('Input:', params);
                            console.log('Generated URL:', url);
                            console.log('Token:', token);
                            console.log('Decoded:', decoded);
                          } catch (error) {
                            setTestResults({
                              url: '',
                              token: '',
                              decoded: null,
                              error:
                                error instanceof Error
                                  ? error.message
                                  : 'Unknown error',
                            });
                            console.error('Test error:', error);
                          }
                        }}
                      >
                        Test Encode/Decode
                      </Button>
                    </Box>
                  </Box>

                  {/* Results Section */}
                  <Box>
                    <Typography
                      variant="subtitle2"
                      sx={{ mb: 1, fontWeight: 'bold' }}
                    >
                      Results
                    </Typography>
                    {testResults ? (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 2,
                        }}
                      >
                        {testResults.error ? (
                          <Box
                            sx={{
                              p: 2,
                              bgcolor: 'error.light',
                              borderRadius: 1,
                            }}
                          >
                            <Typography variant="body2" color="error">
                              <strong>Error:</strong> {testResults.error}
                            </Typography>
                          </Box>
                        ) : (
                          <>
                            <Box
                              sx={{
                                p: 2,
                                bgcolor: 'success.light',
                                borderRadius: 1,
                              }}
                            >
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                <strong>Generated URL:</strong>
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  wordBreak: 'break-all',
                                  fontFamily: 'monospace',
                                  bgcolor: 'white',
                                  p: 1,
                                  borderRadius: 0.5,
                                  display: 'block',
                                }}
                              >
                                {testResults.url}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{ mt: 1, display: 'block' }}
                              >
                                Length: {testResults.url.length} characters
                              </Typography>
                            </Box>

                            <Box
                              sx={{
                                p: 2,
                                bgcolor: 'info.light',
                                borderRadius: 1,
                              }}
                            >
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                <strong>Token:</strong>
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  wordBreak: 'break-all',
                                  fontFamily: 'monospace',
                                  bgcolor: 'white',
                                  p: 1,
                                  borderRadius: 0.5,
                                  display: 'block',
                                }}
                              >
                                {testResults.token}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{ mt: 1, display: 'block' }}
                              >
                                Length: {testResults.token.length} characters
                              </Typography>
                            </Box>

                            {testResults.decoded && (
                              <Box
                                sx={{
                                  p: 2,
                                  bgcolor: 'success.light',
                                  borderRadius: 1,
                                }}
                              >
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                  <strong>Decoded Data:</strong>
                                </Typography>
                                <Box
                                  sx={{
                                    fontFamily: 'monospace',
                                    fontSize: '0.75rem',
                                  }}
                                >
                                  <div>
                                    Account Partial:{' '}
                                    {testResults.decoded.accountPartial}
                                  </div>
                                  <div>
                                    SellPoint Partial:{' '}
                                    {testResults.decoded.sellPointPartial}
                                  </div>
                                  <div>
                                    Floor Plan Index:{' '}
                                    {testResults.decoded.floorPlanIndex}
                                  </div>
                                  <div>
                                    Item Number:{' '}
                                    {testResults.decoded.itemNumber}
                                  </div>
                                </Box>
                              </Box>
                            )}

                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={() => {
                                  navigator.clipboard.writeText(
                                    testResults.url
                                  );
                                  notify('URL copied to clipboard!', {
                                    type: 'success',
                                  });
                                }}
                              >
                                Copy URL
                              </Button>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={() => {
                                  navigator.clipboard.writeText(
                                    testResults.token
                                  );
                                  notify('Token copied to clipboard!', {
                                    type: 'success',
                                  });
                                }}
                              >
                                Copy Token
                              </Button>
                            </Box>
                          </>
                        )}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Click "Test Encode/Decode" to see results
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        )}

        {locationsLoading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="text.secondary">
              Loading locations...
            </Typography>
          </Box>
        ) : locationsError ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="error">
              Error loading locations: {locationsError.message}
            </Typography>
          </Box>
        ) : activeLocations.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="text.secondary">
              No active locations found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Configure locations in the integration settings to generate QR
              codes
            </Typography>
          </Box>
        ) : (
          <Box>
            {activeLocations.map((location: Location) => (
              <LocationCard
                key={location.id}
                location={location}
                accountId={accountId}
                onQRModalOpen={() => setIsQRModalOpen(true)}
                onQRDataSet={setCurrentQRCode}
                isLargeScreen={isLargeScreen}
                isExpanded={expandedLocationId === location.id}
                onToggleExpanded={() => handleAccordionToggle(location.id)}
                isDownloadingQRs={downloadingStates[location.id] || false}
                onDownloadingQRsChange={isDownloading =>
                  handleDownloadingStateChange(location.id, isDownloading)
                }
              />
            ))}
          </Box>
        )}
      </Box>

      {/* QR Code Modal */}
      {currentQRCode && (
        <Dialog
          open={isQRModalOpen}
          onClose={() => setIsQRModalOpen(false)}
          maxWidth="sm"
          fullWidth
          fullScreen={isSmallScreen} // Fullscreen on sm and below, dialog on md+
          onClick={e => e.stopPropagation()} // Prevent event bubbling
          disablePortal={false} // Ensure modal is properly portaled
          hideBackdrop={false} // Keep backdrop for proper modal behavior
          disableEscapeKeyDown={false} // Allow ESC to close
        >
          <ModalHeader
            handleClose={() => setIsQRModalOpen(false)}
            title={`${currentQRCode.title} - ${currentQRCode.subtitle}`}
          />
          <DialogContent onClick={e => e.stopPropagation()}>
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
                <QRCodeSVG
                  value={currentQRCode.url}
                  size={isSmallScreen ? 280 : 320} // Responsive QR code size
                  level={QR_CONFIG.errorCorrectionLevel}
                  marginSize={QR_CONFIG.margin}
                  fgColor={QR_CONFIG.color.dark}
                  bgColor={QR_CONFIG.color.light}
                />
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  justifyContent: 'center',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%',
                  maxWidth: isSmallScreen ? '300px' : '400px',
                  mx: 'auto',
                }}
              >
                {/* Row 1: Copy URL and Copy Print-Ready QR */}
                <Box
                  sx={{
                    display: 'flex',
                    gap: 2,
                    width: '100%',
                    flexDirection: isSmallScreen ? 'column' : 'row',
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={async () => {
                      try {
                        await navigator.clipboard.writeText(currentQRCode.url);
                        notify('URL copied to clipboard!', { type: 'success' });
                      } catch (error) {
                        notify('Failed to copy URL', { type: 'error' });
                      }
                    }}
                    fullWidth={isSmallScreen}
                    sx={{ flex: 1 }}
                  >
                    Copy URL
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={async () => {
                      try {
                        // Create a canvas for high-quality QR code optimized for physical materials
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        if (!ctx) {
                          throw new Error('Could not get canvas context');
                        }

                        // Use optimized canvas size for high-quality printing
                        const size = QR_CONFIG.canvasSize;
                        canvas.width = size;
                        canvas.height = size;

                        // Fill pure white background for maximum contrast
                        ctx.fillStyle = QR_CONFIG.color.light;
                        ctx.fillRect(0, 0, size, size);

                        // Generate high-resolution QR code with optimized settings
                        const QRCode = await import('qrcode');
                        const qrDataURL = await QRCode.toDataURL(
                          currentQRCode.url,
                          {
                            width: QR_CONFIG.printWidth,
                            margin: QR_CONFIG.margin,
                            color: QR_CONFIG.color,
                            errorCorrectionLevel:
                              QR_CONFIG.errorCorrectionLevel,
                          }
                        );

                        // Load the QR code image
                        const img = new Image();
                        await new Promise<void>((resolve, reject) => {
                          img.onload = () => resolve();
                          img.onerror = () =>
                            reject(new Error('Failed to load QR code image'));
                          img.src = qrDataURL;
                        });

                        // Calculate positioning with generous padding for mounting/borders
                        const padding = size * QR_CONFIG.padding;
                        const qrSize = size - padding * 2;
                        const x = padding;
                        const y = padding;

                        // Draw with crisp pixel rendering for maximum quality
                        ctx.imageSmoothingEnabled = false; // Preserve sharp edges
                        ctx.drawImage(img, x, y, qrSize, qrSize);

                        // Convert canvas to blob and copy to clipboard
                        canvas.toBlob(
                          async blob => {
                            if (!blob) {
                              notify('Failed to generate QR code image', {
                                type: 'error',
                              });
                              return;
                            }

                            try {
                              // Copy to clipboard
                              await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob }),
                              ]);

                              notify('QR code copied to clipboard!', {
                                type: 'success',
                              });
                            } catch (error) {
                              notify('Failed to copy QR code to clipboard', {
                                type: 'error',
                              });
                            }
                          },
                          'image/png',
                          1.0
                        );
                      } catch (error) {
                        if (import.meta.env.VITE_NODE_ENV === 'dev') {
                          console.error('Failed to copy QR code:', error);
                        }
                        notify('Failed to copy QR code', { type: 'error' });
                      }
                    }}
                    fullWidth={isSmallScreen}
                    sx={{ flex: 1 }}
                  >
                    Copy QR
                  </Button>
                </Box>

                {/* Row 2: Download PNG and Download SVG */}
                <Box
                  sx={{
                    display: 'flex',
                    gap: 2,
                    width: '100%',
                    flexDirection: isSmallScreen ? 'column' : 'row',
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={async () => {
                      try {
                        // Create a canvas for high-quality QR code optimized for physical materials
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        if (!ctx) {
                          throw new Error('Could not get canvas context');
                        }

                        // Use optimized canvas size for high-quality printing
                        const size = QR_CONFIG.canvasSize;
                        canvas.width = size;
                        canvas.height = size;

                        // Fill pure white background for maximum contrast
                        ctx.fillStyle = QR_CONFIG.color.light;
                        ctx.fillRect(0, 0, size, size);

                        // Generate high-resolution QR code with optimized settings
                        const QRCode = await import('qrcode');
                        const qrDataURL = await QRCode.toDataURL(
                          currentQRCode.url,
                          {
                            width: QR_CONFIG.printWidth,
                            margin: QR_CONFIG.margin,
                            color: QR_CONFIG.color,
                            errorCorrectionLevel:
                              QR_CONFIG.errorCorrectionLevel,
                          }
                        );

                        // Load the QR code image
                        const img = new Image();
                        await new Promise<void>((resolve, reject) => {
                          img.onload = () => resolve();
                          img.onerror = () =>
                            reject(new Error('Failed to load QR code image'));
                          img.src = qrDataURL;
                        });

                        // Calculate positioning with generous padding for mounting/borders
                        const padding = size * QR_CONFIG.padding;
                        const qrSize = size - padding * 2;
                        const x = padding;
                        const y = padding;

                        // Draw with crisp pixel rendering for maximum quality
                        ctx.imageSmoothingEnabled = false; // Preserve sharp edges
                        ctx.drawImage(img, x, y, qrSize, qrSize);

                        // Convert canvas to blob and download
                        canvas.toBlob(
                          blob => {
                            if (!blob) {
                              notify('Failed to generate QR code image', {
                                type: 'error',
                              });
                              return;
                            }

                            // Create download link with cleaner filename
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;

                            // Generate filename using the utility function
                            const filename = generateQRCodeFilenameFromModal(
                              currentQRCode.title,
                              currentQRCode.subtitle,
                              'png'
                            );
                            a.download = filename;

                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);

                            notify('QR code PNG downloaded!', {
                              type: 'success',
                            });
                          },
                          'image/png',
                          1.0
                        );
                      } catch (error) {
                        if (import.meta.env.VITE_NODE_ENV === 'dev') {
                          console.error('Failed to download QR code:', error);
                        }
                        notify('Failed to download QR code', { type: 'error' });
                      }
                    }}
                    fullWidth={isSmallScreen}
                    sx={{ flex: 1 }}
                  >
                    Download PNG
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={async () => {
                      try {
                        // Generate SVG QR code
                        const QRCode = await import('qrcode');
                        const svgString = await QRCode.toString(
                          currentQRCode.url,
                          {
                            type: 'svg',
                            width: QR_CONFIG.printWidth,
                            margin: QR_CONFIG.margin,
                            color: QR_CONFIG.color,
                            errorCorrectionLevel:
                              QR_CONFIG.errorCorrectionLevel,
                          }
                        );

                        // Create blob and download with cleaner filename
                        const blob = new Blob([svgString], {
                          type: 'image/svg+xml',
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;

                        // Generate filename using the utility function
                        const filename = generateQRCodeFilenameFromModal(
                          currentQRCode.title,
                          currentQRCode.subtitle,
                          'svg'
                        );
                        a.download = filename;

                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        notify('QR code SVG downloaded!', { type: 'success' });
                      } catch (error) {
                        if (import.meta.env.VITE_NODE_ENV === 'dev') {
                          console.error('Failed to download SVG:', error);
                        }
                        notify('Failed to download SVG', { type: 'error' });
                      }
                    }}
                    fullWidth={isSmallScreen}
                    sx={{ flex: 1 }}
                  >
                    Download SVG
                  </Button>
                </Box>
              </Box>
            </Box>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};
