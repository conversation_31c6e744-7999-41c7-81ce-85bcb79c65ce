import dayjs from 'dayjs';

const generatePaddedDaysArray = (
  startDay: string,
  endDay: string,
  data: any,
  property: string
) => {
  const dayMap = new Map();

  // Populate the map with the values for existing dates
  data.forEach(({ date, report }: any) => {
    if (report && report[0] && report[0][property] !== undefined) {
      dayMap.set(date, report[0][property]);
    }
  });

  // Generate the labels and padded values
  const labels = [];
  const values = [];

  let currentDate = dayjs(startDay);
  const lastDate = dayjs(endDay);

  while (currentDate.isBefore(lastDate) || currentDate.isSame(lastDate)) {
    const formattedDate = currentDate.format('DD MMM');
    const mapFormatDate = currentDate.format('YYYY-MM-DD');
    labels.push(formattedDate);
    values.push(dayMap.get(mapFormatDate) || 0); // Add the value or pad with 0
    currentDate = currentDate.add(1, 'day');
  }
  return { labels, values };
};

const generatePaddedHoursArray = (
  startHour: number,
  endHour: number,
  data: any,
  property: string
) => {
  let hasNextDay = startHour > endHour;
  const hourArray: { hour: number; day: string; value: number }[] = [];

  data.forEach(({ hourOfDay, whatDay, report }: any) => {
    if (report && report[0] && report[0][property] !== undefined) {
      if (hourOfDay < startHour || hourOfDay > endHour || whatDay == 'next') {
        hasNextDay = true;
      }

      hourArray.push({
        hour: hourOfDay,
        day: whatDay,
        value: report[0][property],
      });
    }
  });

  const labels: string[] = [];
  const values: number[] = [];

  const firstDayEndHour = hasNextDay ? 24 : endHour;

  const addToArrays = (hour: number, value: number) => {
    const isPM = hour >= 12;
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12; // Converts 0 or 24 to 12
    const period = isPM ? 'pm' : 'am';
    labels.push(`${formattedHour} ${period}`);
    values.push(value); // Add the value or pad with 0
  };

  // Fill hours for "this" day
  for (let hour = startHour; hour < firstDayEndHour; hour++) {
    const existing = hourArray.find(d => d.hour === hour && d.day === 'this');
    addToArrays(hour, existing ? existing.value : 0);
  }

  if (hasNextDay) {
    // Fill hours for "next" day up to endHour
    for (let hour = 0; hour < endHour; hour++) {
      const existing = hourArray.find(d => d.hour === hour && d.day === 'next');
      addToArrays(hour, existing ? existing.value : 0);
    }
  }

  return { labels, values };
};

export { generatePaddedDaysArray, generatePaddedHoursArray };
