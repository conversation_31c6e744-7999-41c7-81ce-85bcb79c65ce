import { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';

import { useTheme } from '~/contexts';
import { formatNumber, FormatNumberType } from '~/utils/formatNumber';
import { DarkTooltip } from '../DarkTooltip';

interface HorizontaBarChartProps {
  chartData: Array<{
    value: number;
    label: string;
  }>;
  withTable?: boolean;
  tableStyle?: 'normal' | 'tiny';
  colorSet?: number;
  chartHeight?: string;
  formatData?: (el: string | number) => string | number;
}

export default function HorizontaBarChart({
  chartData,
  withTable,
  tableStyle = 'normal',
  colorSet = 0,
  chartHeight = 'auto',
  formatData = el => el,
}: HorizontaBarChartProps) {
  const { getChartColours } = useTheme();
  const chartColors = getChartColours(chartData.length, colorSet);

  const [percentages, setPercentages] = useState<string[]>();

  useEffect(() => {
    const total = chartData.reduce((acc, data) => acc + data?.value, 0);
    const tmp = chartData.map(el =>
      formatNumber(el?.value / total, 'percentage')
    );
    setPercentages(tmp as string[]);
  }, [chartData]);

  const isTiny = tableStyle === 'tiny';

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        py: 2,
      }}
    >
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-end',
          height: chartHeight,
        }}
      >
        <Box sx={{ display: 'flex', width: '100%' }}>
          {percentages?.map((el, index) => (
            <DarkTooltip
              key={index}
              sx={{
                '.MuiTooltip-tooltipPlacementTop': {
                  maxWidth: '300px !important',
                },
              }}
              text={`${formatData(chartData[index]?.value)} (${el})`}
              label={chartData[index]?.label}
            >
              <Box
                mr={0.5}
                sx={{
                  height: '50px',
                  width: el,
                  bgcolor: chartColors[index],
                  borderRadius: '4px',
                  border: '2px solid',
                  borderColor: 'transparent',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: 'black',
                  },
                }}
              />
            </DarkTooltip>
          ))}
        </Box>

        {/* percentages under graphic */}
        {/* <Box sx={{ display: 'flex', width: '100%' }}>
          {percentages?.map((el, index) => (
            <Box
              key={index}
              sx={{
                width: el,
                textAlign: 'center',
              }}
            >
              <Typography variant='caption' color='custom.gray600'>
                {el}
              </Typography>
            </Box>
          ))}
        </Box> */}
      </Box>

      {withTable && (
        <Box sx={{ mt: isTiny ? 1.5 : 3 }}>
          {chartData.map((el, index) => {
            return (
              <Box
                key={`${index}-${el}`}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  '@media print': {
                    backgroundColor: '#FFFFFF !important',
                    color: 'black !important',
                  },
                }}
                mb={isTiny ? 1 : 2}
                mr={0.5}
              >
                <Box
                  sx={{
                    width: '13px',
                    height: '13px',
                    borderRadius: isTiny ? '50%' : '20%',
                    backgroundColor: chartColors[index],
                    mr: isTiny ? 1.5 : 2,
                  }}
                />
                {/* @ts-ignore */}
                <Typography variant="label" fontWeight={isTiny ? 300 : 400}>
                  {el?.label}
                </Typography>
                <Box sx={{ flex: 1 }} />
                {/* @ts-ignore */}
                <Typography variant="label" fontWeight={300}>
                  {isTiny
                    ? `${formatData(el?.value)} (${percentages?.[index]})`
                    : formatData(el?.value)}
                </Typography>
                {!isTiny && (
                  <Box sx={{ width: '25%', textAlign: 'right' }}>
                    <Typography
                      // @ts-ignore
                      variant="label"
                      fontWeight={400}
                      color="custom.gray600"
                    >
                      {percentages?.[index]}
                    </Typography>
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>
      )}
    </Box>
  );
}
