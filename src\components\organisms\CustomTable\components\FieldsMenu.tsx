import React, { MouseEvent, useState } from 'react';
import ViewWeekIcon from '@mui/icons-material/ViewWeek';
import { Button, Checkbox, Menu, MenuItem, useMediaQuery, Theme } from '@mui/material';

import styles from '../styles.module.css';
import { useTranslation } from 'react-i18next';
type RowData = {
  [key: string]: any;
  items?: RowData[];
  subItems?: RowData[];
  extraData?: { [key: string]: any };
};

interface FieldOption {
  isChecked: boolean;
  value: string;
}

interface FieldsMenuProps {
  config: RowData[];
  fields: FieldOption[];
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  columnsToFilter?: Array<string>;
}

const FieldsMenu = ({
  config,
  fields,
  setFields,
  columnsToFilter,
}: FieldsMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { t } = useTranslation();
  const handleToggleColumn = (columnId: string) => {
    setFields(prevFields =>
      prevFields.map(field =>
        field.value === columnId
          ? { ...field, isChecked: !field.isChecked }
          : field
      )
    );
  };

  const handleOpenMenu = (event: MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget);

  const handleCloseMenu = () => setAnchorEl(null);

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));

  return (
    <>
      <Button
        className={styles.filterButton}
        onClick={handleOpenMenu}
        sx={{
          fontSize: { xs: '12px', sm: '14px' },
          px: { xs: 0, sm: 1 },
          '&:hover': { bgcolor: 'transparent', color: '#0046a8' },
        }}
        startIcon={<ViewWeekIcon />}
      >
        {isXSmall ? '' : t('shared.columns')}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        {config.map(column => {
          if (columnsToFilter && !columnsToFilter.includes(column.id as string))
            return null;
          const fieldOption = fields.find(f => f.value === column.id);
          return (
            <MenuItem
              key={column.id as string}
              onClick={() => handleToggleColumn(column.id as string)}
              sx={{
                textTransform: 'capitalize',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                cursor: 'pointer',
              }}
            >
              <Checkbox
                checked={fieldOption?.isChecked || false}
                color="primary"
              />
              {column.label}
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};

export default FieldsMenu;
