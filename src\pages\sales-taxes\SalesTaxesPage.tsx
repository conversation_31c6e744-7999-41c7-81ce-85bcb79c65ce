import { Box } from '@mui/material';

import PageTitle from '~/components/molecules/PageTitle';
import SalesTaxesList from './SalesTaxesList';
import { useTranslation } from 'react-i18next';

export default function SalesTaxesPage() {
  const { t } = useTranslation();
  return (
    <Box p={2}>
      <PageTitle
        title={t('salesTaxes.title')}
        description={
          <>
            {t('salesTaxes.description')}
            <br /> {t('salesTaxes.description2')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
      />
      <SalesTaxesList />
    </Box>
  );
}
