import { useMemo } from 'react';
import { Box, Chip, Theme, Typography, useMediaQuery } from '@mui/material';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  AutocompleteInput,
  CreateButton,
  FilterButton,
  List,
  ReferenceInput,
  TextInput,
  TopToolbar,
  useCreatePath,
  useListContext,
  useRedirect,
  useTranslate,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { CustomEmpty } from '~/components/organisms/CustomEmpty';
import {
  RESOURCES,
  resourcesInfo,
  useGetListLocationsLive,
} from '~/providers/resources';
import CustomSearchInput from '../../components/atoms/inputs/CustomSearchInput';
import { CustomSelect } from '../../components/atoms/inputs/CustomSelect';
import ListCard from '../../components/atoms/ListCard';
import { useTheme } from '../../contexts';

const TopText = ({ text, color }: { text: string; color: string }) => {
  return (
    <Typography
      variant="body2"
      fontWeight={'500'}
      color={color}
      sx={{ textTransform: 'uppercase' }}
    >
      {text}
    </Typography>
  );
};

/**
 * We need another component that is wrapped in a List component
 * so useListContext knows where to get data from.
 * If we use useListContext directly in DeviceList,
 * data will be null.
 */
const DeviceListInner = () => {
  const { theme } = useTheme();
  const { data: devices } = useListContext();
  const { data: sellpoints } = useGetListLocationsLive();
  const redirect = useRedirect();

  const getSellPointName = (id: string) => {
    return sellpoints?.find(el => el.id === id)?.name || id;
  };

  return (
    <Box
      sx={{
        py: 1,
        display: 'grid',
        gridTemplateColumns: {
          xs: 'repeat(2, 1fr)',
          md: 'repeat(3,1fr)',
          lg: 'repeat(4,1fr)',
          xl: 'repeat(5,1fr)',
        },
        gap: 3,
        // mt: 4,
      }}
    >
      {devices?.map(el => (
        <ListCard
          key={el.id}
          onClick={() => {
            redirect('edit', RESOURCES.DEVICES, el.id, el, {
              _scrollToTop: false,
            });
          }}
          sx={{
            minHeight: '120px',
            justifyContent: 'space-between',
            alignItems: 'normal',
            paddingBottom: 3,
            bgcolor: el.disabled
              ? '#F2F2F2'
              : el.deviceUniqueId
                ? '#EBF6EF'
                : '#FEECEE',
            opacity: el.disabled || el.deviceUniqueId ? 1 : 0.8,
            boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.2)',
            transition: 'box-shadow 0.2s ease',
            '&:hover': {
              boxShadow: 'none',
            },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
            }}
          >
            {el.disabled ? (
              <>
                <img src="/assets/icons/device-disabled.svg" alt="image-icon" />
                <TopText color="error.main" text="Disabled" />
              </>
            ) : el.deviceUniqueId ? (
              <>
                <img src="/assets/icons/device-check.svg" alt="image-icon" />
                <TopText color="success.main" text="Paired" />
              </>
            ) : (
              <>
                <img src="/assets/icons/device-unpaired.svg" alt="image-icon" />
                <TopText color="custom.warningText" text="Unpaired" />
              </>
            )}
          </Box>
          <Box>
            <Typography
              variant="body2"
              color={
                el.disabled
                  ? 'error.main'
                  : el.deviceUniqueId
                    ? 'success.main'
                    : 'custom.warningText'
                // el.disabled || theme.palette.mode === 'dark'
                //   ? 'inherit'
                //   : theme.palette.primary.main
              }
              fontWeight={700}
              textAlign={'center'}
            >
              {el.name}
            </Typography>
            <Typography variant="body2" textAlign={'center'}>
              {getSellPointName(el.sellPointId)}
            </Typography>
          </Box>
          <Box />
        </ListCard>
      ))}
    </Box>
  );
};

const QuickFilter = ({
  label,
}: {
  label: string;
  source?: string;
  defaultValue?: any;
}) => {
  const translate = useTranslate();
  return <Chip sx={{ marginBottom: 1 }} label={translate(label)} />;
};

export default function DeviceList() {
  const { t } = useTranslation();

  const filters = useMemo(
    () => [
      <CustomSearchInput key="search-input" source="q" alwaysOn />,
      //   <CustomSelect
      //     key="disabled-select"
      //     source="disabled"
      //     label="State"
      //     choices={[
      //       { id: 'true', name: 'Disabled' },
      //       { id: 'false', name: 'Paired' },
      //     ]}
      //   />,
      <ReferenceInput
        key="location-sellpoint"
        label={t('shared.location')}
        source="sellPointId"
        reference={RESOURCES.LOCATIONS}
        perPage={50}
        sort={{ field: 'name', order: 'ASC' }}
        filter={{ _d: false }}
        filterToQuery={(searchText: any) => ({ name: [searchText] })}
      >
        <AutocompleteInput
          label={t('shared.location')}
          optionText="name"
          optionValue="id"
        />
      </ReferenceInput>,
      <QuickFilter source="pairedAt_eq" label="Not Paired" defaultValue={0} />,
      <QuickFilter source="pairedAt_gt" label="Paired" defaultValue={0} />,

      //   <TextInput
      //     key="name-search"
      //     label={t('devicesPage.deviceName')}
      //     source="name"
      //   />,
    ],
    [t]
  );

  const ListActions = () => {
    const isXSmall = useMediaQuery<Theme>(theme =>
      theme.breakpoints.down('md')
    );

    return (
      <TopToolbar>
        <FilterButton filters={filters} />
        <CreateButton
          variant="contained"
          label={t('devicesPage.addDevice')}
          {...(isXSmall ? {} : { icon: <></> })}
        />
      </TopToolbar>
    );
  };
  return (
    <List
      resource={RESOURCES.DEVICES}
      perPage={Number.MAX_SAFE_INTEGER}
      pagination={false}
      component={'div'}
      filters={filters}
      sort={resourcesInfo[RESOURCES.DEVICES].defaultSort}
      actions={<ListActions />}
      empty={<CustomEmpty />}
    >
      <DeviceListInner />
      <ListLiveUpdate />
    </List>
  );
}
