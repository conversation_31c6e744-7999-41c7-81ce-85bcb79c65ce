import { useEffect, useMemo, useState } from 'react';
import { Box, Button, Dialog } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import extractItems from '../../../../utils/extractItemsFromObj';
import CheckboxList from '../../../molecules/CheckboxList';
import ModalHeader from '../../../molecules/ModalHeader';

interface ComboAddTileModalProps {
  type: 'product' | 'displayGroup';
  items: any[];
  addItems: (items: any[]) => void;
  onClose: () => void;
}
export default function ComboAddTileModal({
  type,
  items,
  addItems,
  onClose,
}: ComboAddTileModalProps) {
  const [selectedItems, setSelectedItems] = useState<
    { id: string; type: string }[]
  >([]);

  const itemsLength = useMemo(() => {
    return selectedItems.length;
  }, [selectedItems]);

  const addItemsH = () => {
    addItems(selectedItems);
    onClose();
  };

  return (
    <Dialog open onClose={onClose} fullWidth={true} maxWidth={'md'}>
      <ModalHeader
        handleClose={onClose}
        title={'Add ' + (type === 'displayGroup' ? 'Display Groups' : 'Items')}
      >
        <Button
          // @ts-ignore
          variant="contained-light"
          onClick={addItemsH}
          disabled={itemsLength === 0}
        >
          Add {itemsLength} {itemsLength === 1 ? 'item' : 'items'}
        </Button>
      </ModalHeader>
      <Box
        sx={{
          p: 4,
          pt: 2,
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(80vh - 80px)',
        }}
      >
        <CheckboxList
          type={type}
          items={items}
          selectedItems={selectedItems}
          updateSelectedItems={setSelectedItems}
        />
      </Box>
    </Dialog>
  );
}
