import { Typography } from '@mui/material';

import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function PromotionsGraph({
  data,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
}) {
  if (!data.datasets || !data.labels) {
    return <></>;
  }

  return (
    <>
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        fontWeight="500"
        mb={1.5}
      >
        {data.datasets.length >= 3 &&
          `Top ${data.datasets.length} Promotions: Amount Discounted`}
      </Typography>
      <ReportMultiLineChart datasets={data.datasets} labels={data.labels} />
    </>
  );
}
