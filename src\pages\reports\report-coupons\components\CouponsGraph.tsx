import { Typography } from '@mui/material';

import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function CouponsGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency?: CurrencyType;
}) {
  if (!data.datasets || !data.labels) {
    return <></>;
  }

  return (
    <>
      <Typography
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        variant="body2"
        fontWeight="500"
        mb={1.5}
      >
        {data.datasets.length >= 3 &&
          `Top ${data.datasets.length} Coupons: Amount Discounted`}
      </Typography>
      <ReportMultiLineChart
        datasets={data.datasets}
        labels={data.labels}
        formatData={data => formatNumber(data, currency)}
      />
    </>
  );
}
