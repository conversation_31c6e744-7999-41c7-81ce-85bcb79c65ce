import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { onValue, ref } from 'firebase/database';
import { debounce } from 'lodash';

import { storage } from '../configs/firebaseConfig';
import { useFirebase } from '../contexts/FirebaseContext';
import { getReport } from '../fake-provider/reports/getReport';
import { getDateIntervalBreakdown } from '../fake-provider/reports/utils/getDateIntervalBreakdown';
import { getReportsDataFromStorage } from '../fake-provider/reports/utils/getReportsDataFromStorage';
import reportWorkerUrl from '../workers/reportWorker.ts?worker&url';
import { useGlobalResourceFilters } from './useGlobalResourceFilters';

import type {
  OmitKeysWithTypeTransform,
  Report,
  ReportType,
} from '../fake-provider/reports/types';

// Create a worker for heavy data processing
let reportWorker: Worker | null | boolean = null;

// Lazily initialize the worker when needed
const getReportWorker = () => {
  if (!reportWorker && typeof Worker !== 'undefined') {
    try {
      // Try to create as a module worker first
      reportWorker = new Worker(reportWorkerUrl, { type: 'module' });
      console.log('Worker initialized as module successfully');
    } catch (e) {
      console.error('Failed to initialize report worker as module:', e);

      // Fallback to classic worker
      try {
        reportWorker = new Worker(reportWorkerUrl);
        console.log('Worker initialized as classic successfully');
      } catch (e2) {
        console.error('Failed to initialize report worker in any mode:', e2);
        reportWorker = null;
      }
    }
  }
  return reportWorker;
};

// Helper function to log memory usage
const logMemoryUsage = (label: string) => {
  if (window.performance && (performance as any).memory) {
    console.log(
      `${label} - Memory: ${
        Math.round(
          ((performance as any).memory.usedJSHeapSize / (1024 * 1024)) * 100
        ) / 100
      } MB`
    );
  }
};

// Update your reducer to remove these states
const initialState = {
  reportData: [],
  localLoading: true,
  error: null,
  initialStorageFetchDone: false,
  reportDataTimestamp: 0,
};

interface ReportState {
  reportData: any[];
  localLoading: boolean;
  error: unknown;
  initialStorageFetchDone: boolean;
  reportDataTimestamp: number;
}

interface SetReportDataAction {
  type: 'SET_REPORT_DATA';
  payload: any[];
}

interface SetLoadingAction {
  type: 'SET_LOADING';
  payload: boolean;
}

interface SetErrorAction {
  type: 'SET_ERROR';
  payload: unknown;
}

interface ResetAction {
  type: 'RESET';
}

interface BatchUpdateAction {
  type: 'BATCH_UPDATE';
  payload: Partial<ReportState>;
}

interface SetInitialStorageFetchDoneAction {
  type: 'SET_INITIAL_STORAGE_FETCH_DONE';
  payload: boolean;
}

interface SetReportDataTimestampAction {
  type: 'SET_REPORT_DATA_TIMESTAMP';
  payload: number;
}

type ReportAction =
  | SetReportDataAction
  | SetLoadingAction
  | SetErrorAction
  | ResetAction
  | BatchUpdateAction
  | SetInitialStorageFetchDoneAction
  | SetReportDataTimestampAction;

function reportReducer(state: ReportState, action: ReportAction): ReportState {
  switch (action.type) {
    case 'SET_REPORT_DATA':
      return { ...state, reportData: action.payload };
    case 'SET_LOADING':
      return { ...state, localLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'RESET':
      return { ...state, localLoading: true, error: null };
    case 'BATCH_UPDATE':
      return { ...state, ...action.payload };
    case 'SET_INITIAL_STORAGE_FETCH_DONE':
      return { ...state, initialStorageFetchDone: action.payload };
    case 'SET_REPORT_DATA_TIMESTAMP':
      return { ...state, reportDataTimestamp: action.payload };
    default:
      return state;
  }
}

export const useReportRawData = <K extends keyof ReportType>(
  reportType: K,
  customDateRange?: [any, any]
) => {
  const { loading: fbLoading, details: fbDetails } = useFirebase();
  const {
    isLoading: rfLoading,
    sellPoints,
    sellPointId,
    dateRange: globalDateRange,
  } = useGlobalResourceFilters();

  // Use custom date range if provided, otherwise fall back to global date range
  const dateRange =
    customDateRange && customDateRange[0] && customDateRange[1]
      ? customDateRange
      : globalDateRange;

  console.log(
    `[${reportType}] Using date range: ${dateRange?.[0]?.format('YYYY-MM-DD')}-${dateRange?.[1]?.add(1, 'seconds').format('YYYY-MM-DD')}`
  );

  // Use refs to track previous values and prevent unnecessary updates
  const isFetchingAndProcessingStorageRef = useRef(false);
  const mountedRef = useRef(false);
  const storageFetchCountRef = useRef(0);
  const isInitialStorageFetchDoneRef = useRef(false);
  const workerCallbacksRef = useRef<Map<string, (data: any) => void>>(
    new Map()
  );
  // Add a timestamp ref to track the most recent data update
  const lastUpdateOperationTimestampRef = useRef(0);
  // Add a state update lock ref
  const isReportDataUpdateInProgressRef = useRef(false);

  // Add a ref to track the latest realtime data
  const latestRealtimeDataRef = useRef<Record<string, unknown>>({});
  // Add a ref for storage data
  const latestStorageDataRef = useRef<unknown[]>([]);

  const [state, dispatch] = useReducer(reportReducer, initialState);
  const {
    reportData,
    reportDataTimestamp,
    localLoading,
    error,
    initialStorageFetchDone,
  } = state;

  // Combined loading state from all sources
  const isLoading = fbLoading || rfLoading || localLoading;

  // Get the locale from the selected sell point
  const sellPoint = sellPoints?.find(sp => sp.id === sellPointId);
  const locale = sellPoint?.localization || 'en-US';

  // Initialize worker when the hook is first used
  useEffect(() => {
    const worker = getReportWorker();
    if (worker !== null && typeof worker !== 'boolean') {
      worker.onmessage = event => {
        const { requestId, data, error: workerError } = event.data;
        console.log(
          `[${reportType}] Worker message received for request ID: ${requestId}`
        );
        const callback = workerCallbacksRef.current.get(requestId);
        if (callback) {
          if (workerError) {
            console.error(`[Worker Error] ${workerError}`);
            // Pass error so promise can be rejected
            callback({ error: new Error(workerError) });
          } else {
            callback(data || []); // Ensure we always return an array
          }
          workerCallbacksRef.current.delete(requestId);
        }
      };

      // Handle worker errors
      worker.onerror = event => {
        console.error(`Worker error: ${event.message}`);
        // Mark worker as failed
        reportWorker = false;
      };
    }

    return () => {
      console.log(`[${reportType}] Cleaning up worker`);
      // Clean up any pending callbacks
      workerCallbacksRef.current.clear();
    };
  }, []);

  // Add a central error handler
  interface HandleErrorParams {
    source: string;
    error: unknown;
  }

  const handleError = useCallback(
    ({ source, error }: HandleErrorParams) => {
      console.error(`[${reportType}] Error in ${source}:`, error);
      if (mountedRef.current) {
        dispatch({
          type: 'BATCH_UPDATE',
          payload: {
            localLoading: false,
            error: error,
          },
        });
      }
    },
    [reportType]
  );

  // Add this function to your hook
  const sendWorkerMessage = useCallback(function <T>(
    action: string,
    payload: Record<string, any>
  ): Promise<T> {
    // Get worker instance
    const worker = getReportWorker();
    if (!worker || typeof worker === 'boolean') {
      return Promise.reject(new Error('Worker not available'));
    }

    return new Promise<T>((resolve, reject) => {
      // Create unique request ID
      const requestId = `${action}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Set timeout to prevent hanging if worker fails
      const timeoutId = setTimeout(() => {
        console.error(
          `[${reportType}] Worker timeout for action: ${action} with ID: ${requestId} and payload:`,
          payload
        );
        workerCallbacksRef.current.delete(requestId);
        reject(new Error(`Worker timeout for action: ${action}`));
      }, 30000); // 10 second timeout

      // Register callback for this request
      workerCallbacksRef.current.set(requestId, (response: any) => {
        console.log(
          `[${reportType}] Worker response for action: ${action} with ID: ${requestId} and payload:`,
          payload,
          response
        );
        clearTimeout(timeoutId);

        if (response && response.error) {
          reject(response.error);
          return;
        }

        resolve(response as T);
      });

      // Send message to worker
      try {
        console.log(
          `[${reportType}] Sending message to worker for action: ${action} with ID: ${requestId} and payload:`,
          payload
        );
        worker.postMessage({
          requestId,
          action,
          ...payload,
        });
      } catch (e) {
        console.error(`[${reportType}] Error sending message to worker:`, e);
        clearTimeout(timeoutId);
        workerCallbacksRef.current.delete(requestId);
        reject(e);
      }
    });
  }, []);

  // Function to fetch storage data - only called when storage data actually changes
  const fetchStorageData = useCallback(
    async (startDate: string, endDate: string) => {
      // Exit immediately if unmounted
      if (!mountedRef.current) {
        console.log(
          `[${reportType}] Component unmounted, skipping storage fetch`
        );
        return;
      }

      logMemoryUsage(`[${reportType}] Starting storage fetch`);
      const startTime = performance.now();

      // Prevent concurrent storage fetches
      if (isFetchingAndProcessingStorageRef.current) {
        console.log(
          `[${reportType}] Storage fetch already in progress, skipping`
        );
        return;
      }

      if (!fbDetails.selectedAccount || !sellPointId) {
        console.log(
          `[${reportType}] Missing account or sellpoint, skipping storage fetch`
        );
        return;
      }

      console.log(
        `[${reportType}] Fetching storage data (${++storageFetchCountRef.current})`
      );

      isFetchingAndProcessingStorageRef.current = true;

      try {
        // Check mounted status again before expensive operations
        if (!mountedRef.current) return;

        // Parse the interval into groups of dates
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);

        // Get date interval breakdown
        const reportDates = await getDateIntervalBreakdown(
          startDateObj,
          endDateObj,
          locale
        );

        // Check mounted status after each async operation
        if (!mountedRef.current) {
          console.log(
            `[${reportType}] Component unmounted during storage fetch, aborting`
          );
          return;
        }

        // Create file paths for storage
        const filesNeededFromStorage = reportDates.map(
          date =>
            `accounts/${fbDetails.selectedAccount}/sellPoints/${sellPointId}/reports/${date}/${reportType}.json`
        );

        // Fetch storage data
        const data = await getReportsDataFromStorage(
          storage,
          filesNeededFromStorage
        );

        // Final mounted check before any state updates
        if (!mountedRef.current) {
          console.log(
            `[${reportType}] Component unmounted after storage fetch, aborting`
          );
          return;
        }

        console.log(
          `[${reportType}] Storage data fetched, items: ${data.length}`
        );

        console.log(
          `[${reportType}] Realtime data current, items: ${
            Object.keys(latestRealtimeDataRef.current).length
          }`
        );

        latestStorageDataRef.current = data;

        // Set the initial fetch flag HERE after successful fetch
        if (!isInitialStorageFetchDoneRef.current) {
          isInitialStorageFetchDoneRef.current = true;
        }

        // Use the latest realtime data from the ref
        if (mountedRef.current) {
          processAndUpdateReportData(startDate, endDate);
        }
      } catch (error) {
        if (mountedRef.current) {
          handleError({ source: 'fetchStorageData', error });
        }
      } finally {
        isFetchingAndProcessingStorageRef.current = false;

        // Only log if still relevant
        if (mountedRef.current) {
          const endTime = performance.now();
          console.log(
            `[${reportType}] Storage fetch took ${endTime - startTime}ms`
          );
          logMemoryUsage(`[${reportType}] After storage fetch`);
        }
      }
    },
    [fbDetails.selectedAccount, sellPointId, reportType, locale, handleError]
  );

  // Keep the useCallback version but add a stable ref
  const fetchStorageDataRef = useRef(fetchStorageData);
  fetchStorageDataRef.current = fetchStorageData;

  // Function to process report data without re-fetching storage
  const processAndUpdateReportData = useCallback(
    async (startDate: string, endDate: string) => {
      // Use ref values directly inside the function
      const currentRealtimeData = latestRealtimeDataRef.current;
      const currentStorageData = latestStorageDataRef.current;

      // Exit immediately if unmounted
      if (!mountedRef.current) {
        console.log(
          `[${reportType}] Component unmounted, skipping report processing`
        );
        return;
      }

      const operationId = Date.now();
      console.log(
        `[${reportType}] Starting report processing operation #${operationId}`
      );
      logMemoryUsage(`[${reportType}] Starting report processing`);
      const startTime = performance.now();

      if (!fbDetails.selectedAccount || !sellPointId || !fbDetails.rtdb) {
        console.log(
          `[${reportType}] Missing account, sellpoint or database, skipping report update`
        );
        return;
      }

      console.log(`[${reportType}] Processing report data`);

      // Check for in-progress state updates or unmounting
      if (isReportDataUpdateInProgressRef.current || !mountedRef.current) {
        console.log(
          `[${reportType}] State update already in progress or component unmounted, skipping`
        );
        return;
      }

      isReportDataUpdateInProgressRef.current = true;

      try {
        // Additional unmount check
        if (!mountedRef.current) {
          console.log(
            `[${reportType}] Component unmounted during processing, aborting`
          );
          return;
        }

        let worker: Worker | null = null;

        // Only try to get worker if we haven't had previous failures
        if (reportWorker !== false) {
          worker = getReportWorker() as Worker;
        }

        if (worker) {
          try {
            // Use sendWorkerMessage instead of manual worker promise
            console.log(
              `[${reportType}] Processing report data in worker for ${reportType} for period ${startDate} to ${endDate}`
            );
            const data = await sendWorkerMessage<
              Array<OmitKeysWithTypeTransform<Report<K>>>
            >('processReport', {
              accountId: fbDetails.selectedAccount,
              sellPointId,
              reportType,
              startDate,
              endDate,
              locale,
              storageData: currentStorageData,
              realtimeData: currentRealtimeData,
            });

            // After getting the data, validate it before setting state
            if (data && Array.isArray(data) && data.length > 0) {
              console.log(
                `[${reportType}] Report data processed, items: ${data.length}`
              );

              // Only update state if we have valid data
              if (mountedRef.current) {
                const currentTimestamp = Date.now();
                if (
                  currentTimestamp > lastUpdateOperationTimestampRef.current
                ) {
                  lastUpdateOperationTimestampRef.current = currentTimestamp;

                  // Update state only if this is the most recent update
                  // Final check right before dispatching
                  if (mountedRef.current) {
                    dispatch({
                      type: 'BATCH_UPDATE',
                      payload: {
                        reportData: data,
                        reportDataTimestamp: currentTimestamp,
                        localLoading: false,
                        initialStorageFetchDone:
                          isInitialStorageFetchDoneRef.current,
                      },
                    });
                  }
                } else {
                  console.log(`[${reportType}] Ignoring outdated update`);
                }
              }
            } else {
              // If we're still in initial loading phase, don't report "no data"
              const isStillLoading =
                isFetchingAndProcessingStorageRef.current ||
                !isInitialStorageFetchDoneRef.current;

              if (isStillLoading) {
                console.log(
                  `[${reportType}] Empty data during loading, maintaining loading state`
                );
                // Don't change loading state, waiting for storage data
              } else if (reportData.length > 0) {
                // Keep existing data if we have it
                console.log(
                  `[${reportType}] Received empty data but keeping existing ${reportData.length} items`
                );
                dispatch({
                  type: 'BATCH_UPDATE',
                  payload: {
                    localLoading: false,
                    initialStorageFetchDone:
                      isInitialStorageFetchDoneRef.current,
                  },
                });
              } else {
                // Only set empty data if we don't already have data and aren't in initial loading
                console.log(`[${reportType}] No data available`);
                dispatch({
                  type: 'BATCH_UPDATE',
                  payload: {
                    reportData: [],
                    reportDataTimestamp: Date.now(),
                    localLoading: false,
                    initialStorageFetchDone:
                      isInitialStorageFetchDoneRef.current,
                  },
                });
              }
            }
          } catch (error) {
            console.error(
              `[${reportType}] Worker error, falling back to main thread:`,
              error
            );
            reportWorker = false; // Disable worker for future calls in this session

            // Fallback to main thread
            const backend = {};

            const data = await getReport(
              fbDetails.selectedAccount!,
              sellPointId,
              reportType,
              startDate,
              endDate,
              locale,
              backend,
              currentStorageData || [],
              currentRealtimeData
            );

            // Add timestamp check here for the fallback path too
            if (mountedRef.current) {
              const currentTimestamp = Date.now();
              if (currentTimestamp > lastUpdateOperationTimestampRef.current) {
                lastUpdateOperationTimestampRef.current = currentTimestamp;

                // Update state only if this is the most recent update
                // Final check right before dispatching
                if (mountedRef.current) {
                  dispatch({
                    type: 'BATCH_UPDATE',
                    payload: {
                      reportData: data,
                      reportDataTimestamp: currentTimestamp,
                      localLoading: false,
                      initialStorageFetchDone:
                        isInitialStorageFetchDoneRef.current,
                    },
                  });
                }
              } else {
                console.log(
                  `[${reportType}] Ignoring outdated update from main thread fallback`
                );
              }
            }
          }
        } else {
          // Process in main thread
          console.log(`[${reportType}] Processing in main thread`);
          const backend = {};

          const data = await getReport(
            fbDetails.selectedAccount,
            sellPointId,
            reportType,
            startDate,
            endDate,
            locale,
            backend,
            currentStorageData,
            currentRealtimeData
          );

          console.log(
            `[${reportType}] Report data processed in main thread, items: ${data?.length || 0}`
          );

          // Only update state if the component is still mounted
          if (mountedRef.current) {
            const currentTimestamp = Date.now();
            if (currentTimestamp > lastUpdateOperationTimestampRef.current) {
              lastUpdateOperationTimestampRef.current = currentTimestamp;

              // Update state only if this is the most recent update
              // Final check right before dispatching
              if (mountedRef.current) {
                dispatch({
                  type: 'BATCH_UPDATE',
                  payload: {
                    reportData: data,
                    reportDataTimestamp: currentTimestamp,
                    localLoading: false,
                    initialStorageFetchDone:
                      isInitialStorageFetchDoneRef.current,
                  },
                });
              }
            } else {
              console.log(`[${reportType}] Ignoring outdated update`);
            }
          }
        }
      } catch (error) {
        if (mountedRef.current) {
          handleError({ source: 'fetchAndUpdateReportData', error });
        }
      } finally {
        isReportDataUpdateInProgressRef.current = false;

        // Only log if still relevant
        if (mountedRef.current) {
          const endTime = performance.now();
          console.log(
            `[${reportType}] Report processing took ${endTime - startTime}ms`
          );
          logMemoryUsage(`[${reportType}] After report processing`);
          console.log(
            `[${reportType}] Completed report processing operation #${operationId}`
          );
        } else {
          console.log(
            `[${reportType}] Component unmounted during operation #${operationId}`
          );
        }
      }
    },
    [
      fbDetails.selectedAccount,
      fbDetails.rtdb,
      sellPointId,
      reportType,
      locale,
      handleError,
      sendWorkerMessage, // Add this dependency
    ]
  );

  // Debounced version to avoid too many processing calls
  const debouncedFetchAndUpdateReportData = useCallback(
    debounce(
      (startDate, endDate) => {
        // Only process if we still need the data
        if (mountedRef.current) {
          processAndUpdateReportData(startDate, endDate);
        } else {
          console.log(
            `[${reportType}] Component unmounted, skipping debounced update`
          );
        }
      },
      300, // 300ms debounce time
      {
        maxWait: 1000, // Maximum wait time
        leading: false, // Don't execute on the leading edge
        trailing: true, // Execute on the trailing edge
      }
    ),
    [processAndUpdateReportData]
  );

  // Create a stable dateRangeKey with useMemo
  const dateRangeKey = useMemo(() => {
    if (!dateRange?.[0] || !dateRange?.[1]) return 'no-dates';
    return `${dateRange[0].format('YYYY-MM-DD')}-${dateRange[1].add(1, 'seconds').format('YYYY-MM-DD')}`;
  }, [
    dateRange && dateRange[0]?.format('YYYY-MM-DD'),
    dateRange && dateRange[1]?.add(1, 'seconds').format('YYYY-MM-DD'),
  ]);

  useEffect(() => {
    logMemoryUsage(`[${reportType}] Hook mounting`);
    console.log(`[${reportType}] Hook mounting/updating`);

    mountedRef.current = true;

    if (
      !fbDetails.selectedAccount ||
      !sellPointId ||
      !fbDetails.rtdb ||
      !dateRange?.[0] ||
      !dateRange?.[1]
    ) {
      console.log(
        `[${reportType}] Missing required data, not setting up listeners`
      );
      return;
    }

    dispatch({
      type: 'BATCH_UPDATE',
      payload: { localLoading: true, error: null },
    });

    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].add(1, 'seconds').format('YYYY-MM-DD');

    console.log(`[${reportType}] Setting up realtime listeners`);

    // Set up realtime listeners
    const realtimeReportsRef = ref(
      fbDetails.rtdb,
      `accounts/${fbDetails.selectedAccount}/sellPoints/${sellPointId}/reports/${reportType}`
    );
    const reportsGeneratingRef = ref(
      fbDetails.rtdb,
      `accounts/${fbDetails.selectedAccount}/sellPoints/${sellPointId}/info/generatingReports`
    );

    // Update the realtime data listener to handle empty data correctly
    const realtimeReportListener = onValue(
      realtimeReportsRef,
      snapshot => {
        // Check if still mounted before processing callback
        if (!mountedRef.current) {
          console.log(
            `[${reportType}] Component unmounted, ignoring realtime update`
          );
          return;
        }

        const data = snapshot.val() || {};
        console.log(
          `[${reportType}] Realtime data updated, items: ${Object.keys(data).length}`
        );

        // Update the latest data ref
        latestRealtimeDataRef.current = data;

        // Check if this update is a "clear" operation (empty data)
        const isEmpty = !data || Object.keys(data).length === 0;

        if (isEmpty) {
          console.log(
            `[${reportType}] Realtime data empty, skipping processing`
          );
          // Don't process empty data - storage will handle it via the generatingReports listener
        } else {
          // Only process realtime data if initial storage fetch has completed
          // or if we already have storage data
          if (!isInitialStorageFetchDoneRef.current) {
            console.log(
              `[${reportType}] Waiting for initial storage fetch before processing realtime data`
            );
          } else if (isFetchingAndProcessingStorageRef.current) {
            console.log(
              `[${reportType}] Skipping realtime processing until storage fetch completes`
            );
          } else {
            // Set loading to true before processing new data
            dispatch({ type: 'SET_LOADING', payload: true });
            console.log(
              `[${reportType}] Processing new realtime data, setting loading state`
            );

            // If we have actual data, process it with storage data
            debouncedFetchAndUpdateReportData(startDate, endDate);
          }
        }
      },
      error => {
        handleError({ source: 'realtimeReportListener', error });
      }
    );

    // Listen for report generation status - only fetch storage when generation completes
    const realtimeReportGenerationListener = onValue(
      reportsGeneratingRef,
      snapshot => {
        // Check if still mounted before processing
        if (!mountedRef.current) {
          console.log(
            `[${reportType}] Component unmounted, ignoring generation status update`
          );
          return;
        }

        const isGenerating = snapshot.exists();
        console.log(`[${reportType}] Reports generating: ${isGenerating}`);

        // Only fetch if not generating AND not the initial fetch AND still mounted
        if (
          !isGenerating &&
          isInitialStorageFetchDoneRef.current &&
          mountedRef.current
        ) {
          console.log(
            `[${reportType}] Reports generation completed, fetching new storage data`
          );

          // Set loading to true before fetching new data
          dispatch({ type: 'SET_LOADING', payload: true });

          fetchStorageDataRef.current(startDate, endDate);
        } else if (!isGenerating) {
          console.log(
            `[${reportType}] Initial fetch, skipping generation completed event`
          );
        }
      },
      error => {
        if (mountedRef.current) {
          handleError({ source: 'realtimeReportGenerationListener', error });
        }
      }
    );

    // Initial fetch of storage data
    console.log(`[${reportType}] Performing initial storage data fetch`);
    fetchStorageDataRef.current(startDate, endDate);

    // Cleanup listeners on unmount
    return () => {
      console.log(
        `[${reportType}] Cleaning up listeners and canceling operations`
      );
      mountedRef.current = false;

      // Cancel any in-progress operations
      isFetchingAndProcessingStorageRef.current = false;

      // Clean up Firebase listeners
      try {
        realtimeReportListener();
        realtimeReportGenerationListener();
      } catch (e) {
        console.error(`[${reportType}] Error in cleanup:`, e);
      }

      // Clean up debounced function
      debouncedFetchAndUpdateReportData.cancel();
    };
  }, [
    fbDetails.selectedAccount,
    sellPointId,
    reportType,
    fbDetails.rtdb,
    debouncedFetchAndUpdateReportData,
    dateRangeKey,
  ]);

  return {
    data: reportData,
    isLoading,
    error,
    isInitialStorageFetchDone: initialStorageFetchDone,
    dataTimestamp: reportDataTimestamp,
  };
};
