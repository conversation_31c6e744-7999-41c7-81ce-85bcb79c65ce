import { Menu, MenuItem } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { TileActionI } from './CatalogContainer';
import { MenuGroup, MenuItem as MenuItemI } from './types';

interface ActiveTileMenuDropdownProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  tile: MenuItemI | MenuGroup;
  canChangeTileSize: boolean;
  onClose: () => void;
  updateTile: (tile: Partial<MenuItemI>) => void;
  removeTile: () => void;
  editTile: (el: TileActionI) => void;
}

export default function ActiveTileMenuDropdown({
  anchorEl,
  open,
  tile,
  canChangeTileSize,
  onClose,
  updateTile,
  removeTile,
  editTile,
}: ActiveTileMenuDropdownProps) {
  const { t } = useTranslation('');
  if (!tile) return <></>;
  const isSmall = tile.position.endY - tile.position.startY == 1;

  const changeSize = () => {
    updateTile({
      position: {
        startX: tile.position.startX,
        startY: tile.position.startY,
        endX: tile.position.endX,
        endY: +tile.position.endY + (isSmall ? 1 : -1),
      },
    });
    onClose();
  };

  const editTileH = () => {
    editTile({
      type: tile.type ?? 'product',
      edit: true,
      position: tile.position,
    });
    onClose();
  };

  return (
    <Menu
      id="menu-item-menu"
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      MenuListProps={{
        'aria-labelledby': 'basic-button',
      }}
    >
      <MenuItem onClick={changeSize} disabled={!canChangeTileSize}>
        {t('menu.changeTo')} {isSmall ? 'tall' : 'small'}
      </MenuItem>
      {tile.type !== 'function' && (
        <MenuItem onClick={editTileH}>
          {t('shared.edit')}{' '}
          {tile.type === 'displayGroup'
            ? t('menu.menuGroup')
            : tile.type === 'productCombo'
              ? t('menu.combo')
              : t('shared.item')}
        </MenuItem>
      )}
      <MenuItem onClick={removeTile}>{t('menu.removeTile')}</MenuItem>
    </Menu>
  );
}
