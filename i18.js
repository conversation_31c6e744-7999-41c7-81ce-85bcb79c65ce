import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import translationEN from './locales/en.json';
import translationRo from './locales/ro.json';

const resources = {
  en: {
    translation: translationEN,
  },
  ro: {
    translation: translationRo,
  },
};

const savedLanguage = localStorage.getItem('language') || 'en';

i18n.use(initReactI18next).init({
  resources,
  lng: savedLanguage,
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  pluralSeparator: '_',
  keySeparator: '.',
});

export default i18n;
