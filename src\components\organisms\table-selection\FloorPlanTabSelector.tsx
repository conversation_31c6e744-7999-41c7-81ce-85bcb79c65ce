import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Chip,
  FormControlLabel,
  Grid,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import FloorPlanTableSelector from './FloorPlanTableSelector';

interface FloorPlanTabSelectorProps {
  sellPointId: string;
  value?: any[];
  onChange?: (areaItems: any[]) => void;
  excludeAssignedTables?: boolean;
  excludeMemberId?: string;
  // QR mode props
  mode?: 'selection' | 'qr';
  title?: string;
  description?: string;
  onViewQR?: (table: any, floorPlan: any) => void;
  onDownloadAll?: () => void;
  // Data props - passed from parent to avoid unnecessary requests
  teamMembers?: any[];
  existingWorkingAreas?: any[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`floor-plan-tabpanel-${index}`}
      aria-labelledby={`floor-plan-tab-${index}`}
      style={{
        display: value === index ? 'block' : 'none',
      }}
      {...other}
    >
      <Box>{children}</Box>
    </div>
  );
}

interface TableCardProps {
  table: any;
  floorPlan: any;
  isSelected: boolean;
  isAssigned: boolean;
  assignedMemberName?: string;
  onClick: () => void;
  // QR mode props
  mode?: 'selection' | 'qr';
  onViewQR?: () => void;
}

function TableCard({
  table,
  floorPlan,
  isSelected,
  isAssigned,
  assignedMemberName,
  onClick,
  mode = 'selection',
  onViewQR,
}: TableCardProps) {
  const theme = useTheme();

  const getCardStyles = () => {
    const baseStyles = {
      minHeight: 80,
      height: 80,
      display: 'flex',
      flexDirection: 'column',
      transition: 'all 0.2s ease',
      cursor: isAssigned
        ? 'not-allowed'
        : mode === 'selection'
          ? 'pointer'
          : mode === 'qr'
            ? 'pointer'
            : 'default',
      userSelect: 'none',
      zIndex: isSelected ? 10 : isAssigned ? 0 : 1,
      boxSizing: 'border-box',
    };

    // Safe access to theme properties with fallbacks (same as SelectableTable)
    const primaryMain = theme?.palette?.primary?.main || '#1976d2';
    const primaryDark = theme?.palette?.primary?.dark || '#1565c0';
    const customDraggableTable =
      (theme?.palette as any)?.custom?.draggableTable || '#ffffff';

    if (isAssigned) {
      return {
        ...baseStyles,
        backgroundColor: '#e0e0e0', // Medium/light gray for disabled
        border: '2px dashed #bdbdbd',
        opacity: 1,
        '&:hover': {
          backgroundColor: '#e0e0e0',
          border: '2px dashed #bdbdbd',
        },
      };
    } else if (isSelected) {
      return {
        ...baseStyles,
        backgroundColor: primaryMain,
        boxShadow: `inset 0 0 0 2px ${primaryDark}, 0 2px 8px ${primaryMain}40`,
        border: `2px solid ${primaryDark}`,
      };
    } else {
      return {
        ...baseStyles,
        backgroundColor: customDraggableTable,
        border: '1px solid #ddd',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        '&:hover': {
          backgroundColor: '#9c9c9c',
          border: '1px solid rgba(0, 0, 0, 0.15)',
          boxShadow:
            'inset 0 0 0 2px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.15)',
        },
      };
    }
  };

  const getTextColor = () => {
    if (isAssigned) return '#757575'; // Medium gray text for disabled
    if (isSelected) return 'white'; // White text for selected
    return 'white'; // White text for normal state (same as SelectableTable)
  };

  const renderQRIcons = () => {
    // No icons in QR mode - the entire card is clickable
    return null;
  };

  return (
    <Card
      sx={getCardStyles()}
      onClick={
        isAssigned
          ? undefined
          : mode === 'selection'
            ? onClick
            : mode === 'qr' && onViewQR
              ? onViewQR
              : undefined
      }
    >
      <CardContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: 80,
          p: 1,
          textAlign: 'center',
          '&:last-child': { pb: 1 },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 0,
            height: '100%',
            width: '100%',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
              color: getTextColor(),
              fontSize: '1rem',
              lineHeight: 1.2,
            }}
          >
            {table.tag || `${floorPlan.label || ''}${table.number}`}
          </Typography>
          {isAssigned && assignedMemberName && (
            <Typography
              variant="caption"
              sx={{
                textAlign: 'center',
                color: getTextColor(),
                fontSize: '0.75rem',
                fontStyle: 'italic',
                lineHeight: 1,
              }}
            >
              {assignedMemberName}
            </Typography>
          )}
          {renderQRIcons()}
        </Box>
      </CardContent>
    </Card>
  );
}

export default function FloorPlanTabSelector({
  sellPointId,
  value = [],
  onChange,
  excludeAssignedTables = false,
  excludeMemberId,
  mode = 'selection',
  title,
  description,
  onViewQR,
  onDownloadAll,
  teamMembers,
  existingWorkingAreas,
}: FloorPlanTabSelectorProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(
    false
  );
  const [selectedTables, setSelectedTables] = useState<Set<string>>(new Set());

  // Use regular useGetList to avoid unnecessary re-renders when changing locations
  const { data: floorPlans, isLoading } = useGetList(RESOURCES.FLOOR_PLANS, {
    filter: {
      sellPointId: sellPointId,
      active: true,
    },
    pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
    sort: { field: '_index', order: 'ASC' },
    meta: { sellPointId },
  });

  // Build team member ID to name mapping (only when data is provided)
  const teamMemberIdToName = useMemo(() => {
    if (!teamMembers) return {};
    return teamMembers.reduce((acc: any, teamMember: any) => {
      acc[teamMember.id] = teamMember.displayName;
      return acc;
    }, {});
  }, [teamMembers]);

  // Build set of assigned table keys and mapping to member names (only when data is provided)
  const { assignedTableKeys, assignedTableToMember } = useMemo(() => {
    if (
      !excludeAssignedTables ||
      !existingWorkingAreas ||
      !teamMemberIdToName
    ) {
      return {
        assignedTableKeys: new Set<string>(),
        assignedTableToMember: new Map<string, string>(),
      };
    }

    const assignedKeys = new Set<string>();
    const tableToMember = new Map<string, string>();

    existingWorkingAreas.forEach((workingArea: any) => {
      // Skip current member if we're in edit mode
      if (excludeMemberId && workingArea.id === excludeMemberId) return;

      const memberName = teamMemberIdToName[workingArea.id] || '???';
      const areaItems = workingArea.areaItems || [];

      areaItems.forEach((item: any) => {
        const tableKey = `${item.floorPlanId}-${item.itemNumber}-${item.itemTag || ''}`;
        assignedKeys.add(tableKey);
        tableToMember.set(tableKey, memberName);
      });
    });

    return {
      assignedTableKeys: assignedKeys,
      assignedTableToMember: tableToMember,
    };
  }, [
    excludeAssignedTables,
    existingWorkingAreas,
    excludeMemberId,
    teamMemberIdToName,
  ]);

  // Calculate if there are any assigned tables across all floor plans
  const hasAnyAssignedTables = useMemo(() => {
    return excludeAssignedTables && assignedTableKeys.size > 0;
  }, [excludeAssignedTables, assignedTableKeys]);

  // Memoize floor plans to avoid unnecessary re-renders
  const memoizedFloorPlans = useMemo(() => floorPlans || [], [floorPlans]);

  // Generate unique ID for area item
  const generateAreaItemId = useCallback(
    (floorPlanId: string, itemNumber: number) => {
      return `${floorPlanId}-${itemNumber}`;
    },
    []
  );

  // Generate table key for selection tracking (includes tag for uniqueness)
  const generateTableKey = useCallback(
    (floorPlanId: string, itemNumber: number, itemTag?: string) => {
      return `${floorPlanId}-${itemNumber}-${itemTag || ''}`;
    },
    []
  );

  // Initialize selected tables from existing value (only in selection mode)
  useEffect(() => {
    if (mode !== 'selection') return;

    const tableKeys = new Set<string>();
    value.forEach((item: any) => {
      const key = generateTableKey(
        item.floorPlanId,
        item.itemNumber,
        item.itemTag
      );
      tableKeys.add(key);
    });
    setSelectedTables(tableKeys);
  }, [mode, value, generateTableKey]);

  // Auto-expand first accordion on mobile when floor plans load - removed for better UX
  // Now users can choose which accordion to open, none are auto-opened
  // useEffect(() => {
  //   if (isMobile && memoizedFloorPlans.length > 0 && !expandedAccordion) {
  //     setExpandedAccordion(memoizedFloorPlans[0].id);
  //   }
  // }, [isMobile, memoizedFloorPlans, expandedAccordion]);

  // Validate selections when floor plans change (tables might be deleted/modified)
  // Only run this in selection mode since QR mode doesn't use selections
  const validateSelectionsRef = useRef<
    ((validAreaItems: any[]) => void) | undefined
  >(undefined);
  validateSelectionsRef.current = onChange;

  useEffect(() => {
    if (
      mode !== 'selection' ||
      !memoizedFloorPlans.length ||
      !value.length ||
      !validateSelectionsRef.current
    )
      return;

    const validTableKeys = new Set<string>();
    const validAreaItems: any[] = [];

    // Build set of all valid table keys from current floor plans
    memoizedFloorPlans.forEach((floorPlan: any) => {
      const tables = floorPlan.items || [];
      tables.forEach((table: any) => {
        const tableKey = generateTableKey(
          floorPlan.id,
          table.number,
          table.tag
        );
        validTableKeys.add(tableKey);
      });
    });

    // Filter out invalid selections
    value.forEach((item: any) => {
      const tableKey = generateTableKey(
        item.floorPlanId,
        item.itemNumber,
        item.itemTag
      );
      if (validTableKeys.has(tableKey)) {
        validAreaItems.push(item);
      }
    });

    // Update if selections were invalidated
    if (validAreaItems.length !== value.length) {
      validateSelectionsRef.current?.(validAreaItems);
    }
  }, [mode, memoizedFloorPlans, value, generateTableKey]); // Only depend on mode, floor plans, value, and the stable generateTableKey function

  const handleTableSelectionChange = useCallback(
    (tableKey: string, selected: boolean, tableData: any) => {
      if (mode !== 'selection') return; // Don't handle selection changes in QR mode

      const newSelectedTables = new Set(selectedTables);
      let newAreaItems = [...value];

      if (selected) {
        newSelectedTables.add(tableKey);
        newAreaItems.push(tableData);
      } else {
        newSelectedTables.delete(tableKey);
        newAreaItems = newAreaItems.filter(
          item =>
            generateTableKey(
              item.floorPlanId,
              item.itemNumber,
              item.itemTag
            ) !== tableKey
        );
      }

      setSelectedTables(newSelectedTables);
      onChange?.(newAreaItems);
    },
    [mode, selectedTables, value, onChange, generateTableKey]
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleAccordionChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedAccordion(isExpanded ? panel : false);
    };

  // Handle Select All for current active floor plan or accordion
  const handleSelectAllFloorPlan = useCallback(
    (floorPlan: any, isChecked: boolean) => {
      if (mode !== 'selection') return; // Don't handle selection in QR mode

      const currentFloorPlanTables = floorPlan.items || [];

      // Filter out assigned tables when selecting all
      const availableTables = currentFloorPlanTables.filter((table: any) => {
        const tableKey = generateTableKey(
          floorPlan.id,
          table.number,
          table.tag
        );
        return !assignedTableKeys.has(tableKey);
      });

      const newSelectedTables = new Set(selectedTables);
      let newAreaItems = [...value];

      if (isChecked) {
        // Select all available tables from current floor plan
        availableTables.forEach((table: any) => {
          const tableKey = generateTableKey(
            floorPlan.id,
            table.number,
            table.tag
          );
          const tableData = {
            id: generateAreaItemId(floorPlan.id, table.number),
            floorPlanId: floorPlan.id,
            floorPlanName: floorPlan.name || '',
            floorPlanLabel: floorPlan.label || '',
            itemNumber: table.number,
            itemTag: table.tag || undefined,
          };

          if (!newSelectedTables.has(tableKey)) {
            newSelectedTables.add(tableKey);
            newAreaItems.push(tableData);
          }
        });
      } else {
        // Deselect all tables from current floor plan
        currentFloorPlanTables.forEach((table: any) => {
          const tableKey = generateTableKey(
            floorPlan.id,
            table.number,
            table.tag
          );

          if (newSelectedTables.has(tableKey)) {
            newSelectedTables.delete(tableKey);
            newAreaItems = newAreaItems.filter(
              item =>
                generateTableKey(
                  item.floorPlanId,
                  item.itemNumber,
                  item.itemTag
                ) !== tableKey
            );
          }
        });
      }

      setSelectedTables(newSelectedTables);
      onChange?.(newAreaItems);
    },
    [
      mode,
      selectedTables,
      value,
      onChange,
      assignedTableKeys,
      generateTableKey,
      generateAreaItemId,
    ]
  );

  const handleSelectAllCurrentFloorPlan = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (mode !== 'selection') return; // Don't handle selection in QR mode
      if (!memoizedFloorPlans || memoizedFloorPlans.length === 0) return;
      const currentFloorPlan = memoizedFloorPlans[activeTab];
      if (!currentFloorPlan) return;
      handleSelectAllFloorPlan(currentFloorPlan, event.target.checked);
    },
    [mode, memoizedFloorPlans, activeTab, handleSelectAllFloorPlan]
  );

  // Calculate select all state for a specific floor plan
  const getFloorPlanSelectAllState = useCallback(
    (floorPlan: any) => {
      if (mode !== 'selection' || !floorPlan || !floorPlan.items) {
        return { allSelected: false, someSelected: false, availableCount: 0 };
      }

      const currentFloorPlanTables = floorPlan.items;

      // Filter out assigned tables
      const availableTables = currentFloorPlanTables.filter((table: any) => {
        const tableKey = generateTableKey(
          floorPlan.id,
          table.number,
          table.tag
        );
        return !assignedTableKeys.has(tableKey);
      });

      const selectedInCurrentFloorPlan = availableTables.filter(
        (table: any) => {
          const tableKey = generateTableKey(
            floorPlan.id,
            table.number,
            table.tag
          );
          return selectedTables.has(tableKey);
        }
      );

      const allSelected =
        availableTables.length > 0 &&
        selectedInCurrentFloorPlan.length === availableTables.length;
      const someSelected = selectedInCurrentFloorPlan.length > 0;

      return {
        allSelected,
        someSelected,
        availableCount: availableTables.length,
      };
    },
    [mode, selectedTables, assignedTableKeys, generateTableKey]
  );

  // Calculate select all state for current floor plan (desktop tabs)
  const getCurrentFloorPlanSelectAllState = useMemo(() => {
    if (!memoizedFloorPlans || memoizedFloorPlans.length === 0)
      return { allSelected: false, someSelected: false, availableCount: 0 };

    const currentFloorPlan = memoizedFloorPlans[activeTab];
    return getFloorPlanSelectAllState(currentFloorPlan);
  }, [memoizedFloorPlans, activeTab, getFloorPlanSelectAllState]);

  const getSelectedCountForFloorPlan = useCallback(
    (floorPlanId: string) => {
      return value.filter(item => item.floorPlanId === floorPlanId).length;
    },
    [value]
  );

  const handleTableCardClick = useCallback(
    (table: any, floorPlan: any) => {
      if (mode !== 'selection') return; // Don't handle selection in QR mode

      const tableKey = generateTableKey(floorPlan.id, table.number, table.tag);
      const isAssigned = assignedTableKeys.has(tableKey);

      // Don't allow selection of assigned tables
      if (isAssigned) return;

      const isSelected = selectedTables.has(tableKey);

      const tableData = {
        id: generateAreaItemId(floorPlan.id, table.number),
        floorPlanId: floorPlan.id,
        floorPlanName: floorPlan.name || '',
        floorPlanLabel: floorPlan.label || '',
        itemNumber: table.number,
        itemTag: table.tag || undefined,
      };

      handleTableSelectionChange(tableKey, !isSelected, tableData);
    },
    [
      mode,
      assignedTableKeys,
      selectedTables,
      handleTableSelectionChange,
      generateTableKey,
      generateAreaItemId,
    ]
  );

  if (isLoading) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography>Loading floor plans...</Typography>
      </Box>
    );
  }

  if (!memoizedFloorPlans || memoizedFloorPlans.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          {t('floorPlansPage.noFloorPlansYet')}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Create floor plans first to assign tables to team members.
        </Typography>
      </Box>
    );
  }

  // Mobile/Tablet view with accordions
  if (isMobile) {
    return (
      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            mb: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {title && <Typography variant="h6">{title}</Typography>}
          {mode === 'selection' && value.length > 0 && (
            <Chip
              label={`${value.length} selected`}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
        </Box>

        {description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>
        )}

        {hasAnyAssignedTables && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Note: Some tables are already assigned to other team members and
            cannot be selected.
          </Typography>
        )}

        {memoizedFloorPlans.map((floorPlan: any) => {
          const selectedCount = getSelectedCountForFloorPlan(floorPlan.id);
          const { allSelected, someSelected, availableCount } =
            getFloorPlanSelectAllState(floorPlan);
          const tables = floorPlan.items || [];

          return (
            <Accordion
              key={floorPlan.id}
              expanded={expandedAccordion === floorPlan.id}
              onChange={handleAccordionChange(floorPlan.id)}
              sx={{ mb: 1 }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`${floorPlan.id}-content`}
                id={`${floorPlan.id}-header`}
                sx={{
                  '& .MuiAccordionSummary-content': {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                  },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Typography variant="h6">{floorPlan.name}</Typography>
                  {mode === 'selection' && selectedCount > 0 && (
                    <Chip
                      label={selectedCount}
                      size="small"
                      color="primary"
                      sx={{ minWidth: 20, height: 20 }}
                    />
                  )}
                </Box>

                {/* Select All checkbox - only visible when accordion is expanded */}
                {mode === 'selection' && expandedAccordion === floorPlan.id && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5,
                      mr: 1, // Add margin to prevent overlap with expand icon
                    }}
                    onClick={e => e.stopPropagation()} // Prevent accordion toggle when clicking checkbox
                  >
                    <Checkbox
                      checked={allSelected}
                      indeterminate={someSelected && !allSelected}
                      onChange={e =>
                        handleSelectAllFloorPlan(floorPlan, e.target.checked)
                      }
                      disabled={availableCount === 0}
                      size="small"
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: '0.875rem',
                        userSelect: 'none',
                      }}
                    >
                      Select All
                    </Typography>
                  </Box>
                )}
              </AccordionSummary>
              <AccordionDetails>
                {tables.length === 0 ? (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ textAlign: 'center', py: 2 }}
                  >
                    No tables found in this floor plan
                  </Typography>
                ) : (
                  <Grid container spacing={1}>
                    {tables.map((table: any, index: number) => {
                      const tableKey = generateTableKey(
                        floorPlan.id,
                        table.number,
                        table.tag
                      );
                      const isSelected = selectedTables.has(tableKey);
                      const isAssigned = assignedTableKeys.has(tableKey);
                      const assignedMemberName =
                        assignedTableToMember.get(tableKey);

                      return (
                        <Grid
                          item
                          xs={6}
                          sm={3}
                          md={2}
                          key={`table-card-${index}`}
                        >
                          <TableCard
                            table={table}
                            floorPlan={floorPlan}
                            isSelected={isSelected}
                            isAssigned={isAssigned}
                            assignedMemberName={assignedMemberName}
                            onClick={
                              mode === 'selection'
                                ? () => handleTableCardClick(table, floorPlan)
                                : () => {} // Provide empty function for QR mode
                            }
                            mode={mode}
                            onViewQR={
                              mode === 'qr' && onViewQR
                                ? () => onViewQR(table, floorPlan)
                                : undefined
                            }
                          />
                        </Grid>
                      );
                    })}
                  </Grid>
                )}
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Box>
    );
  }

  // Desktop view with tabs (existing implementation)
  const { allSelected, someSelected, availableCount } =
    getCurrentFloorPlanSelectAllState;

  return (
    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <Box sx={{ width: '100%', maxWidth: '1200px' }}>
        <Box
          sx={{
            mb: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
          }}
        >
          <Box sx={{ flexGrow: 1 }}>
            {title && (
              <Typography variant="h6" gutterBottom>
                {title}
              </Typography>
            )}
            {description && (
              <Typography variant="body2" color="text.secondary">
                {description}
              </Typography>
            )}
            {mode === 'selection' && hasAnyAssignedTables && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {t('workingArea.selectTablesFromFloorPlansNote')}
              </Typography>
            )}
          </Box>
          {mode === 'selection' && value.length > 0 && (
            <Chip
              label={`${value.length} ${t('workingArea.selected')}`}
              color="primary"
              variant="outlined"
              size="small"
              sx={{ ml: 2, flexShrink: 0 }}
            />
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            borderBottom: 1,
            borderColor: 'divider',
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ flexGrow: 1 }}
          >
            {memoizedFloorPlans.map((floorPlan: any, index: number) => {
              const selectedCount = getSelectedCountForFloorPlan(floorPlan.id);
              return (
                <Tab
                  key={floorPlan.id}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {floorPlan.name}
                      {mode === 'selection' && selectedCount > 0 && (
                        <Chip
                          label={selectedCount}
                          size="small"
                          color="primary"
                          sx={{ minWidth: 20, height: 20 }}
                        />
                      )}
                    </Box>
                  }
                  id={`floor-plan-tab-${index}`}
                  aria-controls={`floor-plan-tabpanel-${index}`}
                />
              );
            })}
          </Tabs>

          {mode === 'selection' && (
            <Box sx={{ ml: 2, mr: 1 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={allSelected}
                    indeterminate={someSelected && !allSelected}
                    onChange={handleSelectAllCurrentFloorPlan}
                    disabled={availableCount === 0}
                  />
                }
                label={t('workingArea.selectAll')}
              />
            </Box>
          )}
        </Box>

        {memoizedFloorPlans.map((floorPlan: any, index: number) => (
          <TabPanel key={floorPlan.id} value={activeTab} index={index}>
            <FloorPlanTableSelector
              floorPlan={floorPlan}
              selectedTables={selectedTables}
              assignedTableKeys={assignedTableKeys}
              assignedTableToMember={assignedTableToMember}
              onTableSelectionChange={handleTableSelectionChange}
              mode={mode}
              onViewQR={
                onViewQR ? table => onViewQR(table, floorPlan) : undefined
              }
            />
          </TabPanel>
        ))}
      </Box>
    </Box>
  );
}
