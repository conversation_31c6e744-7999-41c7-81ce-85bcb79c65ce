import { useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  Button,
  Dialog,
  IconButton,
  Link,
  Paper,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';

import ModalHeader from '~/components/molecules/ModalHeader';

export interface SocialMediaValues {
  linkTitle: string;
  linkDescription: string;
}

interface SocialMediaModalProps {
  initialValue?: SocialMediaValues;
  onClose: () => void;
  onSave: (values: SocialMediaValues) => void;
}

export default function SocialMediaModal({
  initialValue,
  onClose,
  onSave,
}: SocialMediaModalProps) {
  const [values, setValues] = useState<SocialMediaValues>(
    initialValue || {
      linkTitle: 'BBQ PIZZA <Item name>',
      linkDescription: 'aici apare ce ai scris la <item description>',
    }
  );

  const handleChange = (field: keyof SocialMediaValues, value: string) => {
    setValues(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog
      open
      onClose={(e: any, reason: any) => {
        if (reason === 'backdropClick') {
          e.stopPropagation();
          return;
        }
        onClose();
      }}
      fullWidth
      maxWidth="sm"
    >
      <ModalHeader handleClose={onClose} title="" noBorder>
        <Button variant="contained" onClick={() => onSave(values)}>
          Done
        </Button>
      </ModalHeader>

      <Box p={3}>
        <Typography variant="h3" mb={2}>
          Social media preview
        </Typography>

        <Typography variant="body2" color="textPrimary" mb={3}>
          Control how this item appears when shared on social platforms.{' '}
          <Link href="#" underline="always" color="primary">
            Learn more
          </Link>
        </Typography>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 1,
          }}
        >
          <Typography variant="subtitle2" color="textSecondary">
            PREVIEW
          </Typography>
          <Tooltip title="Display may look different on each device or social media apps.">
            <IconButton size="small">
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        </Box>

        <Paper variant="outlined" sx={{ p: 2, mb: 4 }}>
          <Typography variant="subtitle1" sx={{ mb: 0.5 }}>
            {values.linkTitle}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            example.com
          </Typography>
          <Typography variant="body2">{values.linkDescription}</Typography>
        </Paper>

        <TextField
          label="Link title"
          fullWidth
          value={values.linkTitle}
          onChange={e => handleChange('linkTitle', e.target.value)}
        />
        <Typography variant="caption" color="text.secondary" mt={1}>
          Using SEO title by default.
        </Typography>

        <TextField
          sx={{ mt: 2 }}
          label="Link description"
          multiline
          rows={4}
          fullWidth
          value={values.linkDescription}
          onChange={e => handleChange('linkDescription', e.target.value)}
        />
        <Typography variant="caption" color="text.secondary" mt={1}>
          Using SEO description by default.
        </Typography>
      </Box>
    </Dialog>
  );
}
