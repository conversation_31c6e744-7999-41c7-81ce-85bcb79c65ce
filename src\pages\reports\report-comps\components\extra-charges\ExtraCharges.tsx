import { useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { reportSpecificFields } from '~/fake-provider/reports/constants';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { groupGroupedReportBySpecificFieldsHierarchical } from '~/fake-provider/reports/groupGroupedReportBySpecificFieldsHierarchical';
import { groupReport } from '~/fake-provider/reports/groupReport';
import remapReports from '~/fake-provider/reports/utils/remapReports';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import cleanStringArond from '~/utils/cleanStringArond';
import { FieldOption } from '../../../../../../types/globals';
// import ExtraChargesGraph from './components/ExtraChargesGraph';
import ExtraChargesTable from './components/ExtraChargesTable';

const REPORT_TYPE = 'compedExtraCharges';

const fieldsConstant = [
  { isChecked: false, value: 'vat' },
  { isChecked: true, value: 'quantity' },
  { isChecked: true, value: 'price' },
  { isChecked: true, value: 'name' },
  { isChecked: true, value: 'value' },
  { isChecked: false, value: 'promotionsValue' },
  { isChecked: false, value: 'promotionName' },
  { isChecked: true, value: 'netValue' },
  { isChecked: true, value: 'reason' },
];

export default function ExtraCharges({
  updateCompsData,
  filters,
}: {
  updateCompsData?: any;
  filters: any;
}) {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const [currency, setCurrency] = useState<string>('RON');
  const [tableFields, setTableFields] = useState<FieldOption[]>(fieldsConstant);
  const [rawData, setRawData] = useState<any>();
  const [groupingItems, setGroupingItems] = useState<string[]>(['reason']);
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }

      if (!dateRange[0] || !dateRange[1]) return;

      try {
        const data = await getReportDataHelper({
          database: fbDetails.rtdb!,
          startDate: dateRange[0].format('YYYY-MM-DD'),
          accountId: fbDetails.selectedAccount!,
          sellPointId: sellPointId,
          // we add 1 second because end date is always 23:59:59 and we want it to be next day
          endDate: dateRange[1].add(1, 'seconds').format('YYYY-MM-DD'),
          reportType: REPORT_TYPE,
        });
        setRawData(data);
      } catch (e) {
        console.error(e);
      }
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  const sortData = (items: any[]): any[] => {
    if (!Array.isArray(items)) return [];

    return items
      .map(item => {
        const hasChildren =
          Array.isArray(item.subReport) && item.subReport.length > 0;

        return {
          ...item,
          subReport: hasChildren ? sortData(item.subReport) : undefined,
        };
      })
      .sort((a, b) => {
        const aHasChildren =
          Array.isArray(a.subReport) && a.subReport.length > 0;
        const bHasChildren =
          Array.isArray(b.subReport) && b.subReport.length > 0;

        if (aHasChildren || bHasChildren) {
          return (b.quantity ?? 0) - (a.quantity ?? 0);
        }

        return (a.name || '').localeCompare(b.name || '');
      });
  };

  const { tableData } = useMemo(() => {
    if (!rawData || !filters) return { tableData: [] };

    if (rawData.length) {
      setCurrency(rawData[0].currency);
    }

    const composedFilters = composeFilters(filters, REPORT_TYPE);
    const rawDataFiltered = filterReport(
      REPORT_TYPE,
      rawData,
      composedFilters,
      []
    );

    const highestValueProductIds = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      ['id']
    )[0]
      ?.report.sort((a, b) => b.value ?? 0 - (a.value ?? 0))
      .slice(0, 5)
      ?.map(el => el.id);

    const filteredByIds = filterReport(REPORT_TYPE, rawData, composedFilters, [
      {
        field: 'id',
        operator: 'in',
        value: highestValueProductIds,
      },
    ]);

    const groupedByHour = groupReport(
      REPORT_TYPE,
      filteredByIds,
      ['hourOfDay'],
      ['id']
    );

    const labels = groupedByHour?.map(el => el.hourOfDay.toString());
    const datasets: { label: string; data: number[] }[] =
      highestValueProductIds?.map(id => ({
        label: id,
        data: [],
      }));

    groupedByHour.forEach(({ report: items }) => {
      datasets.forEach(el => {
        const item = items.find(i => i.id == el.label);
        const itemsValue = item?.value || 0;

        const formattedValue = Math.round((itemsValue / 10000) * 10) / 10;

        el.data.push(formattedValue);
      });
    });

    const graphData = {
      datasets: datasets?.map(el => ({
        ...el,
        label: el.label.substring(0, 10),
      })),
      labels,
    };

    const filteredFields = tableFields.filter((field: any) => {
      return (
        field.isChecked &&
        reportSpecificFields.compedExtraCharges.some(
          discountField =>
            cleanStringArond(field.value) === cleanStringArond(discountField)
        )
      );
    });

    const groupedTableData = groupReport(
      REPORT_TYPE,
      rawDataFiltered,
      [],
      filteredFields.map(item => item.value)
    );

    if (!groupedTableData) return { tableData: [] };
    const groupedByItemsTableData =
      groupGroupedReportBySpecificFieldsHierarchical(
        REPORT_TYPE,
        groupedTableData,
        groupingItems as []
      )[0]?.report.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const tableData = remapReports(
      sortData(groupedByItemsTableData) || [],
      'name'
    );

    return { tableData, graphData };
  }, [filters, rawData, groupingItems, tableFields]);

  const onChangeGrouping = (items: string[]) => {
    setGroupingItems(items);
  };

  useEffect(() => {
    if (!tableData || (tableData && tableData.length === 0)) {
      updateCompsData('totalExtraCharges', {});
    }
  }, [tableData]);

  return (
    <Box>
      <PageTitle
        title={t('giftCards.Comped Extra Charges')}
        hideBorder
        doNotPrint
      />

      <Box sx={{ pb: 3 }}>
        <ExtraChargesTable
          filters={filters}
          updateCompsData={updateCompsData}
          fields={tableFields}
          setFields={setTableFields}
          tableData={tableData || []}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
        />
      </Box>
    </Box>
  );
}
