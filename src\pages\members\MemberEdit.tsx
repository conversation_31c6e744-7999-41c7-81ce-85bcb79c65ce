import { Box, Typography, useMediaQuery } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  email,
  ReferenceInput,
  required,
  SaveButton,
  SimpleForm,
  useGetRecordId,
  useRedirect,
  useUnique,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import { validatePhone } from '~/components/atoms/inputs/PhoneNumberInput';
import { RESOURCES } from '~/providers/resources';
import CodeInput from '../../components/atoms/inputs/CodeInput';
import CustomInput from '../../components/atoms/inputs/CustomInput';
import CustomDeleteWithConfirmButton from '../../components/molecules/CustomDeleteWithConfirmButton';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import { validateName } from '~/utils/validateName';

const MemberEditInner = () => {
  const recordId = useGetRecordId();
  const unique = useUnique({ filter: { id_neq: recordId } });
  const redirect = useRedirect();
  const { t } = useTranslation('');
  const handleClose = () => {
    redirect('list', RESOURCES.TEAM_MEMBERS);
  };
  return (
    <>
      <ModalHeader handleClose={handleClose} title={t('members.editTeamMember')}>
        <SaveButton type="button" icon={<></>} label={t('shared.save')} />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '800px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection title={t('members.personalInformation')}>
          <CustomInput
            source="firstName"
            label={t('members.firstName')}
            placeholder="John"
            isRequired
            validate={required()}
          />
          <CustomInput
            source="lastName"
            label={t('members.lastName')}
            placeholder="Smith"
            validate={required()}
          />
          <CustomInput
            source="displayName"
            label={t('members.displayName')}
            placeholder="Johnie"
            validate={[required(), unique(), validateName]}
          />
          <CustomInput
            type="phone"
            source="phone"
            label={t('members.phone')}
            placeholder="720 123 456"
            validate={[required(), validatePhone, unique()]}
          />
          <CustomInput
            source="email"
            label="E-mail"
            disabled={true}
            validate={[required(), email(), unique()]}
            placeholder="<EMAIL>"
          />
          {/* @ts-ignore */}
          <Typography variant="label" fontWeight={200}>
            {t('members.emailDescription')}
          </Typography>
        </Subsection>

        <Subsection title={t('members.permissions')}>
          <ReferenceInput source="roleId" reference={RESOURCES.PERMISSIONS}>
            <CustomInput
              type="autocomplete-select"
              label={t('members.role')}
              optionText="name"
              optionValue="id"
              placeholder={t('members.cashierWaiterBartender')}
              selectOnFocus={false}
              validate={[required()]}
            />
          </ReferenceInput>
          <ReferenceInput
            source="sellPointIds"
            reference={RESOURCES.LOCATIONS}
            filter={{ _d: false }}
          >
            <CustomInput
              type="autocomplete-select-array"
              label={t('shared.location_few')}
              optionText="name"
              optionValue="id"
              placeholder={t('reportsPage.all')}
            />
          </ReferenceInput>
        </Subsection>
        <Subsection
          title={t('members.credentials')}
          subtitle={t('members.credentialsDescription')}
        >
          <CustomInput
            type="code"
            source="pin"
            label={t('members.personalPasscode')}
            digits={6}
            validate={[required(), unique()]}
          />
          {/* @ts-ignore */}
          <Typography variant="label" fontWeight={200}>
            {t('members.personalPasscodeDescription')}
          </Typography>
          <Typography
            // @ts-ignore
            variant="label"
            fontWeight={200}
            display="block"
            mt={1}
          >
            {t('members.personalPasscodeDescription2')}
          </Typography>
        </Subsection>
      </Box>
    </>
  );
};

export const MemberEdit = () => {
  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="optimistic">
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <MemberEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
