import { useEffect, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { Grid, InputAdornment, TextField } from '@mui/material';
import { required, usePrevious } from 'react-admin';
import { useFormContext } from 'react-hook-form';

import { PermissionsParts } from '.';
import CustomInput from '../../../components/atoms/inputs/CustomInput';
import { MuiSwitchInputGroup } from '../../../components/molecules/input-groups/SwitchInputGroup';
import Subsection from '../../../components/molecules/Subsection';
import { PermissionPages, permissionsTree } from './constants';
import { useTranslation } from 'react-i18next';

export default function PermissionCreateStep1({ edit }: { edit?: boolean }) {
  const { setValue, getValues } = useFormContext();
  const permissions = getValues('permissions');

  const [searchQuery, setSearchQuery] = useState('');
  // is full access btn checked
  const [fullAccessCheck, setFullAccessCheck] = useState(
    (permissions ?? []).length === 1 && (permissions ?? [])[0] === 1
  );
  // and id array of allowed/checked permissions
  const [activePermissions, setActivePermissions] = useState<number[]>(
    permissions ?? []
  );
  const previousPermissions = usePrevious(activePermissions);
  // permissions pages that have active state and are editable
  const [activePages, setActivePages] = useState<PermissionPages[]>([]);
  const prevActivePages = usePrevious(activePages);
  // permission page that is currently in view
  const [currentPage, setCurrentPage] = useState<PermissionPages>(
    PermissionPages.ORDERS
  );

  useEffect(() => {
    if (activePermissions.length === 1 && activePermissions[0] === 1) {
      setFullAccessCheck(true);
      setActivePages([
        PermissionPages.ORDERS,
        PermissionPages.CUSTOMERS,
        PermissionPages.GIFT_CARDS,
        PermissionPages.SETTINGS,
      ]);
      return;
    }

    //  we need to initialise active pages that have permissions active
    const pagesWithActivePermissions: PermissionPages[] = [];
    Object.keys(permissionsTree).forEach(pageKey => {
      const pageGroups = permissionsTree[pageKey as PermissionPages];
      const hasActivePermissions = pageGroups.some(group =>
        group.possiblePermissions.some(perm => activePermissions.includes(perm))
      );

      if (hasActivePermissions) {
        pagesWithActivePermissions.push(pageKey as PermissionPages);
      }
    });

    // Set the activePages state with the initialized pages
    setActivePages(pagesWithActivePermissions);
  }, []);

  const toggleFullAccess = (value: boolean) => {
    setFullAccessCheck(value);
    if (value) {
      setValue('permissions', [1]);
      setActivePermissions([1]);
      setActivePages([
        PermissionPages.ORDERS,
        PermissionPages.CUSTOMERS,
        PermissionPages.GIFT_CARDS,
        PermissionPages.SETTINGS,
      ]);
    } else {
      const newPermissions =
        previousPermissions?.[0] === 1 ? [] : (previousPermissions ?? []);
      setValue('permissions', newPermissions);
      setActivePermissions(newPermissions);
      setActivePages(prevActivePages ?? []);
    }
  };

  const handleActivePermissionChange = (permissionId: number) => {
    let newVals = [];
    if (activePermissions.includes(permissionId)) {
      newVals = activePermissions.filter(id => id !== permissionId);
    } else {
      newVals = [...activePermissions, permissionId];
    }

    setValue('permissions', newVals);
    setActivePermissions(newVals);
  };

  const handleActivePageChange = (page: PermissionPages) => {
    if (activePages.includes(page)) {
      setActivePages(activePages.filter(p => p !== page));

      // remove associated permissions
      const permissionsToRemove =
        permissionsTree[page]?.flatMap(group => group.possiblePermissions) ||
        [];
      setActivePermissions(prevPermissions =>
        prevPermissions.filter(perm => !permissionsToRemove.includes(perm))
      );
    } else {
      setActivePages([...activePages, page]);
    }
  };

  const { t } = useTranslation();

  return (
    <>
      <CustomInput
        readOnly={!!edit}
        source="name"
        label={t('createPermissions.permissionSetName')}
        validate={[
          required(),
          (value: string) => {
            if (value && /[.#$\/\[\]]/.test(value)) {
              return t('permissions.nameValidationError');
            }
            return undefined;
          }
        ]}
      />

      <Subsection
        title={t('createPermissions.customize')}
        subtitle={t('createPermissions.customizeSubtitle')}
      >
        <MuiSwitchInputGroup
          checked={fullAccessCheck}
          onChange={e => {
            toggleFullAccess(e.target.checked);
          }}
          label={t('createPermissions.fullAccess')}
          description={t('createPermissions.fullAccessDescription')}
        />
        <TextField
          sx={{ mt: 4, mb: 2 }}
          placeholder={t('createPermissions.searchPermissions')}
          value={searchQuery}
          onChange={e => {
            setSearchQuery(e.target.value);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        <PermissionsParts.SearchResults
          query={searchQuery}
          setCurrentPage={setCurrentPage}
          resetQuery={() => setSearchQuery('')}
        />

        {!searchQuery && (
          <Grid
            container
            sx={{
              minHeight: { xs: 0, md: '1200px' },
            }}
          >
            <Grid item xs={12} md={4}>
              <PermissionsParts.SideMenu
                activePages={activePages}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            </Grid>
            <Grid
              item
              xs={12}
              md={8}
              sx={{
                display: 'flex',
                height: 'fit-content',
                py: 2,
                px: 4,
                border: '1px #d9d9d9 solid',
                borderRadius: '6px',
              }}
            >
              {/* merge facut un map */}
              {currentPage === PermissionPages.ORDERS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.ORDERS}
                  isPageActive={activePages.includes(PermissionPages.ORDERS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.orders}
                />
              )}

              {currentPage === PermissionPages.CUSTOMERS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.CUSTOMERS}
                  isPageActive={activePages.includes(PermissionPages.CUSTOMERS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.customers}
                />
              )}

              {currentPage === PermissionPages.GIFT_CARDS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.GIFT_CARDS}
                  isPageActive={activePages.includes(
                    PermissionPages.GIFT_CARDS
                  )}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.giftCards}
                />
              )}

              {currentPage === PermissionPages.SETTINGS && (
                <PermissionsParts.GeneralPage
                  pageLabel={PermissionPages.SETTINGS}
                  isPageActive={activePages.includes(PermissionPages.SETTINGS)}
                  togglePageActive={handleActivePageChange}
                  activePermissions={activePermissions}
                  handlePermissionChange={handleActivePermissionChange}
                  groups={permissionsTree.settings}
                />
              )}
            </Grid>
          </Grid>
        )}
      </Subsection>
    </>
  );
}
