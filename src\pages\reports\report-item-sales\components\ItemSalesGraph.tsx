import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import capitalize from '~/utils/capitalize';
import { CurrencyType, formatNumber } from '~/utils/formatNumber';
import ReportMultiLineChart from '../../components/ReportMultiLineChart';

export default function ItemSalesGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency?: CurrencyType;
}) {
  const { t } = useTranslation();

  return (
    <>
      {!data.datasets || !data.labels ? (
        <></>
      ) : (
        <>
          {data.datasets.length > 0 && (
            <>
              <Typography
                sx={{
                  display: { xs: 'none', md: 'block' },
                  '@media print': {
                    backgroundColor: '#FFFFFF !important',
                    color: 'black !important',
                  },
                }}
                variant="body2"
                fontWeight="500"
                mb={1.5}
              >
                {data.datasets.length >= 3 &&
                  `Top ${data.datasets.length} ${t('itemSales.topGrossSales')}`}
              </Typography>
              <ReportMultiLineChart
                datasets={data.datasets}
                labels={data.labels}
                formatData={data => formatNumber(data, currency)}
              />
            </>
          )}
        </>
      )}
    </>
  );
}
