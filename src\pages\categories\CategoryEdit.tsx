import { useState } from 'react';
import { Box } from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  required,
  SaveButton,
  SimpleForm,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import { validateName } from '~/utils/validateName';

import CustomInput from '~/components/atoms/inputs/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';

const CategoryEditInner = () => {
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('categoryLibrary.editCategory')}
      >
        <SaveButton
          type="submit"
          label={t('shared.save')}
          icon={<></>}
          alwaysEnable
        />
      </ModalHeader>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
          width: '100%',
        }}
      >
        <Box>
          <CustomInput
            source="name"
            label={t('shared.name')}
            validate={[required(), validateName]}
          />
          <CustomInput
            source="straightFire"
            type="switch"
            label="Straight fire"
          />
        </Box>
      </Box>
    </>
  );
};

const validateCategoryEdit = (values: any) => {
  const errors: any = {};
  // const nameError = validateName(values.name);
  if(!values.name) {
    return 'Name is required'
  }
  console.log('errors', errors)
  return errors;
};

export const CategoryEdit = () => {
  const transform = (data: any) => {
    return {
      ...data,
    };
  };

  return (
    <EditDialog
      maxWidth="sm"
      fullWidth
      transform={transform}
      mutationMode="optimistic"
    >
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <CategoryEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
