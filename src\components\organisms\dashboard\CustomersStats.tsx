import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Box, Divider, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function CustomersStats() {
  const { t } = useTranslation();

  const data = [
    {
      label: t('dashboard.totalCustomers'),
      value: 1233,
    },
    {
      label: t('dashboard.newCustomers'),
      value: 343,
    },
    {
      label: t('dashboard.returningCustomers'),
      value: 33213,
    },
    {
      label: t('dashboard.averageVisits'),
      value: 3.5,
    },
  ];

  return (
    <Box display="grid">
      <Typography variant="h4" pb={2}>
        {t('dashboard.customers')}
      </Typography>
      <Divider />
      {data.map((el: any) => {
        return (
          <Box
            key={el.label}
            sx={{
              ':hover': {
                bgcolor: 'primary.light',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                py: 2,
                px: 1,
              }}
            >
              <Typography variant="body2" fontWeight={200}>
                {el.label}
              </Typography>
              <Box sx={{ flex: 1 }} />
              <Typography variant="body2" fontWeight={200}>
                {el.value}
              </Typography>
              <KeyboardArrowRightIcon color="disabled" />
            </Box>
            <Divider />
          </Box>
        );
      })}
    </Box>
  );
}
