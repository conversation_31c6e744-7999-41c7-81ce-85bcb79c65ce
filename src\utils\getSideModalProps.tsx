import React from 'react';
import { Theme, useMediaQuery } from '@mui/material';
import Slide from '@mui/material/Slide';

const MODAL_WIDTH = 450;

interface SideModalPropsI {
  hideBackdrop?: boolean;
}

export const Transition = React.forwardRef(function Transition(
  props: any,
  ref
) {
  return (
    <Slide direction="left" ref={ref} {...props}>
      {props.children}
    </Slide>
  );
});

export default function getSideModalProps({ hideBackdrop }: SideModalPropsI) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return {
    TransitionComponent: Transition,
    fullScreen: true,
    hideBackdrop: !!hideBackdrop,
    sx: {
      maxWidth: isXSmall ? '100vw' : MODAL_WIDTH,
      marginLeft: isXSmall ? '0' : `calc(100vw - ${MODAL_WIDTH}px)`,
    },
  };
}
