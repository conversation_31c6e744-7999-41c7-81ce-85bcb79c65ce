import React, { useRef } from 'react';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import PrintIcon from '@mui/icons-material/Print';
import {
  Box,
  Button,
  IconButton,
  Modal,
  Slide,
  Typography,
} from '@mui/material';
import { useReactToPrint } from 'react-to-print';
import { useTheme } from '~/contexts/ThemeContext';

type InfoModalProps = {
  open: boolean;
  onClose: () => void;
  extraData?: any;
  children?: React.ReactNode;
};

const InfoModal: React.FC<InfoModalProps> = ({
  open,
  onClose,
  extraData,
  children,
}) => {
  const { theme } = useTheme();
  const contentRef = useRef<HTMLDivElement>(null);
  const handlePrint = useReactToPrint({
    contentRef,
    bodyClass: 'print-body',
    documentTitle: extraData?.title || 'Print',
    onBeforePrint: () => {
      if (contentRef.current) {
        const style = document.createElement('style');
        style.innerHTML = `
          @media print {
            body {
              padding-top: 100px !important;
            }
          }
        `;
        contentRef.current.appendChild(style);
      }
      return Promise.resolve();
    },
  });

  return (
    <Modal open={open} onClose={onClose} closeAfterTransition>
      <Slide direction="up" in={open} mountOnEnter unmountOnExit>
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            width: '100%',
            height: '100%',
            bgcolor: 'background.paper',
            boxShadow: 24,
            overflowY: 'auto',
            px: 4,
            pb: 4,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              top: 0,
              pt: 4,
              backgroundColor: 'background.paper',
              position: 'sticky',
              zIndex: '1000 !important',
              borderBottom: '2px solid #F2F2F2',
              pb: 2,
              mb: 4,
            }}
          >
            <Button
              sx={{
                bgcolor: theme.palette.mode == 'light'?'#F5F5F5':'#1E1E22',
                borderRadius: '8px',
                border: 0,
                color: theme.palette.mode == 'light'?'black':'white',
                '&:hover': {
                  border: 0,
                  bgcolor: theme.palette.mode == 'light'?'#E0E0E0':'#26262B',
                },
              }}
              onClick={onClose}
              variant="outlined"
            >
              <CloseRoundedIcon />
            </Button>
            <Typography variant="h2">
              {extraData?.title ||
                extraData?.rowData?.id ||
                extraData?.rowData?.name ||
                'Info'}
            </Typography>
            <IconButton
              onClick={() => {
                handlePrint();
              }}
            >
              <PrintIcon color="primary" />
            </IconButton>
          </Box>
          <Box ref={contentRef}>
            {children ? (
              children
            ) : (
              <Typography variant="body1">No data available</Typography>
            )}
          </Box>
        </Box>
      </Slide>
    </Modal>
  );
};

export default InfoModal;
