import { useEffect, useRef, useState } from 'react';
import CloudOffIcon from '@mui/icons-material/CloudOff';
import RefreshIcon from '@mui/icons-material/Refresh';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import { useTranslation } from 'react-i18next';
import {
  Backdrop,
  Box,
  Button,
  CircularProgress,
  Typography,
} from '@mui/material';
import {
  onDisconnect,
  onValue,
  push,
  ref,
  remove,
  serverTimestamp,
  set,
} from 'firebase/database';

import { useTheme } from '~/contexts';
import { useFirebase } from '~/contexts/FirebaseContext';
import cleanupActions from '~/providers/utils/cleanupActions';

// TODO! on hot reload in dev the connections are not cleared.

// Types
interface ConnectionMonitorProps {
  children: React.ReactNode;
}

type ConnectionState = 'online' | 'offline' | 'firebase-disconnected';

interface ConnectionInfo {
  icon: React.ComponentType<any>;
  title: string;
  message: string;
  showCheckButton: boolean;
}

interface OnDisconnectRefs {
  connection: any;
  lastOnline: any;
}

// Constants
const CONNECTION_CHECK_INTERVAL = 10000; // 10 seconds
const INITIAL_CONNECTION_DELAY = {
  CONNECTED: 100,
  DISCONNECTED: 2000,
};
const MANUAL_CHECK_DELAY = 500;

const ConnectionMonitor: React.FC<ConnectionMonitorProps> = ({ children }) => {
  // Hooks
  const { theme } = useTheme();
  const { details: fbDetails, loading: fbLoading } = useFirebase();
  const { t } = useTranslation('');
  // State
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isFirebaseConnected, setIsFirebaseConnected] = useState<
    boolean | null
  >(null);
  const [
    firebaseInitialConnectionReceived,
    setFirebaseInitialConnectionReceived,
  ] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  // Refs
  const hasReceivedInitialConnection = useRef(false);
  const currentAccountRef = useRef<string | null>(null);
  const currentUserRef = useRef<string | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);
  const unregisterCleanupRef = useRef<(() => void) | null>(null);
  const currentConnectionRef = useRef<any>(null);
  const onDisconnectRefs = useRef<OnDisconnectRefs>({
    connection: null,
    lastOnline: null,
  });

  // Computed values
  const isDark = theme?.palette?.mode === 'dark';
  const safeTheme = theme || {};
  const safePalette = safeTheme.palette || {};
  const shouldMonitorFirebase = Boolean(
    fbDetails?.rtdb && fbDetails.selectedAccount
  );

  // Connection state logic
  const getConnectionState = (): ConnectionState => {
    if (!isOnline) return 'offline';

    if (shouldMonitorFirebase) {
      if (!firebaseInitialConnectionReceived || isFirebaseConnected === false) {
        return 'firebase-disconnected';
      }
    }

    return 'online';
  };

  const connectionState = getConnectionState();

  const getShouldShowConnectionMonitor = (): boolean => {
    if (fbLoading) return false;

    if (!isOnline) return true;

    if (shouldMonitorFirebase) {
      return firebaseInitialConnectionReceived && isFirebaseConnected === false;
    }

    return false;
  };

  const shouldShowConnectionMonitor = getShouldShowConnectionMonitor();

  // Utility functions
  const resetFirebaseConnectionState = () => {
    console.log('Resetting Firebase connection state');
    setFirebaseInitialConnectionReceived(false);
    setIsFirebaseConnected(null);
    hasReceivedInitialConnection.current = false;
  };

  const cancelOnDisconnectHandlers = () => {
    if (onDisconnectRefs.current.connection) {
      onDisconnectRefs.current.connection
        .cancel()
        .catch((error: any) =>
          console.error('Error canceling connection onDisconnect:', error)
        );
      onDisconnectRefs.current.connection = null;
    }

    if (onDisconnectRefs.current.lastOnline) {
      onDisconnectRefs.current.lastOnline
        .cancel()
        .catch((error: any) =>
          console.error('Error canceling lastOnline onDisconnect:', error)
        );
      onDisconnectRefs.current.lastOnline = null;
    }
  };

  const cleanupCurrentListener = () => {
    if (cleanupRef.current) {
      console.log('Cleaning up current Firebase listener');
      cleanupRef.current();
      cleanupRef.current = null;
    }

    cancelOnDisconnectHandlers();
    currentConnectionRef.current = null;
  };

  // Presence management
  const cleanupPresence = () => {
    const accountId = currentAccountRef.current;
    const userId = currentUserRef.current;
    const connectionRef = currentConnectionRef.current;

    if (!accountId || !userId || !fbDetails?.rtdb) {
      console.log('No presence to cleanup - missing data');
      return;
    }

    console.log(
      'Cleaning up presence for account:',
      accountId,
      'user:',
      userId
    );

    try {
      cancelOnDisconnectHandlers();

      const lastOnlineRef = ref(
        fbDetails.rtdb,
        `accounts/${accountId}/presence/${userId}/lastOnlineAt`
      );

      if (connectionRef) {
        remove(connectionRef).catch(error =>
          console.error(
            'Error removing specific connection during cleanup:',
            error
          )
        );
      }

      set(lastOnlineRef, serverTimestamp()).catch(error =>
        console.error('Error setting last online during cleanup:', error)
      );

      console.log('Presence cleanup initiated');
    } catch (error) {
      console.error('Error during presence cleanup:', error);
    }
  };

  const setupFirebasePresence = (fbDetails: any) => {
    const connectionsRef = ref(
      fbDetails.rtdb!,
      `accounts/${fbDetails.selectedAccount}/presence/${fbDetails.user?.uid}/connections`
    );
    const lastOnlineRef = ref(
      fbDetails.rtdb!,
      `accounts/${fbDetails.selectedAccount}/presence/${fbDetails.user?.uid}/lastOnlineAt`
    );

    const con = push(connectionsRef);
    currentConnectionRef.current = con;

    onDisconnectRefs.current.connection = onDisconnect(con);
    onDisconnectRefs.current.connection.remove();

    onDisconnectRefs.current.lastOnline = onDisconnect(lastOnlineRef);
    onDisconnectRefs.current.lastOnline.set(serverTimestamp());

    set(con, 1); // 0 = pos, 1 = manager
  };

  // Connection checking
  const checkBrowserConnection = async () => {
    try {
      await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache',
        mode: 'no-cors',
      });
      return true;
    } catch (error) {
      console.log('Browser connection check failed:', error);
      return false;
    }
  };

  const handleCheckAgain = () => {
    setIsChecking(true);
    setTimeout(async () => {
      try {
        const isConnected = await checkBrowserConnection();
        if (isConnected) {
          console.log('Manual check successful');
          setIsOnline(true);
        }
      } catch (error) {
        console.log('Manual check failed:', error);
      } finally {
        setIsChecking(false);
      }
    }, MANUAL_CHECK_DELAY);
  };

  // Connection info configuration
  const getConnectionInfo = (): ConnectionInfo | null => {
    switch (connectionState) {
      case 'offline':
        return {
          icon: WifiOffIcon,
          title: t('connectionMonitor.titleNoInternet'),
          message:
            t('connectionMonitor.descriptionNoInternet'),
          showCheckButton: true,
        };
      case 'firebase-disconnected':
        return {
          icon: CloudOffIcon,
          title: t('connectionMonitor.titleFirebaseDisconnected'),
          message:
            t('connectionMonitor.descriptionFirebaseDisconnected'),
          showCheckButton: false,
        };
      default:
        return null;
    }
  };

  // Effects

  // Browser connection monitoring
  useEffect(() => {
    const handleOnline = () => {
      console.log('Browser connection restored');
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log('Browser connection lost');
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    let intervalId: number;

    const periodicConnectionCheck = async () => {
      if (!isOnline) {
        const isConnected = await checkBrowserConnection();
        if (isConnected) {
          console.log('Browser connection restored via check');
          setIsOnline(true);
        }
      }
    };

    if (!isOnline) {
      intervalId = window.setInterval(
        periodicConnectionCheck,
        CONNECTION_CHECK_INTERVAL
      );
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isOnline]);

  // Firebase connection monitoring with account change handling
  useEffect(() => {
    const hasAccountChanged =
      currentAccountRef.current !== fbDetails?.selectedAccount;
    const hasUserChanged = currentUserRef.current !== fbDetails?.user?.uid;

    if (hasAccountChanged || hasUserChanged) {
      console.log('Account or user changed:', {
        previousAccount: currentAccountRef.current,
        currentAccount: fbDetails?.selectedAccount,
        previousUser: currentUserRef.current,
        currentUser: fbDetails?.user?.uid,
      });

      cleanupCurrentListener();
      resetFirebaseConnectionState();

      currentAccountRef.current = fbDetails?.selectedAccount || null;
      currentUserRef.current = fbDetails?.user?.uid || null;
    }

    if (shouldMonitorFirebase) {
      const connectedRef = ref(fbDetails.rtdb!, '.info/connected');

      console.log(
        'Setting up Firebase connection listener for account:',
        fbDetails.selectedAccount,
        'user:',
        fbDetails.user?.uid
      );

      let initialConnectionTimeout: NodeJS.Timeout;

      const handleConnectionChange = (snapshot: any) => {
        const connected = snapshot.val() === true;

        if (connected) {
          setupFirebasePresence(fbDetails);
        }

        console.log(
          'Firebase connection state changed:',
          connected,
          'for account:',
          fbDetails.selectedAccount
        );

        setIsFirebaseConnected(connected);

        if (!hasReceivedInitialConnection.current) {
          hasReceivedInitialConnection.current = true;
          const delay = connected
            ? INITIAL_CONNECTION_DELAY.CONNECTED
            : INITIAL_CONNECTION_DELAY.DISCONNECTED;

          initialConnectionTimeout = setTimeout(() => {
            setFirebaseInitialConnectionReceived(true);
          }, delay);
        } else {
          setFirebaseInitialConnectionReceived(true);
        }
      };

      const unsubscribe = onValue(connectedRef, handleConnectionChange);

      cleanupRef.current = () => {
        console.log(
          'Cleaning up Firebase connection listener for account:',
          fbDetails.selectedAccount
        );
        if (initialConnectionTimeout) {
          clearTimeout(initialConnectionTimeout);
        }
        unsubscribe();
      };

      return () => {
        cleanupCurrentListener();
        resetFirebaseConnectionState();
      };
    } else {
      console.log('Should not monitor Firebase - resetting state');
      cleanupCurrentListener();
      resetFirebaseConnectionState();
      currentAccountRef.current = null;
      currentUserRef.current = null;
    }
  }, [
    shouldMonitorFirebase,
    fbDetails?.rtdb,
    fbDetails?.selectedAccount,
    fbDetails?.user?.uid,
  ]);

  // Presence cleanup registration
  useEffect(() => {
    if (
      shouldMonitorFirebase &&
      currentAccountRef.current &&
      currentUserRef.current
    ) {
      if (!unregisterCleanupRef.current) {
        console.log('Registering presence cleanup with cleanupActions');
        unregisterCleanupRef.current = cleanupActions.register(cleanupPresence);
      }
    } else {
      if (unregisterCleanupRef.current) {
        console.log('Unregistering presence cleanup from cleanupActions');
        unregisterCleanupRef.current();
        unregisterCleanupRef.current = null;
      }
    }

    return () => {
      if (unregisterCleanupRef.current) {
        console.log(
          'Unregistering presence cleanup from cleanupActions on unmount'
        );
        unregisterCleanupRef.current();
        unregisterCleanupRef.current = null;
      }
    };
  }, [
    shouldMonitorFirebase,
    currentAccountRef.current,
    currentUserRef.current,
  ]);

  // Debug logging
  console.log('Connection Monitor Debug:', {
    shouldMonitorFirebase,
    firebaseInitialConnectionReceived,
    isFirebaseConnected,
    connectionState,
    shouldShowConnectionMonitor,
    fbLoading,
    currentAccount: currentAccountRef.current,
    selectedAccount: fbDetails?.selectedAccount,
  });

  const connectionInfo = getConnectionInfo();

  // Render
  return (
    <>
      {children}

      <Backdrop
        open={shouldShowConnectionMonitor}
        sx={{
          color: isDark ? '#f2f2f2' : '#000',
          zIndex: safeTheme.zIndex?.modal ? safeTheme.zIndex.modal + 1 : 1400,
          bgcolor: isDark ? 'rgba(0, 0, 0, 0.85)' : 'rgba(0, 0, 0, 0.8)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            bgcolor:
              safePalette.background?.default ||
              (isDark ? '#1a1a1a' : '#ffffff'),
            borderRadius: { xs: 0, sm: '12px' },
            p: { xs: 3, sm: 4 },
            mx: { xs: 0, sm: 3 },
            maxWidth: { xs: '100%', sm: '500px' },
            width: '100%',
            height: { xs: '100vh', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
            boxShadow: {
              xs: 'none',
              sm:
                safeTheme.shadows?.[24] || '0px 8px 10px -5px rgba(0,0,0,0.2)',
            },
            border: { xs: 'none', sm: '1px solid' },
            borderColor: {
              xs: 'transparent',
              sm: isDark
                ? safePalette.custom?.gray400 || '#404040'
                : safePalette.divider || '#e0e0e0',
            },
          }}
        >
          {connectionInfo && (
            <>
              <connectionInfo.icon
                sx={{
                  fontSize: { xs: '120px', sm: '100px' },
                  color:
                    connectionState === 'firebase-disconnected'
                      ? safePalette.warning?.main || '#ff9800'
                      : safePalette.error?.main || '#f44336',
                  mb: { xs: 3, sm: 2 },
                }}
              />

              <Typography
                variant="h5"
                fontWeight={600}
                sx={{
                  mb: 2,
                  fontSize: { xs: '1.5rem', sm: '1.25rem' },
                  color: isDark
                    ? safePalette.custom?.text || '#ffffff'
                    : safePalette.text?.primary || '#000000',
                }}
              >
                {connectionInfo.title}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  mb: { xs: 4, sm: 3 },
                  lineHeight: 1.6,
                  maxWidth: { xs: '100%', sm: '400px' },
                  color: isDark
                    ? safePalette.custom?.fadedText || '#b0b0b0'
                    : safePalette.text?.secondary || '#666666',
                }}
              >
                {connectionInfo.message}
              </Typography>

              {connectionInfo.showCheckButton && (
                <Button
                  variant="contained"
                  onClick={handleCheckAgain}
                  disabled={isChecking}
                  startIcon={
                    isChecking ? (
                      <CircularProgress size={16} color="inherit" />
                    ) : (
                      <RefreshIcon />
                    )
                  }
                  sx={{
                    mb: 4,
                    minWidth: 120,
                    backgroundColor: safePalette.primary?.main || '#1976d2',
                    color: 'white',
                    borderRadius: '6px',
                    padding: { xs: '12px 20px', sm: '10px 20px' },
                    fontWeight: '500',
                    fontSize: { xs: '15px', sm: '14px' },
                    textTransform: 'none',
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: isChecking
                        ? safePalette.primary?.main || '#1976d2'
                        : safePalette.primary?.dark || '#1565c0',
                      boxShadow: 'none',
                    },
                    '&:disabled': {
                      backgroundColor: safePalette.primary?.main || '#1976d2',
                      color: 'white',
                      opacity: 0.7,
                    },
                  }}
                >
                  {isChecking ? t('connectionMonitor.checking') : t('connectionMonitor.checkAgain')}
                </Button>
              )}

              <Box
                sx={{
                  p: 2,
                  borderRadius: '6px',
                  bgcolor:
                    safePalette.background?.paper ||
                    (isDark ? '#2a2a2a' : '#f5f5f5'),
                  border: '1px solid',
                  borderColor: isDark
                    ? safePalette.custom?.gray400 || '#404040'
                    : safePalette.divider || '#e0e0e0',
                  maxWidth: { xs: '100%', sm: '500px' },
                  width: '100%',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: isDark
                      ? safePalette.custom?.text || '#ffffff'
                      : safePalette.text?.primary || '#000000',
                    fontWeight: 600,
                  }}
                >
                  {connectionState === 'firebase-disconnected'
                    ? t('connectionMonitor.whatDoesThisMean')
                    : t('connectionMonitor.whyDoINeedAnInternetConnection')}
                </Typography>
                <Typography
                  variant="body2"
                  component="ul"
                  sx={{
                    mt: 1,
                    pl: 2,
                    textAlign: 'left',
                    color: isDark
                      ? safePalette.custom?.fadedText || '#b0b0b0'
                      : safePalette.text?.secondary || '#666666',
                  }}
                >
                  {connectionState === 'firebase-disconnected' ? (
                    <>
                      <li>{t('connectionMonitor.yourInternetConnectionIsWorkingFine')}</li>
                      <li>
                        {t('connectionMonitor.thereIsATemporaryIssueConnectingToOurDataServers')}
                      </li>
                      <li>
                        {t('connectionMonitor.weAreAutomaticallyTryingToReconnectInTheBackground')}
                      </li>
                      <li>{t('connectionMonitor.yourWorkIsSafeAndWillSyncOnceReconnected')}</li>
                    </>
                  ) : (
                    <>
                      <li>
                        {t('connectionMonitor.yourChangesNeedToBeSavedToOurSecureServers')}
                      </li>
                      <li>{t('connectionMonitor.dataMustBeSynchronizedAcrossAllYourDevices')}</li>
                      <li>
                        {t('connectionMonitor.realTimeUpdatesEnsureEveryoneSeesTheLatestInformation')}
                      </li>
                      <li>{t('connectionMonitor.yourWorkIsAutomaticallyBackedUpAsYouGo')}</li>
                    </>
                  )}
                </Typography>
              </Box>
            </>
          )}
        </Box>
      </Backdrop>
    </>
  );
};

export default ConnectionMonitor;
