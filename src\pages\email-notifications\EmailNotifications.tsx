import React, { useState } from 'react';
import { Box, Checkbox, Divider, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import PageTitle from '~/components/molecules/PageTitle';
import Subsection from '../../components/molecules/Subsection';

interface CheckboxState {
  [key: string]: boolean;
}

interface SectionItem {
  name: keyof CheckboxState;
  title: string;
  description: string;
}

interface Section {
  title: string;
  items: SectionItem[];
}

const EmailNotifications: React.FC = () => {
  const { t } = useTranslation();
  const initialCheckboxState: CheckboxState = {
    dailySalesSummary: false,
    monthlyAnnualSalesSummaries: false,
    productUpdates: false,
    programsEvents: false,
    newToSquare: false,
    governmentPrograms: false,
    squareLoansPayments: false,
    inventoryAlerts: false,
    squareMarketing: false,
    perPayment: false,
    perRefund: false,
    perTransfer: false,
  };

  const [checkboxes, setCheckboxes] =
    useState<CheckboxState>(initialCheckboxState);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    setCheckboxes(prev => ({ ...prev, [name]: checked }));
  };

  const sections: Section[] = [
    {
      title: t('emailNotifications.accountEmails'),
      items: [
        {
          name: 'dailySalesSummary',
          title: t('emailNotifications.dailySalesSummary'),
          description: t('emailNotifications.dailySalesSummaryDescription'),
        },
        {
          name: 'monthlyAnnualSalesSummaries',
          title: t('emailNotifications.monthlyAnnualSalesSummaries'),
          description: t('emailNotifications.monthlyAnnualSalesSummariesDescription'),
        },
        {
          name: 'productUpdates',
          title: t('emailNotifications.productUpdates'),
          description: t('emailNotifications.productUpdatesDescription'),
        },
        {
          name: 'programsEvents',
          title: t('emailNotifications.programsEvents'),
          description: t('emailNotifications.programsEventsDescription'),
        },
        {
          name: 'newToSquare',
          title: t('emailNotifications.newToSquare'),
          description: t('emailNotifications.newToSquareDescription'),
        },
        {
          name: 'governmentPrograms',
          title: t('emailNotifications.governmentPrograms'),
          description: t('emailNotifications.governmentProgramsDescription'),
        },
      ],
    },
    // {
    //   title: "Product or feature emails",
    //   items: [
    //     {
    //       name: "squareLoansPayments",
    //       title: "Selio Loans payments",
    //       description:
    //         "Receive an email showing your daily Selio Loans progress.",
    //     },
    //     {
    //       name: "inventoryAlerts",
    //       title: "Inventory Alerts",
    //       description:
    //         "Receive a daily email of items that are low or out of stock.",
    //     },
    //     {
    //       name: "squareMarketing",
    //       title: "Selio marketing",
    //       description:
    //         "Receive summaries of the email communication you have with your customers.",
    //     },
    //   ],
    // },
    {
      title: t('emailNotifications.transactionalEmails'),
      items: [
        {
          name: 'perPayment',
          title: t('emailNotifications.perPayment'),
          description: t('emailNotifications.perPaymentDescription'),
        },
        {
          name: 'perRefund',
          title: t('emailNotifications.perRefund'),
          description: t('emailNotifications.perRefundDescription'),
        },
        {
          name: 'perTransfer',
          title: t('emailNotifications.perTransfer'),
          description: t('emailNotifications.perTransferDescription'),
        },
      ],
    },
  ];

  return (
    <>
      <Box
        p={2}
        sx={{
          mt: 3,
          width: '100%',
          maxWidth: '600px',
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
        }}
      >
        <Box>
          <PageTitle
            title={t('emailNotifications.title')}
            description={t('emailNotifications.description')}
          />

          <Box sx={{ display: 'flex', flexDirection: 'column', mb: 10 }}>
            {sections.map(section => (
              <Box sx={{ mt: 7 }} key={section.title}>
                <Subsection title={section.title} />
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 3,
                  }}
                >
                  {section.items.map(({ name, title, description }) => (
                    <Box
                      key={name}
                      sx={{ display: 'flex', alignItems: 'start' }}
                    >
                      <Checkbox
                        sx={{ p: 0, mr: 1, mt: 0.6 }}
                        checked={checkboxes[name]}
                        onChange={handleCheckboxChange}
                        name={name as string}
                      />
                      <Box>
                        <Typography variant="subtitle1">{title}</Typography>
                        <Typography
                          sx={{ fontWeight: 200 }}
                          variant="body2"
                          color="textSecondary"
                        >
                          {description}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default EmailNotifications;
