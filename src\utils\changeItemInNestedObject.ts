import { cloneDeep } from "lodash";

const changeItemInNestedObjectRecursive = (
  page: any[],
  itemId: string,
  newValue: any,
  valuesToChange: string[]
): any[] | undefined => {
  for (const item of page) {
    // If this is the item we're looking for, update it
    if (item.id === itemId && (item.type === 'product' || !item.type)) {
      valuesToChange.forEach(key => {
        if (key in newValue) {
          item[key] = newValue[key];
        }
      });
    }

    // If this item has nested items, recursively search them
    if (item.items && Array.isArray(item.items)) {
      changeItemInNestedObjectRecursive(
        item.items,
        itemId,
        newValue,
        valuesToChange
      );
    }
  }

  // If this is not the item we're looking for and has no nested items, return as is
  return page;
};

export default function changeItemInNestedObject(
  pages: any[],
  itemId: string,
  newValue: any,
  valuesToChange: string[]
): any[] | undefined {
  const newPages = [];
  for (const page of pages) {
    const newPage = changeItemInNestedObjectRecursive(
      page,
      itemId,
      newValue,
      valuesToChange
    );
    newPages.push(cloneDeep(newPage));
  }

  return newPages;
}
