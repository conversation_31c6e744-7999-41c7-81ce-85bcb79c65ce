// https://www.joshuaslate.com/blog/react-i18n-guide
// https://www.joshuaslate.com/blog/currency-values-in-typescript

const getNoOpFormatter = (
  locale: string = 'default',
  options?: Intl.NumberFormatOptions
) => ({
  format: (x: number | bigint | undefined) => x?.toString() || '',
  formatToParts: (x: number | bigint | undefined) => [
    {
      type: 'unknown' as Intl.NumberFormatPartTypes,
      value: x?.toString() || '',
    },
  ],
  resolvedOptions: new Intl.NumberFormat(locale, options).resolvedOptions,
});

export const getCurrencyFormatter = (
  locale: string = 'default',
  options?: Intl.NumberFormatOptions
): Intl.NumberFormat => {
  try {
    return new Intl.NumberFormat(locale, options);
  } catch {
    if (options?.style === 'currency' && options?.currency) {
      const rootFormatter = new Intl.NumberFormat(locale, {
        ...options,
        currency: 'BTC',
      });

      return {
        format: (x: number | bigint | undefined) =>
          rootFormatter
            .formatToParts(x)
            .map(part =>
              part.type === 'currency' ? options.currency : part.value
            )
            .join(''),
        formatToParts: (x: number | bigint | undefined) =>
          rootFormatter.formatToParts(x).map(part =>
            part.type === 'currency'
              ? ({
                  ...part,
                  value: options.currency,
                } as Intl.NumberFormatPart)
              : part
          ),
        resolvedOptions: rootFormatter.resolvedOptions,
      };
    }

    return getNoOpFormatter(locale, options);
  }
};
