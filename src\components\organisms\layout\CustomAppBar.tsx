import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import DragHandleRoundedIcon from '@mui/icons-material/DragHandleRounded';
import { Box, IconButton } from '@mui/material';
import { AppBar, AppBarProps, useRedirect, useSidebarState } from 'react-admin';

import { useTheme } from '../../../contexts';
import CustomUserMenu from './CustomUserMenu';
import GlobalSearch from './GlobalSearch';
import NotificationsCenter from './NotificationsCenter';

export default function CustomAppBar(props: AppBarProps) {
  const { theme, toggleTheme } = useTheme();
  const [open, setOpen] = useSidebarState();
  const redirect = useRedirect();

  return (
    <AppBar
      color="secondary"
      position="fixed"
      sx={{
        px: { xs: 1, sm: 0 },
        boxShadow: '0 0 4px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.1)',
        fontSize: '14px',
        height: '60px',
        '& .MuiToolbar-root': {
          height: '60px',
        },
      }}
      toolbar={<></>}
      {...props}
      alwaysOn
      userMenu={<CustomUserMenu />}
    >
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton
            color="inherit"
            onClick={() => setOpen(!open)}
            aria-label="menu"
          >
            <DragHandleRoundedIcon />
          </IconButton>
          <Box
            sx={{
              position: {
                xs: 'relative',
                md: 'absolute',
              },
              left: {
                xs: '0',
                md: '50%',
              },
              transform: {
                xs: 'translateX(0)',
                md: 'translateX(-50%)',
              },
              display: 'flex',
              alignItems: 'center',
              height: '40px',
            }}
          >
            <img
              onClick={() => redirect('/')}
              src={`/assets/logo/SELIO_LOGO_${theme.palette.mode === 'dark' ? 'WHITE' : 'BLACK'}.svg`}
              width="80px"
              style={{
                marginTop: '4px',
                cursor: 'pointer',
              }}
            />
          </Box>
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 0, sm: 1 },
          }}
        >
          <GlobalSearch />
          <NotificationsCenter />
          <IconButton onClick={toggleTheme}>
            {theme.palette.mode === 'dark' ? (
              <Brightness7Icon />
            ) : (
              <Brightness4Icon />
            )}
          </IconButton>
        </Box>
      </Box>
    </AppBar>
  );
}
