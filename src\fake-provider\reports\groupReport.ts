import {
  reportCalculatedSummableFields,
  reportSummableFields,
} from './constants';
import {
  OmitKeysWithTypeTransform,
  Report,
  ReportCommonFields,
  ReportType,
  ReportTypeCalculatedSummableFields,
  ReportTypeNonSummableFields,
  ReportTypeSpecificFields,
  ReportTypeSummableFields,
} from './types';
import { isNumberPrimitive } from './utils/isNumber';
import { isObject, isObjectAndNotEmpty } from './utils/isObject';
import { mergeObjectsSummingNumbers } from './utils/mergeObjectsSummingNumbers';
import { safeEval } from './utils/safeEval';

export function groupReport<
  K extends keyof ReportType,
  C extends keyof OmitKeysWithTypeTransform<ReportCommonFields<K>>,
  S extends keyof OmitKeysWithTypeTransform<ReportTypeSpecificFields[K]>,
>(
  reportType: K,
  rawOrFiltredReport: Array<OmitKeysWithTypeTransform<Report<K>>>,
  commonFields: Array<C>,
  specificFields: Array<S>
): Array<
  Pick<ReportCommonFields<K>, C> & {
    report: Array<
      Pick<ReportTypeSpecificFields[K], S> &
        ReportTypeSummableFields[K] &
        OmitKeysWithTypeTransform<ReportTypeCalculatedSummableFields[K]> &
        ReportTypeNonSummableFields[K]
    >;
  }
> {
  // check if the report is empty
  if (rawOrFiltredReport.length === 0) {
    return [];
  }
  const summableFields = reportSummableFields[reportType];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const groupedReports: Record<string, any> = {};

  rawOrFiltredReport.forEach(reportItem => {
    const commonGroupKey = generateGroupKey(reportItem, commonFields);

    if (!groupedReports[commonGroupKey]) {
      groupedReports[commonGroupKey] = initializeGroup(
        reportItem,
        commonFields
      );
      groupedReports[commonGroupKey].report = {};
    }

    reportItem.report.forEach((item, index) => {
      // create a index in case of report where items are not summable - transactions
      const notSummableItemIndex =
        specificFields.length === 0 && summableFields.length === 0
          ? index
          : undefined;
      const specificGroupKey = generateGroupKey(
        item,
        specificFields,
        notSummableItemIndex
      );

      if (!groupedReports[commonGroupKey].report[specificGroupKey]) {
        groupedReports[commonGroupKey].report[specificGroupKey] =
          initializeGroup(item, specificFields, summableFields);
      }
      if (summableFields.length > 0) {
        // in case the report has summable fields we are going to update the summable fields
        updateSummableFields(
          groupedReports[commonGroupKey].report[specificGroupKey],
          item,
          summableFields
        );
      } else {
        // in case the report does not have summable fields we are going to add the item properties to the current to the report
        // transactions, transfers reports are examples of reports that do not have summable fields
        Object.assign(
          groupedReports[commonGroupKey].report[specificGroupKey],
          item
        );
      }
    });
  });

  const result = Object.values(groupedReports).map(group => ({
    ...group,
    report: Object.values(group.report),
  }));

  createCalculatedSummableFields(
    result,
    reportCalculatedSummableFields[reportType]
  );

  return result;
}

function generateGroupKey<T>(
  item: T,
  keys: Array<keyof T>,
  iterationIndex?: number
): string {
  if (keys.length === 0) {
    return 'default' + (iterationIndex ?? '');
  }
  return keys.map(key => item[key]).join('|');
}

function initializeGroup<T, K extends keyof T>(
  item: T,
  keys: Array<K>,
  summableKeys?: Array<keyof T>
): Partial<T> {
  const group: Partial<T> = keys.reduce(
    (acc, key) => ({ ...acc, [key]: item[key] }),
    {}
  );

  if (Array.isArray(summableKeys)) {
    summableKeys.forEach(key => {
      group[key] = undefined;
    });
  }

  return group;
}

// this function is kind of a copy of the mergeObjectsSummingNumbers function
// the difference is that this function is going to update only the summable fields
function updateSummableFields<T extends object>(
  target: Partial<T>,
  source: T,
  summableKeys: Array<keyof T>
): void {
  if (!isObjectAndNotEmpty(source)) {
    return;
  }

  for (const key of summableKeys) {
    if (!(key in source) || !Object.prototype.hasOwnProperty.call(source, key))
      continue;

    const sourceValue = source[key];

    if (sourceValue === undefined) {
      continue;
    }

    if (isNumberPrimitive(sourceValue)) {
      const targetValue = target[key];
      if (isNumberPrimitive(targetValue)) {
        target[key] = (targetValue + sourceValue) as unknown as T[keyof T];
      } else {
        target[key] = sourceValue;
      }
      continue;
    }

    if (isObject(sourceValue)) {
      const targetValue = (isObject(target[key])
        ? target[key]
        : {}) as unknown as T[keyof T];

      if (mergeObjectsSummingNumbers(targetValue, sourceValue)) {
        target[key] = targetValue;
      }
      continue;
    }

    if (Array.isArray(sourceValue)) {
      // Create a properly typed local variable
      const sourceArray: Array<unknown> = sourceValue;
      // Now TypeScript knows sourceArray is definitely an array
      if (sourceArray.length > 0) {
        if (Array.isArray(target[key])) {
          // Faster than concat for small arrays
          const targetArray = target[key] as Array<unknown>;
          for (let i = 0; i < sourceArray.length; i++) {
            targetArray.push(sourceArray[i]);
          }
        } else {
          target[key] = [...sourceArray] as unknown as T[keyof T];
        }
      }
      continue;
    }

    target[key] = sourceValue;
  }
}

// this function will create the calculated fields for the report
// calculated fields are fields that are calculated based on the summable fields
// so the user will provide the report, summable keys and calculated keys
// the calculated keys must have the following format: "fieldName@eval:expression"
// the expression will contain mathemahical operations, numbers and summable fields
// which are marcated with the $ sign at the beginning of the field name
// something like totalDividedByTwo@eval:($price*$quantity)/2
// the result will be added to the report array item object as a new field
// also the expression can contain math functions min, max, avg, sum and dummy
// something like totalDividedByMin@eval:($price*$quantity)/$min(price) or totalDividedByMax@eval:100/$max(price)+$min(price)+$price+100
// calculated fields can be used in calculated functions as well but not the other way around
function createCalculatedSummableFields(
  result: unknown,
  calculatedKeys: Array<string>
): void {
  if (
    Array.isArray(calculatedKeys) &&
    calculatedKeys.length > 0 &&
    Array.isArray(result) &&
    result.length > 0
  ) {
    // extract the keys that contain min, max, avg, sum and dummy functions and the keys that do not contain them
    // regex: /\$min\(|\$max\(|\$avg\(|\$sum\|\$dummy\((/.test(calculatedKey) - this regex will return true if the calculated key contains min, max, avg, sum and dummy
    // regex: !/\$min\(|\$max\(|\$avg\(|\$sum\|\$dummy\((/.test(calculatedKey) - this regex will return true if the calculated key does not contain min, max avg, sum and dummy;
    const { calculatedKeysWithFunctions, calculatedKeysWithoutFunctions } =
      calculatedKeys.reduce(
        (acc, calculatedKey) => {
          if (/\$min\(|\$max\(|\$avg\(|\$sum\(|\$dummy\(/.test(calculatedKey)) {
            acc.calculatedKeysWithFunctions.push(calculatedKey);
          } else {
            acc.calculatedKeysWithoutFunctions.push(calculatedKey);
          }
          return acc;
        },
        {
          calculatedKeysWithFunctions: [] as string[],
          calculatedKeysWithoutFunctions: [] as string[],
        }
      );
    // first we need to process the calculated keys that do not contain min, max, avg, sum and dummy functions
    // because we need to calculate the fields before we calculate the functions that use the fields
    // so we are going to extract the field name and the expression
    // based on the format fieldName@eval:expression
    // the expression can contain math operations, numbers and summable fields
    // the summable fields are marked with the $ sign at the beginning of the field name
    // we are going to iterate over the result and calculate the expression for each item
    // the result of the expression is going to be added to the item object
    if (calculatedKeysWithoutFunctions.length > 0) {
      result.forEach(group => {
        group.report.forEach((item: Record<string, unknown>) => {
          calculatedKeysWithoutFunctions.forEach(calculatedKey => {
            const [fieldName, expression] = calculatedKey.split('@eval:');
            // Replace field tokens with item values in the expression string
            const processedExpression = replaceFieldTokens(expression, item);
            // Evaluate the expression and add the result to the item
            item[fieldName] = safeEval(processedExpression);
          });
        });
      });
    }
    // now that we have calculated the fields that do not contain min, max, avg, sum and dummy functions
    // we need to calculate the min, max, avg, sum and dummy functions
    if (calculatedKeysWithFunctions.length > 0) {
      // create a set with all the fields that need to be removed from the group after the functions are calculated
      const fieldsToRemoveFromGroup = new Set<string>();
      // now we need to extract the function and the field name used in the function from all the calculated keys
      const functionTokens = extractFunctionTokens(calculatedKeysWithFunctions);
      // we are going to iterate over the function tokens and calculate the extracted function for each group
      functionTokens.forEach(functionToken => {
        // we strip the $ and ) from the function token to extract the function name and the field name
        const [functionName, summableFieldName] = functionToken
          .slice(1, -1)
          .split('(');
        // add the field name to the set to remove it from the group
        fieldsToRemoveFromGroup.add(functionName + summableFieldName);
        // iterate over the groups and calculate the extracted functions for each group
        // the result is going to be added to the group object in the field name that is function + field name (eg minPrice)
        result.forEach(group => {
          group[functionName + summableFieldName] = calculateFunctionValue(
            group.report,
            functionName,
            summableFieldName
          );
        });
      });
      // now we have the min, max, avg, sum and dummy functions calculated for each group
      // we need to calculate the expressions for each item in the group
      // we need to iterate over the calculated keys with functions and replace the function tokens and field tokens
      // function tokens start with $ and contain () - $min(fieldName) and the field tokens start with $ - $fieldName
      // the function tokens found in the expression are going to be replaced with the value from the group
      // found in the group object in the field name that is function + field name (eg $min(price) is going to be the value of minPrice)
      // the field tokens found in the expression are going to be replaced with the value from the item
      // if the field does not exist in the item we are going to set it to 0
      // the result of the expression is going to be added to the item object
      result.forEach(group => {
        group.report.forEach((item: Record<string, unknown>) => {
          calculatedKeysWithFunctions.forEach(calculatedKey => {
            const [fieldName, expressionWithFunctions] =
              calculatedKey.split('@eval:');
            // Replace function tokens with group values
            let processedExpression = replaceFunctionTokens(
              expressionWithFunctions,
              group
            );
            // Replace field tokens with item values
            processedExpression = replaceFieldTokens(processedExpression, item);
            // Evaluate the expression and add the result to the item
            item[fieldName] = safeEval(processedExpression);
          });
        });
        // remove the fields used to calculate the min, max, avg, sum and dummy functions from the group
        fieldsToRemoveFromGroup.forEach(field => {
          delete group[field];
        });
      });
    }
  }
}

// Helper function to replace field tokens with item values in the expression string
// replacing strings that start with $ containing only letters and numbers and not followed by ( with the value from the item
// we do not want to replace the function tokens that start with $ and contain () - $min(fieldName)
// so we need to distinct $min from $min(fieldName)
function replaceFieldTokens(
  expression: string,
  item: Record<string, unknown>
): string {
  return expression.replace(/\$[a-zA-Z0-9]+(?!\()/g, fieldToken => {
    const tokenFieldName = fieldToken.slice(1);
    return (item[tokenFieldName] ?? 0).toString();
  });
}

// Helper function to extract function tokens from the calculated keys with functions
// function tokens start with $ and contain () - $min(fieldName)
// we need to extract the function name and the field name from the token
// based on the format $function(fieldName)
// we are going to iterate over the calculated keys with functions and extract the function tokens
// after that we are going to create a set with all the unique function tokens
// and return the set
function extractFunctionTokens(
  calculatedKeysWithFunctions: string[]
): Set<string> {
  return calculatedKeysWithFunctions.reduce((acc, calculatedKey) => {
    const expressionWithFunctions = calculatedKey.split('@eval:')[1];
    const functionTokens = expressionWithFunctions.match(
      /\$[a-zA-Z0-9]+\([a-zA-Z0-9]+\)/g
    );
    if (functionTokens !== null) {
      functionTokens.forEach(functionToken => {
        acc.add(functionToken);
      });
    }
    return acc;
  }, new Set<string>());
}

// Helper function to calculate the min, max, avg, sum and dummy functions for the group
function calculateFunctionValue(
  report: Array<Record<string, unknown>>,
  functionName: string,
  summableFieldName: string
): number {
  if (report.length === 0) {
    return 0;
  }
  const values = report.map(item => (item[summableFieldName] as number) ?? 0);
  switch (functionName) {
    case 'min':
      return Math.min(...values);
    case 'max':
      return Math.max(...values);
    case 'avg': {
      const sum = values.reduce((acc, val) => acc + val, 0);
      return sum / values.length;
    }
    case 'sum':
      return values.reduce((acc, val) => acc + val, 0);
    default:
      return 0;
  }
}

// Helper function to replace function tokens with group values in the expression string
// replacing strings that start with $ containing only letters and numbers and followed by ( fieldName )
// with the value from the group
function replaceFunctionTokens(
  expression: string,
  group: Record<string, unknown>
): string {
  return expression.replace(
    /\$[a-zA-Z0-9]+\([a-zA-Z0-9]+\)/g,
    functionToken => {
      const [functionName, tokenFieldName] = functionToken
        .slice(1, -1)
        .split('(');
      return (group[functionName + tokenFieldName] ?? 0).toString();
    }
  );
}
