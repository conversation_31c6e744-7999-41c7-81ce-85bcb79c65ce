import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
// Add LinearProgress to imports
import {
  Box,
  capitalize,
  CircularProgress,
  LinearProgress,
} from '@mui/material';
import { useGetListLive } from '@react-admin/ra-realtime';
import { downloadCSV } from 'react-admin';
import { useTranslation } from 'react-i18next';
import { useReactToPrint } from 'react-to-print';

import ChangeViewportBtn from '~/components/molecules/ChangeViewportBtn';
import PageTitle from '~/components/molecules/PageTitle';
import composeFilters from '~/fake-provider/reports/composeFilters';
import { filterReport } from '~/fake-provider/reports/filterReport';
import { getReportCommonFieldsValues } from '~/fake-provider/reports/getReportCommonFieldsValues';
import { groupReport } from '~/fake-provider/reports/groupReport';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useReportRawData } from '~/hooks/useReportRawData';
import { useGetListLocationsLive } from '~/providers/resources';
import { CurrencyType } from '~/utils/formatNumber';
import { FieldOption } from '../../../../types/globals';
import { OptionType } from '../components/FilterItem';
import ReportDateTitle from '../components/ReportDateTitle';
import ReportFilters, {
  DiningOption,
  ReportFiltersState,
  SourceOption,
} from '../components/ReportFilters';
import { SourceTypes } from '../report-sales-revenue/SalesRevenue';
import PaymentMethodsGraph from './components/PaymentMethodsGraph';
import PaymentMethodsTable from './components/PaymentMethodTable';

const REPORT_TYPE = 'payments';

export default function PaymentMethods() {
  const { t } = useTranslation();
  const { sellPointId, dateRange, timeRange } = useGlobalResourceFilters();

  // Add the useReportRawData hook
  const { data, dataTimestamp, isLoading, error, isInitialStorageFetchDone } =
    useReportRawData(REPORT_TYPE);

  const rawData = useMemo(() => data, [data]);

  // Initialize filters with default values to avoid undefined
  const defaultValues = useMemo(
    () => ({
      dateRange,
      sellpointId: sellPointId,
      timeRange: {
        allDay: !timeRange,
        start: timeRange?.[0],
        end: timeRange?.[1],
      },
      diningOption: DiningOption.ALL,
      source: SourceOption.ALL,
    }),
    [dateRange, sellPointId, timeRange]
  );

  const [filters, setFilters] = useState<ReportFiltersState>(defaultValues);
  const [commonFields, setCommonFields] = useState<{
    [key: string]: OptionType[];
  }>({});
  const [tableFields, setTableFields] = useState<FieldOption[]>([
    { isChecked: true, value: 'type' },
    { isChecked: true, value: 'value' },
    { isChecked: true, value: 'quantity' },
  ]);
  const [currency, setCurrency] = useState<CurrencyType>();

  const contentRef = useRef<HTMLDivElement>(null);

  const mountCountRef = useRef(0);

  const updateCommonField = useCallback((key: string, value: OptionType[]) => {
    setCommonFields(prevState => ({
      ...prevState,
      [key]: value,
    }));
  }, []);

  // Extract processing common fields into its own effect
  useEffect(() => {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) return;

    try {
      // Set currency from data
      if (rawData.length && rawData[0].currency) {
        setCurrency(rawData[0].currency as CurrencyType);
      }

      const commonFieldsValues = getReportCommonFieldsValues(
        REPORT_TYPE,
        rawData
      );

      // Process members
      const tmpMembers: OptionType[] = [];
      commonFieldsValues.member?.forEach((el, index) => {
        if (el) {
          tmpMembers.push({
            label: el,
            value: commonFieldsValues.memberId?.[index] || el,
          });
        }
      });
      updateCommonField('member', tmpMembers);

      // Process floors
      const tmpFloors: OptionType[] = [];
      commonFieldsValues.section?.forEach(el => {
        if (el) {
          tmpFloors.push({
            label: el,
            value: el,
          });
        }
      });
      updateCommonField('floor', tmpFloors);

      // Process service types
      const tmpServiceType: OptionType[] = [];
      commonFieldsValues.serviceType?.forEach(el => {
        if (el) {
          tmpServiceType.push({
            label: capitalize(el),
            value: el,
          });
        }
      });
      updateCommonField('serviceType', tmpServiceType);

      // Process sources
      const tmpSources: OptionType[] = [];
      commonFieldsValues.source?.forEach(el => {
        if (el) {
          tmpSources.push({
            label: SourceTypes[el as keyof typeof SourceTypes] || el,
            value: el,
          });
        }
      });
      updateCommonField('sources', tmpSources);
    } catch (e) {
      console.error('Error processing common fields:', e);
    }

    return () => {
      console.log('PaymentsReport unmounted');
    };
  }, [rawData, updateCommonField]);

  // Add this near your other state variables
  const [isProcessingData, setIsProcessingData] = useState(false);

  // Modify your data processing useMemo
  const { tableData, graphData, composedFilters } = useMemo(() => {
    if (!rawData || !filters || !Array.isArray(rawData))
      return { tableData: [], graphData: [], composedFilters: [] };

    setIsProcessingData(true);
    try {
      // Your existing processing logic
      const composedFilters = composeFilters(filters, REPORT_TYPE);

      const newFilteredData = filterReport(
        REPORT_TYPE,
        rawData,
        composedFilters,
        []
      );

      // Process graph data
      const groupedGraphData = groupReport(
        REPORT_TYPE,
        newFilteredData,
        [],
        ['type']
      );

      const graphData =
        groupedGraphData[0]?.report?.sort((a, b) => {
          return (b.value ?? 0) - (a.value ?? 0);
        }) || [];

      // Process table data
      const groupedTableData = groupReport(
        REPORT_TYPE,
        newFilteredData,
        [],
        ['type']
      );

      const tableData =
        groupedTableData[0]?.report?.sort((a, b) =>
          (a.type || '').localeCompare(b.type || '')
        ) || [];

      setIsProcessingData(false);
      return { tableData, graphData, composedFilters };
    } catch (e) {
      console.error('Error processing report data:', e);
      setIsProcessingData(false);
      return { tableData: [], graphData: [], composedFilters: [] };
    }
  }, [filters, rawData]);

  const onFiltersChange = useCallback((newFilters: ReportFiltersState) => {
    setFilters(newFilters);
  }, []);

  // Scroll to top on mount
  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  // Format filters for display
  const formattedFilters = useMemo(() => {
    if (!filters) return {};

    const getLabel = (field: string, value: string) => {
      const options = commonFields[field];
      const found = options?.find(opt => opt.value === value);
      return found?.label || value;
    };

    return {
      sellpoint: filters.sellpointId,
      dateRange: filters.dateRange.map(d => d?.format('YYYY-MM-DD')),
      timeRange: filters.timeRange,
      member: getLabel('member', filters.member ?? ''),
      floor: getLabel('floor', filters.floor ?? ''),
      serviceType: getLabel('serviceType', filters.diningOption),
      source: getLabel('sources', filters.source),
    };
  }, [filters, commonFields]);

  // Memoize the setTableFields callback
  const handleSetTableFields = useCallback((newFields: FieldOption[]) => {
    setTableFields(newFields);
  }, []);

  useEffect(() => {
    console.log(
      `PaymentsReport mounted - iteration ${++mountCountRef.current}`
    );

    return () => {
      console.log('PaymentsReport unmounted');
    };
  }, []); // Empty deps means it runs only on mount/unmount
  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();

  const handleExport = () => {
    const title = 'Report payment methods';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      ['Payment Method', 'Count', 'Collected'].join(','),
      ...tableData.map(el =>
        [el.type, el.quantity / 1000, el.value / 10000].join(',')
      ),
      [
        'Total',
        tableData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        tableData.reduce((acc, el) => acc + el.value, 0) / 10000,
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'paymentMethods');
  };

  return (
    <Box p={2} ref={contentRef}>
      <ChangeViewportBtn />
      <PageTitle
        title={t('menu.paymentMethods')}
        description={
          <>
            {t('menu.paymentMethodsDescription')}{' '}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
      <ReportFilters
        defaultValues={defaultValues}
        onFiltersChange={onFiltersChange}
        commonFields={commonFields}
        contentRef={contentRef}
        handleExport={handleExport}
      />
      {/* Show linear progress at the top when loading */}
      {(isLoading || isProcessingData) &&
        ((
          <Box sx={{ width: '100%', position: 'sticky', top: 0, zIndex: 1000 }}>
            <LinearProgress />
          </Box>
        ) as any)}

      <ReportDateTitle />

      {/* Show error when applicable */}
      {error && !isLoading && !isProcessingData && (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 2 }}>
          {t('errors.data-loading-error', {
            error:
              typeof error === 'object' && error && 'message' in error
                ? (error as any).message
                : String(error),
          })}
        </Box>
      )}

      {/* Always render content when we have data, regardless of loading state */}
      {isInitialStorageFetchDone && (
        <>
          <PaymentMethodsGraph tableData={graphData} currency={currency} />
          <PaymentMethodsTable
            formattedFilters={formattedFilters}
            reportType={REPORT_TYPE}
            rawData={rawData}
            composedFilters={composedFilters}
            filters={filters}
            fields={tableFields}
            setFields={
              handleSetTableFields as React.Dispatch<
                React.SetStateAction<FieldOption[]>
              >
            }
            tableData={tableData || []}
          />
        </>
      )}
    </Box>
  );
}
