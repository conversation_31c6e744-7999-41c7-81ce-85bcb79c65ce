export const measureUnits = [
  {
    id: 'BUC',
    unece: 'PCE',
    symbol: { en: 'pce', ro: 'buc' },
    name: { en: 'Piece', ro: 'Bucată' },
    description: {
      en: 'For items sold individually. E.g., A bottle of water, a chocolate bar.',
      ro: 'Pentru articole vândute individual. Ex: O sticlă de apă, o ciocolată.',
    },
    type: 'discrete',
    order: 0,
  },
  {
    id: 'NA',
    unece: 'QAN',
    symbol: { en: ' ', ro: ' ' },
    name: { en: 'Without', ro: 'Fără' },
    description: {
      en: "A generic unit when a more specific unit isn't needed. E.g., Miscellaneous small parts, number of people (e.g., for 'coperto').",
      ro: "O unitate generică când nu este necesară o unitate mai specifică. Ex: Diverse piese mici, numărul de persoane (ex: pentru 'coperto').",
    },
    type: 'discrete',
    order: 1,
  },
  {
    id: 'G',
    unece: 'GRM',
    symbol: { en: 'g', ro: 'g' },
    name: { en: 'Gram', ro: 'Gram' },
    description: {
      en: 'For measuring light items by weight. E.g., Spices sold in bulk, small quantities of herbs.',
      ro: 'Pentru măsurarea articolelor ușoare după greutate. Ex: Condimente vândute în vrac, cantități mici de ierburi aromatice.',
    },
    type: 'mass',
    order: 2,
  },
  {
    id: 'KG',
    unece: 'KGM',
    symbol: { en: 'kg', ro: 'kg' },
    name: { en: 'Kilogram', ro: 'Kilogram' },
    description: {
      en: 'For measuring items by weight. E.g., Fruits, vegetables, meat.',
      ro: 'Pentru măsurarea articolelor după greutate. Ex: Fructe, legume, carne.',
    },
    type: 'mass',
    order: 3,
  },
  {
    id: 'ML',
    unece: 'MLT',
    symbol: { en: 'ml', ro: 'ml' },
    name: { en: 'Millilitre', ro: 'Mililitru' },
    description: {
      en: 'For measuring small liquid volumes. E.g., A shot of spirit, perfume samples.',
      ro: 'Pentru măsurarea volumelor mici de lichide. Ex: Un shot de tărie, mostre de parfum.',
    },
    type: 'volume',
    order: 4,
  },
  {
    id: 'L',
    unece: 'LTR',
    symbol: { en: 'l', ro: 'l' },
    name: { en: 'Litre', ro: 'Litru' },
    description: {
      en: 'For measuring liquid volumes. E.g., Milk, soft drinks, paint.',
      ro: 'Pentru măsurarea volumelor de lichide. Ex: Lapte, băuturi răcoritoare, vopsea.',
    },
    type: 'volume',
    order: 5,
  },
  {
    id: 'M',
    unece: 'MTR',
    symbol: { en: 'm', ro: 'm' },
    name: { en: 'Metre', ro: 'Metru' },
    description: {
      en: 'For measuring lengths or services based on length. E.g., Rope, fencing installation per meter.',
      ro: 'Pentru măsurarea lungimilor sau serviciilor bazate pe lungime. Ex: Frânghie, instalare gard pe metru.',
    },
    type: 'length',
    order: 6,
  },
  {
    id: 'KM',
    unece: 'KMT',
    symbol: { en: 'km', ro: 'km' },
    name: { en: 'Kilometre', ro: 'Kilometru' },
    description: {
      en: 'For measuring long distances or services based on distance. E.g., Delivery service fee per kilometre.',
      ro: 'Pentru măsurarea distanțelor mari sau a serviciilor bazate pe distanță. Ex: Taxă de livrare pe kilometru.',
    },
    type: 'length',
    order: 7,
  },
  {
    id: 'M2',
    unece: 'MTK',
    symbol: { en: 'm²', ro: 'm²' },
    name: { en: 'Square metre', ro: 'Metru pătrat' },
    description: {
      en: 'For measuring areas or services based on area. E.g., Flooring material, painting service per square meter.',
      ro: 'Pentru măsurarea suprafețelor sau a serviciilor bazate pe suprafață. Ex: Material de pardoseală, serviciu de zugrăvire pe metru pătrat.',
    },
    type: 'area',
    order: 8,
  },
  {
    id: 'M3',
    unece: 'MTQ',
    symbol: { en: 'm³', ro: 'm³' },
    name: { en: 'Cubic metre', ro: 'Metru cub' },
    description: {
      en: 'For measuring large volumes or services based on volume. E.g., Sand, concrete, waste removal per cubic meter.',
      ro: 'Pentru măsurarea volumelor mari sau a serviciilor bazate pe volum. Ex: Nisip, beton, îndepărtare deșeuri pe metru cub.',
    },
    type: 'volume',
    order: 9,
  },
  {
    id: 'MIN',
    unece: 'MIN',
    symbol: { en: 'min', ro: 'min' },
    name: { en: 'Minute', ro: 'Minut' },
    description: {
      en: 'For services billed per minute. E.g., Phone call charges, specific service tasks.',
      ro: 'Pentru servicii facturate pe minut. Ex: Taxe apeluri telefonice, sarcini specifice de serviciu.',
    },
    type: 'time',
    order: 10,
  },
  {
    id: 'H',
    unece: 'HUR',
    symbol: { en: 'h', ro: 'h' },
    name: { en: 'Hour', ro: 'Oră' },
    description: {
      en: 'For services billed hourly. E.g., Consulting service, equipment rental.',
      ro: 'Pentru servicii facturate pe oră. Ex: Serviciu de consultanță, închiriere echipamente.',
    },
    type: 'time',
    order: 11,
  },
  {
    id: 'DAY',
    unece: 'DAY',
    symbol: { en: 'day', ro: 'zi' },
    name: { en: 'Day', ro: 'Zi' },
    description: {
      en: 'For services or rentals per day. E.g., Car rental per day, daily parking fee.',
      ro: 'Pentru servicii sau închirieri pe zi. Ex: Închiriere mașină pe zi, taxă zilnică de parcare.',
    },
    type: 'time',
    order: 12,
  },
  {
    id: 'WEE',
    unece: 'WEE',
    symbol: { en: 'week', ro: 'săptămână' },
    name: { en: 'Week', ro: 'Săptămână' },
    description: {
      en: 'For services or rentals per week. E.g., Long-term equipment rental, weekly cleaning service.',
      ro: 'Pentru servicii sau închirieri pe săptămână. Ex: Închiriere echipamente pe termen lung, serviciu de curățenie săptămânal.',
    },
    type: 'time',
    order: 13,
  },
  {
    id: 'MO',
    unece: 'MON',
    symbol: { en: 'mo', ro: 'lună' },
    name: { en: 'Month', ro: 'Lună' },
    description: {
      en: 'For services or subscriptions billed monthly. E.g., Software subscription, monthly membership.',
      ro: 'Pentru servicii sau abonamente facturate lunar. Ex: Abonament software, abonament lunar.',
    },
    type: 'time',
    order: 14,
  },
  {
    id: 'YR',
    unece: 'ANN',
    symbol: { en: 'yr', ro: 'an' },
    name: { en: 'Year', ro: 'An' },
    description: {
      en: 'For services or subscriptions billed annually. E.g., Annual maintenance contract, yearly service plan.',
      ro: 'Pentru servicii sau abonamente facturate anual. Ex: Contract anual de întreținere, plan de servicii anual.',
    },
    type: 'time',
    order: 15,
  },
];
