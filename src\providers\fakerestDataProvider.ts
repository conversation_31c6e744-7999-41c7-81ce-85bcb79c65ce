// source : https://github.com/marmelab/react-admin/tree/master/packages/ra-data-fakerest

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { Database } from 'fakerest';
import { DataProvider } from 'ra-core';

function log(type: any, resource: any, params: any, response: any) {
  if (typeof console.group === 'function') {
    console.groupCollapsed(type, resource, JSON.stringify(params));
    console.log(response);
    console.groupEnd();
  } else {
    console.log('FakeRest request ', type, resource, params);
    console.log('FakeRest response', response);
  }
}

function delayed(response: any, delay?: number) {
  return delay
    ? new Promise(resolve => {
        setTimeout(() => resolve(response), delay);
      })
    : response;
}

export default (
  data: any,
  loggingEnabled = false,
  delay?: number
): DataProvider => {
  const database = new Database({ data });
  if (typeof window !== 'undefined') {
    (window as any)._database = database;
  }

  function getResponse(type: any, resource: any, params: any) {
    switch (type) {
      case 'getList': {
        const { page, perPage } = params.pagination;
        const { field, order } = params.sort;
        const query = {
          sort: [field, order] as [string, 'asc' | 'desc'],
          range: [(page - 1) * perPage, page * perPage - 1] as [number, number],
          filter: params.filter,
          embed: getEmbedParam(params.meta?.embed, params.meta?.prefetch),
        };
        const data = database.getAll(resource, query);
        const prefetched = getPrefetchedData(data, params.meta?.prefetch);
        return delayed(
          {
            data: removePrefetchedData(data, params.meta?.prefetch),
            total: database.getCount(resource, {
              filter: params.filter,
            }),
            meta: params.meta?.prefetch ? { prefetched } : undefined,
          },
          delay
        );
      }
      case 'getOne': {
        const data = database.getOne(resource, params.id, {
          ...params,
          embed: getEmbedParam(params.meta?.embed, params.meta?.prefetch),
        });
        const prefetched = getPrefetchedData(data, params.meta?.prefetch);
        return delayed(
          {
            data: removePrefetchedData(data, params.meta?.prefetch),
            meta: params.meta?.prefetch ? { prefetched } : undefined,
          },
          delay
        );
      }
      case 'getMany': {
        const data = params.ids.map((id: any) =>
          database.getOne(resource, id, {
            ...params,
            embed: getEmbedParam(params.meta?.embed, params.meta?.prefetch),
          })
        );
        const prefetched = getPrefetchedData(data, params.meta?.prefetch);
        return delayed(
          {
            data: removePrefetchedData(data, params.meta?.prefetch),
            meta: params.meta?.prefetch ? { prefetched } : undefined,
          },
          delay
        );
      }
      case 'getManyReference': {
        const { page, perPage } = params.pagination;
        const { field, order } = params.sort;
        const query = {
          sort: [field, order] as [string, 'asc' | 'desc'],
          range: [(page - 1) * perPage, page * perPage - 1] as [number, number],
          filter: { ...params.filter, [params.target]: params.id },
          embed: getEmbedParam(params.meta?.embed, params.meta?.prefetch),
        };
        const data = database.getAll(resource, query);
        const prefetched = getPrefetchedData(data, params.meta?.prefetch);
        return delayed(
          {
            data: removePrefetchedData(data, params.meta?.prefetch),
            total: database.getCount(resource, {
              filter: query.filter,
            }),
            meta: params.meta?.prefetch ? { prefetched } : undefined,
          },
          delay
        );
      }
      case 'update':
        return delayed(
          {
            data: database.updateOne(
              resource,
              params.id,
              cleanupData(params.data)
            ),
          },
          delay
        );
      case 'updateMany':
        params.ids.forEach((id: any) =>
          database.updateOne(resource, id, cleanupData(params.data))
        );
        return delayed({ data: params.ids }, delay);
      case 'create':
        return delayed(
          {
            data: database.addOne(resource, cleanupData(params.data)),
          },
          delay
        );
      case 'delete':
        return delayed(
          { data: database.removeOne(resource, params.id) },
          delay
        );
      case 'deleteMany':
        params.ids.forEach((id: any) => database.removeOne(resource, id));
        return delayed({ data: params.ids }, delay);
      default:
        return false;
    }
  }

  const handle = async (
    type: any,
    resource: any,
    params: any
  ): Promise<any> => {
    const collection = database.getCollection(resource);
    if (!collection && type !== 'create') {
      const error = new UndefinedResourceError(
        `Undefined collection "${resource}"`
      );
      error.code = 1; // make that error detectable
      throw error;
    }
    const response = await getResponse(type, resource, params);

    if (loggingEnabled) {
      const { signal, ...paramsWithoutSignal } = params;
      log(type, resource, paramsWithoutSignal, response);
    }
    return response;
  };

  return {
    getList: (resource, params) => handle('getList', resource, params),
    getOne: (resource, params) => handle('getOne', resource, params),
    getMany: (resource, params) => handle('getMany', resource, params),
    getManyReference: (resource, params) =>
      handle('getManyReference', resource, params),
    update: (resource, params) => handle('update', resource, params),
    updateMany: (resource, params) => handle('updateMany', resource, params),
    create: (resource, params) => handle('create', resource, params),
    delete: (resource, params) => handle('delete', resource, params),
    deleteMany: (resource, params) => handle('deleteMany', resource, params),
  };
};

function getEmbedParam(embed: string[], prefetch: string[]) {
  if (!embed && !prefetch) return;
  const param = new Set<string>();
  if (embed) embed.forEach(e => param.add(e));
  if (prefetch) prefetch.forEach(e => param.add(e));
  return Array.from(param);
}

const getPrefetchedData = (data: any, prefetchParam?: string[]) => {
  if (!prefetchParam) return undefined;
  const prefetched: Record<string, any[]> = {};
  const dataArray = Array.isArray(data) ? data : [data];
  prefetchParam.forEach(name => {
    const resource = name.endsWith('s') ? name : `${name}s`;
    dataArray.forEach(record => {
      if (!prefetched[resource]) {
        prefetched[resource] = [];
      }
      if (prefetched[resource].some(r => r.id === record[name].id)) {
        // do not add the record if it's already there
        return;
      }
      prefetched[resource].push(record[name]);
    });
  });

  return prefetched;
};

const removePrefetchedData = (data: any, prefetchParam?: string[]) => {
  if (!prefetchParam) return data;
  const dataArray = Array.isArray(data) ? data : [data];
  const newDataArray = dataArray.map(record => {
    const newRecord: Record<string, any> = {};
    for (const key in record) {
      if (!prefetchParam.includes(key)) {
        newRecord[key] = record[key];
      }
    }
    return newRecord;
  });
  return Array.isArray(data) ? newDataArray : newDataArray[0];
};

const cleanupData = <T>(data: T): T => JSON.parse(JSON.stringify(data));

class UndefinedResourceError extends Error {
  code!: number;
}
