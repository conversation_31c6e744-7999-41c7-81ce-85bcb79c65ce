import { Coordinates } from '../components/organisms/table-dnd/types';

export function elementsCollide(a: Coordinates, b: Coordinates): boolean {
  const xOverlap = checkOverlap(+a.startX, +a.endX, +b.startX, +b.endX);
  const yOverlap = checkOverlap(+a.startY, +a.endY, +b.startY, +b.endY);
  return xOverlap && yOverlap;
}

function checkOverlap(
  start1: number,
  end1: number,
  start2: number,
  end2: number
): boolean {
  return (
    (start1 >= start2 && start1 < end2) ||
    (end1 > start2 && end1 <= end2) ||
    (start1 <= start2 && end1 >= end2)
  );
}
