import { formatNumber } from './formatNumber';

function formatAndDivideNumber(
  n: number | undefined,
  formatType: 'number' | 'currency' | 'percentage' | undefined = 'currency'
) {
  if (!n) return '';

  const dividedValue = n / 10000;
  const number = formatNumber(dividedValue, formatType).toString();
  return number;
}

export default function parseTableProperty(
  obj: object | number | undefined,
  key?: string,
  formatType: 'number' | 'currency' | 'percentage' | undefined = 'currency'
): string {
  if (!obj) return '';
  if (typeof obj === 'number') return formatAndDivideNumber(obj, formatType);

  if (!key) return '';
  if (!(key in obj)) return '';
  const value = obj[key as keyof typeof obj];
  return formatAndDivideNumber(value, formatType);
}
