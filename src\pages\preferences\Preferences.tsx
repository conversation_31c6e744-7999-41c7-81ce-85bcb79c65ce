import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import LanguageSelectorDropdown from '~/components/molecules/LanguageSelectDropdown';
import PageTitle from '~/components/molecules/PageTitle';
import Subsection from '~/components/molecules/Subsection';
import { useCookiesConsent } from '~/contexts';

const Preferences: React.FC = () => {
  const { t } = useTranslation('');
  const { handleInsideButtonClick } = useCookiesConsent();

  return (
    <Box p={2} sx={{ mt: 2, width: '100%', maxWidth: '600px' }}>
      <PageTitle
        title={t('preferences.title')}
        description={
          <>
            {t('preferences.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
      />
      <LanguageSelectorDropdown />
      <Box sx={{ mt: 8 }}>
        <Subsection
          title={t('preferences.manageCookies')}
          subtitle={t('preferences.cookiesDescription')}
        >
          <Button variant="contained" onClick={handleInsideButtonClick}>
            {t('preferences.customizeCookiePreferences')}
          </Button>
        </Subsection>
      </Box>
    </Box>
  );
};

export default Preferences;
