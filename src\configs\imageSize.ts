/**
 * Standard image size definitions used across the application
 * These sizes are generated for every uploaded image and known by the backend
 */

export interface StandardImageSize {
    key: string;           // Unique identifier (e.g., 'thumbnail', 'card')
    name: string;          // Human-readable name
    width: number;         // Target width in pixels
    height: number;        // Target height in pixels
    aspectRatio: number;   // Calculated aspect ratio (width/height)
    description?: string;  // Optional description of usage
    isDefault?: boolean;   // Whether this size is always generated
}

/**
 * Standard image sizes used throughout the application
 * Backend will know to serve these specific sizes
 */
export const STANDARD_IMAGE_SIZES: Record<string, StandardImageSize> = {
    // Automatic thumbnail (always required for public images)
    thumbnail: {
        key: 'thumbnail',
        name: 'Thumbnail',
        width: 50,
        height: 50,
        aspectRatio: 1,
        description: 'Automatic thumbnail for all public images',
        isDefault: true, // Always generated
    },

    // Square formats (1:1 aspect ratio)
    square_s: {
        key: 'square_s',
        name: 'Square Small',
        width: 150,
        height: 150,
        aspectRatio: 1,
        description: 'Small square image for lists and cards',
    },
    square_m: {
        key: 'square_m',
        name: 'Square Medium',
        width: 300,
        height: 300,
        aspectRatio: 1,
        description: 'Medium square image for card displays',
    },
    square_l: {
        key: 'square_l',
        name: 'Square Large',
        width: 600,
        height: 600,
        aspectRatio: 1,
        description: 'Large square image for detail views',
    },
    profile: {
        key: 'profile',
        name: 'Profile',
        width: 200,
        height: 200,
        aspectRatio: 1,
        description: 'User profile images',
    },

    // Banner formats (3:1 aspect ratio)
    banner_small: {
        key: 'banner_small',
        name: 'Banner Small',
        width: 150,
        height: 50,
        aspectRatio: 3,
        description: 'Small promotional banners',
    },
    banner_medium: {
        key: 'banner_medium',
        name: 'Banner Medium',
        width: 300,
        height: 100,
        aspectRatio: 3,
        description: 'Medium promotional banners',
    },
    banner_large: {
        key: 'banner_large',
        name: 'Banner Large',
        width: 600,
        height: 200,
        aspectRatio: 3,
        description: 'Large promotional banners',
    },

    // Wide formats (16:9 aspect ratio)
    hero_small: {
        key: 'hero_small',
        name: 'Hero Small',
        width: 320,
        height: 180,
        aspectRatio: 16 / 9,
        description: 'Small hero images',
    },
    hero_medium: {
        key: 'hero_medium',
        name: 'Hero Medium',
        width: 640,
        height: 360,
        aspectRatio: 16 / 9,
        description: 'Medium hero images',
    },
    hero_large: {
        key: 'hero_large',
        name: 'Hero Large',
        width: 1280,
        height: 720,
        aspectRatio: 16 / 9,
        description: 'Large hero images',
    },

    // Mobile ad formats
    ad_mobile_banner: {
        key: 'ad_mobile_banner',
        name: 'Mobile Banner Ad',
        width: 320,
        height: 50,
        aspectRatio: 6.4,
        description: 'Mobile banner advertisements',
    },
    ad_mobile_fullscreen: {
        key: 'ad_mobile_fullscreen',
        name: 'Mobile Fullscreen Ad',
        width: 320,
        height: 480,
        aspectRatio: 2 / 3,
        description: 'Mobile fullscreen advertisements',
    },

    // Portrait formats (2:3 aspect ratio)
    portrait_small: {
        key: 'portrait_small',
        name: 'Portrait Small',
        width: 200,
        height: 300,
        aspectRatio: 2 / 3,
        description: 'Small portrait images',
    },
    portrait_medium: {
        key: 'portrait_medium',
        name: 'Portrait Medium',
        width: 400,
        height: 600,
        aspectRatio: 2 / 3,
        description: 'Medium portrait images',
    },

    // OrderNow specific formats
    ordernow_logo: {
        key: 'ordernow_logo',
        name: 'OrderNow Logo',
        width: 150,
        height: 40,
        aspectRatio: 3.75,
        description: 'OrderNow integration logo format',
    },
} as const;

/**
 * Get all default sizes that should always be generated
 */
export const getDefaultImageSizes = (): StandardImageSize[] => {
    return Object.values(STANDARD_IMAGE_SIZES).filter(size => size.isDefault);
};

/**
 * Get sizes by aspect ratio for grouping
 */
export const getSizesByAspectRatio = (): Record<string, StandardImageSize[]> => {
    const grouped: Record<string, StandardImageSize[]> = {};

    Object.values(STANDARD_IMAGE_SIZES).forEach(size => {
        const ratio = size.aspectRatio.toString();
        if (!grouped[ratio]) {
            grouped[ratio] = [];
        }
        grouped[ratio].push(size);
    });

    return grouped;
};

/**
 * Get sizes by keys
 */
export const getSizesByKeys = (keys: string[]): StandardImageSize[] => {
    return keys
        .map(key => STANDARD_IMAGE_SIZES[key])
        .filter(Boolean);
};

/**
 * Convert standard sizes to TargetSize format for image editor
 */
export const standardSizesToTargetSizes = (sizeKeys: string[]) => {
    return sizeKeys
        .map(key => STANDARD_IMAGE_SIZES[key])
        .filter(Boolean)
        .map(size => ({
            width: size.width,
            height: size.height,
            name: size.key, // Use key as name for consistency
        }));
};

/**
 * Get all available size keys for selection
 */
export const getAvailableSizeKeys = (): string[] => {
    return Object.keys(STANDARD_IMAGE_SIZES);
};

/**
 * Predefined size combinations for common use cases
 */
export const PREDEFINED_SIZE_COMBINATIONS = {
    itemsImages: ['square_s', 'square_l', 'hero_small', 'banner_small'],
    user_profiles: ['profile'],
    hero_images: ['hero_small', 'hero_medium', 'hero_large'],
    promotional_banners: ['banner_small', 'banner_medium', 'banner_large'],
    product_gallery: ['square_s', 'square_l', 'portrait_small', 'portrait_medium'],
    mobile_ads: ['ad_mobile_banner', 'ad_mobile_fullscreen'],
} as const;

export type SizeCombinationKey = keyof typeof PREDEFINED_SIZE_COMBINATIONS;
