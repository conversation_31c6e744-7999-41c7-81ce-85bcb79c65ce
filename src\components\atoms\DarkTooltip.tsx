import styled from '@emotion/styled';
import {
  Fade,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
} from '@mui/material';

interface DarkTooltip extends Omit<TooltipProps, 'title'> {
  text?: string | number;
  label?: string | number;
  title?: React.ReactNode;
  bigFont?: boolean;
}

export const DarkTooltip = styled(
  ({ className, text, label, title, bigFont, ...props }: DarkTooltip) => (
    <Tooltip
      placement="top"
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 0 }}
      slotProps={{
        popper: {
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, -7],
              },
            },
          ],
        },
      }}
      classes={{ popper: className }}
      title={
        title ?? (
          <>
            {text && (
              <Typography
                sx={{ fontSize: '18px' }}
                variant={bigFont ? 'h5' : 'body2'}
              >
                {text}
              </Typography>
            )}
            {label && (
              <Typography
                sx={{ fontSize: '16px' }}
                variant={bigFont ? 'body2' : 'caption'}
                color="custom.gray600"
              >
                {label}
              </Typography>
            )}
          </>
        )
      }
      {...props}
    />
  )
)(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: (theme as any).palette.common.black,
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: (theme as any).palette.common.black,
    width: '180px',
    padding: '12px',
  },
}));
