import { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { TextInput, useRecordContext } from 'react-admin';
import { useFormContext } from 'react-hook-form';

type Unit = {
  id: number;
  title: string;
  value: number;
};

const units = [
  {
    id: 0,
    title: '50',
    value: 50,
  },
  {
    id: 1,
    title: '100',
    value: 100,
  },
  {
    id: 2,
    title: '200',
    value: 200,
  },
  {
    id: 3,
    title: 'Custom',
    value: 0,
  },
];

export default function UnitsNeeded() {
  const source = 'units-needed';
  const record = useRecordContext();
  const { setValue } = useFormContext();

  const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null);
  const [customValue, setCustomValue] = useState<string>('');

  useEffect(() => {
    if (record && record[source]) {
      const foundUnit = units.find(
        unit => unit.value === record[source] || unit.title === 'Custom'
      );
      setSelectedUnit(foundUnit || null);
      if (foundUnit?.title === 'Custom') {
        setCustomValue(record[source]);
      }
    }
  }, [record]);

  useEffect(() => {
    if (selectedUnit?.title === 'Custom') {
      setValue(source, customValue, {
        shouldDirty: true,
        shouldTouch: true,
      });
    } else {
      setValue(source, selectedUnit?.value || '', {
        shouldDirty: true,
        shouldTouch: true,
      });
    }
  }, [selectedUnit, customValue]);

  const toggleOption = (unit: Unit) => {
    setSelectedUnit((prev: Unit | null) =>
      prev?.id === unit.id ? null : unit
    );
  };

  const handleCustomValueChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCustomValue(event.target.value);
    setValue(source, event.target.value, {
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          width: '100%',
          mb: 1,
        }}
      >
        {units.map(unit => (
          <Box
            key={unit.id}
            onClick={() => toggleOption(unit)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              border: '1px solid',
              color: selectedUnit?.id === unit.id ? 'white' : '',
              backgroundColor:
                selectedUnit?.id === unit.id ? 'primary.main' : '',
              borderColor:
                selectedUnit?.id === unit.id
                  ? 'primary.main'
                  : 'custom.gray400',
              borderRadius: '6px',
              px: 2.5,
              py: 2,
              cursor: 'pointer',
              ':hover': {
                bgcolor:
                  selectedUnit?.id === unit.id
                    ? 'primary.main'
                    : 'primary.veryLight',
              },
            }}
          >
            <Typography variant="h6">{unit.title}</Typography>
          </Box>
        ))}
      </Box>

      {selectedUnit?.title === 'Custom' && (
        <TextInput
          sx={{ mb: 1 }}
          source={source}
          label="Enter custom value"
          size="medium"
          value={customValue}
          onChange={handleCustomValueChange}
        />
      )}
    </Box>
  );
}
