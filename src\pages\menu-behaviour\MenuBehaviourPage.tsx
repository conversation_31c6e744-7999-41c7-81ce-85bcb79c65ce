import { useEffect, useMemo, useState } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  TextField,
} from '@mui/material';
import { useUpdate } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { RESOURCES, useGetListLocationsLive } from '~/providers/resources';
import ControlledRadioInputGroup from '../../components/molecules/controlled-input-groups/ControlledRadioInputGroup';
import PageTitle from '../../components/molecules/PageTitle';

export default function MenuBehaviourPage() {
  const [returnToHome, setReturnToHome] = useState<'return' | 'stayInPlace'>();
  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  const { t } = useTranslation();
  const { data: sellPoints } = useGetListLocationsLive();
  const { sellPointId, setSellPointId } = useGlobalResourceFilters();

  const [update] = useUpdate();

  const valueFromSellpoint = useMemo(() => {
    if (sellPoints && sellPointId) {
      const returnHome = sellPoints.find(
        ({ id }) => id === sellPointId
      ).menuBehaviourReturnToHome;
      return returnHome ? 'return' : 'stayInPlace';
    }
  }, [sellPoints, sellPointId]);

  useEffect(() => {
    if (sellPoints) {
      setSellPointId(sellPoints[0].id);
    }
  }, [sellPoints]);

  useEffect(() => {
    setReturnToHome(valueFromSellpoint);
    setIsLoading(false);
  }, [sellPointId, sellPoints]);

  const handleChange = (_: any, newValue: any) => {
    setSellPointId(newValue ? newValue.id : null);
  };

  const resetForm = () => {
    setIsDirty(false);
    setReturnToHome(valueFromSellpoint);
  };

  const saveForm = () => {
    update(RESOURCES.LOCATIONS, {
      id: sellPointId,
      data: {
        menuBehaviourReturnToHome: returnToHome,
      },
    });
    setIsDirty(false);
  };

  return (
    <Box p={2}>
      <PageTitle
        title={t('devices.menuBehaviour.title')}
        description={t('devices.menuBehaviour.description')}
      />
      <Autocomplete
        sx={{ maxWidth: 300 }}
        options={sellPoints ?? []}
        getOptionLabel={option => option.name}
        value={sellPoints?.find(({ id }) => id === sellPointId) ?? null}
        onChange={handleChange}
        renderInput={params => (
          <TextField {...params} label={t('shared.location')} />
        )}
        disableClearable
      />
      <Box sx={{ height: 20 }} />
      {isLoading ? (
        <CircularProgress sx={{ mx: 'auto', mt: 3 }} />
      ) : (
        <ControlledRadioInputGroup
          title={t('devices.menuBehaviour.stepType')}
          value={returnToHome}
          setValue={val => {
            setReturnToHome(val);
            setIsDirty(true);
          }}
          choices={[
            {
              id: 'return',
              name: t('devices.menuBehaviour.returnToHomeScreen'),
              description: t(
                'devices.menuBehaviour.returnToHomeScreenDescription'
              ),
            },
            {
              id: 'stayInPlace',
              name: t('devices.menuBehaviour.stayInPlace'),
              description: t('devices.menuBehaviour.stayInPlaceDescription'),
            },
          ]}
        />
      )}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          py: 2,
          px: 4,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 2,
          width: '100vw',
          bgcolor: 'background.paper',
          borderTop: 'solid 1px',
          borderColor: 'custom.gray400',
          zIndex: 1000,
        }}
      >
        <Button onClick={resetForm} variant="contained">
          {t('shared.cancel')}
        </Button>
        <Button onClick={saveForm} variant="contained" disabled={!isDirty}>
          {t('shared.save')}
        </Button>
      </Box>
    </Box>
  );
}
