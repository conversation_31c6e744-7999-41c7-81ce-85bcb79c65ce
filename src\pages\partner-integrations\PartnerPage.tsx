import { useMemo, useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import {
  Box,
  Button,
  InputAdornment,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useGetList, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import PageTitle from '../../components/molecules/PageTitle';
import PartnerCard from './partnerCard';
import { partners } from './partners';
import PartnerShow from './PartnerShow';

export default function PartnerPage() {
  const { t } = useTranslation('');
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const [partnerModal, setPartnerModal] = useState<boolean>(false);
  const [partnerExtraData, setPartnerExtraData] = useState<any>({});
  const [searchTerm, setSearchTerm] = useState<string>('');
  const redirect = useRedirect();

  const tabs = useMemo(
    () => [
      {
        name: t('partnerIntegrations.accounting'),
        value: 'accounting',
      },
      {
        name: t('partnerIntegrations.onlineOrdering'),
        value: 'onlineOrdering',
      },
      {
        name: t('partnerIntegrations.hotelPMS'),
        value: 'hotelPMS',
      },
    ],
    [t]
  );

  const filteredPartners = useMemo(() => {
    if (!searchTerm.trim()) {
      return partners;
    }
    
    return partners.filter(partner => 
      partner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      partner.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  return (
    <Box p={2}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        flexDirection={isXSmall ? 'column' : 'row'}
      >
        <PageTitle
          sx={{
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
          title={t('partnerIntegrations.title')}
          description={<>{t('partnerIntegrations.description')}</>}
          hideBorder
          doNotPrint
        />
      </Box>

      <Box
        display="flex"
        flexDirection={isXSmall ? 'column' : 'row'}
        marginBottom={2}
        width={isXSmall ? '100%' : 'auto'}
        alignItems="center"
        justifyContent="space-between"
      >
        <TextField
          placeholder={t('shared.search')}
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ width: isXSmall ? '100%' : '250px' }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        <Button
          variant="contained"
          color="primary"
          sx={{ width: isXSmall ? '100%' : 'auto' }}
          onClick={() => {
            redirect('list', RESOURCES.MY_INTEGRATIONS);
          }}
        >
          {t('partnerIntegrations.viewMyIntegrations')}
        </Button>
      </Box>

      <Box
        display="flex"
        gap={4}
        marginBottom={2}
        sx={{
          overflowX: { xs: 'scroll', md: 'auto' },
          flexDirection: 'column',
        }}
      >
        {tabs.map(t => {
          return (
            <Box key={t.value} sx={{ marginTop: { xs: '20px', md: '0px' } }}>
              {filteredPartners.filter(partner => partner.tab === t.value).length > 0 && 
              <Typography
                key={t.value}
                variant="body1"
                sx={{
                  fontSize: { xs: '20px', md: '22px' },
                  mb: '20px',
                }}
              >
                {t.name}
              </Typography>
              }

              <Box
                display="flex"
                sx={{
                  flexDirection: { xs: 'column', md: 'row' },
                  paddingLeft: { xs: '0px', md: '20px' },
                }}
                gap={4}
              >
                {filteredPartners
                  .filter(partner => partner.tab === t.value)
                  .map(partner => (
                    <PartnerCard
                      key={partner.name}
                      partner={partner}
                      setPartnerModal={setPartnerModal}
                      setPartnerExtraData={setPartnerExtraData}
                    />
                  ))}
              </Box>
            </Box>
          );
        })}
      </Box>

      <PartnerShow
        onClose={() => setPartnerModal(false)}
        open={partnerModal}
        partnerExtraData={partnerExtraData}
      />
    </Box>
  );
}
