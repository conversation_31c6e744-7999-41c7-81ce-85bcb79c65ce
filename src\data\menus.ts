export const menusData = {
  catalogs: [
    {
      sellPoints: ['W1HXNP7cHSCDfhskGxUd', 'W1HXNP7cHSCDfhskGxUd2'],
      name: 'MENU 1',
      id: 1,
      pages: [
        [
          {
            color: '#00B8AC',
            displayName: 'Pizza',
            active: true,
            position: {
              endY: '2',
              endX: '1',
              startY: '0',
              startX: '0',
            },
            id: 'hjHCtKaWaVjgeFyywFRr',
            type: 'displayGroup',
            items: [
              {
                displayName: 'NESTED PIZZA',
                price: '350000',
                measureUnit: 'BUC',
                id: 'epHHbruw7R7mCs0W2rZH',
                group: 'OetLvqQtm7PGYvbZd008',
                color: '#00B8AC',
                type: 'displayGroup',
                items: [
                  {
                    displayName: 'PIZZA BOCCONCINI 400GR',
                    price: '260000',
                    measureUnit: 'BUC',
                    id: 'PSszOAnrswkQ4m7Een7W',
                    group: 'OetLvqQtm7PGYvbZd008',
                  },
                  {
                    displayName: 'PIZZA BUFALA 450G',
                    price: '330000',
                    measureUnit: 'BUC',
                    id: 'TEYTV9tSGoFRzyHQ16hb',
                    group: 'OetLvqQtm7PGYvbZd008',
                  },
                ],
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BOCCONCINI 400GR',
                price: '260000',
                measureUnit: 'BUC',
                id: 'PSszOAnrswkQ4m7Een7W',
                group: 'OetLvqQtm7PGYvbZd008',
              },
              {
                displayName: 'PIZZA BUFALA 450G',
                price: '330000',
                measureUnit: 'BUC',
                id: 'TEYTV9tSGoFRzyHQ16hb',
                group: 'OetLvqQtm7PGYvbZd008',
              },
            ],
          },
          {
            color: '#0046fd',
            displayName: 'Pasta',
            active: true,
            position: {
              endY: '2',
              endX: '2',
              startY: '0',
              startX: '1',
            },
            id: 'iqdHSVfYGyu2ISUFJFtZ',
            type: 'displayGroup',
            items: [
              {
                displayName: 'LASAGNA PARMEGIANA AL FORNO – 350G',
                price: '350000',
                measureUnit: 'BUC',
                id: '2U2AI9BLpCXTKcfyeH6y',
                group: 'ZlwptBsuqMQGjsXh3eSL',
              },
              {
                displayName: 'MACARONI SALSICCIA E BROCCOLI – 350G',
                price: '350000',
                measureUnit: 'BUC',
                id: 'gqBMgi7LSHAGjxQUtZlX',
                group: 'ZlwptBsuqMQGjsXh3eSL',
              },
            ],
          },
          {
            price: '370000',
            color: '#F75120',
            displayName:
              'LASAGNA BOLOGNESE AL FORNO LASAGNA PARMEGIANA  – 350G',
            active: true,
            measureUnit: 'BUC',
            id: 'AEzOg5HNsjKmYdHoFgm5',
            position: {
              endY: '1',
              endX: '3',
              startY: '0',
              startX: '2',
            },
            type: 'product',
            group: 'ZlwptBsuqMQGjsXh3eSL',
          },
          {
            displayName: 'Courses',
            active: true,
            position: {
              endY: '8',
              endX: '4',
              startY: '7',
              startX: '3',
            },
            id: 'coursesOnOff',
            type: 'function',
          },
        ],
        [
          {
            color: '#42f590',
            displayName: 'Combos',
            active: true,
            position: {
              endY: '2',
              endX: '1',
              startY: '0',
              startX: '0',
            },
            id: 'P1BgS3aEOnxxLCuU4jdp',
            type: 'displayGroup',
            items: [
              {
                price: '350000',
                displayName: 'PIZZA COMBO',
                measureUnit: 'BUC',
                id: '5K2X1eRURqyI2HqLEPnP',
                type: 'productCombo',
                steps: [
                  {
                    selection: {
                      min: '1',
                      max: '1',
                    },
                    price: '200000',
                    name: 'Choose pizza',
                    type: 'required',
                    items: [
                      {
                        id: 'hjHCtKaWaVjgeFyywFRr',
                        type: 'displayGroup',
                      },
                    ],
                  },
                  {
                    selection: {
                      min: '1',
                      max: '1',
                    },
                    price: '100000',
                    name: 'Choose desert',
                    type: 'required',
                    items: [
                      {
                        id: '36l6tSeDEBIeBN24mXeJ',
                        type: 'displayGroup',
                      },
                      {
                        id: 'IsPiszVJ3WS1icS0pEpE',
                        type: 'product',
                      },
                    ],
                  },
                ],
                group: 'uweslytLYgHOlz1LI6g5',
              },
            ],
          },
        ],
        [
          {
            color: '#42f590',
            displayName: 'Test',
            active: true,
            position: {
              endY: '2',
              endX: '1',
              startY: '1',
              startX: '0',
            },
            id: 'P1BgS3aEOnxxLCuU4jdp',
            type: 'displayGroup',
            items: [
              {
                price: '350000',
                displayName: 'PIZZA COMBO',
                measureUnit: 'BUC',
                id: '5K2X1eRURqyI2HqLEPnP',
                type: 'productCombo',
                steps: [
                  {
                    selection: {
                      min: '1',
                      max: '1',
                    },
                    price: '200000',
                    name: 'Choose pizza',
                    type: 'required',
                    items: [
                      {
                        id: 'hjHCtKaWaVjgeFyywFRr',
                        type: 'displayGroup',
                      },
                    ],
                  },
                  {
                    selection: {
                      min: '1',
                      max: '1',
                    },
                    price: '100000',
                    name: 'Choose desert',
                    type: 'required',
                    items: [
                      {
                        id: '36l6tSeDEBIeBN24mXeJ',
                        type: 'displayGroup',
                      },
                      {
                        id: 'IsPiszVJ3WS1icS0pEpE',
                        type: 'product',
                      },
                    ],
                  },
                ],
                group: 'uweslytLYgHOlz1LI6g5',
              },
            ],
          },
        ],
      ],
    },
    {
      sellPoints: ['W1HXNP7cHSCDfhskGxUd'],
      name: 'Empty menu',
      id: 2,
      pages: [[]],
    },
  ],
};
