import { ResourceCallbacks } from 'react-admin';

import { RESOURCES } from '../resources';

const getDefaultCatalogSchedule = () => [
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
  { endAt: '23:59', startAt: '00:00' },
];

export const updateSellPointsOnCatalogsChange: ResourceCallbacks = {
  resource: RESOURCES.HOSPITALITY_CATALOGS,
  afterCreate: async (result, dataProvider, resource) => {
    const catalogId = result.data.id;
    const catalogName = result.data.name || 'Unnamed Catalog';
    const sellPointIds = result.data.sellPointIds || [];

    if (sellPointIds.length > 0) {
      // Get all sell points that need to be updated
      const { data: sellPoints } = await dataProvider.getList(
        RESOURCES.LOCATIONS,
        {
          pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
          filter: {
            id_eq_any: sellPointIds,
            _d: false,
          },
        }
      );

      // Update each sell point to include the new catalog
      const updatePromises = sellPoints.map(sellPoint => {
        const updatedCatalogs = {
          ...sellPoint.catalogs,
          [catalogId]: {
            active: true,
            name: catalogName,
            schedule: getDefaultCatalogSchedule(),
          },
        };

        return dataProvider.update(RESOURCES.LOCATIONS, {
          id: sellPoint.id,
          data: { catalogs: updatedCatalogs },
          previousData: sellPoint,
        });
      });

      await Promise.allSettled(updatePromises);
    }

    return result;
  },
  afterUpdate: async (result, dataProvider, resource) => {
    const catalogId = result.data.id;
    const catalogName = result.data.name || 'Unnamed Catalog';
    const currentSellPointIds = result.data.sellPointIds || [];

    // Get all sell points to check which ones currently have this catalog
    const { data: allSellPoints } = await dataProvider.getList(
      RESOURCES.LOCATIONS,
      {
        pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
        filter: { _d: false },
      }
    );

    const updatePromises = allSellPoints.map(sellPoint => {
      const hasCatalog = sellPoint.catalogs && sellPoint.catalogs[catalogId];
      const shouldHaveCatalog = currentSellPointIds.includes(sellPoint.id);

      if (shouldHaveCatalog && !hasCatalog) {
        // Add catalog to sell point
        const updatedCatalogs = {
          ...sellPoint.catalogs,
          [catalogId]: {
            active: true,
            name: catalogName,
            schedule: getDefaultCatalogSchedule(),
          },
        };

        return dataProvider.update(RESOURCES.LOCATIONS, {
          id: sellPoint.id,
          data: { catalogs: updatedCatalogs },
          previousData: sellPoint,
        });
      } else if (!shouldHaveCatalog && hasCatalog) {
        // Remove catalog from sell point
        const updatedCatalogs = { ...sellPoint.catalogs };
        delete updatedCatalogs[catalogId];

        return dataProvider.update(RESOURCES.LOCATIONS, {
          id: sellPoint.id,
          data: { catalogs: updatedCatalogs },
          previousData: sellPoint,
        });
      } else if (
        shouldHaveCatalog &&
        hasCatalog &&
        sellPoint.catalogs[catalogId].name !== catalogName
      ) {
        // Update catalog name if it changed
        const updatedCatalogs = {
          ...sellPoint.catalogs,
          [catalogId]: {
            ...sellPoint.catalogs[catalogId],
            name: catalogName,
          },
        };

        return dataProvider.update(RESOURCES.LOCATIONS, {
          id: sellPoint.id,
          data: { catalogs: updatedCatalogs },
          previousData: sellPoint,
        });
      }

      return Promise.resolve();
    });

    await Promise.allSettled(updatePromises);

    return result;
  },
  afterUpdateMany: async (result, dataProvider, resource) => {
    // Skip for now
    return result;
  },
  afterDelete: async (result, dataProvider, resource) => {
    const catalogId = result.data.id;

    // Get all sell points to remove the catalog from them
    const { data: allSellPoints } = await dataProvider.getList(
      RESOURCES.LOCATIONS,
      {
        pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
        filter: { _d: false },
      }
    );

    const updatePromises = allSellPoints
      .filter(sellPoint => sellPoint.catalogs && sellPoint.catalogs[catalogId])
      .map(sellPoint => {
        const updatedCatalogs = { ...sellPoint.catalogs };
        delete updatedCatalogs[catalogId];

        return dataProvider.update(RESOURCES.LOCATIONS, {
          id: sellPoint.id,
          data: { catalogs: updatedCatalogs },
          previousData: sellPoint,
        });
      });

    await Promise.allSettled(updatePromises);
    return result;
  },
  afterDeleteMany: async (result, dataProvider, resource) => {
    const deletedCatalogIds = result.data || [];

    if (deletedCatalogIds.length > 0) {
      // Get all sell points to remove the catalogs from them
      const { data: allSellPoints } = await dataProvider.getList(
        RESOURCES.LOCATIONS,
        {
          pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
          filter: { _d: false },
        }
      );

      const updatePromises = allSellPoints
        .filter(sellPoint => {
          return (
            sellPoint.catalogs &&
            deletedCatalogIds.some(catalogId => sellPoint.catalogs[catalogId])
          );
        })
        .map(sellPoint => {
          const updatedCatalogs = { ...sellPoint.catalogs };
          deletedCatalogIds.forEach(catalogId => {
            delete updatedCatalogs[catalogId];
          });

          return dataProvider.update(RESOURCES.LOCATIONS, {
            id: sellPoint.id,
            data: { catalogs: updatedCatalogs },
            previousData: sellPoint,
          });
        });

      await Promise.allSettled(updatePromises);
    }

    return result;
  },
};
