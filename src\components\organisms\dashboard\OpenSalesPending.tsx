import { useEffect, useMemo, useState } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { off, onValue, ref } from 'firebase/database';
import { useTranslation } from 'react-i18next';

import OpenOrdersModal from '~/components/molecules/OpenOrdersModal';
import { useFirebase } from '~/contexts/FirebaseContext';
import { itCanBeCastToNumber } from '~/fake-provider/reports/utils/isNumber';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { useGetListFloorPlansLive } from '~/providers/resources';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';

interface OpenSalesData {
  total: number;
  stats: Array<{
    label: string;
    value: number;
  }>;
  details: Array<{
    billId: string;
    billName: string;
    customerName?: string;
    hasUnsentItems: boolean;
    openedAt: number;
    paid: boolean;
    paymentDue?: number;
    section: string;
    updatedAt?: number;
    memberId: string;
    value: number;
    note?: string;
  }>;
}

type RtdbOpenSalesData = {
  [section: string]: {
    [memberId: string]: {
      [billParentName: string]: {
        [billId: string]: {
          customer?: string;
          hasUnsentItems?: boolean;
          index?: number;
          openedAt: number;
          paid?: boolean;
          paymentDue?: number;
          updatedAt?: number;
          value: number;
          note?: string;
        };
      };
    };
  };
};

const initialData: OpenSalesData = {
  total: 0,
  stats: [
    {
      label: 'dashboard.openBills', // Keep keys for translation
      value: 0,
    },
    {
      label: 'dashboard.customers',
      value: 0,
    },
    {
      label: 'menu.itemSales',
      value: 0,
    },
  ],
  details: [],
};

const processRtdbOpenSalesData = (data: RtdbOpenSalesData): OpenSalesData => {
  const openSalesData: OpenSalesData = structuredClone(initialData);

  Object.entries(data).forEach(([section, sectionData]) => {
    Object.entries(sectionData).forEach(([memberId, memberData]) => {
      Object.entries(memberData).forEach(([billParentName, billParentData]) => {
        Object.entries(billParentData).forEach(([billId, billData]) => {
          // Increment openBills count
          openSalesData.stats[0].value += 1;
          // Increment customers count
          if (billData.customer) {
            openSalesData.stats[1].value += 1;
          }
          // Increment total value
          if (billData.value) {
            openSalesData.total += billData.value;
          }
          // Add bill to details
          const billParentNameWithoutAt = billParentName.startsWith('@')
            ? billParentName.substring(1)
            : billParentName;
          const billMultiOrderIndex = billData.index
            ? billData.index > 1
              ? `-${billData.index}`
              : ''
            : '';
          openSalesData.details.push({
            billId,
            billName: `${billParentNameWithoutAt}${billMultiOrderIndex}`,
            customerName: billData.customer || undefined,
            hasUnsentItems: billData.hasUnsentItems || false,
            openedAt: billData.openedAt,
            paid: billData.paid || false,
            paymentDue:
              billData.paymentDue !== undefined
                ? billData.paymentDue
                : billData.value || 0,
            section: section,
            updatedAt: billData.updatedAt,
            memberId: memberId,
            value: billData.value || 0,
            note: billData.note || undefined,
          });
        });
      });
    });
  });

  return openSalesData;
};

export default function OpenSalesPending() {
  const { t } = useTranslation();
  const { details: fbDetails } = useFirebase();
  const { sellPointId } = useGlobalResourceFilters();
  const [openSalesData, setOpenSalesData] =
    useState<OpenSalesData>(initialData);

  const { data: floorPlans } = useGetListFloorPlansLive({
    meta: {
      sellPointId,
    },
  });

  useEffect(() => {
    if (
      fbDetails &&
      fbDetails.rtdb &&
      fbDetails.selectedAccount &&
      sellPointId
    ) {
      // Construct your Firebase path here. Example:
      const dataPath = `accounts/${fbDetails.selectedAccount}/sellPoints/${sellPointId}/bills`;
      const dataRef = ref(fbDetails.rtdb, dataPath);
      console.log('creating rtdb listener for path:', dataPath);
      const listener = onValue(dataRef, snapshot => {
        console.log('RTDB data changed for path:', dataPath);
        const data = snapshot.val();
        if (data) {
          const processedData = processRtdbOpenSalesData(data);
          console.log('RTDB Processed data:', processedData);
          setOpenSalesData(processedData);
        } else {
          setOpenSalesData(initialData);
        }
      });

      // Cleanup listener on unmount
      return () => {
        console.log('Cleaning up rtdb listener');
        listener();
      };
    }
  }, [fbDetails, sellPointId]);

  const [openOrdersModal, setOpenOrdersModal] = useState(false);

  const handleOpenOrdersModal = () => {
    if (!openOrdersModal) {
      setOpenOrdersModal(true);
    }
  };

  const handleCloseOrdersModal = () => {
    setOpenOrdersModal(false);
  };

  const sectionsOrders = useMemo(() => {
    const floorPlansWithOrders =
      floorPlans?.map(section => {
        return {
          id: section.id,
          name: section.name,
          orders: openSalesData.details
            .filter(el => {
              if (itCanBeCastToNumber(el.section)) {
                return parseInt(el.section) === parseInt(section._index);
              }
            })
            .sort((a, b) => b.openedAt - a.openedAt),
        };
      }) || [];
    const customSection = openSalesData.details
      .filter(el => {
        if (el.section === 'custom') {
          return el;
        }
      })
      .sort((a, b) => b.openedAt - a.openedAt);

    if (customSection.length > 0) {
      floorPlansWithOrders?.push({
        id: 'custom',
        name: 'Custom',
        orders: customSection,
      });
    }

    return floorPlansWithOrders;
  }, [floorPlans, openSalesData.details]);

  console.log('sectionsOrders', sectionsOrders);

  return (
    <Grid
      onClick={handleOpenOrdersModal}
      container
      mt={1}
      sx={{
        borderRadius: '6px',
        border: `1px solid`,
        boxShadow: '2px 4px 4px 0px rgba(0, 0, 0, 0.1)',
        borderColor: 'custom.gray200',
        p: 2,
        alignItems: 'center',
        cursor: 'pointer',
      }}
    >
      <Grid item xs={12} sm={4.5} sx={{ pl: 1 }}>
        <Typography variant="subtitle1">{t('dashboard.openSales')}</Typography>
        <Typography variant="h2" component="p">
          {formatAndDivideNumber(openSalesData.total)}
        </Typography>
      </Grid>
      {openSalesData.stats.map(el => (
        <Grid item key={el.label} xs={12} sm={2.5}>
          {' '}
          {/* Changed key to el.label for stability if values can be non-unique */}
          <Box
            sx={{
              borderRadius: '6px',
              p: 2,
              bgcolor: 'background.tinted',
              cursor: 'pointer',
              m: 1,
              boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.2)',
              transition: 'box-shadow 0.2s ease',
              '&:hover': {
                boxShadow: 'none',
              },
            }}
          >
            <Typography variant="subtitle2">{t(el.label)}</Typography>{' '}
            {/* Translate the label here */}
            <Typography>{el.value}</Typography>
          </Box>
        </Grid>
      ))}

      <OpenOrdersModal
        isOpen={openOrdersModal}
        handleCloseOrdersModal={handleCloseOrdersModal}
        sectionsOrders={sectionsOrders}
      />
    </Grid>
  );
}
