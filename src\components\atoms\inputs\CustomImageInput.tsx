import { useEffect, useState } from 'react';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Box, Button, SxProps, Theme, Typography } from '@mui/material';
import { useRecordContext } from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
const WIDTH = 200;
const HEIGHT = 160;
export default function CustomImageInput({
  source,
  sx,
}: {
  source: string;
  sx?: SxProps<Theme>;
}) {
  const { t } = useTranslation();
  const [image, setImage] = useState<string>();

  const record = useRecordContext();
  const { setValue } = useFormContext();

  useEffect(() => {
    if (record) {
      setImage(record[source]);
    }
  }, [record]);

  useEffect(() => {
    setValue(source, image, { shouldDirty: true, shouldTouch: true });
  }, [image]);

  const handleFileChange = (event: any) => {
    const files = event.target.files;
    if (files && files[0]) {
      setImage(URL.createObjectURL(files[0]));
    }
  };

  const handleDrop = (event: any) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files && files[0]) {
      setImage(URL.createObjectURL(files[0]));
    }
  };

  const handleRemoveFile = () => {
    setImage('');
  };

  return (
    <Box
      onDrop={handleDrop}
      onDragOver={event => event.preventDefault()}
      sx={{
        width: `${WIDTH}px`,
        height: `${HEIGHT}px`,
        borderRadius: '6px',
        borderColor: 'custom.gray400',
        borderStyle: 'dashed',
        position: 'relative',
      }}
    >
      {!!image ? (
        <Box
          sx={{
            ...centerStyle,
            width: '100%',
            height: '100%',
            ':hover': {
              '> button': {
                opacity: '1 !important',
              },
            },
          }}
        >
          <img
            alt="preview image"
            src={image}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
            }}
          />
          <Button
            // @ts-ignore
            variant="contained-light"
            color="error"
            onClick={handleRemoveFile}
            sx={{ position: 'absolute', top: '6px', right: '6px', opacity: 0 }}
          >
            <DeleteOutlineIcon fontSize="small" />
          </Button>
        </Box>
      ) : (
        <>
          <input
            id="browse"
            type="file"
            hidden
            onChange={handleFileChange}
            accept="image/*"
          />
          <label
            htmlFor="browse"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: `${WIDTH}px`,
              height: `${HEIGHT}px`,
              cursor: 'pointer',
              flexDirection: 'column',
              ...centerStyle,
            }}
          >
            <img
              src="/assets/image.svg"
              alt="image-icon"
              style={{ marginBottom: '30px' }}
            />
            {/* @ts-ignore */}
            <Typography variant="label" fontWeight={300}>
              {t('sellPointsPage.dragAndDropLogo')}
            </Typography>
            {/* @ts-ignore */}
            <Typography variant="label" color="primary">
              {t('sellPointsPage.browseFiles')}
            </Typography>
          </label>
        </>
      )}
    </Box>
  );
}

const centerStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};
