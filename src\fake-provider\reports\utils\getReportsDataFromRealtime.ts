// This function is used to get the data from the realtime database
// it will receive the database and the path that we need to get

import { Database, get, ref } from 'firebase/database';

import { GetReportsDataFromRealtime } from '../types';

// and will return an object with the contents of the path
export const getReportsDataFromRealtime: GetReportsDataFromRealtime = async (
  database,
  path
) => {
  // first we need to verify the path
  if (!path || typeof path !== 'string') {
    throw new Error('Invalid path');
  }
  // first we need to validate and cast the database to Database
  if (!database) {
    throw new Error('Invalid database');
  }
  console.info(`getDataFromRealtime: ${path}`);
  // cast the database to Database
  const db = database as Database;
  // create a reference to the path
  const dbRef = ref(db, path);
  // get the data
  const data = await get(dbRef);
  // check if the data exists
  if (!data.exists()) {
    return {};
  }
  // return the data
  return data.val();
};
