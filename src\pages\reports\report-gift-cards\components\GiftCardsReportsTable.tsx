import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { groupReport } from '~/fake-provider/reports/groupReport';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';

type TableRow = {
  vat: string;
  promotionsValue: number;
  price: number;
  quantity: number;
  couponsValue: number;
  discountsValue: number;
  netValue: number;
  type: string;
  value: number;
};

export default function GiftCardsReportsTable({
  tableData,
  onChangeGrouping,
  groupingItems,
  fields,
  setFields,
}: {
  tableData: TableRow[] | undefined;
  groupingItems: string[];
  fields: FieldOption[];
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  onChangeGrouping?: (items: any) => void;
}) {
  const { t } = useTranslation();
  const GiftCardsReportsData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let totalItemsData = mergeAndSumObjects(tableData);
      totalItemsData.type = 'Total';
      totalItemsData.vat = '';
      totalItemsData.subItems = [];

      const sortedData = [...tableData].sort((a, b) => b.value - a.value);
      return [...sortedData, totalItemsData];
    }
    return [];
  }, [tableData]);

  const GiftCardsReportsConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'type',
      label: t('giftCards.type'),
      textAlign: 'start',
      render: (row: TableRow) => {
        const isVat = row?.type?.toLowerCase().includes('vat');

        if (groupingItems && groupingItems.length > 0 && isVat) {
          return <>{row.type}</>;
        }

        return <>{capitalize(row.type.replace('@', ''))}</>;
      },
    },
    {
      id: 'vat',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
      label: t('shared.tva'),
      textAlign: 'end',
    },

    {
      id: 'price',
      label: t('shared.price'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.price)}</>;
      },
    },
    {
      id: 'quantity',
      label: t('giftCards.quantity'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row?.quantity, true)}</>;
      },
    },
    {
      id: 'value',
      label: t('itemSales.grossSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },
    {
      id: 'discountsValue',
      label: t('itemSales.discounts'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.discountsValue
              ? '- ' + formatAndDivideNumber(row?.discountsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'couponsValue',
      label: t('itemSales.coupons'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.couponsValue
              ? '- ' + formatAndDivideNumber(row?.couponsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'promotionsValue',
      label: t('categorySales.promotions'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              // @ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {row?.promotionsValue
              ? '- ' + formatAndDivideNumber(row?.promotionsValue)
              : ''}
          </div>
        );
      },
    },
    {
      id: 'netValue',
      label: t('shared.netSales'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.netValue)}</>;
      },
    },
  ];

  const columnsToFilter = useMemo(() => {
    const columns = [
      'vat',
      'price',
      'value',
      'discountName',
      'promotionName',
      'discountsValue',
      'couponsValue',
      'promotionsValue',
      'quantity',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [{ value: 'vat', label: 'VAT' }];

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          config={GiftCardsReportsConfig}
          data={GiftCardsReportsData}
          fields={fields}
          separateFirstColumn={true}
          groupingItems={groupingItems}
          groupingOptions={groupingOptions}
          setFields={setFields}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          fixedFirstColumn={true}
        />
      </Box>
    </>
  );
}
