import { PropsWithChildren } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Typography } from '@mui/material';

interface ModalHeaderProps extends PropsWithChildren {
  title: any;
  alignCenter?: boolean;
  handleClose: () => void;
  noBorder?: boolean;
  titleSize?: number;
}

export default function ModalHeader({
  handleClose,
  title,
  children,
  alignCenter,
  noBorder = false,
  titleSize,
}: ModalHeaderProps) {
  return (
    <Box
      sx={{
        position: 'sticky',
        top: 0,
        width: '100%',
        zIndex: 100,
        m: 0,
        p: 2,
        display: 'flex',
        alignItems: 'center',
        borderBottom: noBorder ? 'none' : '1px solid rgba(0,0,0,.05)',
        bgcolor: 'custom.modalHeader',
      }}
    >
      <Button
        // @ts-ignore
        variant="close-btn"
        aria-label="close"
        onClick={handleClose}
        sx={{ '& span': { mr: 0 } }}
      >
        <CloseIcon fontSize="small" />
      </Button>
      <Typography
        sx={{
          fontSize: titleSize || 19,
          ...(alignCenter
            ? {
                position: 'absolute',
                left: '50%',
                transform: 'translateX(-50%)',
              }
            : {
                flex: '1',
                ml: 2,
                mr: 6,
              }),
        }}
        variant="h2"
      >
        {title}
      </Typography>
      {alignCenter && <Box sx={{ flex: '1' }} />}
      <Box>{children}</Box>
    </Box>
  );
}
