import { FieldValue, Timestamp } from 'firebase/firestore';
import { RaRecord } from 'react-admin';

// METADATA
export type ResourceMetadataCreatedAt = {
  _c: number | FieldValue | Timestamp;
};
export type ResourceMetadataUpdatedAt = {
  _u: number | FieldValue | Timestamp;
};
export type ResourceMetadataCommon = ResourceMetadataCreatedAt &
  ResourceMetadataUpdatedAt;
export type RecordWithMetadataCommon = RaRecord<string> &
  ResourceMetadataCommon;
export type ResourceMetadataAccount = {
  _a: string;
};
export type BusinessTypeMetadataHospitality = 'h';
export type BusinessTypeMetadataRetail = 'r';
export type ResourceMetadataBusinessType = {
  _b?: BusinessTypeMetadataHospitality | BusinessTypeMetadataRetail;
};
export type ResourceMetadataBusinessTypeHospitality = {
  _b: BusinessTypeMetadataHospitality;
};
export type ResourceMetadataBusinessTypeRetail = {
  _b: BusinessTypeMetadataRetail;
};
export type ResourceMetadataDeleted = {
  _d: boolean;
};
export type ResourceMetadataEventId = {
  _e: number;
};
export type ResourceMetadataUpdatedFromResource = {
  _ur: string;
};
export type ResourceCollectionMetadata = ResourceMetadataCommon &
  ResourceMetadataAccount &
  ResourceMetadataBusinessType &
  ResourceMetadataDeleted;
export type RecordWithCollectionMetadata = RaRecord<string> &
  ResourceMetadataCommon &
  ResourceMetadataAccount &
  ResourceMetadataBusinessType &
  Partial<ResourceMetadataDeleted> &
  Partial<ResourceMetadataEventId>;

// MISCS
export type TranslatableField = {
  [language: string]: string;
};

export type Address = {
  city: string;
  country: string;
  label?: string;
  line1: string;
  line2?: string;
  state?: string;
  zip?: string;
  latitute?: number;
  longitude?: number;
};

export type HourInterval = {
  startAt: string;
  endAt: string;
};

export type Position = {
  endX: number;
  endY: number;
  startX: number;
  startY: number;
};

// HOSPITALITY CATALOG
export type CatalogHospitalityItemCommon = {
  active: boolean;
  displayName: string;
  id: string;
  type: 'displayGroup' | 'product' | 'productCombo' | 'function';
};
export type CatalogHospitalityHomePageItemCommon = {
  position: Position;
};
export type CatalogHospitalityProductCommon = {
  kitchenName: string;
  measureUnit: string;
  price: number;
  vat: number;
  barcode?: string;
  description?: TranslatableField;
  publicName?: TranslatableField;
  shippingBox?: unknown;
};

export type CatalogHospitalityDisplayGroup = CatalogHospitalityItemCommon & {
  items: Array<
    | CatalogHospitalityDisplayGroup
    | CatalogHospitalityProduct
    | CatalogHospitalityCombo
  >;
  type: 'displayGroup';
  color?: string;
  description?: TranslatableField;
  publicName?: TranslatableField;
};
export type CatalogHospitalityHomePageDisplayGroup =
  CatalogHospitalityHomePageItemCommon & CatalogHospitalityDisplayGroup;

export type CatalogHospitalityFunction = CatalogHospitalityItemCommon & {
  type: 'function';
};
export type CatalogHospitalityHomePageFunction =
  CatalogHospitalityHomePageItemCommon & CatalogHospitalityFunction;

export type CatalogHospitalityProduct = CatalogHospitalityItemCommon &
  CatalogHospitalityProductCommon & {
    type: 'product';
    forceModify: boolean;
    forceQuantity: boolean;
    group: string; //TODO! Deprecated but still in use :( same as groupId
    groupId: string;
    gtin: string;
    mpn: string;
    sku: string;
    age?: number;
    alcohol?: boolean;
    allergens?: Array<unknown>;
    calories?: number;
    dietaryPreferences?: Array<unknown>;
    itemPrep?: unknown;
    nutritionalValues?: unknown;
    straightFire?: boolean;
    weight?: number;
  };
export type CatalogHospitalityHomePageProduct =
  CatalogHospitalityHomePageItemCommon & CatalogHospitalityProduct;

export type CatalogHospitalityComboStep = {
  items: Array<{
    id: string;
    price?: number;
    type: 'product' | 'displayGroup';
  }>;
  name: string;
  selection: {
    min: number;
    max: number;
  };
  type: 'required' | 'optional';
  description?: TranslatableField;
  publicName?: TranslatableField;
};
export type CatalogHospitalityCombo = CatalogHospitalityItemCommon &
  CatalogHospitalityProductCommon & {
    type: 'productCombo';
    steps: Array<CatalogHospitalityComboStep>;
  };
export type CatalogHospitalityHomePageCombo =
  CatalogHospitalityHomePageItemCommon & CatalogHospitalityCombo;

export type CatalogHospitalityItem =
  | CatalogHospitalityProduct
  | CatalogHospitalityDisplayGroup
  | CatalogHospitalityFunction
  | CatalogHospitalityCombo;

export type CatalogHospitalityHomePageItem =
  | CatalogHospitalityHomePageProduct
  | CatalogHospitalityHomePageDisplayGroup
  | CatalogHospitalityHomePageFunction
  | CatalogHospitalityHomePageCombo;

export type CatalogHospitality = {
  id: string;
  name: string;
  pages: {
    [pageNo: string]: Array<CatalogHospitalityHomePageItem>;
  };
  sellPointIds: Array<string>;
  type: 'pos';
};

export type CatalogHospitalityWithMeta = CatalogHospitality &
  ResourceCollectionMetadata &
  ResourceMetadataBusinessTypeHospitality &
  ResourceMetadataUpdatedFromResource;

// DEVICES
export type Device = {
  deviceType?: 'pos' | 'kds';
  deviceUniqueId?: string;
  disabled: boolean;
  id: string;
  name: string;
  sellPointId?: string;
};

export type DeviceWithMeta = Device & ResourceMetadataCommon;

// FLOOR PLANS
export type FloorPlan = {
  active: boolean;
  id: string;
  items: unknown[];
  label: string;
  name: string;
  order: number;
  publicName?: TranslatableField;
  sellPointId: string;
};

export type FloorPlanWithMeta = FloorPlan & ResourceCollectionMetadata;

// HOSPITALITY GROUPS
export type GroupHospitality = {
  id: string;
  name: string;
  straightFire?: boolean; // TODO! Deprecated but still in use :(
};

export type GroupHospitalityWithMeta = GroupHospitality &
  ResourceCollectionMetadata;

// HOSPITALITY ITEMS
export type ItemLibraryHospitalityItem = {
  groupId: string;
  gtin: string;
  id: string;
  measureUnit: string;
  mpn: string;
  name: string;
  sku: string;
  type: 'product';
  age?: number;
  alcohol?: boolean;
  allergens?: Array<unknown>;
  calorieCount?: number;
  dietaryPreferences?: Array<unknown>;
  nutritionalValues?: unknown;
  shippingBox?: unknown;
  weight?: number;
  catalogSpecific?: {
    [catalogId: string]: {
      forceModify: boolean;
      forceQuantity: boolean;
      kitchenName: string;
      price: number;
      vat: number;
      barcode?: string;
      description?: TranslatableField;
      itemPrep?: unknown;
      publicName?: TranslatableField;
      straightFire?: boolean;
    };
  };
};

export type ItemLibraryHospitalityItemWithMeta = ItemLibraryHospitalityItem &
  ResourceCollectionMetadata &
  ResourceMetadataBusinessTypeHospitality &
  ResourceMetadataUpdatedFromResource;

// MEMBERS
export type Member = {
  displayName: string;
  firstName: string;
  email: string;
  id: string;
  lastName: string;
  pin: string;
  role: string; // TODO! Deprecated but still in use :( needs to be same as roleId
  roleId: string;
  sellPointIds: string[];
};

export type MemberWithMeta = Member & ResourceMetadataCommon;

// PREP STATIONS
export type PrepStation = {
  id: string;
  name: string;
  groups: string[]; // TODO! Deprecated but still in use :( needs to be same as groupIds
  groupIds: string[];
};

export type PrepStationWithMeta = PrepStation & ResourceMetadataCommon;

// ROLES
export type Role = {
  id: string;
  name: string;
  permissions: number[];
  access: number[]; // 0 pos, 1 manager, 2 kds, to be expanded...
};

export type RoleWithMeta = Role & ResourceMetadataCommon;

// PAYMENT TYPES
export type PaymentTypeCommon = {
  active: boolean;
  askForConfirmation: boolean;
  id: string;
  order: number;
};
export type PaymentType3rdParty = PaymentTypeCommon & {
  id: '3rdParty';
  values: string[];
};
export type PaymentTypeCard = PaymentTypeCommon & {
  id: 'card';
  implementation: string;
};
export type PaymentTypeCash = PaymentTypeCommon & {
  id: 'cash';
};
export type PaymentTypeCashless = PaymentTypeCommon & {
  id: 'cashless';
};
export type PaymentTypeGiftCard = PaymentTypeCommon & {
  id: 'giftCard';
};
export type PaymentTypeMealTicket = PaymentTypeCommon & {
  id: 'mealTicket';
};
export type PaymentTypeOnline = PaymentTypeCommon & {
  id: 'online';
  askForDetails: boolean;
  implementation: string;
};
export type PaymentTypeTapToPay = PaymentTypeCommon & {
  id: 'tapToPay';
  implementation: string;
};
export type PaymentTypeValueTicket = PaymentTypeCommon & {
  id: 'valueTicket';
};
export type PaymentTypeVoucher = PaymentTypeCommon & {
  id: 'voucher';
  askForNumber: boolean;
  askForPartner: boolean;
  partnerValues?: string[]; // TODO! Deprecated but still in use :( needs to be same as values
  values?: string[];
};
export type PaymentTypeWireTransfer = PaymentTypeCommon & {
  id: 'wireTransfer';
};
export type PaymentType =
  | PaymentType3rdParty
  | PaymentTypeCard
  | PaymentTypeCash
  | PaymentTypeCashless
  | PaymentTypeGiftCard
  | PaymentTypeMealTicket
  | PaymentTypeOnline
  | PaymentTypeTapToPay
  | PaymentTypeValueTicket
  | PaymentTypeVoucher
  | PaymentTypeWireTransfer;

export type PaymentTypeWithMeta = PaymentType & ResourceMetadataCommon;

// EXTRA CHARGES
export type ExtraCharge = {
  appliesTo: 'bill' | 'product';
  charge: {
    fixed?: {
      value: number;
      vat: number;
    };
    calculated?: {
      expression: string;
      minValue: number;
      vat: number;
    };
  };
  conditions: {
    property: string;
    operator: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'in';
    constraint: string[];
    source: 'bill' | 'product';
  }[];
  name: string;
  publicName?: TranslatableField;
};

export type ExtraChargeWithMeta = ExtraCharge & ResourceMetadataCommon;

// SELL POINTS
export type SellPoint = {
  address: Address;
  askForCovers?: boolean;
  billColorTimings: {
    [hexColor: string]: number;
  };
  businessHoursExceptions?: {
    [dayOfWeek: string]: HourInterval;
  };
  businessHoursSchedule: HourInterval;
  businessType: 'hospitality' | 'retail';
  catalogs: {
    [catalogId: string]: {
      active: boolean;
      name: string; // same as catalog.name - needs this because we do not load catalog to check it's name - we load only on request
      schedule: HourInterval[];
    };
  };
  compReasons: string[];
  contactInfo: {
    email?: string;
    phone?: string;
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter_x?: string;
    youtube?: string;
    linkedin?: string;
    pinterest?: string;
    google?: string;
    tiktok?: string;
    [key: string]: string | undefined;
  };
  country: string;
  courses: string[];
  coursesFireHold: boolean;
  currency: string;
  defaultMeasureUnit: string;
  description: string;
  discounts: {
    [discountName: string]: number;
  };
  emailNotifications: string[]; // TODO!
  excludedFromRewards: boolean;
  extraCharges: {
    [extraChargeId: string]: ExtraCharge;
  };
  id: string;
  localization: string;
  logoURL: string;
  name: string;
  nextInvoiceNumber: number; // TODO! Deprecated but still in use :( will be removed soon
  outageNotifications: {
    email?: string;
    phone?: string;
    localization: string;
  }[];
  paymentTypes: {
    '3rdParty': PaymentType3rdParty;
    card: PaymentTypeCard;
    cash: PaymentTypeCash;
    cashless: PaymentTypeCashless;
    giftCard: PaymentTypeGiftCard;
    mealTicket: PaymentTypeMealTicket;
    online: PaymentTypeOnline;
    tapToPay: PaymentTypeTapToPay;
    valueTicket: PaymentTypeValueTicket;
    voucher: PaymentTypeVoucher;
    wireTransfer: PaymentTypeWireTransfer;
  };
  sellingType: string;
  specficSellingType: string;
  timezone: string;
  tips: number[];
  tipsVat: number;
  togoCatalogId?: string;
  type: 'physical' | 'virtual';
  voidReasons: string[];
};

export type SellPointWithMeta = SellPoint & ResourceCollectionMetadata;
