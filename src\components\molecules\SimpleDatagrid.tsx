// import { Divider } from '@mui/material';

// interface SimpleDatagridProps {
//   header: {
//     name: string;
//     field: string;
//     align: 'start' | 'center' | 'end';
//     columnStyle?: any;
//   };
//   columns: any[];
//   onClick: (idx: number) => void;
// }
// export default function SimpleDatagrid({
//   header,
//   columns,
//   onClick,
// }: SimpleDatagridProps) {
//   return (
//     <>
//       <Divider />
//     </>
//   );
// }

import { Divider, Grid, Typography } from '@mui/material';
import { NumberField } from 'react-admin';

import { useTheme } from '~/contexts';

export interface HeaderSimpleDatagrid {
  name: string;
  field: string;
  align?: 'start' | 'center' | 'end';
  columnStyle?: any;
}

interface SimpleDatagridProps {
  headers: HeaderSimpleDatagrid[]; // Array of headers, each with a name, field, and alignment
  columns: Record<string, any>[]; // Array of objects, each object representing a row
  onClick: (idx: number) => void; // idx is the row index
}

export default function SimpleDatagrid({
  headers,
  columns: data,
  onClick,
}: SimpleDatagridProps) {
  const { theme } = useTheme();
  return (
    <>
      <Divider />
      {/* Header Row */}
      <Grid
        container
        sx={{
          py: 2,
          px: 1,
          borderBottom: 'solid 1px',
          borderColor: 'custom.gray600',
          backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
        }}
      >
        {headers.map((header, index) => (
          <Grid
            item
            xs={12 / headers.length}
            key={index}
            sx={{ textAlign: header.align ?? 'start' }}
          >
            <Typography variant="body2" fontWeight={500}>
              {header.name}
            </Typography>
          </Grid>
        ))}
      </Grid>

      {/* Data Rows */}
      {!data.length && (
        <Typography variant="caption" color="custom.gray600">
          No entries
        </Typography>
      )}
      {data.map((row, rowIndex) => (
        <div key={rowIndex}>
          <Grid
            container
            onClick={() => onClick(rowIndex)}
            sx={{
              ':hover': {
                bgcolor: 'primary.light',
              },
              cursor: 'pointer',
              py: 2,
              px: 1,
            }}
          >
            {headers.map((header, colIndex) => (
              <Grid
                item
                xs={12 / headers.length}
                key={colIndex}
                sx={{ textAlign: header.align }}
              >
                {header.field === 'value' ? (
                  <NumberField
                    sx={{
                      paddingRight: '8px',
                    }}
                    defaultValue={(row[header.field])/10000}
                    source=""
                    locales={'ro-RO'}
                    options={{
                      style: 'percent',
                      maximumFractionDigits: 2,
                    }}
                  />
                ) : (
                  <Typography
                    variant="body2"
                    sx={headers[colIndex].columnStyle}
                  >
                    {row[header.field]}
                  </Typography>
                )}
              </Grid>
            ))}
          </Grid>
          <Divider />
        </div>
      ))}
    </>
  );
}
