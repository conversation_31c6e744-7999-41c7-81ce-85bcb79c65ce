import { ElementType, useEffect, useMemo, useState } from 'react';
import { Box, Button, SxProps, Theme } from '@mui/material';
import { DateRange, DateRangePicker } from '@mui/x-date-pickers-pro';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  PickersShortcutsItem,
  PickersShortcutsProps,
} from '@mui/x-date-pickers/PickersShortcuts';
import { isValid } from 'date-fns-v4';
import dayjs, { Dayjs } from 'dayjs';
import updateLocale from 'dayjs/plugin/updateLocale';
import { useTranslation } from 'react-i18next';

import DateRangeButtonField from './DateRangePickerBtn';

dayjs.extend(updateLocale);
dayjs.updateLocale('en', {
  weekStart: 1,
});

const CustomRangeShortcuts = (
  props: PickersShortcutsProps<DateRange<Dayjs>>
) => {
  const { items, onChange, isValid } = props;
  const { t } = useTranslation('');

  if (items == null || items.length === 0) {
    return null;
  }

  const resolvedItems = items.map(item => {
    const newValue = item.getValue({ isValid });

    return {
      label: item.label,
      onClick: () => {
        onChange(newValue, 'set', { label: item.label });
      },
      disabled: !isValid(newValue),
    };
  });

  return (
    <Box
      sx={{
        gridRow: { xs: 1, sm: 2 },
        gridColumn: { xs: 2, sm: 1 },
        display: 'flex',
        flexDirection: { xs: 'row', sm: 'column' },
        flexWrap: 'wrap',
        p: 2,
      }}
    >
      {resolvedItems.map((item, index) => {
        return (
          <Button
            key={index}
            // @ts-ignore
            variant="container-light"
            sx={{
              textAlign: 'left',
              display: 'block',
              py: { xs: 2, sm: 1.5 },
              width: '100%',
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            }}
            {...item}
          >
            {item.label}
          </Button>
        );
      })}
    </Box>
  );
};

interface DateRangePickerBtnProps {
  sx?: SxProps<Theme>;
  dateRange: DateRange<dayjs.Dayjs>;
  setDateRange: (range: DateRange<dayjs.Dayjs>) => void;
  ButtonComponent?: ElementType<any, any> | undefined;
  fullWidthButtons?: boolean;
}

const formatDate = (date: Dayjs) => {
  if (date.year() === dayjs().year()) {
    return date.format('MMM DD');
  }
  return date.format('MMM DD, YYYY');
};

export default function DateRangePickerCustom({
  dateRange,
  setDateRange,
  sx,
  fullWidthButtons = false,
  ButtonComponent = DateRangeButtonField,
}: DateRangePickerBtnProps) {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation('');
  const [date, setDate] = useState<DateRange<Dayjs>>(dateRange);

  const shortcutsItems: PickersShortcutsItem<DateRange<Dayjs>>[] = useMemo(
    () => [
      {
        label: t('dashboard.today'),
        getValue: () => {
          const today = dayjs();
          return [today.startOf('day'), today.startOf('day')];
        },
      },
      {
        label: t('dashboard.yesterday'),
        getValue: () => {
          const today = dayjs();
          const yesterday = today.subtract(1, 'day');
          return [yesterday.startOf('day'), yesterday.startOf('day')];
        },
      },
      {
        label: t('dashboard.thisWeek'),
        getValue: () => {
          const today = dayjs();
          return [today.startOf('week'), today.endOf('week').startOf('day')];
        },
      },
      {
        label: t('dashboard.lastWeek'),
        getValue: () => {
          const today = dayjs();
          const prevWeek = today.subtract(7, 'day');
          return [
            prevWeek.startOf('week'),
            prevWeek.endOf('week').startOf('day'),
          ];
        },
      },
      {
        label: t('dashboard.thisMonth'),
        getValue: () => {
          const today = dayjs();
          return [today.startOf('month'), today.endOf('month').startOf('day')];
        },
      },
      {
        label: t('dashboard.lastMonth'),
        getValue: () => {
          const today = dayjs();
          const lastMonth = today.subtract(1, 'month');
          return [
            lastMonth.startOf('month'),
            lastMonth.endOf('month').startOf('day'),
          ];
        },
      },
      {
        label: t('dashboard.thisYear'),
        getValue: () => {
          const today = dayjs();
          return [today.startOf('year'), today.endOf('year').startOf('day')];
        },
      },
      {
        label: t('dashboard.lastYear'),
        getValue: () => {
          const today = dayjs();
          const lastYear = today.subtract(1, 'year');
          return [
            lastYear.startOf('year'),
            lastYear.endOf('year').startOf('day'),
          ];
        },
      },
    ],
    [t]
  );

  const label = useMemo(() => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) {
      return null;
    }

    let computedLabel = dateRange[0].isSame(dateRange[1], 'date')
      ? formatDate(dateRange[0])
      : dateRange.map(d => (d ? formatDate(d) : 'null')).join(' - ');

    for (const item of shortcutsItems) {
      const values = item.getValue({ isValid });
      if (dateRange[0].isSame(values[0]) && dateRange[1].isSame(values[1])) {
        computedLabel = item.label;
      }
    }

    return computedLabel;
  }, [dateRange, shortcutsItems, date]);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DateRangePicker
        // disableFuture
        value={date}
        label={label}
        closeOnSelect={false}
        onChange={(interval, context) => {
          if (date[1] !== null && context.shortcut === undefined) {
            setDate([interval[0], null]);
          } else {
            setDate(interval);
          }
          if (interval[1] === null) {
            setDate([interval[0], interval[0]]);
          } else if (dateRange[1] !== null && interval[1] === dateRange[1]) {
            setDate([interval[0], interval[0]]);
          } else {
            setDate(interval);
          }
        }}
        defaultValue={dateRange}
        open={open}
        onClose={() => {
          setOpen(false);
          setDateRange(date);
        }}
        onOpen={() => setOpen(true)}
        slots={{
          field: ButtonComponent,
          shortcuts: CustomRangeShortcuts,
        }}
        slotProps={{
          field: {
            fullWidth: fullWidthButtons,
            setOpen,
          } as any,
          shortcuts: {
            items: shortcutsItems,
          },
          actionBar: {
            actions: ['clear', 'accept'],
            onClear: () => {
              setDate(dateRange);
              setOpen(false);
            },
            onAccept: () => {
              setDateRange(date);
              setOpen(false);
            },
          },
          toolbar: {
            hidden: true,
          },
          textField: {
            InputProps: label
              ? {
                  value: label,
                  sx: {
                    '& ::selection': {
                      background: 'transparent',
                    },
                  },
                }
              : {},
          },
        }}
        calendars={1}
        sx={sx ? sx : { width: 'fit-content' }}
        desktopModeMediaQuery="@media (min-width: 600px)"
      />
    </LocalizationProvider>
  );
}
