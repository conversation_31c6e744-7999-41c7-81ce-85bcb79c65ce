import { useEffect } from 'react';

import { useFirebase } from '~/contexts/FirebaseContext';
import { getReportDataHelper } from '~/fake-provider';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';

export default function Test() {
  const { details: fbDetails } = useFirebase();
  const { dateRange, sellPointId } = useGlobalResourceFilters();

  useEffect(() => {
    async function fetchData() {
      if (
        !fbDetails.user ||
        !fbDetails.rtdb ||
        !fbDetails.selectedAccount ||
        !sellPointId
      ) {
        return;
      }
      if (!dateRange[0] || !dateRange[1]) return;
      const rawData = await getReportDataHelper({
        database: fbDetails.rtdb!,
        startDate: dateRange[0].format('YYYY-MM-DD'),
        accountId: fbDetails.selectedAccount!,
        sellPointId: sellPointId,
        endDate: dateRange[1].format('YYYY-MM-DD'),
        reportType: 'sales',
      });
    }

    fetchData();
  }, [dateRange, sellPointId, fbDetails]);

  return <></>;
}
