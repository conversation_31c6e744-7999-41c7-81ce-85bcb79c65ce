import { ReactElement } from 'react';
import { Box, FormControlLabel, SwitchProps, Typography } from '@mui/material';
import { BooleanInputProps } from 'react-admin';

import { MuiSwitchInput, SwitchInput } from '../../atoms/inputs/SwitchInput';

interface SwitchInputGroupProps extends Omit<BooleanInputProps, 'options'> {
  description?: string;
  label?: string;
}

const InnerSwitch = ({
  disabled,
  description,
  label,
  children,
}: {
  disabled?: boolean;
  description?: string;
  label?: string;
  children: ReactElement<any, any>;
}) => {
  return (
    <Box
      sx={{
        px: 4,
        py: 3,
        border: `1px solid`,
        borderColor: 'custom.gray400',
        opacity: disabled ? 0.5 : 1,
        marginTop: '-1px',
        borderRadius: '6px',
      }}
    >
      <FormControlLabel
        sx={{
          cursor: disabled ? 'not-allowed !important' : 'pointer',
        }}
        control={children}
        // @ts-ignore
        label={label ? <Typography variant="label">{label}</Typography> : <></>}
      />
      {description && (
        <Typography variant="subtitle2" ml={'45px'}>
          {description}
        </Typography>
      )}
    </Box>
  );
};
const SwitchInputGroup = ({
  label,
  description,
  source,
  disabled = false,
  ...props
}: SwitchInputGroupProps) => {
  return (
    <InnerSwitch disabled={disabled} description={description} label={label}>
      <SwitchInput {...props} disabled={disabled} source={source} />
    </InnerSwitch>
  );
};

interface MuiSwitchInputGroupProps extends SwitchProps {
  description?: string;
  label?: string;
}

const MuiSwitchInputGroup = ({
  disabled,
  description,
  label,
  ...props
}: MuiSwitchInputGroupProps) => {
  return (
    <InnerSwitch disabled={disabled} description={description} label={label}>
      <MuiSwitchInput {...props} disabled={disabled} />
    </InnerSwitch>
  );
};

export { SwitchInputGroup, MuiSwitchInputGroup };
