import { Box, Link, Typography } from '@mui/material';

import PageTitle from '../../../components/molecules/PageTitle';
import GiftCardsHeaderCards from './GiftCardsHeaderCards';
import { GiftCardsList } from './GiftCardsList';
import GiftCardsShow from './GiftCardsShow';

const cards = [
  { title: 'Activations (last 30 days)', value: 0.0 },
  { title: 'Redemptions (last 30 days)', value: 0.0 },
  { title: 'Outstanding (all time)', value: 6485.0 },
];

export default function GiftCardsPage() {
  return (
    <Box p={2}>
      <PageTitle
        hideBorder
        title="Overview"
        description={
          <>
            <Typography variant="subtitle2" mb={3} color="inherit">
              To view additional gift card reporting, go to{' '}
              <Link
                target="_blank"
                href="/reports"
                sx={{ color: '#005AD9', cursor: 'pointer' }}
              >
                Reports.
              </Link>
            </Typography>
          </>
        }
      />
      <GiftCardsHeaderCards cards={cards} />
      <Box
        sx={{
          height: 7,
          width: '100%',
          backgroundColor: '#EAEAEA',
          opacity: 0.5,
          my: 2,
        }}
      />
      <GiftCardsList />
      <GiftCardsShow />
    </Box>
  );
}
