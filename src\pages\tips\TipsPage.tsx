import { Box, Theme, useMediaQuery } from '@mui/material';
import {
  CreateButton,
  TopToolbar,
} from 'react-admin';
import { useTranslation } from 'react-i18next';
import PageTitle from '../../components/molecules/PageTitle';
import TipsCreate from './TipsCreate';
import TipsEdit from './TipsEdit';
import TipsList from './TipsList';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';


export default function TipsPage() {
  const { t } = useTranslation();
  const { sellPointId, isLoading } = useGlobalResourceFilters();

  if (isLoading || !sellPointId) {
    return null;
  }

  return (
    <Box sx={{ height: '100%' }} p={2}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title={t('tipsPage.title')}
        description={
          <>
            {t('tipsPage.description')}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              {t('support.support-link')}
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />

      <TipsList sellPointId={sellPointId} />
      <TipsEdit sellPointId={sellPointId} />
      <TipsCreate sellPointId={sellPointId} />
    </Box>
  );
}
