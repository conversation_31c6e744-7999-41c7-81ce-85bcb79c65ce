import { readFileSync, writeFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const versionFile = join(__dirname, '../version.ts');
const versionContent = readFileSync(versionFile, 'utf8');
const currentVersion = versionContent.match(/['"](\d+\.\d+\.\d+)['"]/)[1];

//const [major, minor, patch] = currentVersion.split('.').map(Number);
//const newVersion = `${major}.${minor}.${patch + 1}`;

// Generate a 6-digit random number
const buildId = Math.floor(100000 + Math.random() * 900000);
const newVersion = `${currentVersion}+${buildId}`;

const newContent = `/** The current application version */\nexport const APP_VERSION: string = '${newVersion}';\n`;
writeFileSync(versionFile, newContent);

console.log(`Version updated from ${currentVersion} to ${newVersion}`);
