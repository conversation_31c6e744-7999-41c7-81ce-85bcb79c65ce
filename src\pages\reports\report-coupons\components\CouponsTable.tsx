import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { ReportType } from '~/fake-provider/reports/types';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';

interface ExtraData {
  [key: string]: any;
}

interface TableRow {
  name: string;
  itemsQty: string;
  vat: number | undefined;
  itemsValue: string;
  giftCardsQty: string;
  modifiersQty: string;
  modifiersValue: string;
  giftCardsValue: string;
  totalValue: number;
  items?: TableRow[];
  subItems?: TableRow[];
  extraData?: ExtraData;
}

export default function CouponsTable({
  tableData,
  fields,
  setFields,
  groupingItems,
  onChangeGrouping,
}: {
  tableData: ReportType[keyof ReportType][] | undefined;
  fields: FieldOption[];
  groupingItems: string[];
  onChangeGrouping?: (items: any[]) => void;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
}) {
  const { t } = useTranslation();
  const couponsTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.name = 'Total';
      totalItemsData.subItems = [];
      totalItemsData.vat = undefined;

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }
    return [];
  }, [tableData]);

  const columnsToFilter = useMemo(() => {
    const columns = [
      'itemsQty',
      'itemsValue',
      'itemsValue',
      'modifiersValue',
      'modifiersQty',
      'giftCardsQty',
      'vat',
      'giftCardsValue',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'itemsQty', label: t('reportsPage.itemsDiscountApplied') },
    { value: 'modifiersQty', label: t('reportsPage.modifiersDiscountApplied') },
    { value: 'giftCardsQty', label: t('reportsPage.giftCardCouponsApplied') },
    { value: 'vat', label: t('shared.tva') },
  ];

  const couponsTablConfig: ColumnConfig<TableRow>[] = useMemo(
    () => [
      {
        id: 'name',
        textAlign: 'start',
        label: t('shared.name'),
        render: (row: TableRow) => {
          return (
            <>
              {row.name && row.name === '@coupon'
                ? t('discounts.rewardCoupons')
                : row.name}
            </>
          );
        },
      },
      {
        id: 'vat',
        render: (row: TableRow) => {
          return (
            <div
              style={{
                whiteSpace: 'nowrap',
                fontSize: '14px',
                //@ts-ignore
                '@media print': {
                  backgroundColor: '#FFFFFF !important',
                  color: 'black !important',
                },
              }}
            >
              {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
            </div>
          );
        },
        textAlign: 'end',
        label: 'VAT',
      },
      {
        id: 'itemsQty',
        textAlign: 'end',
        label: 'Items Discount Applied',
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.itemsQty), true)}</>;
        },
      },
      {
        id: 'itemsValue',
        textAlign: 'end',
        label: 'Items Discount Amount',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.itemsValue))}</>;
        },
      },
      {
        id: 'modifiersQty',
        textAlign: 'end',
        label: 'Modifiers Discount Applied',
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.modifiersQty), true)}</>;
        },
      },
      {
        id: 'modifiersValue',
        textAlign: 'end',
        label: 'Modifiers Discount Amount',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.modifiersValue))}</>;
        },
      },
      {
        id: 'giftCardsQty',
        label: 'Gift Card Coupons Applied',
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatNumberIntl(Number(row.giftCardsQty), true)}</>;
        },
      },
      {
        id: 'giftCardsValue',
        label: 'Gift Card Amount Discounted',
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.giftCardsValue))}</>;
        },
      },
      {
        id: 'totalValue',
        label: t('discounts.totalValue'),
        textAlign: 'end',
        render: (row: TableRow) => {
          return <>{formatAndDivideNumber(Number(row.totalValue))}</>;
        },
      },
    ],
    [fields]
  );

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          config={couponsTablConfig}
          data={couponsTableData}
          fields={fields}
          separateFirstColumn={true}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
        />
      </Box>
    </>
  );
}
