import { ResourcesInfo, resourcesInfo } from '../resources';

export function getResourceInfo(
  resource: string,
  meta?: { [key: string]: any }
) {
  const resourceInfo = resourcesInfo[resource as keyof ResourcesInfo];
  if (resourceInfo === undefined) {
    throw new Error(`Resource ${resource} not configured!`);
  }
  if (resourceInfo?.needsMetaFields?.length) {
    // check if meta fields are present
    resourceInfo.needsMetaFields.forEach(metaField => {
      if (meta?.[metaField] === undefined) {
        throw new Error(
          `Missing meta field ${metaField} for resource ${resource}`
        );
      }
    });
  }
  return resourceInfo;
}
