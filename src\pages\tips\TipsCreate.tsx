import { Box } from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { SaveButton, SimpleForm, useRedirect } from 'react-admin';
import { useTranslation } from 'react-i18next';

import { RESOURCES } from '~/providers/resources';
import CustomInput from '../../components/atoms/inputs/CustomInput';
import ModalHeader from '../../components/molecules/ModalHeader';

export default function TipsCreate({ sellPointId }: { sellPointId: string }) {
  const redirect = useRedirect();
  const { t } = useTranslation();
  const handleClose = () => {
    redirect('list', RESOURCES.TIPS);
  };

  return (
    <CreateDialog
      fullWidth={true}
      maxWidth={'sm'}
      mutationMode="optimistic"
      mutationOptions={{ meta: { sellPointId: sellPointId } }}
    >
      <SimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={{ _isJustAValue: true }}
      >
        <ModalHeader
          handleClose={handleClose}
          title={t('tipsPage.createNewTip')}
        >
          <SaveButton type="button" icon={<></>} label={t('shared.save')} />
        </ModalHeader>

        {/* Body */}
        <Box p={2} width="100%">
          <CustomInput
            source="value"
            type="number"
            label={t('tipsPage.percentage')}
            locale="ro-RO"
            format={v => v / 100}
            parse={v => Math.floor(v * 100)}
            options={{
              style: 'percent',
              maximumFractionDigits: 2,
            }}
            slotProps={{
              input: {
                endAdornment: '%',
              },
            }}
          />
        </Box>
      </SimpleForm>
    </CreateDialog>
  );
}
