import { useDraggable } from '@dnd-kit/core';
import { Box, Typography } from '@mui/material';

import { useTheme } from '../../../contexts';
import styles from './DraggableTable.module.css';
import Resizer from './Resizer';
import { Table } from './types';

interface DraggableItemProps extends Table {
  id: number;
  label: string;
  selected: boolean;
  error: boolean;
  updateTable: (table: Partial<Table>) => void;
}

export default function DraggableTable({
  id,
  number,
  shape,
  position,
  label,
  tag,
  selected,
  error,
  updateTable,
}: DraggableItemProps) {
  const { startX, startY, endX, endY } = position;

  const { theme } = useTheme();
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useDraggable({
      id: id + 1,
    });

  const handleResize = (
    direction: 'startX' | 'startY' | 'endX' | 'endY',
    delta: number
  ) => {
    const newPos = { ...position };
    newPos[direction] = +position[direction] + delta;

    if (
      +newPos.endY - +newPos.startY < 100 ||
      +newPos.endX - +newPos.startX < 100
    ) {
      return;
    }

    updateTable({
      position: newPos,
    });
  };

  // if the tag is provided we need to make the label two lines
  // and the label+number should be on the first line with smaller font and opacity
  // and the tag on the second line with normal font and no opacity
  // if the tag is not provided we need to keep the label as it is
  return (
    <div
      className={styles.Draggable + ' ' + (isDragging && styles.dragging)}
      style={
        {
          '--translate-x': `${transform?.x ?? 0}px`,
          '--translate-y': `${transform?.y ?? 0}px`,
          '--border-radius': shape === 'circle' ? '50%' : '6px',
          '--bg-color': error
            ? theme.palette.error.main
            : selected
              ? theme.palette.primary.main
              : theme.palette.custom.draggableTable,
          '--z-index': selected ? 2 : 1,
          top: startY + 'px',
          left: startX + 'px',
          width: endX - startX,
          height: endY - startY,
        } as React.CSSProperties
      }
    >
      <Resizer onResize={handleResize} />
      <div
        className={styles.innerDiv}
        {...attributes}
        aria-label="Draggable"
        data-cypress="draggable-item"
        {...listeners}
        tabIndex={undefined}
        ref={setNodeRef}
      >
        <label>
          {tag ? (
            <>
              <Typography
                variant="caption"
                fontSize={12}
                sx={{ opacity: 0.7, display: 'block' }}
              >
                {label + number}
              </Typography>
              <Typography
                variant="caption"
                fontSize={14}
                sx={{ display: 'block' }}
              >
                {tag}
              </Typography>
            </>
          ) : (
            <Typography variant="caption" fontSize={14}>
              {label + number}
            </Typography>
          )}
        </label>
      </div>
    </div>
  );
}
