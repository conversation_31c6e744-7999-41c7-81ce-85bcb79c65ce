import { useState } from 'react';
import { <PERSON>, Button, Checkbox, Divider, Typography } from '@mui/material';

import Subsection from '../../../components/molecules/Subsection';

interface CheckboxState {
  [key: string]: boolean;
}

interface Section {
  name: keyof CheckboxState;
  title: string;
  description: string;
  price: number;
  unit: string;
  numberOfUnits: number;
}

export default function PricingSubscriptionsCreateStep3({
  handleClose,
}: {
  handleClose: () => void;
}) {
  const initialCheckboxState: CheckboxState = {
    advancedAccess: false,
    selioPos: false,
    selioKitchenDisplay: false,
  };

  const [checkboxes, setCheckboxes] =
    useState<CheckboxState>(initialCheckboxState);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    setCheckboxes(prev => ({ ...prev, [name]: checked }));
  };

  const sections: Section[] = [
    {
      name: 'advancedAccess',
      title: 'Advanced Access',
      description: 'per location',
      price: 60,
      unit: 'device',
      numberOfUnits: 1,
    },
    {
      name: 'selioPos',
      title: 'Selio POS',
      description: 'per display',
      price: 25,
      unit: 'display',
      numberOfUnits: 1,
    },
    {
      name: 'selioKitchenDisplay',
      title: 'Selio Kitchen Display (KDS)',
      description: 'per display',
      price: 15,
      unit: 'display',
      numberOfUnits: 1,
    },
  ];

  return (
    <Box sx={{ mt: 4 }}>
      <Subsection
        title={'Don’t Lose Access'}
        subtitle={
          'Selio for Restaurants Premium includes services you may want to continue subscribing to. Select any subscriptions you would like to keep.'
        }
      />
      <Divider sx={{ my: 3, backgroundColor: 'f9f9f9' }} />
      <Box
        sx={{
          display: 'flex',
          width: '100%',
          justifyContent: 'space-between',
          gap: 1,
          my: 2,
          px: 2,
        }}
      >
        <Typography sx={{ fontWeight: 600 }}>Subscription</Typography>
        <Typography sx={{ fontWeight: 600 }}>Monthly Total</Typography>
      </Box>
      <Divider sx={{ my: 3, backgroundColor: '#999999' }} />
      <Box sx={{ display: 'flex', flexDirection: 'column', mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
          }}
        >
          {sections.map(
            ({
              name,
              title,
              description,
              price,
              unit,
              numberOfUnits,
            }: Section) => (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    gap: 3,
                    py: 1,
                    px: { xs: 1, sm: 4 },
                    alignItems: 'center',
                  }}
                >
                  <Box
                    key={name}
                    sx={{ display: 'flex', alignItems: 'start', gap: 1 }}
                  >
                    <Checkbox
                      sx={{ p: 0, mr: 1, mt: 0.6 }}
                      checked={checkboxes[name]}
                      onChange={handleCheckboxChange}
                      name={name as string}
                    />
                    <Box>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        {title}
                      </Typography>
                      <Typography sx={{ fontWeight: 400 }} variant="body2">
                        {price} € {description}
                      </Typography>
                      <Typography
                        sx={{ fontWeight: 400 }}
                        color="textSecondary"
                        variant="body2"
                      >
                        You have {numberOfUnits} € {unit}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, whiteSpace: 'nowrap' }}
                  >
                    {price} €
                  </Typography>
                </Box>
                <Divider sx={{}} />
              </>
            )
          )}
        </Box>
      </Box>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-start',
        }}
      >
        <Button
          // @ts-ignore
          variant="contained-light"
          href="/pricing-subscriptions"
          sx={{ px: 3, width: 'fit-content' }}
          onClick={handleClose}
        >
          Keep premium plan
        </Button>
      </Box>
    </Box>
  );
}
