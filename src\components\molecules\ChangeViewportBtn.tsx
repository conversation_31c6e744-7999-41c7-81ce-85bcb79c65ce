import { useEffect } from 'react';
import { Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '~/hooks';

export default function ChangeViewportBtn() {
  const { t } = useTranslation();
  const [forceDesktopViewport, setForceDesktopViewport] = useLocalStorage(
    'forceDesktopViewport',
    false
  );

  const isMobile =
    navigator.userAgent.match(/iPhone/i) ||
    navigator.userAgent.match(/iPad/i) ||
    navigator.userAgent.match(/Android/i);

  const changeViewport = () => {
    const metaViewport = document.querySelector('meta[name="viewport"]');

    if (metaViewport) {
      if (forceDesktopViewport) {
        metaViewport.setAttribute(
          'content',
          'width=device-width, initial-scale=1, maximum-scale=1'
        );
      } else {
        metaViewport.setAttribute('content', 'width=1024');
      }
      setForceDesktopViewport(!forceDesktopViewport);
    } else {
      console.warn('Viewport meta tag not found');
    }
  };

  useEffect(() => {
    if (forceDesktopViewport) {
      changeViewport();
    }
  }, []);

  if (!isMobile) return <></>;

  return (
    <Button
      className="do-not-print"
      variant="contained"
      fullWidth
      onClick={changeViewport}
    >
      {forceDesktopViewport
        ? t('menu.backToMobileSite')
        : t('menu.useDesktopSiteInstead')}
    </Button>
  );
}
