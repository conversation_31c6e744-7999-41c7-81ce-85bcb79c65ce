import { ImageEditorConfig, TargetSize } from '~/types/fileUpload';
import {
    STANDARD_IMAGE_SIZES,
    getDefaultImageSizes,
    standardSizesToTargetSizes,
    PREDEFINED_SIZE_COMBINATIONS,
    SizeCombinationKey
} from '~/configs/imageSize';

/**
 * Enhanced image editor configuration that includes standard sizes
 */
export interface StandardImageEditorConfig extends Omit<ImageEditorConfig, 'targetSizes'> {
    // Use standard size keys instead of custom target sizes
    standardSizes?: string[];

    // Or use predefined combinations
    sizePreset?: SizeCombinationKey;

    // Include additional custom sizes if needed
    customSizes?: TargetSize[];

    // Whether to include default sizes (thumbnail, etc.) automatically
    includeDefaults?: boolean;
}

/**
 * Convert standard image editor config to regular image editor config
 */
export const resolveImageEditorConfig = (config: StandardImageEditorConfig): ImageEditorConfig => {
    let allSizes: TargetSize[] = [];

    // Add sizes from preset
    if (config.sizePreset) {
        const presetKeys = [...PREDEFINED_SIZE_COMBINATIONS[config.sizePreset]];
        const presetSizes = standardSizesToTargetSizes(presetKeys);
        allSizes.push(...presetSizes);
    }

    // Add sizes from standardSizes
    if (config.standardSizes) {
        const standardSizes = standardSizesToTargetSizes(config.standardSizes);
        allSizes.push(...standardSizes);
    }

    // Add custom sizes
    if (config.customSizes) {
        allSizes.push(...config.customSizes);
    }

    // Add default sizes if requested or if no sizes specified
    if (config.includeDefaults !== false) {
        const defaultSizes = getDefaultImageSizes();
        const defaultTargetSizes = defaultSizes.map(size => ({
            width: size.width,
            height: size.height,
            name: size.key,
        }));

        // Merge with existing sizes, avoiding duplicates
        const existingNames = new Set(allSizes.map(s => s.name));
        const newDefaults = defaultTargetSizes.filter(s => !existingNames.has(s.name));
        allSizes.unshift(...newDefaults); // Add defaults at the beginning
    }

    // Remove duplicates by name
    const uniqueSizes = allSizes.filter((size, index, array) =>
        array.findIndex(s => s.name === size.name) === index
    );

    return {
        ...config,
        targetSizes: uniqueSizes,
    };
};

/**
 * Get all sizes that will be generated for an image (including defaults)
 */
export const getAllGeneratedSizes = (config: StandardImageEditorConfig): string[] => {
    const resolved = resolveImageEditorConfig(config);
    return resolved.targetSizes?.map(size => size.name || `${size.width}x${size.height}`) || [];
};

/**
 * Create a standard image editor config for common use cases
 */
export const createStandardImageConfig = {
    itemsImages: (): StandardImageEditorConfig => ({
        sizePreset: 'itemsImages',
        cropShape: 'rect',
        allowZoom: true,
        outputFormat: 'webp', // Use WebP for better compression
        outputQuality: 0.9,
    }),

    userProfiles: (): StandardImageEditorConfig => ({
        sizePreset: 'user_profiles',
        cropShape: 'circle',
        allowZoom: true,
        outputFormat: 'webp',
        outputQuality: 0.8,
    }),

    heroImages: (): StandardImageEditorConfig => ({
        sizePreset: 'hero_images',
        cropShape: 'rect',
        allowZoom: true,
        allowRotate: true,
        outputFormat: 'webp',
        outputQuality: 0.95,
    }),

    promotionalBanners: (): StandardImageEditorConfig => ({
        sizePreset: 'promotional_banners',
        cropShape: 'rect',
        allowZoom: true,
        outputFormat: 'webp',
        outputQuality: 0.9,
    }),

    productGallery: (): StandardImageEditorConfig => ({
        sizePreset: 'product_gallery',
        cropShape: 'rect',
        allowZoom: true,
        outputFormat: 'webp',
        outputQuality: 0.9,
    }),

    custom: (standardSizes: string[], options: Partial<StandardImageEditorConfig> = {}): StandardImageEditorConfig => ({
        standardSizes,
        cropShape: 'rect',
        allowZoom: true,
        outputFormat: 'webp',
        outputQuality: 0.9,
        ...options,
    }),
};

/**
 * Helper to validate that size keys exist in standard sizes
 */
export const validateSizeKeys = (keys: string[]): { valid: string[], invalid: string[] } => {
    const valid: string[] = [];
    const invalid: string[] = [];

    keys.forEach(key => {
        if (STANDARD_IMAGE_SIZES[key]) {
            valid.push(key);
        } else {
            invalid.push(key);
        }
    });

    return { valid, invalid };
};
