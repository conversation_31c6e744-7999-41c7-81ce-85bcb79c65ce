// Main components
export { default as FileUploadComponent } from './FileUploadComponent';
export { default as RaFileUploadComponent } from './RaFileUploadComponent';

// Types
export type {
    FileUploadConfig,
    FileUploadComponentProps,
    RaFileUploadComponentProps,
    ValidationError,
    ImageConfig,
    ValidationConfig,
    UIConfig,
    CallbackConfig,
    UploadState,
    FileListState,
    ImageEditorState,
    ValidationState,
} from './types';

// Hooks
export { useFileList } from './hooks/useFileList';
export { useFileValidation } from './hooks/useFileValidation';
export { useFileUpload } from './hooks/useFileUpload';
export { useImageEditor } from './hooks/useImageEditor';
export { useFileLifecycle } from './hooks/useFileLifecycle';

// Utilities
export {
    mergeWithDefaults,
    validateConfig,
    createConfig,
    DEFAULT_CONFIGS,
} from './utils/fileUploadConfig';

export {
    validateFile,
    validateFiles,
    filterValidFiles,
    hasValidationErrors,
    groupValidationErrors,
    getValidationSummary,
    validateFileTypeConfiguration,
} from './utils/fileUploadValidation';

export {
    generateFileId,
    isImageFile,
    isVideoFile,
    canPreviewFile,
    getFileExtension,
    getFilenameWithoutExtension,
    formatFileSize,
    mapFileTypeToCode,
    shouldHaveVariants,
    isTempFile,
    isPermanentFile,
    getMimeTypeFromExtension,
    isFileTypeAccepted,
    debounce,
    throttle,
    delay,
    areFilesEqual,
    removeDuplicateFiles,
    sortFiles,
    getFileIcon,
    isValidUploadedFile,
} from './utils/fileUploadHelpers';

// Sub-components
export { FileDropzone } from './components/FileDropzone/FileDropzone';
export { FileList } from './components/FileList/FileList';
export { FileItem } from './components/FileList/FileItem';
export { ValidationDisplay } from './components/FileValidation/ValidationDisplay';
