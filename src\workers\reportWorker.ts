import { getReport } from '../fake-provider/reports/getReport';

// Make the onmessage handler async so we can await the Promise
self.onmessage = async function (event) {
  const {
    requestId,
    action,
    accountId,
    sellPointId,
    reportType,
    startDate,
    endDate,
    locale,
    storageData,
    realtimeData,
  } = event.data;

  if (action === 'processReport') {
    try {
      const backend = {};

      console.log(
        `Worker: Processing report for accountId: ${accountId}, sellPointId: ${sellPointId}, reportType: ${reportType}, startDate: ${startDate}, endDate: ${endDate}`
      );

      // Wait for the Promise to resolve
      const result = await getReport(
        accountId,
        sellPointId,
        reportType,
        startDate,
        endDate,
        locale,
        backend,
        storageData || [],
        realtimeData
      );

      // Now we can safely send back the resolved data, not the Promise
      self.postMessage({
        requestId,
        data: result,
      });
    } catch (error) {
      self.postMessage({
        requestId,
        error:
          error && typeof error === 'object' && 'message' in error
            ? (error as Error).message
            : String(error),
      });
    }
  }
};
