import { Box, Button, Typography } from '@mui/material';

import DateRangePickerCustom from '~/components/molecules/DateRangePickerCustom';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import DatePickerFilterBtnDisabled from '../../components/DatePickerFilterBtnDisabled';
import LocationPickerBtn from '../../components/LocationPickerBtn';
import TimeRangePickerBtn from '../../components/TimeRangePickerBtn';
import { useTranslation } from 'react-i18next';
export default function FormattedFilters({
  formattedFilters,
}: {
  formattedFilters: any;
}) {
  const { sellPointId, dateRange } = useGlobalResourceFilters();
  const { t } = useTranslation();
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      <LocationPickerBtn
        disabled={true}
        sellpointId={sellPointId}
        setSellpointId={() => {}}
      />
      <DateRangePickerCustom
        dateRange={dateRange}
        setDateRange={() => {}}
        ButtonComponent={DatePickerFilterBtnDisabled}
      />
      <TimeRangePickerBtn
        disabled={true}
        defaultValues={formattedFilters.timeRange}
        setTimeRange={() => {}}
      />
      <Button
        //@ts-ignore
        variant={'contained-light'}
        disabled={true}
      >
        {/* @ts-ignore */}
        <Typography variant="outlined" fontWeight={500} color="custom.gray800">
          {formattedFilters.member || t('reportsPage.allMembers')}
        </Typography>
      </Button>
      <Button
        //@ts-ignore
        variant={'contained-light'}
        disabled={true}
      >
        {/* @ts-ignore */}
        <Typography variant="outlined" fontWeight={500} color="custom.gray800">
          {formattedFilters.floor || t('reportsPage.allFloors')}
        </Typography>
      </Button>
      <Button
        //@ts-ignore
        variant={'contained-light'}
        disabled={true}
      >
        {/* @ts-ignore */}
        <Typography variant="outlined" fontWeight={500} color="custom.gray800">
          {formattedFilters.serviceType &&
          formattedFilters.serviceType === 'all'
            ? t('reportsPage.allServiceTypes')
            : formattedFilters.serviceType}
        </Typography>
      </Button>
      <Button
        //@ts-ignore
        variant={'contained-light'}
        disabled={true}
      >
        {/* @ts-ignore */}
        <Typography variant="outlined" fontWeight={500} color="custom.gray800">
          {formattedFilters.source && formattedFilters.source === 'all'
            ? t('reportsPage.allSources')
            : formattedFilters.source}
        </Typography>
      </Button>
    </Box>
  );
}
