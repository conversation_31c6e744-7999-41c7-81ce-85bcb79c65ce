import { getApp, getApps, initializeApp } from 'firebase/app';
import { initializeApp<PERSON><PERSON><PERSON>, Re<PERSON>aptchaV3Provider } from 'firebase/app-check';
import {
    connectAuthEmulator,
    getAuth,
    onAuthStateChanged,
} from 'firebase/auth';
import { Database, getDatabase } from 'firebase/database';
import {
    connectFirestoreEmulator,
    initializeFirestore,
    persistentLocalCache,
} from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { FirebaseStorage, getStorage } from 'firebase/storage';

const firebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
    console.error(
        'Firebase configuration environment variables are missing! Ensure VITE_FIREBASE_* variables are set in your .env file.'
    );
}

const USE_EMULATORS = import.meta.env.VITE_USE_FIREBASE_EMULATORS === 'true';

const firebaseApp = !getApps().length
    ? initializeApp(firebaseConfig)
    : getApp();

const appCheck =
    import.meta.env.VITE_NODE_ENV === 'prod' &&
        import.meta.env.VITE_FIREBASE_APP_CHECK_SITE_KEY
        ? initializeAppCheck(firebaseApp, {
            provider: new ReCaptchaV3Provider(
                import.meta.env.VITE_FIREBASE_APP_CHECK_SITE_KEY
            ),
            isTokenAutoRefreshEnabled: true,
        })
        : null;

const auth = getAuth(firebaseApp);

const db = initializeFirestore(firebaseApp, {
    ignoreUndefinedProperties: true,
    localCache: persistentLocalCache({
        cacheSizeBytes: 10 * 1024 * 1024, // 10 MB
        // Or specify a byte size: cacheSizeBytes: CACHE_SIZE_UNLIMITED,
        // use this with caution, can consume a lot of disk space
    }),
});

const storage = getStorage(firebaseApp);

// Function to get a cached storage instance for different buckets
const storageInstances: { [bucketName: string]: FirebaseStorage } = {};
const getStorageInstance = (bucketName?: string): FirebaseStorage => {
    // If no bucket name provided, return default storage
    if (!bucketName) {
        return storage;
    }

    try {
        if (!storageInstances[bucketName]) {
            storageInstances[bucketName] = getStorage(
                firebaseApp,
                `gs://${bucketName}`
            );
        }
        return storageInstances[bucketName];
    } catch (error) {
        console.error(
            `Failed to initialize storage for bucket ${bucketName}:`,
            error
        );
        // Return default storage as fallback
        return storage;
    }
};

// Function to get a cached RTDB instance
const rtdbInstances: { [url: string]: Database } = {};
const getRtdbInstance = (url: string | null): Database | null => {
    if (!url) return null;

    try {
        if (!rtdbInstances[url]) {
            rtdbInstances[url] = getDatabase(firebaseApp, url);
        }
        return rtdbInstances[url];
    } catch (error) {
        console.error('Failed to initialize RTDB:', error);
        return null;
    }
};

const functions = getFunctions(
    firebaseApp,
    import.meta.env.VITE_NODE_ENV === 'prod' ? 'europe-west1' : 'europe-west3'
);

// Add this new function to check when auth is initialized
const waitForAuthInit = () => {
    return new Promise<void>(resolve => {
        const unsubscribe = onAuthStateChanged(auth, () => {
            unsubscribe();
            resolve();
        });
    });
};

// Setup cache cleanup on auth state changes
onAuthStateChanged(auth, (user) => {
    if (!user) {
        // User logged out, clear all caches and temp files
        import('../utils/bucketManager').then(({ clearPrivateFileCache, cleanupTempFiles }) => {
            clearPrivateFileCache();
            cleanupTempFiles();
        }).catch(console.error);
    }
});

if (USE_EMULATORS) {
    console.warn('--- USING FIREBASE EMULATORS ---');
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    connectFirestoreEmulator(db, 'localhost', 8080);
}

export {
    firebaseApp,
    auth,
    db,
    storage,
    getStorageInstance,
    getRtdbInstance,
    waitForAuthInit,
    functions,
};
