import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import {
  CreateButton,
  DeleteWithConfirmButton,
  useCreatePath,
  useGetList,
} from 'react-admin';
import { useNavigate } from 'react-router-dom';

import Subsection from '../../components/molecules/Subsection';
import { useTheme } from '../../contexts';
import LoyaltyCard from './components/LoyaltyCard';

export default function LoyaltyList() {
  const navigate = useNavigate();
  const { data: rewards } = useGetList('loyalty');
  const createPath = useCreatePath();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { theme } = useTheme();

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mt: { xs: 0, sm: 3 },
        height: '100%',
      }}
    >
      {rewards && rewards?.length > 0 ? (
        <Box
          sx={{
            width: '100%',
            flexDirection: 'column',
            justifyContent: 'space-between',
            alignItems: 'center',
            maxWidth: '500px !important',
            height: '100%',
          }}
        >
          <Subsection
            title="Reward is active"
            titleSx={{
              fontWeight: 500,
              fontSize: 17,
              width: '100%',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                width: '100%',
              }}
            >
              {rewards?.map(reward => {
                return (
                  <Box
                    key={reward.id}
                    sx={{
                      border: '1.5px solid #0064F0',
                      boxShadow: '0px 10px 7px -5px rgba(0,100,240,0.3)',
                      borderRadius: '6px',
                      p: 2,
                      width: '100%',
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      navigate(
                        createPath({
                          resource: 'loyalty',
                          type: 'edit',
                          id: reward.id,
                        })
                      );
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 0.2,
                          justifyContent: 'center',
                        }}
                      >
                        <Typography
                          fontWeight={500}
                          fontSize={13}
                          variant="caption"
                        >
                          Coupon ({reward['units-needed'] || '-'})
                          {reward.units.values.plural}
                        </Typography>
                        <Typography
                          sx={{ color: 'gray' }}
                          fontWeight={300}
                          fontSize={13}
                          variant="caption"
                        >
                          Discount on entire sale
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          alignItems: 'center',
                          width: { sm: '50px', xs: '40px' },
                          height: 'fit-content',
                          backgroundColor: '#f2f2f2',
                          borderRadius: '10px',
                        }}
                      >
                        <DeleteWithConfirmButton
                          confirmTitle={`Delete this coupon?`}
                          label=""
                          record={reward}
                          translateOptions={{ name: reward['units-needed'] }}
                          resource="loyalty"
                          size={'small'}
                          sx={{
                            m: 0,
                            py: 1.2,
                            px: 1,
                            minWidth: 0,
                          }}
                          icon={
                            <DeleteOutlineIcon
                              sx={{
                                color: '#CA0000',
                                width: '100%',
                                fontSize: '22px !important',
                                pl: { xs: 0.2, sm: 1 },
                              }}
                            />
                          }
                        />
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Box>
            <Box sx={{ alignSelf: 'end', mt: 2 }}>
              <CreateButton
                sx={{
                  minWidth: 'fit-content',
                  px: 2,
                  py: 1,

                  backgroundColor: '#015AD9 !important',
                  color: 'white',
                  ':hover': {
                    backgroundColor: '#004a9b !important',
                  },
                }}
                variant="text"
                color="primary"
                label="Create another reward"
                {...(isXSmall ? (
                  <AddIcon
                    sx={{
                      color: 'white',
                      fontSize: 24,
                      fontWeight: 500,
                    }}
                  />
                ) : (
                  { icon: <></> }
                ))}
              />
            </Box>
          </Subsection>
        </Box>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            height: '100%',
            maxWidth: '800px',
          }}
        >
          <Box sx={{ alignSelf: 'end' }}>
            <CreateButton
              variant="text"
              color="primary"
              {...(isXSmall ? (
                <AddIcon sx={{ color: 'white', fontSize: 24 }} />
              ) : (
                { icon: <></> }
              ))}
              sx={{
                minWidth: 'fit-content',
                backgroundColor: '#015AD9 !important',
                color: 'white',
                ':hover': {
                  backgroundColor: '#004a9b !important',
                },
              }}
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              maxWidth: '800px',
              pt: 6,
            }}
          >
            <Subsection
              title="Build repeat business and reward your customers"
              titleSx={{
                fontWeight: 600,
                textShadow:
                  theme.palette.mode !== 'light' ? '' : '0px 3px 3px #a5a5a5',
                fontSize: { xs: '26px !important', sm: '32px !important' },
                maxWidth: '550px',
                textAlign: { xs: 'center', sm: 'start' },
              }}
            />
            <Typography
              sx={{
                color: '#005AD9',
                maxWidth: '550px',
                width: '100%',
                textShadow:
                  theme.palette.mode !== 'light' ? '' : '0px 3px 3px #bcbcbc',
                textAlign: { xs: 'center', sm: 'start' },
              }}
              fontWeight={500}
              fontSize={15}
              variant="caption"
            >
              Free. Cancel anytime.
            </Typography>
            <Box
              sx={{
                display: 'flex',
                gap: 6,
                mt: 5,
                justifyContent: { xs: 'center', sm: 'start' },
                alignItems: { xs: 'center', sm: 'start' },
                flexDirection: { xs: 'column', sm: 'row' },
              }}
            >
              <LoyaltyCard
                title="More sales"
                description="Sellers who use Selio Loyalty see a minimum 25% increase in customer visit frequency."
                icon="/assets/loyalty/sales.svg"
              />
              <LoyaltyCard
                title="Results you can see"
                description="Track your program, customer visits, and reward redemption in the loyalty dashboard."
                icon="/assets/loyalty/statistic.svg"
              />
              <LoyaltyCard
                title="No extra devices"
                description="Get started with just your existing tools."
                icon="/assets/loyalty/computer.svg"
              />
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
}
