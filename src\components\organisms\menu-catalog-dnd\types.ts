import { menuColors } from '~/data/menu-colors';
import { UploadedFile } from '~/types/fileUpload';
import { NutritionalValues } from './add-item-modal/NutritionalValuesModal';

export interface Coordinates {
    startY: number;
    startX: number;
    endX: number;
    endY: number;
}

export const menuTypes = [
    'product',
    'displayGroup',
    'function',
    'productCombo',
] as const;

export interface BaseMenuItem {
    id: string;
    position: Coordinates;
    active: boolean;
    type: (typeof menuTypes)[number];
}
export interface SEOValues {
    title: string;
    description: string;
    permalink: string;
}

export interface SocialMediaValues {
    linkTitle: string;
    linkDescription: string;
}

export interface ItemPrepValues {
    useLocationDefault?: boolean;
    itemPrepTime?: number;
}

export interface ItemType {
    id?: string;
    color?: string;
    images?: UploadedFile[];
    displayName: string;
    price?: number;
    measureUnit?: 'BUC' | 'KG' | 'L' | 'PORTIE' | 'G';
    group?: string;
    sku?: string;
    gtin?: string;
    ean?: string;
    kitchenName?: string;
    vat?: 0 | 5 | 9 | 19;
    age?: number;
    description?: string;
    calories?: number;
    dietaryPreferences?: string[];
    allergens?: string[];
    nutritionalValues?: NutritionalValues;
    forceModify?: boolean;
    forceQuantity?: boolean;
    itemPrep?: ItemPrepValues;
    seo?: SEOValues;
    socialMedia?: SocialMediaValues;
    excludedFromDiscount?: boolean;
}

export type MenuItem = BaseMenuItem & ItemType;
export type MenuGroup = BaseMenuItem & {
    displayName: string;
    items: MenuItem[];
    color: string;
};
