import { useState } from 'react';

const useLocalStorage = <T>(
  key: string,
  defaultValue: T,
  transform?: (val: any) => T
) => {
  const [localStorageValue, setLocalStorageValue] = useState(() => {
    try {
      const value = localStorage.getItem(key);
      if (value) {
        return transform ? transform(JSON.parse(value)) : JSON.parse(value);
      } else {
        localStorage.setItem(key, JSON.stringify(defaultValue));
        return defaultValue;
      }
    } catch (error) {
      localStorage.setItem(key, JSON.stringify(defaultValue));
      return defaultValue;
    }
  });

  const setLocalStorageStateValue = (valueOrFn: T | ((val: T) => T)) => {
    let newValue;
    if (typeof valueOrFn === 'function') {
      newValue = (valueOrFn as (val: T) => T)(localStorageValue);
    } else {
      newValue = valueOrFn;
    }
    localStorage.setItem(key, JSON.stringify(newValue));
    setLocalStorageValue(newValue);
  };

  return [localStorageValue, setLocalStorageStateValue];
};

export { useLocalStorage };
