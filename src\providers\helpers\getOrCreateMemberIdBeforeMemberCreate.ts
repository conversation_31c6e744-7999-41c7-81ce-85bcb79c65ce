import { httpsCallable } from 'firebase/functions';
import { ResourceCallbacks } from 'react-admin';

import { functions } from '~/configs/firebaseConfig';
import { RESOURCES } from '../resources';

const getOrCreateMemberId = httpsCallable(
  functions,
  'callables-getOrCreateMemberId'
);

export const getOrCreateMemberIdBeforeMemberCreate: ResourceCallbacks = {
  resource: RESOURCES.TEAM_MEMBERS,
  beforeCreate: async (params, dataProvider, resource) => {
    if (!params.data.email) {
      throw new Error('Email is required');
    }
    if (!params.data.displayName) {
      throw new Error('Display name is required');
    }
    if (!params.meta?.memberId) {
      throw new Error('Member ID is required');
    }
    if (typeof dataProvider.getAccountId !== 'function') {
      throw new Error('Account ID is required');
    }
    const result = await getOrCreateMemberId({
      accountId: dataProvider.getAccountId(),
      email: params.data.email,
      displayName: params.data.displayName,
      firstName: params.data.firstName,
      lastName: params.data.lastName,
      memberId: params.meta.memberId,
      memberName: params.meta?.memberName ?? 'N/A',
    });
    const data: { value: string } = result.data as any;
    params.data.id = data.value;
    return params;
  },
};
