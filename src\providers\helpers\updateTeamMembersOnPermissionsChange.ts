import { DataProvider, ResourceCallbacks } from 'react-admin';

import { isObject } from '~/fake-provider/reports/utils/isObject';
import { RESOURCES } from '../resources';

const processPermissionsChangeOnTeamMembers = async (
  isMany: boolean,
  result: any,
  dataProvider: DataProvider,
  resource: any
) => {
  const idsWithChanges: string[] = [];
  if (isMany) {
    if (Array.isArray(result.data)) {
      result.data.forEach((item: unknown) => {
        if (isObject(item)) {
          if (typeof item.id === 'string') {
            idsWithChanges.push(item.id);
          }
        } else if (typeof item === 'string') {
          idsWithChanges.push(item);
        }
      });
    }
  } else {
    if (isObject(result.data) && typeof result.data.id === 'string') {
      idsWithChanges.push(result.data.id);
    }
  }
  if (idsWithChanges.length === 0) return;
  // update all members that have a role id in idsWithChanges to trigger a permissions change
  const membersWithChanges = await dataProvider
    .getList(RESOURCES.TEAM_MEMBERS, {
      pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
      filter: { roleId_eq_any: idsWithChanges },
    })
    .then(res => res.data)
    .catch(() => []);
  const memberIdsWithChanges = membersWithChanges.map(
    (member: any) => member.id
  );
  if (memberIdsWithChanges.length === 0) return;
  const refreshEvent = Math.floor(Math.random() * 1000000);
  await dataProvider.updateMany(RESOURCES.TEAM_MEMBERS, {
    ids: memberIdsWithChanges,
    data: { _re: refreshEvent },
  });
};

export const updateTeamMembersOnPermissionsChange: ResourceCallbacks = {
  resource: RESOURCES.PERMISSIONS,
  afterUpdate: async (result, dataProvider, resource) => {
    processPermissionsChangeOnTeamMembers(
      false,
      result,
      dataProvider,
      resource
    );
    return result;
  },
  afterUpdateMany: async (result, dataProvider, resource) => {
    processPermissionsChangeOnTeamMembers(true, result, dataProvider, resource);
    return result;
  },
  afterDelete: async (result, dataProvider, resource) => {
    processPermissionsChangeOnTeamMembers(
      false,
      result,
      dataProvider,
      resource
    );
    return result;
  },
  afterDeleteMany: async (result, dataProvider, resource) => {
    processPermissionsChangeOnTeamMembers(true, result, dataProvider, resource);
    return result;
  },
};
