import { useEffect, useMemo, useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  Divider,
  FormControlLabel,
  Typography,
} from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import ModalHeader from '../../../components/molecules/ModalHeader';

export interface Category {
  id: string;
  name: string;
  straightFire: boolean;
}

interface CategoriesEditProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: Category[]) => void;
  categoriesData: Category[];
}
export default function CategoriesEditModal({
  open,
  onClose,
  onSave,
  categoriesData,
}: CategoriesEditProps) {
  const [categories, setCategories] = useState<Category[]>(
    categoriesData ?? []
  );
  const [initialDataJSON, setInitialDataJSON] = useState(
    JSON.stringify(categoriesData ?? [])
  );
  const { t } = useTranslation('');
  useEffect(() => {
    if (initialDataJSON !== JSON.stringify(categoriesData)) {
      setInitialDataJSON(JSON.stringify(categoriesData ?? []));
      setCategories(categoriesData ?? []);
    }
  }, [categoriesData]);

  const isDisabled = useMemo(() => {
    return initialDataJSON === JSON.stringify(categories);
  }, [initialDataJSON, categories]);

  const handleCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    id: string
  ) => {
    const updatedCategories = categories.map(category =>
      category.id === id
        ? { ...category, straightFire: event.target.checked } // Toggle straightFire based on checkbox value
        : category
    );

    setCategories(updatedCategories);
  };

  const handleSave = () => {
    onSave(categories);
    setInitialDataJSON(JSON.stringify(categories));
    onClose();
  };

  return (
    <Dialog
      fullWidth={true}
      maxWidth={'sm'}
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <ModalHeader title="" handleClose={onClose} noBorder>
        <Button onClick={handleSave} variant="contained" disabled={isDisabled}>
          {t('shared.save')}
        </Button>
      </ModalHeader>
      <Box p={4} pt={1}>
        <Typography variant="h4">{t('devices.courses.editStraightFireCategories')}</Typography>
        <Typography variant="body2" mt={1
          
        }>
          {t('devices.courses.editStraightFireCategoriesDescription')}
        </Typography>

        <Typography variant="h4" mt={3}>
          {t('devices.courses.assignedCategories')}
        </Typography>
        <Typography variant="body2" mt={1}>
          {t('devices.courses.assignedCategoriesDescription')}
        </Typography>

        <Box mt={2} display="flex" flexDirection="column">
          {categories.map(category => (
            <>
              <FormControlLabel
                key={category.id}
                sx={{
                  alignItems: 'center',
                  borderBottom: 'solid 1px',
                  borderColor: 'custom.gray200',
                  ml: 0,
                  py: 0.5,
                }}
                label={<Typography variant="body2">{category.name}</Typography>}
                control={
                  <Checkbox
                    checked={category.straightFire}
                    onChange={e => handleCheckboxChange(e, category.id)}
                    inputProps={{ 'aria-label': 'controlled' }}
                  />
                }
              />
            </>
          ))}
        </Box>
      </Box>
    </Dialog>
  );
}
