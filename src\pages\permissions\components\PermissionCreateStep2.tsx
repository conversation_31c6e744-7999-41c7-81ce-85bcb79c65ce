import Subsection from '../../../components/molecules/Subsection';
import AccessPointsSelect from './AccessPointsSelect';
import { useTranslation } from 'react-i18next';

export default function PermissionCreateStep2() {
  const { t } = useTranslation();
  return (
    <>
      <Subsection
        title={t('createPermissions.step2.title')}
        subtitle={t('createPermissions.step2.subtitle')}
      >
        <AccessPointsSelect />
      </Subsection>
    </>
  );
}
