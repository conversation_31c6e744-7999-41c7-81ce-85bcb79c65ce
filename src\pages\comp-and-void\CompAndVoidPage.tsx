import { useEffect, useReducer, useState } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  Divider,
  TextField,
  Typography,
} from '@mui/material';
import { useUpdate } from 'react-admin';
import { useTranslation } from 'react-i18next';

import PageTitle from '~/components/molecules/PageTitle';
import SimpleList from '~/components/molecules/SimpleList';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';
import { RESOURCES, useGetListLocationsLive } from '~/providers/resources';
import ReasonForm from './ReasonForm';
import {
  addComp,
  addVoid,
  deleteComp,
  deleteVoid,
  editComp,
  editVoid,
  reducer,
  setReasons,
} from './reducer';

const Spacer = () => <Box sx={{ height: 20 }} />;

interface DialogState {
  open: boolean;
  type?: 'comp' | 'void';
  index?: number;
}

const initialReducerState = {
  compReasons: [],
  voidReasons: [],
};

export default function CompAndVoidPage() {
  const { t } = useTranslation();

  const [isLoading, setIsLoading] = useState(true);
  const [initialState, setInitialState] = useState(
    JSON.stringify(initialReducerState)
  );
  const [dialogState, setDialogState] = useState<DialogState>({ open: false });

  const [state, dispatch] = useReducer(reducer, initialReducerState);
  const { data: sellPoints } = useGetListLocationsLive();
  const { sellPointId, setSellPointId } = useGlobalResourceFilters();

  const [update] = useUpdate();
  const isEdit = dialogState.index !== undefined;

  // we change sellpoint - we need to set comp and void values
  useEffect(() => {
    if (sellPointId && sellPoints) {
      const sellPoint = sellPoints.find(({ id }) => id === sellPointId);
      dispatch(setReasons(sellPoint.compReasons, sellPoint.voidReasons));
      setInitialState(
        JSON.stringify({
          compReasons: sellPoint.compReasons,
          voidReasons: sellPoint.voidReasons,
        })
      );
      setIsLoading(false);
    }
  }, [sellPointId, sellPoints]);

  const handleSellPointChange = (_: any, newValue: any) => {
    setSellPointId(newValue ? newValue.id : null);
  };

  useEffect(() => {
    if (initialState !== JSON.stringify(state)) {
      update(RESOURCES.LOCATIONS, {
        id: sellPointId,
        data: {
          compReasons: state?.compReasons,
          voidReasons: state?.voidReasons,
        },
      });
      setInitialState(JSON.stringify(state));
    }
  }, [state]);

  const handleFormSave = (newValue: string) => {
    if (isEdit) {
      const f = dialogState.type === 'comp' ? editComp : editVoid;
      dispatch(f(dialogState.index ?? 0, newValue));
      setDialogState({ open: false });
    } else {
      const f = dialogState.type === 'comp' ? addComp : addVoid;
      dispatch(f(newValue));
      setDialogState({ open: false });
    }
  };

  const handleFormDelete = () => {
    const f = dialogState.type === 'comp' ? deleteComp : deleteVoid;
    if (dialogState.index !== undefined) {
      dispatch(f(dialogState.index));
      setDialogState({ open: false });
    }
  };

  return (
    <>
      <Box p={2}>
        <PageTitle
          sx={{
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
          title={t('compAndVoidPage.title')}
          description={
            <>
              {t('compAndVoidPage.description')}
              <a
                href="https://selio.io/support-center"
                target="_blank"
                rel="noreferrer"
              >
                {t('support.support-link')}
              </a>
            </>
          }
          hideBorder
          doNotPrint
        />
        <Autocomplete
          sx={{ maxWidth: 300 }}
          options={sellPoints ?? []}
          getOptionLabel={option => option.name}
          value={sellPoints?.find(({ id }) => id === sellPointId) ?? null}
          onChange={handleSellPointChange}
          renderInput={params => <TextField {...params} label="Sellpoint" />}
          disableClearable
        />
        {isLoading ? (
          <CircularProgress sx={{ mx: 'auto', mt: 3 }} />
        ) : (
          <>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography variant="h5">
                {t('items.compAndVoid.compReasons')}
              </Typography>
              <Button
                onClick={() => {
                  setDialogState({
                    open: true,
                    type: 'comp',
                  });
                }}
                variant="contained"
              >
                {t('items.compAndVoid.createComp')}
              </Button>
            </Box>
            <Spacer />
            <SimpleList
              hideDivider={true}
              hideHeader={true}
              title="Name"
              data={state?.compReasons ?? []}
              onClick={idx => {
                setDialogState({
                  open: true,
                  type: 'comp',
                  index: idx,
                });
              }}
            />
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                pt: 5,
              }}
            >
              <Typography variant="h5">
                {t('items.compAndVoid.voidReasons')}
              </Typography>
              <Button
                onClick={() => {
                  setDialogState({
                    open: true,
                    type: 'void',
                  });
                }}
                variant="contained"
              >
                {t('items.compAndVoid.createVoid')}
              </Button>
            </Box>
            <Spacer />
            <SimpleList
              hideDivider={true}
              hideHeader={true}
              title="Name"
              data={state?.voidReasons ?? []}
              onClick={idx => {
                setDialogState({
                  open: true,
                  type: 'void',
                  index: idx,
                });
              }}
            />
          </>
        )}
      </Box>

      <Dialog
        fullWidth={true}
        maxWidth={'sm'}
        open={dialogState.open}
        onClose={() => setDialogState({ open: false })}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <ReasonForm
          onClose={() => setDialogState({ open: false })}
          reasonType={dialogState.type}
          onSave={handleFormSave}
          onDelete={handleFormDelete}
          isEdit={isEdit}
          initialValue={
            isEdit
              ? state?.[
                  dialogState.type === 'comp' ? 'compReasons' : 'voidReasons'
                ][dialogState.index ?? 0]
              : undefined
          }
        />
      </Dialog>
    </>
  );
}
