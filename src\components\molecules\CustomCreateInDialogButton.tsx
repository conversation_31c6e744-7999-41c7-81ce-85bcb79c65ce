import React, { useState } from 'react';
import ContentAdd from '@mui/icons-material/Add';
import { FormDialogButton, FormDialogButtonProps } from '@react-admin/ra-form-layout';
import { CreateDialog, CreateDialogProps } from '@react-admin/ra-form-layout';
import { CustomFormDialogButton } from './CustomFormDialogButton';

/**
 * A component which creates a `<CreateDialog>`, along with a `<Button>` to open it.
 * This component is also responsible for managing the open/close state of the Dialog
 * (using an internal state, not the router).
 *
 * @example
 * const createButton = (
 *  <CreateInDialogButton fullWidth maxWidth="md">
 *      <SimpleForm>
 *          <TextInput source="first_name" validate={required()} fullWidth />
 *      </SimpleForm>
 *  </CreateInDialogButton>
 * );
 */
export const CustomCreateInDialogButton = ({open, setOpen, label, ButtonProps, inline, icon, ...createDialogProps}: any) => {

    const createDialog = <CreateDialog {...createDialogProps} isOpen={open} />;

    return (
        <CustomFormDialogButton
            setOpen={setOpen}
            icon={icon}
            label={label}
            dialog={createDialog}
            inline={inline}
            ButtonProps={ButtonProps}
        />
    );
};

const defaultIcon = <ContentAdd />;

export type CreateInDialogButtonProps = Omit<FormDialogButtonProps, 'dialog'> &
    CreateDialogProps;
