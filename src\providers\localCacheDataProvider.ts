/**
 * Local Data Provider for React Admin
 *
 * This data provider uses localforage to persist data in the browser's storage
 * (IndexedDB, WebSQL, or localStorage depending on browser support).
 *
 * It wraps around ra-data-fakerest for in-memory operations and adds persistence
 * by saving changes to localforage after each write operation.
 *
 * All delete operations are real deletes (not soft deletes), removing items from the array.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */ // Disable any for this file due to fakerest interaction

import { debounce, DebouncedFunc } from 'lodash';
import {
  CreateParams,
  DataProvider,
  DeleteManyParams,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetManyParams,
  GetManyReferenceParams,
  GetOneParams,
  GetOneResult,
  Identifier,
  RaRecord,
  UpdateManyParams,
  UpdateParams,
} from 'ra-core';

import { generateFirestoreId } from '~/utils/generateFirestoreId';
import { measureUnits } from './data/measureUnits';
import fakeRestDataProvider from './fakerestDataProvider';
import { RESOURCES, ResourcesInfo, resourcesInfo } from './resources';
import { LOCAL_FORAGE_DATABASE_NAME } from './utils/constants';
import { createEncryptedInstance } from './utils/encryptedLocalForage';

export interface LocalCacheDataProvider extends DataProvider {
  getLastUpdateTimestamp: (
    resource: string,
    resourceMeta: { [key: string]: any } | undefined
  ) => Promise<number | null>;
  cleanup: () => void;
  generateFirestoreId: () => string;
  getAccountId: () => string;
}

// we need to filter resourcesInfo to exclude those that have dataProvider different from 'hybrid'
// and from those that remain exclude those with type embeddedArray or with type em
// TODO!
const availableResources = Object.keys(resourcesInfo);

// Store reference to the current handler function so we can remove it later
let currentBeforeUnloadHandler: (() => void) | null = null;

const getResourceNameWithMeta = (
  resource: string,
  meta?: { [key: string]: any }
) => {
  const resourceInfo = resourcesInfo[resource as keyof ResourcesInfo];
  if (resourceInfo !== undefined) {
    // check if the resource has meta fields
    // if the resource has meta fields, return the resource name with meta $resource|metaField1Value|metaField2Value...
    // if some meta fields are missing, throw an error
    if (resourceInfo.needsMetaFields?.length) {
      const metaFields = resourceInfo.needsMetaFields.map(
        metaField => meta?.[metaField]
      );
      if (metaFields.some(metaField => metaField === undefined)) {
        throw new Error(
          `Missing meta fields ${resourceInfo.needsMetaFields} for resource ${resource}`
        );
      }
      return `${resource}|${metaFields.join('|')}`;
    } else {
      return resource;
    }
  } else {
    return resource;
  }
};

export const getLocalCacheDataProvider = async (
  accountId: string
): Promise<LocalCacheDataProvider> => {
  if (!accountId) {
    throw new Error('Account ID is required for local cache data provider.');
  }

  const dataLocalForageInstance = createEncryptedInstance(accountId, {
    name: LOCAL_FORAGE_DATABASE_NAME,
    storeName: accountId,
  });

  let data: Record<string, any[]> | undefined;
  let baseDataProvider: DataProvider | undefined;
  let initializePromise: Promise<void> | undefined;

  const getLocalForageData = async (): Promise<Record<string, any[]>> => {
    // Get all keys from localforage
    const keys = await dataLocalForageInstance.keys();

    const resources = keys.filter(key =>
      availableResources.some(resource => key.startsWith(resource))
    );

    const localForageData: Record<string, any[]> = {};

    // Load data for each resource
    for (const resource of resources) {
      const resourceData = await dataLocalForageInstance.getItem(resource);
      // Ensure the retrieved item is treated as an array
      if (
        typeof resourceData === 'object' &&
        resourceData !== null &&
        Array.isArray(resourceData)
      ) {
        localForageData[resource] = resourceData;
      } else {
        localForageData[resource] = [];
      }
    }
    return localForageData;
  };

  const initialize = async () => {
    if (!initializePromise) {
      initializePromise = initializeProvider();
    }
    return initializePromise;
  };

  const initializeProvider = async () => {
    // Load data from localforage or use default data if none exists
    const localForageData = await getLocalForageData();
    // append the data with the default data like measureUnits, etc.
    data = {
      ...(localForageData ?? {}),
      [RESOURCES.MEASURE_UNITS]: measureUnits,
    };
    // Create the underlying fakerest provider with the loaded data
    baseDataProvider = fakeRestDataProvider(data, false) as DataProvider;
  };

  // Create a cache for debounced update functions per resource
  const debouncedUpdates: Record<
    string,
    DebouncedFunc<() => Promise<void>>
  > = {};
  const DEBOUNCE_WAIT = 300;
  const DEBOUNCE_MAX_WAIT = 1000;

  // Get or create a debounced update function for a specific resource
  const getDebouncedUpdate = (resource: string) => {
    if (!debouncedUpdates[resource]) {
      debouncedUpdates[resource] = debounce(
        async () => {
          if (!data) {
            throw new Error('The dataProvider is not initialized.');
          }
          try {
            await dataLocalForageInstance.setItem(resource, data[resource]);
          } catch (error) {
            console.error(`Failed to update resource ${resource}:`, error);
          }
        },
        DEBOUNCE_WAIT,
        { maxWait: DEBOUNCE_MAX_WAIT }
      ); // maxWait ensures it runs at least every second
    }
    return debouncedUpdates[resource];
  };

  const updateLocalResource = async (resource: string): Promise<boolean> => {
    if (!data) {
      throw new Error('The dataProvider is not initialized.');
    }

    // Get or create the debounced update function for this resource
    const debouncedUpdate = getDebouncedUpdate(resource);

    // Schedule the update
    debouncedUpdate();

    return true;
  };

  // Function to flush all pending updates
  const flushAllPendingUpdates = async () => {
    const resources = Object.keys(debouncedUpdates);
    for (const resource of resources) {
      if (
        debouncedUpdates[resource] &&
        typeof debouncedUpdates[resource].flush === 'function'
      ) {
        debouncedUpdates[resource].flush();
      }
    }
  };
  // Clean up any existing handler first
  if (currentBeforeUnloadHandler !== null) {
    // Flush any pending updates from the previous provider
    flushAllPendingUpdates();

    // Remove the existing event listener
    window.removeEventListener('beforeunload', currentBeforeUnloadHandler);
    currentBeforeUnloadHandler = null;
  }
  // Function to handle beforeunload event
  currentBeforeUnloadHandler = () => {
    flushAllPendingUpdates();
  };
  // Add the new event listener
  window.addEventListener('beforeunload', currentBeforeUnloadHandler);

  // Add cleanup function to the provider
  const cleanup = () => {
    // Flush pending updates
    flushAllPendingUpdates();

    // Remove the event listener
    if (currentBeforeUnloadHandler) {
      window.removeEventListener('beforeunload', currentBeforeUnloadHandler);
      currentBeforeUnloadHandler = null;
    }
  };

  return {
    getList: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: GetListParams
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      const newResource = getResourceNameWithMeta(resource, params.meta);
      const { page = 1, perPage = Number.MAX_SAFE_INTEGER } =
        params.pagination ?? {};
      const { field = 'id', order = 'ASC' } = params.sort ?? {};
      const newParams = {
        ...params,
        pagination: { page, perPage },
        sort: { field, order },
      };
      return baseDataProvider
        .getList<RecordType>(newResource, newParams)
        .catch(error => {
          if (error.code === 1) {
            return { data: [], total: 0 };
          } else {
            throw error;
          }
        });
    },
    getOne: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: GetOneParams
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      try {
        const newResource = getResourceNameWithMeta(resource, params.meta);
        return baseDataProvider.getOne<RecordType>(newResource, params);
      } catch (error) {
        // TODO! edit dialog with meta fields calls this method without meta fields (floorPlans)
        // console.warn('Error in getOne:', error);
        return Promise.resolve<GetOneResult>({ data: { id: params.id } });
      }
    },
    getMany: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: GetManyParams<RecordType>
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      const newResource = getResourceNameWithMeta(resource, params.meta);
      // return baseDataProvider.getMany<RecordType>(newResource, params);
      // we comment the line above and use getList instead of getMany
      // because getMany throws an error if the record is not found

      // extract ids from params and the rest of the params
      const { ids, ...restParams } = params;
      const newParams: GetListParams = {
        ...restParams,
        pagination: { page: 1, perPage: Number.MAX_SAFE_INTEGER },
        sort: { field: 'id', order: 'ASC' },
        filter: { id_eq_any: ids },
      };
      return baseDataProvider.getList<RecordType>(newResource, newParams);
    },
    getManyReference: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: GetManyReferenceParams
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      const newResource = getResourceNameWithMeta(resource, params.meta);
      const { page = 1, perPage = Number.MAX_SAFE_INTEGER } =
        params.pagination ?? {};
      const { field = 'id', order = 'ASC' } = params.sort ?? {};
      const newParams = {
        ...params,
        pagination: { page, perPage },
        sort: { field, order },
      };
      return baseDataProvider
        .getManyReference<RecordType>(newResource, newParams)
        .catch(error => {
          if (error.code === 1) {
            return { data: [], total: 0 };
          } else {
            throw error;
          }
        });
    },
    update: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: UpdateParams
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      console.log('LocalCacheDP: update', resource, params);
      const newResource = getResourceNameWithMeta(resource, params.meta);
      const index = data![newResource].findIndex(
        (record: { id: any }) => record.id === params.id
      );
      data![newResource][index] = {
        ...data![newResource][index],
        ...params.data,
      };

      await updateLocalResource(newResource);

      return baseDataProvider.update<RecordType>(newResource, params);
    },
    updateMany: async (resource: string, params: UpdateManyParams) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      const newResource = getResourceNameWithMeta(resource, params.meta);
      // create an array of ids that are found in the cache
      const foundIds: Identifier[] = [];
      params.ids.forEach((id: Identifier) => {
        const index = data![newResource].findIndex(
          (record: { id: any }) => record.id === id
        );
        if (index !== -1) {
          // add the id to the found ids array
          foundIds.push(id);
          // update the record in the cache
          data![newResource][index] = {
            ...data![newResource][index],
            ...params.data,
          };
        }
      });

      if (foundIds.length) {
        await updateLocalResource(newResource);

        // update the ids in the params to only include the found ids
        // so that fakerest does not throw an error
        params.ids = foundIds;

        return baseDataProvider.updateMany(newResource, params);
      }
      return { data: [] };
    },
    create: async <
      RecordType extends Omit<RaRecord, 'id'> = Omit<RaRecord, 'id'>,
    >(
      resource: string,
      params: CreateParams
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      console.log('LocalCacheDP: create', resource, params);
      const newResource = getResourceNameWithMeta(resource, params.meta);
      return baseDataProvider
        .create<RecordType>(newResource, params)
        .then(async response => {
          if (!data) {
            throw new Error('The dataProvider is not initialized.');
          }

          if (!Object.prototype.hasOwnProperty.call(data, newResource)) {
            data[newResource] = [];
          }

          data[newResource].push(response.data);

          await updateLocalResource(newResource);

          return response;
        });
    },
    delete: async <RecordType extends RaRecord = RaRecord>(
      resource: string,
      params: DeleteParams<RecordType>
    ) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      console.log('LocalCacheDP: delete', resource, params);
      const newResource = getResourceNameWithMeta(resource, params.meta);
      const index = data![newResource].findIndex(
        (record: { id: any }) => record.id === params.id
      );
      if (index !== -1) {
        data![newResource].splice(index, 1);
        // update localforage only if the record exists in memory
        await updateLocalResource(newResource);
      }
      // we need to use a try catch here because fakerest throws an error if the record is not found
      // and in our hybrid provider when we delete a record the listener acts first and deletes the record from the cache
      // and then the delete request is sent to the local cache provider and it fails because the record is not found
      // so we need to catch the error and return the previous data as the deleted record
      try {
        const result = await baseDataProvider.delete<RecordType>(
          newResource,
          params
        );
        return result;
      } catch (error) {
        return Promise.resolve<DeleteResult>({
          data: { id: params.id, ...params.previousData },
        });
      }
    },
    deleteMany: async (resource: string, params: DeleteManyParams) => {
      await initialize();
      if (!baseDataProvider) {
        throw new Error('The dataProvider is not initialized.');
      }
      const newResource = getResourceNameWithMeta(resource, params.meta);
      const foundIds: Identifier[] = [];
      params.ids.forEach((id: Identifier) => {
        const index = data![newResource].findIndex(
          (record: { id: any }) => record.id === id
        );
        if (index !== -1) {
          // add the id to the found ids array
          foundIds.push(id);
          // delete the record from the cache
          data![newResource].splice(index, 1);
        }
      });

      if (foundIds.length) {
        await updateLocalResource(newResource);

        // update the ids in the params to only include the found ids
        // so that fakerest does not throw an error
        params.ids = foundIds;

        return baseDataProvider.deleteMany(newResource, params);
      }
      return { data: [] };
    },
    subscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    unsubscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    publish: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    getLastUpdateTimestamp: async (
      resource: string,
      resourceMeta: { [key: string]: any } | undefined = undefined
    ): Promise<number | null> => {
      const newResource = getResourceNameWithMeta(resource, resourceMeta);
      return (dataLocalForageInstance as any).getLastUpdateTimestamp(
        newResource
      );
    },
    cleanup,
    generateFirestoreId: generateFirestoreId,
    getAccountId: () => accountId,
  };
};
