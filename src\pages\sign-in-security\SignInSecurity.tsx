import React, { useEffect, useState } from 'react';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { Box, Button, MenuItem, TextField, Typography } from '@mui/material';
import { useDataProvider } from 'react-admin';
import { Link } from '@mui/material';
import PageTitle from '~/components/molecules/PageTitle';
import AccountItem from '../../components/molecules/AccountItem';
import CustomModal from '../../components/molecules/CustomModal';
import Subsection from '../../components/molecules/Subsection';
import { useTranslation } from 'react-i18next';
interface AccountInfo {
  email: string;
  password: string;
  phone?: {
    number: string;
    prefix: string;
  };
}

const SignInSecurity: React.FC = () => {
  const { t } = useTranslation();
  const dataProvider = useDataProvider();
  const [accountInfo, setAccountInfo] = useState<AccountInfo>(
    {} as AccountInfo
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [emailState, setEmailState] = useState("");
  // const [confirmEmailState, setConfirmEmailState] = useState("");
  // const [emailsMatch, setEmailsMatch] = useState(true);
  // const [emailModal, setEmailModal] = useState(false);
  const [passwordModal, setPasswordModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [phoneState, setPhoneState] = useState('');
  const [phoneModal, setPhoneModal] = useState(false);
  const [countryCode, setCountryCode] = useState('+40');
  const [countryCodes] = useState([
    { code: '+40', flag: '🇷🇴' },
    { code: '+44', flag: '🇬🇧' },
    { code: '+33', flag: '🇫🇷' },
    { code: '+34', flag: '🇪🇸' },
  ]);

  useEffect(() => {
    const fetchAccountInfo = async () => {
      try {
        const { data } = await dataProvider.getOne('accountInfo', { id: '1' });
        setAccountInfo(data);
        // setEmailState(data.email);
        setPhoneState(data.phone?.number || '');
        setCountryCode(data.phone?.prefix || '+40');
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAccountInfo();
  }, [dataProvider]);

  const updateAccountInfo = async (updatedInfo: AccountInfo) => {
    try {
      await dataProvider.update('accountInfo', {
        id: '1',
        data: updatedInfo,
        previousData: accountInfo,
      });
      setAccountInfo(updatedInfo);
      closeAllModals();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const closeAllModals = () => {
    // handleCloseEmail();
    handleClosePassword();
    handleClosePhone();
  };

  const handleClosePhone = () => {
    setPhoneModal(false);
    resetPhoneState();
  };

  const openPhoneModal = () => {
    resetPhoneState();
    setPhoneModal(true);
  };

  const resetPhoneState = () => {
    setPhoneState(accountInfo.phone?.number || '');
    setCountryCode(accountInfo.phone?.prefix || '+40');
  };

  const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneState(event.target.value);
  };

  const handleCountryCodeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCountryCode(event.target.value);
  };

  const items = [
    {
      title: 'Email',
      value: accountInfo.email || '-',
      buttonText: '',
      buttonAction: () => {},
    },
    {
      title: t('signInSecurity.phone'),
      value: accountInfo.phone
        ? `${accountInfo.phone.prefix} ${accountInfo.phone.number}`
        : '-',
      buttonText: accountInfo.phone ? t('signInSecurity.update') : t('signInSecurity.add'),
      buttonAction: openPhoneModal,
    },
    {
      title: t('signInSecurity.password'),
      value: '********',
      buttonText: t('signInSecurity.update'),
      buttonAction: () => setPasswordModal(true),
    },
  ];

  const Pos = {
    title: '666888',
    value: "Owner at X SRL",
    buttonText: '',
    buttonAction: () => {},
  };

  // const handleCloseEmail = () => {
  //   setEmailModal(false);
  //   setEmailState(accountInfo.email);
  //   setConfirmEmailState("");
  // };

  const handleClosePassword = () => {
    setError(null);
    setPasswordModal(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  // const handleEmailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   setEmailState(event.target.value);
  // };

  // const handleConfirmEmailChange = (
  //   event: React.ChangeEvent<HTMLInputElement>
  // ) => {
  //   setConfirmEmailState(event.target.value);
  // };

  const handleCurrentPasswordChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCurrentPassword(event.target.value);
    setError(null);
  };

  const handleNewPasswordChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setNewPassword(event.target.value);
  };

  const handleConfirmPasswordChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfirmPassword(event.target.value);
  };

  // useEffect(() => {
  //   setEmailsMatch(emailState === confirmEmailState);
  // }, [emailState, confirmEmailState]);

  useEffect(() => {
    setPasswordsMatch(newPassword === confirmPassword);
  }, [newPassword, confirmPassword]);

  const handlePasswordSubmit = async () => {
    if (accountInfo.password === currentPassword) {
      await updateAccountInfo({ ...accountInfo, password: newPassword });
      setError(null);
    } else {
      setError('Current password is incorrect');
    }
  };

  return (
    <>
      <Box p={2} sx={{ mt: 2, width: '100%', maxWidth: '600px' }}>
        <PageTitle
          title={t('signInSecurity.title')}
          description={
            <>
              {t('signInSecurity.description')}
              <a
                href="https://selio.io/support-center"
                target="_blank"
                rel="noreferrer"
              >
                {t('support.support-link')}
              </a>
            </>
          }
        />
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            mb: 8,
          }}
        >
          {items.map((item, index) => (
            <AccountItem key={index} item={item} />
          ))}
        </Box>
        <Subsection
          title={t('signInSecurity.personalPOSPasscode') + ' Let\'s burger'}
          subtitle={t('signInSecurity.personalPOSPasscodeSubtitle')}
        >
          <AccountItem item={Pos} type="code" />
        </Subsection>
        <Box sx={{ mt: 9 }}>
          <Subsection
            title={t('signInSecurity.signOutEverywhere')}
            subtitle={t('signInSecurity.signOutEverywhereSubtitle')}
          >
            <Button
              sx={{
                color: '#BF0120',
                border: '1.5px solid #BF0120',
                p: 1.2,
                borderRadius: '8px',
                '&.MuiButtonBase-root:hover': {
                  bgcolor: 'transparent',
                  opacity: 0.7,
                },
              }}
            >
              {t('signInSecurity.signOutEverywhere')}
            </Button>
          </Subsection>
        </Box>
      </Box>

      <CustomModal
        modalTextButton="Confirm"
        onSubmit={() =>
          updateAccountInfo({
            ...accountInfo,
            phone: { number: phoneState, prefix: countryCode },
          })
        }
        modalHeaderTile={
          phoneState ? t('signInSecurity.changePhone') : t('signInSecurity.addPhone')
        }
        open={phoneModal}
        handleClose={handleClosePhone}
        confirmDisabled={!phoneState || !countryCode}
        fullWidth
        maxWidth="sm"
      >
        <Box sx={{ p: 2 }}>
          <Typography
            sx={{
              fontWeight: 300,
              lineHeight: { xs: 1.5, md: 2 },
              mb: { xs: 1, md: 4 },
            }}
          >
            {t('signInSecurity.phoneDescription')}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              select
              sx={{ width: { xs: 140, md: 120 } }}
              value={countryCode}
              onChange={handleCountryCodeChange}
              margin="normal"
            >
              {countryCodes.map(country => (
                <MenuItem key={country.code} value={country.code}>
                  {country.flag} {country.code}
                </MenuItem>
              ))}
            </TextField>
            <TextField
              label={t('signInSecurity.phoneNumber')}
              type="number"
              value={phoneState}
              onChange={handlePhoneChange}
              fullWidth
              margin="normal"
            />
          </Box>
          <Typography
            sx={{
              lineHeight: 1.5,
              mt: 1,
              fontSize: { xs: 12, md: 14 },
              fontWeight: 300,
            }}
          >
            {t('signInSecurity.phoneTerms')}
            <Box
              sx={{ display: 'inline', color: '#0169FF', cursor: 'pointer' }}
            >
              <Link href="https://selio.io/privacy-policy/" target="_blank" rel="noreferrer">
                {t('signInSecurity.privacyPolicy')}
              </Link>
            </Box>{' '}
            {t('signInSecurity.and')}
            <Box
              sx={{ display: 'inline', color: '#0169FF', cursor: 'pointer' }}
            >
              <Link href="https://selio.io/terms-of-service/" target="_blank" rel="noreferrer">
                {t('signInSecurity.termsAndConditions')}
              </Link>
            </Box>
            .
          </Typography>
        </Box>
      </CustomModal>

      {/* <CustomModal
        modalTextButton="Confirm"
        onSubmit={() =>
          updateAccountInfo({ ...accountInfo, email: emailState })
        }
        modalHeaderTile="Change email address"
        open={emailModal}
        handleClose={handleCloseEmail}
        confirmDisabled={!emailState || !confirmEmailState || !emailsMatch}
        fullWidth
        maxWidth="sm"
      >
        <Box sx={{ p: 2 }}>
          <Typography sx={{ fontWeight: 300, lineHeight: 2, mb: 4 }}>
            Enter an email address to associate with your account.
          </Typography>
          <TextField
            label="Email Address"
            type="email"
            value={emailState}
            onChange={handleEmailChange}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Confirm Email Address"
            type="email"
            value={confirmEmailState}
            onChange={handleConfirmEmailChange}
            fullWidth
            margin="normal"
            helperText={!emailsMatch && "Email addresses do not match"}
          />
        </Box>
      </CustomModal> */}

      <CustomModal
        modalTextButton="Confirm"
        onSubmit={handlePasswordSubmit}
        modalHeaderTile={t('signInSecurity.changePassword')}
        open={passwordModal}
        handleClose={handleClosePassword}
        confirmDisabled={
          !currentPassword ||
          !newPassword ||
          !confirmPassword ||
          !passwordsMatch
        }
        fullWidth
        maxWidth="sm"
      >
        <Box sx={{ p: 2 }}>
          <Typography sx={{ fontWeight: 300, lineHeight: 2, mb: 4 }}>
            {t('signInSecurity.changePasswordDescription')}
          </Typography>
          <TextField
            label={t('signInSecurity.currentPassword')}
            type={showCurrentPassword ? 'text' : 'password'}
            value={currentPassword}
            onChange={handleCurrentPasswordChange}
            fullWidth
            error={!!error}
            margin="normal"
            InputProps={{
              endAdornment: (
                <Button
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <VisibilityOffIcon />
                  ) : (
                    <VisibilityIcon />
                  )}
                </Button>
              ),
            }}
          />
          <Typography sx={{ fontSize: 12, color: '#DC4437' }}>
            {error}
          </Typography>
          <TextField
            label={t('signInSecurity.newPassword')}
            type={showNewPassword ? 'text' : 'password'}
            value={newPassword}
            onChange={handleNewPasswordChange}
            fullWidth
            margin="normal"
            InputProps={{
              endAdornment: (
                <Button onClick={() => setShowNewPassword(!showNewPassword)}>
                  {showNewPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </Button>
              ),
            }}
          />
          <TextField
            label={t('signInSecurity.confirmNewPassword')}
            type={showConfirmPassword ? 'text' : 'password'}
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            fullWidth
            margin="normal"
            error={!passwordsMatch}
            helperText={!passwordsMatch && t('signInSecurity.passwordsDoNotMatch')}
            InputProps={{
              endAdornment: (
                <Button
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <VisibilityOffIcon />
                  ) : (
                    <VisibilityIcon />
                  )}
                </Button>
              ),
            }}
          />
        </Box>
      </CustomModal>
    </>
  );
};

export default SignInSecurity;
