import { useEffect } from 'react';
import { Box, Theme, useMediaQuery } from '@mui/material';
import { Sidebar, SidebarProps, useSidebarState } from 'react-admin';
import { useLocation } from 'react-router-dom';

import { RESOURCES } from '~/providers/resources';
import MainMenu from '../menus/MainMenu';

export default function CustomSidebar(props: SidebarProps) {
  const location = useLocation();
  const [open, setOpen] = useSidebarState();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  const hasSecondaryMenu = ![
    '/',
    '/shifts',
    '/payroll',
  ].includes(location.pathname);

  useEffect(() => {
    if (hasSecondaryMenu) {
      setOpen(false);
    } else if (!isXSmall) {
      setOpen(true);
    }
  }, [hasSecondaryMenu]);

  return (
    <Sidebar
      {...props}
      appBarAlwaysOn
      sx={{
        zIndex: (hasSecondaryMenu && open) || (open && isXSmall) ? 3 : 0,
        transition:
          hasSecondaryMenu && open ? 'z-index 0s ease' : 'z-index 0.4s 0s',
        '& .RaSidebar-fixed': {},
      }}
    >
      <>
        <Box
          sx={{
            display: 'flex',
            height: '100%',
          }}
        >
          {/* backdrop for main menu */}
          <Box
            onClick={() => setOpen(false)}
            sx={{
              position: 'fixed',
              width: '100vw',
              height: '100%',
              background: hasSecondaryMenu && open ? '#00000066' : '#00000000',
              zIndex: hasSecondaryMenu && open ? 5 : -1,
              transition:
                hasSecondaryMenu && open
                  ? 'background 0.4s ease'
                  : 'background 0.4s ease, z-index 0.4s 0.4s',
            }}
          />
          <Box
            sx={{
              bgcolor: 'background.default',
              zIndex: 6,
              height: '100%',
            }}
          >
            {/* main menu */}
            <Box
              sx={{
                position:
                  hasSecondaryMenu && isXSmall ? 'absolute' : 'relative',
                zIndex: 6,
                bgcolor: 'background.default',
                height: '100%',
              }}
            >
              <MainMenu />
            </Box>
          </Box>
        </Box>
      </>
    </Sidebar>
  );
}
