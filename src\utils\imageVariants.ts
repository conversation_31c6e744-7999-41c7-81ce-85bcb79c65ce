/**
 * Utilities for handling public image variants and URLs
 */

import { ref, listAll } from 'firebase/storage';
import { UploadedFile } from '~/types/fileUpload';
import { generateFileUrl } from '~/utils/urlGeneration';
import { getStorageInstanceForFile } from '~/utils/bucketManager';

/**
 * Check if a file should have image variants (public images with client-side processing)
 * Since we now always use folder structure for images, we check if it's an image type 'i'
 */
export const hasImageVariants = (file: UploadedFile): boolean => {
    return file.t === 'i';
};

/**
 * Get all available variants for a public image by listing the actual storage folder
 * Returns only the variants that actually exist in storage
 */
export const getImageVariants = async (file: UploadedFile): Promise<string[]> => {
    if (!hasImageVariants(file)) {
        return [];
    }

    try {
        // Get the appropriate storage instance (temp or permanent)
        const storage = getStorageInstanceForFile(file.t, file.x || false);
        const folderPath = `i/${file.fn}/`;
        const folderRef = ref(storage, folderPath);

        // List all files in the folder
        const listResult = await listAll(folderRef);

        // Extract variant names from file names
        const variants = listResult.items.map(itemRef => {
            const fileName = itemRef.name;
            // Remove file extension to get variant name
            return fileName.replace(/\.[^/.]+$/, '');
        });

        return variants;
    } catch (error) {
        // Fallback to common variants that might exist
        return ['original', 'thumbnail'];
    }
};

/**
 * Generate URL for a specific variant of a public image
 */
export const generateVariantUrl = async (
    file: UploadedFile,
    variant: string = 'thumbnail',
    format?: string
): Promise<string> => {
    if (!hasImageVariants(file)) {
        // Fallback to standard URL generation for non-variant images
        return generateFileUrl(file);
    }

    // Determine format based on variant
    let variantFormat = format;
    if (!variantFormat) {
        // Original keeps the original extension, others default to webp
        variantFormat = variant === 'original' ? file.e : 'webp';
    }

    // For public images (t: 'i'), keep fn clean but indicate it's a variant path internally
    // The fn field stays clean (just uniqueId), we handle folder structure in URL generation
    const variantFile: UploadedFile = {
        ...file,
        e: variantFormat,
        // fn remains clean - no slashes or variant names
        // Remove url property to force regeneration with variant option
        url: undefined,
    };

    const url = await generateFileUrl(variantFile, { imageVariant: variant as any });

    return url;
};

/**
 * Get the thumbnail URL for display purposes
 */
export const getThumbnailUrl = async (file: UploadedFile): Promise<string> => {
    if (hasImageVariants(file)) {
        return generateVariantUrl(file, 'thumbnail');
    }
    // For non-variant images, use the standard URL
    return generateFileUrl(file);
};

/**
 * Get display names for variants
 */
export const getVariantDisplayName = (variant: string): string => {
    const displayNames: Record<string, string> = {
        original: 'Original',
        thumbnail: 'Thumbnail (50×50)',
        square_s: 'Square Small (150×150)',
        square_m: 'Square Medium (300×300)',
        square_l: 'Square Large (600×600)',
        profile: 'Profile (200×200)',
        banner_small: 'Banner Small (150×50)',
        banner_medium: 'Banner Medium (300×100)',
        banner_large: 'Banner Large (600×200)',
        hero_small: 'Hero Small (320×180)',
        hero_medium: 'Hero Medium (640×360)',
        hero_large: 'Hero Large (1280×720)',
        portrait_small: 'Portrait Small (200×300)',
        portrait_medium: 'Portrait Medium (400×600)',
        ad_mobile_banner: 'Mobile Banner (320×50)',
        ad_mobile_fullscreen: 'Mobile Fullscreen (320×480)',
    };

    return displayNames[variant] || variant;
};

/**
 * Sort variants by importance for display
 * Priority: thumbnail first, original after, then by size importance
 */
export const sortVariantsByImportance = (variants: string[]): string[] => {
    const order = [
        'thumbnail',    // Always first (automatic thumbnail)
        'original',     // Always second (original file)
        'square_l',     // Large sizes first
        'square_m',
        'square_s',
        'profile',
        'hero_large',
        'hero_medium',
        'hero_small',
        'banner_large',
        'banner_medium',
        'banner_small',
        'portrait_medium',
        'portrait_small',
        'ad_mobile_fullscreen',
        'ad_mobile_banner',
    ];

    return variants.sort((a, b) => {
        const indexA = order.indexOf(a);
        const indexB = order.indexOf(b);

        // If both are in the order array, sort by order
        if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
        }

        // If only one is in the order array, it comes first
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;

        // If neither is in the order array, sort alphabetically
        return a.localeCompare(b);
    });
};

/**
 * Get the best variant for a given display context
 */
export const getBestVariantForContext = (
    file: UploadedFile,
    context: 'thumbnail' | 'preview' | 'fullsize' = 'preview'
): string => {
    if (!hasImageVariants(file)) {
        return 'original';
    }

    // Return variants that actually exist in our system
    switch (context) {
        case 'thumbnail':
            return 'thumbnail';

        case 'preview':
            return 'card';

        case 'fullsize':
            return 'detail';

        default:
            return 'original';
    }
};

/**
 * Check if a variant represents the original image
 */
export const isOriginalVariant = (variant: string): boolean => {
    return variant === 'original';
};

/**
 * Get file size estimation for a variant (approximate)
 */
export const getVariantSizeEstimate = (variant: string, originalSize?: number): string => {
    if (variant === 'original' && originalSize) {
        return `${(originalSize / 1024).toFixed(1)} KB`;
    }

    // Rough estimates based on typical compression ratios
    const estimates: Record<string, string> = {
        thumbnail: '~2 KB',
        square_s: '~15 KB',
        square_m: '~45 KB',
        square_l: '~120 KB',
        profile: '~25 KB',
        banner_small: '~8 KB',
        banner_medium: '~20 KB',
        banner_large: '~50 KB',
        hero_small: '~35 KB',
        hero_medium: '~80 KB',
        hero_large: '~200 KB',
        portrait_small: '~30 KB',
        portrait_medium: '~70 KB',
        ad_mobile_banner: '~6 KB',
        ad_mobile_fullscreen: '~40 KB',
    };

    return estimates[variant] || '~unknown';
};
