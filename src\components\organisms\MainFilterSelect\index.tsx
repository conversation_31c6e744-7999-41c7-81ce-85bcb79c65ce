import { useState } from 'react';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  Button,
  Checkbox,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useFilters } from '~/contexts/FilterContext';
import camelCaseToNormalWords from '~/utils/camelCaseToNormalWords';

export const MainFilterSelect = ({ options }: { options: string[] }) => {
  const { activeFilters, toggleFilter } = useFilters();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { t } = useTranslation();

  const open = Boolean(anchorEl);

  const handleButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(open ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleOptionToggle = (option: string) => {
    toggleFilter(option);
  };

  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  return (
    <>
      <Button
        sx={{ whiteSpace: 'nowrap', height: '50px' }}
        onClick={handleButtonClick}
      >
        <FilterListIcon sx={{ mr: isXSmall ? 0 : 1 }} /> {isXSmall ? '' : t('reportsPage.addFilter')}
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        {options.map(option => (
          <MenuItem
            key={option}
            onClick={() => handleOptionToggle(option)}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'transparent',
              },
              '&:hover': {
                backgroundColor: '#f5f5f5',
              },
            }}
          >
            <ListItemIcon>
              <Checkbox
                checked={activeFilters.includes(option)}
                edge="start"
                sx={
                  {
                    // color: 'b',
                    // '&.Mui-checked': {
                    //   color: 'black',
                    // },
                  }
                }
              />
            </ListItemIcon>
            <ListItemText
              primary={
                <Typography sx={{ fontSize: '0.9rem', color: '#0064F0' }}>
                  {t('transactionsPage.' + option) ||
                    camelCaseToNormalWords(option)}
                </Typography>
              }
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
