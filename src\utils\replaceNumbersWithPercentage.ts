export default function replaceNumberWithPercentage(
  input: string,
  baseValue = 10000
): string {
  if (!input) return '';
  return input.replace(/\((\d+(\.\d+)?)\)/g, (_, number) => {
    const percentage = (parseFloat(number) / baseValue) * 100;
    const truncatedPercentage = Math.floor(percentage * 100) / 100;

    return `(${truncatedPercentage % 1 === 0 ? truncatedPercentage : truncatedPercentage.toString().replace(/(\.\d*?[1-9])0+$/, '$1')}%)`;
  });
}
