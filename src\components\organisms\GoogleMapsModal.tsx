import React, { useEffect, useRef, useState } from 'react';
import { LocationOn, MyLocation } from '@mui/icons-material';
import {
  Alert,
  Box,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  Grid,
  IconButton,
  Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';

import {
  createBoundsFromRadius,
  formatCoordinates,
  getCurrentLocation,
  initializeGoogleMaps,
} from '../../utils/googleMapsUtils';
import ModalHeader from '../molecules/ModalHeader';
import CloseWithConfirmationModal from './CloseWithConfirmationModal';

interface GoogleMapsModalProps {
  open: boolean;
  onClose: () => void;
  location: { lat: number; lng: number };
  deliveryRange: number; // This represents ordering range (distance in meters)
  onLocationChange: (location: { lat: number; lng: number }) => void;
  title?: string;
}

export const GoogleMapsModal: React.FC<GoogleMapsModalProps> = ({
  open,
  onClose,
  location,
  deliveryRange,
  onLocationChange,
  title = 'Select Location',
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const circleRef = useRef<any>(null);
  const isAdvancedMarkerRef = useRef<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mapsApi, setMapsApi] = useState<any>(null);
  const [currentLocation, setCurrentLocation] = useState(location);
  const [originalLocation, setOriginalLocation] = useState(location);
  const [hasChanges, setHasChanges] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Mobile detection for fullscreen behavior
  const isMobile = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));

  // Initialize Google Maps when modal opens
  useEffect(() => {
    if (open && (!mapsApi || !mapInstanceRef.current)) {
      setOriginalLocation(location);
      setCurrentLocation(location);
      setHasChanges(false);
      initializeMaps();
    }
  }, [open, location]);

  // Reset when modal closes
  useEffect(() => {
    if (!open) {
      setCurrentLocation(location);
      setOriginalLocation(location);
      setHasChanges(false);
      setError(null);
      // Reset maps API and cleanup map references
      setMapsApi(null);
      mapInstanceRef.current = null;
      markerRef.current = null;
      circleRef.current = null;
      isAdvancedMarkerRef.current = false;
    }
  }, [open, location]);

  // Check for changes
  useEffect(() => {
    const locationChanged =
      currentLocation.lat !== originalLocation.lat ||
      currentLocation.lng !== originalLocation.lng;
    setHasChanges(locationChanged);
  }, [currentLocation, originalLocation]);

  // Update map when location or range changes
  useEffect(() => {
    if (mapsApi && mapInstanceRef.current && open) {
      updateMapLocation(currentLocation, deliveryRange);
    }
  }, [currentLocation, deliveryRange, mapsApi, open]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Cleanup map instances when component unmounts
      if (mapInstanceRef.current) {
        mapInstanceRef.current = null;
      }
      if (markerRef.current) {
        markerRef.current = null;
      }
      if (circleRef.current) {
        circleRef.current = null;
      }
      isAdvancedMarkerRef.current = false;
    };
  }, []);

  const initializeMaps = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const maps = await initializeGoogleMaps();
      setMapsApi(maps);

      // Wait for DOM to be ready and ensure modal is still open
      setTimeout(() => {
        if (open && mapRef.current) {
          createMap(maps);
        }
      }, 100);
    } catch (err) {
      setError(
        'Failed to load Google Maps. Please check your internet connection.'
      );
      console.error('Maps initialization error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const createMap = (maps: any) => {
    if (!mapRef.current || !open) return;

    // Create map instance with Map ID for AdvancedMarkerElement support
    mapInstanceRef.current = new maps.Map(mapRef.current, {
      center: currentLocation,
      zoom: 15,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      zoomControl: true,
      disableDefaultUI: false,
      gestureHandling: 'auto',
      // Use demo Map ID for AdvancedMarkerElement support
      // Note: When using mapId, styles are controlled via Google Cloud Console
      mapId: 'DEMO_MAP_ID',
    });

    // Check if Advanced Markers are available and use them, otherwise fallback to legacy Marker
    const createMarker = () => {
      try {
        // Check map capabilities for Advanced Markers
        const mapCapabilities = mapInstanceRef.current.getMapCapabilities?.();
        const isAdvancedMarkersAvailable =
          mapCapabilities?.isAdvancedMarkersAvailable;

        if (isAdvancedMarkersAvailable && maps.marker?.AdvancedMarkerElement) {
          // Create marker using AdvancedMarkerElement (preferred)
          markerRef.current = new maps.marker.AdvancedMarkerElement({
            position: currentLocation,
            map: mapInstanceRef.current,
            title: 'Ordering Location',
            gmpDraggable: true,
          });
          isAdvancedMarkerRef.current = true;
          console.log('Successfully created AdvancedMarkerElement');
        } else {
          throw new Error('Advanced Markers not available, using fallback');
        }
      } catch (error) {
        console.warn(
          'Failed to create AdvancedMarkerElement, falling back to legacy Marker:',
          error
        );
        // Fallback to legacy Marker
        markerRef.current = new maps.Marker({
          position: currentLocation,
          map: mapInstanceRef.current,
          draggable: true,
          title: 'Ordering Location',
          icon: {
            path: maps.SymbolPath.CIRCLE,
            scale: 8,
            fillColor: '#1976d2',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          },
        });
        isAdvancedMarkerRef.current = false;
      }

      // Add event listeners after marker creation
      setupMarkerListeners();
    };

    const setupMarkerListeners = () => {
      if (!markerRef.current) return;

      // Add marker drag listener for both marker types
      markerRef.current.addListener('dragend', (event: any) => {
        const newLocation = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng(),
        };
        setCurrentLocation(newLocation);
      });
    };

    // Try to use AdvancedMarkerElement, fallback to legacy Marker if not available
    try {
      if (maps.marker && maps.marker.AdvancedMarkerElement) {
        // Wait for map capabilities to be ready
        if (mapInstanceRef.current.getMapCapabilities) {
          createMarker();
        } else {
          // Listen for map capabilities change
          const listener = mapInstanceRef.current.addListener(
            'mapcapabilities_changed',
            () => {
              createMarker();
              maps.event.removeListener(listener);
            }
          );
        }
      } else {
        // Fallback to legacy Marker if AdvancedMarkerElement is not available
        markerRef.current = new maps.Marker({
          position: currentLocation,
          map: mapInstanceRef.current,
          draggable: true,
          title: 'Ordering Location',
          icon: {
            path: maps.SymbolPath.CIRCLE,
            scale: 8,
            fillColor: '#1976d2',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          },
        });
        isAdvancedMarkerRef.current = false;
        setupMarkerListeners();
      }
    } catch (error) {
      console.warn(
        'Failed to create AdvancedMarkerElement, falling back to legacy Marker:',
        error
      );
      // Fallback to legacy Marker
      markerRef.current = new maps.Marker({
        position: currentLocation,
        map: mapInstanceRef.current,
        draggable: true,
        title: 'Ordering Location',
        icon: {
          path: maps.SymbolPath.CIRCLE,
          scale: 8,
          fillColor: '#1976d2',
          fillOpacity: 1,
          strokeColor: '#ffffff',
          strokeWeight: 2,
        },
      });
      isAdvancedMarkerRef.current = false;
      setupMarkerListeners();
    }

    // Create ordering range circle
    circleRef.current = new maps.Circle({
      map: mapInstanceRef.current,
      center: currentLocation,
      radius: deliveryRange,
      fillColor: '#1976d2',
      fillOpacity: 0.1,
      strokeColor: '#1976d2',
      strokeOpacity: 0.3,
      strokeWeight: 2,
      editable: false,
    });

    // Add map click listener
    mapInstanceRef.current.addListener('click', (event: any) => {
      const newLocation = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      setCurrentLocation(newLocation);
    });

    // Fit map to show entire ordering area
    fitMapToBounds(maps, currentLocation, deliveryRange);
  };

  const updateMapLocation = (
    newLocation: { lat: number; lng: number },
    range: number
  ) => {
    if (!markerRef.current || !circleRef.current || !mapInstanceRef.current)
      return;

    // Update marker position based on marker type
    if (isAdvancedMarkerRef.current) {
      // AdvancedMarkerElement uses position property
      markerRef.current.position = newLocation;
    } else {
      // Legacy Marker uses setPosition method
      const latLng = new mapsApi.LatLng(newLocation.lat, newLocation.lng);
      markerRef.current.setPosition(latLng);
    }

    // Update circle
    const latLng = new mapsApi.LatLng(newLocation.lat, newLocation.lng);
    circleRef.current.setCenter(latLng);
    circleRef.current.setRadius(range);

    // Center map on new location
    mapInstanceRef.current.setCenter(latLng);

    // Fit bounds to show ordering area
    fitMapToBounds(mapsApi, newLocation, range);
  };

  const fitMapToBounds = (
    maps: any,
    center: { lat: number; lng: number },
    radius: number
  ) => {
    if (!mapInstanceRef.current) return;

    const bounds = createBoundsFromRadius(center, radius, maps);
    mapInstanceRef.current.fitBounds(bounds);

    // Ensure minimum zoom level
    const listener = mapInstanceRef.current.addListener(
      'bounds_changed',
      () => {
        if (mapInstanceRef.current.getZoom() > 18) {
          mapInstanceRef.current.setZoom(18);
        }
        maps.event.removeListener(listener);
      }
    );
  };

  const handleSave = () => {
    onLocationChange(currentLocation);
    onClose();
  };

  const handleClose = () => {
    if (hasChanges) {
      setShowConfirmModal(true);
    } else {
      onClose();
    }
  };

  const handleDiscard = () => {
    setCurrentLocation(originalLocation);
    setHasChanges(false);
    setShowConfirmModal(false);
    onClose();
  };

  const formatDistance = (meters: number): string => {
    if (meters >= 1000) {
      const km = (meters / 1000).toFixed(1);
      return `${km} km`;
    }
    return `${meters} m`;
  };

  const handleUseCurrentLocation = async () => {
    setIsGettingLocation(true);
    setError(null);

    try {
      const newLocation = await getCurrentLocation();
      setCurrentLocation(newLocation);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to get current location'
      );
    } finally {
      setIsGettingLocation(false);
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
        PaperProps={{
          sx: {
            height: isMobile ? '100vh' : '80vh',
            minHeight: isMobile ? '100vh' : 600,
          },
        }}
      >
        <ModalHeader handleClose={handleClose} title={title}>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={!hasChanges}
          >
            Save
          </Button>
        </ModalHeader>

        <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column' }}>
          {error && (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: 2,
              }}
            >
              {/* Left side - Coordinates */}
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="caption"
                  color="textSecondary"
                  fontSize="0.75rem"
                  display="block"
                >
                  Selected Coordinates:
                </Typography>
                <Typography
                  variant="caption"
                  fontFamily="monospace"
                  fontSize="0.75rem"
                  display="block"
                  sx={{ mt: 0.5 }}
                >
                  {formatCoordinates(currentLocation.lat, currentLocation.lng)}
                </Typography>
              </Box>

              {/* Right side - Controls */}
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                  alignItems: 'center',
                  flexShrink: 0,
                }}
              >
                <IconButton
                  onClick={handleUseCurrentLocation}
                  disabled={isGettingLocation}
                  size="small"
                  color="primary"
                  title="Use Current Location"
                  sx={{
                    border: 1,
                    borderColor: 'primary.main',
                    borderRadius: 4,
                    padding: 0.5,
                    height: 32,
                    width: 32,
                  }}
                >
                  {isGettingLocation ? (
                    <CircularProgress size={16} />
                  ) : (
                    <MyLocation fontSize="small" />
                  )}
                </IconButton>
                <Chip
                  icon={<LocationOn />}
                  label={`Range: ${formatDistance(deliveryRange)}`}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{
                    height: 32,
                    borderRadius: 4,
                  }}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={{ flex: 1, position: 'relative' }}>
            {isLoading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  zIndex: 1000,
                }}
              >
                <CircularProgress />
              </Box>
            )}

            <div
              ref={mapRef}
              style={{
                width: '100%',
                height: '100%',
                minHeight: isMobile ? 300 : 400,
              }}
            />
          </Box>

          <Box
            sx={{
              p: 2,
              borderTop: 1,
              borderColor: 'divider',
              backgroundColor: 'grey.50',
            }}
          >
            <Typography variant="body2" color="textSecondary">
              💡 <strong>Tips:</strong> Click anywhere on the map or drag the
              marker to set your ordering location. Use the location button next
              to the range indicator to center on your current location. The
              blue circle shows your ordering range (
              {formatDistance(deliveryRange)}).
            </Typography>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Confirmation Modal */}
      <CloseWithConfirmationModal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onDiscard={handleDiscard}
        saveButton={
          <Button variant="contained" onClick={handleSave}>
            Save Location
          </Button>
        }
        title="Unsaved Changes"
        message="You have unsaved changes to the location. Would you like to save them?"
        btnDiscardText="Discard Changes"
      />
    </>
  );
};
