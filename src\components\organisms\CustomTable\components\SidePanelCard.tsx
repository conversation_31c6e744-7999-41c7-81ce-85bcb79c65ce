import { ReactNode } from 'react';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { Box, Button, Modal, Slide, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

type SidePanelCardProps = {
  open: boolean;
  onClose: () => void;
  title?: string;
  extraData?: { [key: string]: any };
  children?: ReactNode;
};

const SidePanelCard = ({
  open,
  onClose,
  title,
  children,
}: SidePanelCardProps) => {
  const { t } = useTranslation('');
  return (
    <Modal
      sx={{
        '.MuiModal-backdrop	': { backgroundColor: 'transparent' },
        border: 0,
        ring: 0,
      }}
      open={open}
      onClose={onClose}
      closeAfterTransition
    >
      <Slide direction="left" in={open} mountOnEnter unmountOnExit>
        <Box
          sx={{
            outline: 'none',
            '&:focus-visible': {
              outline: 'none',
            },
            position: 'fixed',
            top: 0,
            right: 0,
            maxWidth: '500px',
            width: '100%',
            height: '100vh',
            bgcolor: 'background.paper',
            boxShadow: 24,
            overflowY: 'auto',
            px: { xs: 2, sm: 4 },
            pb: 4,
            zIndex: 1300,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              top: 0,
              pt: 4,
              zIndex: 10,
              backgroundColor: 'background.paper',
              position: 'sticky',
              borderBottom: '2px solid #F2F2F2',
              pb: 2,
              mb: 4,
            }}
          >
            <Button
              sx={{
                bgcolor: '#F5F5F5',
                borderRadius: '8px',
                border: 0,
                color: 'black',
                '&:hover': {
                  border: 0,
                  bgcolor: '#E0E0E0',
                },
              }}
              onClick={onClose}
              variant="outlined"
            >
              <CloseRoundedIcon />
            </Button>
            <Typography variant="h2">{title || t('shared.details')}</Typography>
          </Box>
          <Box>
            {children ? (
              children
            ) : (
              <Typography>{t('shared.noContentAvailable')}</Typography>
            )}
          </Box>
        </Box>
      </Slide>
    </Modal>
  );
};

export default SidePanelCard;
