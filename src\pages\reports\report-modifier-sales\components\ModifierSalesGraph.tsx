import { useCallback, useEffect, useState } from 'react';
import { Typography } from '@mui/material';
import { useGetList } from 'react-admin';
import { useTranslation } from 'react-i18next';

import ReportMultiLineChart from '~/pages/reports/components/ReportMultiLineChart';
import capitalize from '~/utils/capitalize';
import { CurrencyType, formatNumber } from '~/utils/formatNumber';

export default function ModifierSalesGraph({
  data,
  currency,
}: {
  data: {
    datasets?: {
      label: string;
      data: number[];
    }[];
    labels?: string[];
  };
  currency?: CurrencyType;
}) {
  // TODO! change to live after implementing
  const { data: modifiersLibrary } = useGetList('modifiers');
  const [graphData, setGraphData] = useState<
    { label: string; data: number[] }[]
  >([]);

  const { t } = useTranslation();

  const fetchModifiersSalesData = useCallback(() => {
    if (!data.datasets) return;

    const controller = new AbortController();

    try {
      const resolvedData = data.datasets.map(item => {
        try {
          const modifierFound = modifiersLibrary?.find(
            modifierLibrary => modifierLibrary.id === item.label
          );
          return {
            ...item,
            label: capitalize(modifierFound?.name.toLowerCase()),
          };
        } catch (error) {
          console.error(`Failed to fetch item with id: ${item.label}`, error);
          return { ...item, label: item.label };
        }
      });

      setGraphData(resolvedData);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching item sales data:', error);
      }
    }

    return () => controller.abort();
  }, [data.datasets, modifiersLibrary]);

  useEffect(() => {
    fetchModifiersSalesData();
  }, [fetchModifiersSalesData]);

  return (
    <>
      {!data.datasets || !data.labels ? (
        <></>
      ) : (
        <>
          {graphData.length > 0 && (
            <>
              <Typography
                sx={{
                  display: { xs: 'none', md: 'block' },
                  '@media print': {
                    backgroundColor: '#FFFFFF !important',
                    color: 'black !important',
                  },
                }}
                variant="body2"
                fontWeight="500"
                mb={1.5}
              >
                {data.datasets.length >= 3 &&
                  `Top ${data.datasets.length} ${t('modifierSales.topGrossSales')}`}
              </Typography>
              <ReportMultiLineChart
                datasets={graphData}
                labels={data.labels}
                formatData={data => formatNumber(data, currency)}
              />
            </>
          )}
        </>
      )}
    </>
  );
}
