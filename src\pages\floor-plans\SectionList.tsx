import { Box, Theme, Typography, useMediaQuery } from '@mui/material';
import {
  EditableDatagrid,
  EditRowButton,
  RowForm,
} from '@react-admin/ra-editable-datagrid';
import { ListLiveUpdate } from '@react-admin/ra-realtime';
import {
  ArrayField,
  BooleanField,
  BooleanInput,
  CreateButton,
  Datagrid,
  DataTable,
  List,
  required,
  TextField,
  TextInput,
  useListContext,
  useRedirect,
} from 'react-admin';
import { useTranslation } from 'react-i18next';

import Pill from '~/components/atoms/Pill';
import MobileCard from '~/components/molecules/MobileCard';
import MobileGrid from '~/components/molecules/MobileGrid';
import LocationAndDateSelectors from '~/components/organisms/dashboard/LocationAndDateSelectors';
import { ActionsField } from '~/components/organisms/DatagridActionsField';
import { RESOURCES, resourcesInfo } from '~/providers/resources';
import CustomDeleteWithConfirmIconButton from '../../components/molecules/CustomDeleteWithConfirmIconButton';
import { useTheme } from '../../contexts';

const CustomEmpty = () => {
  const { t } = useTranslation('');
  return (
    <div style={{ textAlign: 'center' }}>
      <LocationAndDateSelectors isDate={false} hideShadow />
      <img src="/assets/icons/table.svg" width="45px" />
      <Typography variant="h3" sx={{ mt: 2 }}>
        {t('floorPlansPage.noFloorPlansYet')}
      </Typography>
      <Typography
        variant="body2"
        my={3}
        maxWidth="550px"
        mx="auto"
        color="text.secondary"
      >
        {t('floorPlansPage.noFloorPlansYetDescription')}
      </Typography>
      <CreateButton
        variant="contained"
        label={t('floorPlansPage.createFloorPlan')}
      />
    </div>
  );
};

export default function SectionList(props: { sellPointId: string }) {
  const { sellPointId } = props;
  const redirect = useRedirect();
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('md'));
  const { t } = useTranslation();
  const { theme } = useTheme();

  if (!sellPointId) {
    return null;
  }

  return (
    <>
      <List
        resource={RESOURCES.FLOOR_PLANS}
        sort={resourcesInfo[RESOURCES.FLOOR_PLANS].defaultSort}
        pagination={false}
        perPage={Number.MAX_SAFE_INTEGER}
        component="div"
        exporter={false}
        actions={false}
        empty={<CustomEmpty />}
        queryOptions={{ meta: { sellPointId: sellPointId } }}
        filter={{ sellPointId: sellPointId }}
        sx={{
          '& .RaFilterFormInput-spacer': {
            display: { xs: 'none', md: 'block' },
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <LocationAndDateSelectors isDate={false} hideShadow />
          <CreateButton
            variant="contained"
            label={t('floorPlansPage.createFloorPlan')}
            {...(isXSmall ? {} : { icon: <></> })}
          />
        </Box>
        {isXSmall ? (
          <MobileGrid>
            <MobileCard actions={true} cardClick="edit">
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body1" fontWeight={300} fontSize={14}>
                  {t('floorPlansPage.active')}
                </Typography>
                <BooleanField
                  source="active"
                  textAlign="right"
                  label={t('floorPlansPage.active')}
                />
              </Box>

              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body1" fontWeight={300} fontSize={14}>
                  {t('floorPlansPage.tables')}
                </Typography>
                <ArrayField
                  source="items"
                  label={t('floorPlansPage.tables')}
                  textAlign="right"
                >
                  <LengthField />
                </ArrayField>
              </Box>
            </MobileCard>
          </MobileGrid>
        ) : (
          <DataTable
            rowClick={(_, __, row) => {
              console.log('typeof row.id', typeof row.id);
              redirect('edit', RESOURCES.FLOOR_PLANS, row.id, row, {
                _scrollToTop: false,
              });
              return false;
            }}
            bulkActionButtons={false}
            // editForm={<SectionForm />}
            // actions={<CustomActions />}
            sx={{
              marginTop: '10px',
              '& .RaDataTable-headerCell': {
                backgroundColor: `#${theme.palette.mode === 'dark' ? '26262B' : 'F2F2F2'}`,
                borderBottom: `1px solid #${theme.palette.mode === 'dark' ? '515151' : 'E0E0E0'}`,
              },
              '& .MuiTableCell-root:last-of-type': {
                textAlign: 'right',
                '& button': {
                  visibility: 'visible',
                },
              },
            }}
          >
            <DataTable.Col source="name" label={t('shared.name')} />

            <DataTable.Col label={t('floorPlansPage.active')}>
              <BooleanField source="active" textAlign="right" />
            </DataTable.Col>

            <DataTable.Col
              label={t('floorPlansPage.tables')}
              sx={{ textAlign: 'right' }}
            >
              <ArrayField source="items" textAlign="right">
                <LengthField />
              </ArrayField>
            </DataTable.Col>

            <DataTable.Col label={t('prepStations.actions')} align="right">
              <ActionsField
                hasEdit
                hasDelete
                deleteMutationMode="optimistic"
                deleteMutationOptions={{ meta: { sellPointId: sellPointId } }}
              />
            </DataTable.Col>
          </DataTable>
        )}

        <ListLiveUpdate />
      </List>
    </>
  );
}

const LengthField = () => {
  const { data } = useListContext();
  const { theme } = useTheme();
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-end',
        paddingRight: { xs: 0, md: 2 },
      }}
    >
      <Pill>
        <Typography variant="body2">{data ? data.length : 'empty'}</Typography>
        <img
          src="/assets/icons/table.svg"
          width="17px"
          style={{
            filter: theme.palette.mode === 'dark' ? 'invert(1)' : 'none',
          }}
        />
      </Pill>
    </Box>
  );
};
