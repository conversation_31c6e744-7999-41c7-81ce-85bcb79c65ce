import { useMemo } from 'react';

import {
  useGetListHospitalityCategoriesLive,
  useGetListPrepStationsLive,
} from '~/providers/resources';

/**
 * Hook to get categories that are available for assignment to prep stations.
 * Categories that are already assigned to other prep stations will be filtered out.
 *
 * @param currentPrepStationId - ID of the prep station being edited (allows its current categories to remain available)
 * @returns Object with available categories and loading state
 */
export const useAvailableCategories = (currentPrepStationId?: string) => {
  const { data: allCategories, isPending: categoriesLoading } =
    useGetListHospitalityCategoriesLive({
      filter: { _d: false },
    });

  const { data: allPrepStations, isPending: prepStationsLoading } =
    useGetListPrepStationsLive();

  const availableCategories = useMemo(() => {
    if (!allCategories || !allPrepStations) return [];

    // Get all category IDs that are assigned to other prep stations
    const assignedCategoryIds = new Set<string>();

    allPrepStations.forEach(prepStation => {
      // Skip the current prep station being edited
      if (currentPrepStationId && prepStation.id === currentPrepStationId) {
        return;
      }

      // Add all category IDs from this prep station (handle both groups and groupIds)
      if (prepStation.groups && Array.isArray(prepStation.groups)) {
        prepStation.groups.forEach((categoryId: string) =>
          assignedCategoryIds.add(categoryId)
        );
      }
      if (prepStation.groupIds && Array.isArray(prepStation.groupIds)) {
        prepStation.groupIds.forEach((categoryId: string) =>
          assignedCategoryIds.add(categoryId)
        );
      }
    });

    // Filter out assigned categories
    return allCategories.filter(
      category => !assignedCategoryIds.has(category.id)
    );
  }, [allCategories, allPrepStations, currentPrepStationId]);

  const isLoading = categoriesLoading || prepStationsLoading;

  return {
    availableCategories,
    isLoading,
    allCategories: allCategories || [],
  };
};
