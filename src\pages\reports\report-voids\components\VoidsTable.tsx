import { useMemo } from 'react';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import { groupReport } from '~/fake-provider/reports/groupReport';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import formatNumberIntl from '~/utils/formatNumberIntl';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';
import { downloadCSV } from 'react-admin';
import { useGetListLive } from '@react-admin/ra-realtime';
import { useGetListLocationsLive } from '~/providers/resources';
import { useGlobalResourceFilters } from '~/hooks/useGlobalResourceFilters';

type TableRow = {
  type: string;
  quantity: number;
  name: string;
  id: string;
  reason: string;
  vat: number | undefined;
  variant: string;
  prepStation: string;
  price: number;
  groupName: string;
  groupId: string;
  measureUnit: string;
  value: number;
  subItems: [];
};

export default function VoidsTable({
  tableData,
  groupingItems,
  fields,
  onChangeGrouping,
  setFields,
  filters,
}: {
  groupingItems: string[];
  fields: FieldOption[];
  onChangeGrouping?: (items: any[]) => void;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
  tableData: ReturnType<typeof groupReport>[number]['report'] | undefined;
  filters: any;
}) {
  const voidsData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as TableRow[];
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.id = 'Total';
      totalItemsData.name = 'Total';
      totalItemsData.vat = undefined;
      totalItemsData.groupId = '';
      totalItemsData.prepStation = '';
      totalItemsData.type = '';
      totalItemsData.groupName = '';
      totalItemsData.reason = '';
      totalItemsData.subItems = [];
      totalItemsData.variant = '';
      totalItemsData.measureUnit = '';
      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }
    return [];
  }, [tableData]);

  const { t } = useTranslation();

  const voidsConfig: ColumnConfig<TableRow>[] = [
    {
      id: 'id',
      label: t('shared.name'),
      textAlign: 'start',
      render: (row: TableRow) => {
        if (row.subItems && row.subItems.length > 0) {
          return (
            <span
              style={{
                fontSize: '14px',
                whiteSpace: 'nowrap',
              }}
            >
              {row.id.includes('@none')
                ? 'Regular'
                : row?.id.includes('Vat')
                  ? capitalize(row?.id?.toLowerCase())
                  : row.id}
            </span>
          );
        } else {
          return (
            <Box
              sx={{
                fontSize: '14px',
                minWidth: { xs: '120px', sm: '190px' },
                maxWidth: { xs: '100px', sm: '' },
                whiteSpace: 'normal',
              }}
            >
              {row.name === '@none'
                ? 'Regular'
                : capitalize(row?.name?.toLowerCase()) || row.id}{' '}
            </Box>
          );
        }
      },
    },
    {
      id: 'reason',
      label: t('voids.reason'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.reason === '@na' ? 'N/A' : row.reason}</>;
      },
    },
    {
      id: 'type',
      label: t('reportsPage.type'),
      textAlign: 'end',
    },
    {
      id: 'vat',
      label: t('shared.tva'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <div
            style={{
              whiteSpace: 'nowrap',
              fontSize: '14px',
              //@ts-ignore
              '@media print': {
                backgroundColor: '#FFFFFF !important',
                color: 'black !important',
              },
            }}
          >
            {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
          </div>
        );
      },
    },
    {
      id: 'prepStation',
      label: t('reportsPage.prepStation'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.prepStation}</>;
      },
    },
    {
      id: 'groupId',
      label: t('shared.category_capitalize'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{row.groupName}</>;
      },
    },
    {
      id: 'measureUnit',
      label: t('itemSales.unit'),
      textAlign: 'end',
    },
    {
      id: 'variant',
      label: 'Variant',
      textAlign: 'end',
      render: (row: TableRow) => {
        return (
          <>
            {row.variant && row.variant === '@none'
              ? 'Regular'
              : capitalize(row.variant)}
          </>
        );
      },
    },
    {
      id: 'price',
      label: t('shared.price'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.price)}</>;
      },
    },
    {
      id: 'quantity',
      label: t('voids.itemsVoided'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatNumberIntl(row.quantity / 1000)}</>;
      },
    },
    {
      id: 'value',
      label: t('voids.amountVoided'),
      textAlign: 'end',
      render: (row: TableRow) => {
        return <>{formatAndDivideNumber(row.value)}</>;
      },
    },
  ];

  const columnsToFilter = useMemo(() => {
    const columns = [
      'reason',
      'vat',
      'groupId',
      'quantity',
      'measureUnit',
      'type',
      'prepStation',
      'price',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems]);

  const groupingOptions = [
    { value: 'reason', label: t('reportsPage.reason') },
    { value: 'vat', label: t('shared.tva') },
    { value: 'groupId', label: t('shared.category_capitalize') },
    { value: 'price', label: t('shared.price') },
    { value: 'measureUnit', label: t('itemSales.unit') },
    { value: 'type', label: t('reportsPage.type') },
    { value: 'prepStation', label: t('reportsPage.prepStation') },
  ];

  const { data: members } = useGetListLive('members');
  const { data: sellPoints } = useGetListLocationsLive();
  const { dateRange, timeRange } = useGlobalResourceFilters();

  const handleExport = () => {
    const title = 'Report voids';
    const filtersCsv = `${sellPoints?.find(el => el.id === filters?.sellpointId)?.name} | ${dateRange[0]?.format('DD/MM/YYYY')} - ${dateRange[1]?.format('DD/MM/YYYY')} | ${timeRange ? `${timeRange?.[0]?.format('HH:mm')} - ${timeRange?.[1]?.format('HH:mm')}` : 'All day'} | ${filters?.member ? (filters?.member !== 'all' ? members?.find(el => el.id === filters?.member)?.displayName : 'All members') : 'All members'} | ${filters?.floor ? (filters?.floor !== 'all' ? filters?.floor : 'All floors') : 'All floors'} | ${filters?.diningOption !== 'all' ? filters?.diningOption : 'All service types'} | ${filters?.source !== 'all' ? filters?.source : 'All sources'}`;
    const emptyRow = '';
    const csvContent = [
      title,
      filtersCsv,
      emptyRow,
      [
        'Category Name',
        'Reason',
        'Type',
        'VAT',
        'Prep Station',
        'Category',
        'Unit',
        'Price',
        'Items Voided',
        'Amount Voided',
      ].join(','),
      ...voidsData.map(el => el.subItems.map((sub: any) => sub.subItems.map((subItem: any) => {
          return [
            subItem.name || subItem.id,
            el.id,
            sub.id,
            subItem.vat,
            subItem.prepStation,
            subItem.groupName,
            subItem.measureUnit,
            subItem.price / 10000 || 0,
            subItem.quantity / 1000 || 0,
            subItem.value / 10000 || 0,
          ].join(',')
        })).flat(),
      ).flat(),

      [
        'Total',
        '',
        '',
        '',
        '',
        '',
        voidsData.reduce((acc, el) => acc + el.quantity, 0) / 1000,
        voidsData.reduce((acc, el) => acc + el.value, 0) / 10000,
      ].join(','),
    ].join('\n');
    downloadCSV(csvContent, 'voids');
  };

  return (
    <>
      <Box sx={{ py: 7 }}>
        <GroupingTable
          exportCSV={true}
          config={voidsConfig}
          data={voidsData}
          fields={fields}
          separateFirstColumn={true}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          fixedFirstColumn={true}
          handleExport={handleExport}
        />
      </Box>
    </>
  );
}
