import { PropsWithChildren, useEffect, useState } from 'react';
import { Box, Theme, useMediaQuery } from '@mui/material';
import { useLocation } from 'react-router-dom';

import menuConfig from '../../data/menu-items';
import Footer from '../organisms/layout/Footer';
import { MenuItemI } from './DropdownMenuItem';
import SecondMenu, { DROPDOWN_MENU_WIDTH } from './SecondMenu';

export default function ContainerWithCustomMenu({
  children,
}: PropsWithChildren) {
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { pathname } = useLocation();
  const [secondMenuItems, setSecondMenuItems] = useState<MenuItemI[]>([]);

  useEffect(() => {
    let changed = false;

    // Here we set items for the second menu by iterating
    // through menu config to find the current pathname
    menuConfig.forEach(menuGroup => {
      menuGroup.items?.forEach(subgroup => {
        if (subgroup.href === pathname && subgroup.items) {
          setSecondMenuItems(subgroup.items ?? []);
          changed = true;
        } else if (subgroup.items?.length) {
          subgroup.items.forEach(el => {
            if (el.href && pathname.includes(el.href)) {
              setSecondMenuItems(subgroup.items ?? []);
              changed = true;
            } else if (el.items?.length) {
              el.items.forEach(lastEl => {
                if (lastEl.href && pathname.includes(lastEl.href)) {
                  setSecondMenuItems(subgroup.items ?? []);
                  changed = true;
                }
              });
            }
          });
        }
      });

      if (!changed) {
        setSecondMenuItems([]);
      }
    });
  }, [pathname]);

  return (
    <Box sx={{ display: 'flex', '@media print': { display: 'none' } }}>
      {!isXSmall && !!secondMenuItems.length && (
        <SecondMenu items={secondMenuItems} />
      )}
      <Box
        sx={{
          width: '100%',
          ml:
            isXSmall || !secondMenuItems.length
              ? 0
              : `${DROPDOWN_MENU_WIDTH}px`,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 'calc(100vh - 60px)',
        }}
      >
        <Box sx={{ flex: 1 }}>{children}</Box>
        <Footer />
      </Box>
    </Box>
  );
}
