import { Dispatch, SetStateAction } from 'react';
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import { Box, Button, Icon, TextField, Typography } from '@mui/material';
import { useGetList, useNotify, useRedirect, useUpdate } from 'react-admin';

import Subsection from '../../../components/molecules/Subsection';

const warnings = [
  'Advanced reports, and other advanced features will no longer be available.',
  'You will lose access to custom permissions and sales attribution at all of your locations.',
  'Selio for KDS will no longer be available.',
  'Kitchen displays and kitchen performance reporting will no longer be available.',
  'You will lose access to multiple floor plan and table features.',
  'Additional Selio for Restaurants POS devices will no longer be available.',
];

export default function PricingSubscriptionsCreateStep4Premium({
  setStep,
}: {
  setStep: (step: number) => void;
}) {
  const { data: subscriptions } = useGetList('pricing-subscriptions');

  const [update] = useUpdate();
  const notify = useNotify();
  const redirect = useRedirect();

  const currentSubscription = subscriptions && subscriptions[0];

  const handleSave = async () => {
    if (!subscriptions || subscriptions.length === 0) return;

    try {
      await update('pricing-subscriptions', {
        id: currentSubscription.id,
        data: {
          isPremium: false,
        },
      });
      setStep(0);
      notify(
        currentSubscription.isPremium
          ? 'Successfully downgraded.'
          : 'Successfully upgraded.',
        { type: 'info' }
      );
      redirect('/pricing-subscriptions');
    } catch (error) {
      notify('Error while updating subscription', { type: 'warning' });
    }
  };
  return (
    <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
      <Box
        p={2}
        sx={{
          mt: 3,
          maxWidth: '600px',
        }}
      >
        <Subsection
          title={'Downgrade from Selio for Restaurants Premium'}
          subtitle={
            'You will still have access to advanced features included with Selio for Restaurants Premium until the end of your billing cycle. After that you may continue to use Selio for Restaurants Free with limited functionality.'
          }
        />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 0.5,
            mb: 4,
          }}
        >
          {warnings.map((warning: string, index: number) => {
            return (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    gap: 1.5,
                    pt: 1.5,
                    pb: 2,
                    backgroundColor: '#CC00231A',
                    borderRadius: 1.5,
                    px: 4,

                    alignItems: 'start',
                  }}
                >
                  <WarningRoundedIcon sx={{ color: '#CC0023E5' }} />
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, textAlign: 'start', width: '100%' }}
                  >
                    {warning}
                  </Typography>
                </Box>
              </>
            );
          })}
        </Box>
        <Subsection title="What could we do to make this service work for you?">
          <TextField
            id="outlined-multiline-static"
            placeholder="Tell us how we can help."
            multiline
            rows={4}
          />
        </Subsection>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            my: 2,
            justifyContent: 'flex-end',
          }}
        >
          <Button variant="contained" onClick={handleSave}>
            Confirm Downgrade
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
