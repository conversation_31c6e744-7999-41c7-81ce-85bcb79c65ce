// This function is used to get the data from the storage
// it will receive the storageBucket and the files that we need to get

import { FirebaseStorage, getDownloadURL, ref } from 'firebase/storage';

import { GetReportsDataFromStorage } from '../types';

// and will return an array with the contents of the files
export const getReportsDataFromStorage: GetReportsDataFromStorage = async (
  storageBucket,
  files
) => {
  // first we need to verify the files
  if (!files || !Array.isArray(files) || files.length === 0) {
    throw new Error('Invalid files');
  }
  // check if the storageBucket is an instance of Bucket
  const bucket = storageBucket as FirebaseStorage;
  if (!bucket) {
    throw new Error('Invalid storage bucket');
  }
  // now we need to check if the files are valid
  // and if they exist in the storage
  // it is better that we process this in parallel all at once
  // so we will use Promise.all
  // if one or more files do not exist they will not be included in the results
  console.info(`getDataFromStorage: ${JSON.stringify(files)}`);
  const results = await Promise.all(
    files.map(async file => {
      // create a new file
      const fileRef = ref(bucket, file);
      try {
        // get the download URL
        const downloadURL = await getDownloadURL(fileRef);
        // check if the download URL is valid

        const response = await fetch(downloadURL);
        if (!response.ok) {
          return null;
        }
        // get json
        return response.json();
      } catch (error) {
        return null;
      }
    })
  );
  // filter out null values
  return results.filter(result => result !== null);
};
