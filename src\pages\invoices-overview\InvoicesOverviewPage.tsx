import { Box } from '@mui/material';

import PageTitle from '~/components/molecules/PageTitle';

export default function InvoicesOverviewPage() {
  return (
    <Box sx={{ p: 2 }}>
      <PageTitle
        sx={{
          '@media print': {
            backgroundColor: '#FFFFFF !important',
            color: 'black !important',
          },
        }}
        title="Overview"
        description={
          <>
            Get a centralized view of orders received from integrated
            third-party platforms like food delivery apps. Monitor performance
            and order flow in real time.{' '}
            <a
              href="https://selio.io/support-center"
              target="_blank"
              rel="noreferrer"
            >
              Learn more in Support Center.
            </a>
          </>
        }
        hideBorder
        doNotPrint
      />
    </Box>
  );
}
