import { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Divider,
  Theme,
  Typography,
  TypographyProps,
  useMediaQuery,
} from '@mui/material';
import { Menu, useSidebarState } from 'react-admin';
import { useTranslation } from 'react-i18next';

import menuConfig from '../../../data/menu-items';
import DropdownMenuItem, { MenuItemI } from '../../molecules/DropdownMenuItem';
import EditQuickAcessModal from '../../molecules/EditQuickAcessModal';
import MainMenuItem from '../../molecules/MainMenuItem';

const homeMenu = {
  label: 'home',
  href: '/',
  icon: {
    props: {
      src: '/assets/menu-icons/home.svg',
      alt: 'menu-icon-home',
    },
  },
};

const typographyProps: TypographyProps = {
  // @ts-ignore
  variant: 'label',
  component: 'p',
  fontWeight: 700,
};

export default function MainMenu() {
  const [open, setOpenSidebar] = useSidebarState();
  const [quickAccessMenu, setQuickAccessMenu] = useState([]);
  const [openMenuItem, setOpenMenuItem] = useState<number | null>(null);
  const isXSmall = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const [itemsSaved, setItemsSaved] = useState(false);
  const [selectedItems, setSelectedItems] = useState<any>([]);
  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => {
    const savedItems = localStorage.getItem('selectedItems');
    setSelectedItems(JSON.parse(savedItems || '[]'));
    setOpenModal(false);
  };

  useEffect(() => {
    const savedItems = localStorage.getItem('selectedItems');
    if (savedItems) {
      setSelectedItems(JSON.parse(savedItems));
      setQuickAccessMenu(JSON.parse(savedItems));
    }
  }, [itemsSaved]);

  const handleSave = () => {
    localStorage.setItem('selectedItems', JSON.stringify(selectedItems || []));
    setQuickAccessMenu(selectedItems || []);
    setItemsSaved(true);
    setOpenModal(false);
  };

  return (
    <Menu
      sx={{
        borderRight: 'solid 1px rgba(0,0,0,0.1)',
        margin: 0,
        height: '100%',
        opacity: open ? 1 : 0,
        transition: open ? 'opacity 0.1s 0.1s ease' : 'opacity 0.1s ease',
      }}
    >
      {/* quick access menu */}
      <Box sx={{ p: 2 }}>
        {isXSmall && (
          <Button
            // @ts-ignore
            variant="close-btn"
            onClick={() => {
              setOpenSidebar(false);
              window.scrollTo({ top: 0, left: 0 });
            }}
            sx={{ float: 'right' }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        )}
        <Box
          sx={{
            pl: 1,
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            justifyContent: isXSmall ? 'flex-start' : 'space-between',
          }}
        >
          <Typography {...typographyProps}>{t('menu.quickAccess')}</Typography>
          <EditQuickAcessModal
            selectedItems={selectedItems}
            setSelectedItems={setSelectedItems}
            handleSave={handleSave}
            handleClose={handleCloseModal}
            handleOpen={handleOpenModal}
            open={openModal}
          />
        </Box>
        <Box
          sx={{
            maxHeight: '385px',
            overflowY: 'auto',
          }}
        >
          <MainMenuItem menuItem={homeMenu} />
          {quickAccessMenu?.map(
            (el: MenuItemI, index: number) => (
              // isXSmall ? (
              //   <DropdownMenuItem key={`${el.label}-${index}`} menuItem={el} />
              // ) : (
              <MainMenuItem key={`${el.href}-${index}`} menuItem={el} />
            )
            // )
          )}
        </Box>
      </Box>

      <Divider sx={{ height: '10px', bgcolor: 'transparent', border: 0 }} />
      {/* all items menu */}
      <Box sx={{ p: 2, overflowY: 'auto', pb: isXSmall ? '100px' : 2 }}>
        {/* @ts-ignore */}
        <Typography sx={{ fontSize: '1rem' }} {...typographyProps}>
          {t('menu.allProducts')}
        </Typography>

        {menuConfig.map((el: MenuItemI, index: number) =>
          isXSmall ? (
            <DropdownMenuItem
              key={`${el.label}-${index}`}
              menuItem={el}
              open={openMenuItem == index}
              setOpen={() => setOpenMenuItem(index)}
            />
          ) : (
            <MainMenuItem key={`${el.href}-${index}`} menuItem={el} />
          )
        )}
      </Box>
    </Menu>
  );
}
