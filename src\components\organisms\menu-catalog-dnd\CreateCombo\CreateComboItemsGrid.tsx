import { useMemo, useState } from 'react';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Button, Menu, MenuItem, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useTheme } from '~/contexts';
import extractItems from '~/utils/extractItemsFromObj';
import { formatNumber } from '~/utils/formatNumber';
import ComboAddTileModal from './ComboAddTileModal';

export default function CreateComboItemsGrid({
  items,
  setItems,
}: {
  items: { id: string; type: string }[];
  setItems: (items: { id: string; type: string }[]) => void;
}) {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [modalOpenType, setModalOpenType] = useState<
    'product' | 'displayGroup' | null
  >(null);
  const dropdownOpen = Boolean(anchorEl);

  const { getValues } = useFormContext();
  const record = getValues();
  const { theme } = useTheme();

  const extractedItems = useMemo(() => {
    const { items: extractedItems } = extractItems(record.pages, 'product');
    return extractedItems;
  }, [record.pages]);

  const extractedGroups = useMemo(() => {
    const { items: extractedGroups } = extractItems(
      record.pages,
      'displayGroup'
    );
    return extractedGroups;
  }, [record.pages]);

  // we need to clear undefined extracteditems
  const populatedItems = useMemo(() => {
    const allExtractedItems = [...extractedItems, ...extractedGroups];
    return items
      .map(item => {
        const extractedItem = allExtractedItems.find(
          extractedItem =>
            extractedItem.id === item.id && extractedItem.type === item.type
        );
        return extractedItem;
      })
      .filter(Boolean)
      .sort((a, b) => {
        const typeA = a.type ?? 'product';
        const typeB = b.type ?? 'product';

        if (typeA === 'displayGroup' && typeB !== 'displayGroup') return -1;
        if (typeA !== 'displayGroup' && typeB === 'displayGroup') return 1;

        return a.displayName.localeCompare(b.displayName);
      });
  }, [items, extractedItems, extractedGroups]);

  const setModalOpenTypeH = (type: 'product' | 'displayGroup') => {
    setModalOpenType(type);
    setAnchorEl(null);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const deleteItem = (index: number) => {
    const tmp = [...items];
    tmp.splice(index, 1);
    setItems(tmp);
  };

  const addItems = (newItems: { id: string; type: string }[]) => {
    const tmp = [...items, ...newItems];
    setItems(tmp);
  };

  return (
    <>
      <Box
        sx={{
          mt: 2,
          ml: 0,
          p: 1,
          display: 'grid',
          gridTemplateColumns: items.length
            ? {
                xs: 'repeat(2, 1fr)',
                sm: 'repeat(3, 1fr)',
                md: 'repeat(4, 1fr)',
              }
            : '1fr',
          gap: 2,
          border: '1px solid',
          borderColor: 'custom.gray400',
          width: '100%',
          cursor: items.length ? 'default' : 'pointer',
          bgcolor: 'custom.fieldBg',
          ':hover': {
            borderColor: items.length ? 'custom.gray400' : 'custom.gray600',
          },
        }}
      >
        {populatedItems.length ? (
          <>
            {populatedItems.map((item: any, index: number) => {
              return (
                <Box
                  key={index}
                  sx={
                    item.type === 'displayGroup'
                      ? {
                          ...tileStyle,
                          bgcolor: item.color,
                          color: 'white',
                          ':hover': {
                            borderColor: 'custom.gray600',
                            '> .floating-delete-btn': {
                              display: 'flex',
                            },
                          },
                        }
                      : tileStyle
                  }
                >
                  <Typography variant="caption" fontSize={14} lineHeight={1}>
                    {item.displayName}
                  </Typography>
                  {item.type === 'displayGroup' && (
                    <Typography
                      variant="caption"
                      fontSize={12}
                      lineHeight={1}
                      sx={{ opacity: 0.5 }}
                      mt={0.5}
                    >
                      {item.items.length} items
                    </Typography>
                  )}
                  {(item.type === 'product' || !item.type) && (
                    <span
                      style={{
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        backgroundColor:
                          theme.palette.mode === 'dark' ? '#26262B' : '#f0f0f0',
                        padding: '0 4px',
                        borderRadius: '6px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Typography variant="caption" fontSize={10}>
                        {!!item.price
                          ? formatNumber(+item.price / 10000, 'currency')
                          : ''}
                      </Typography>
                    </span>
                  )}
                  <Box
                    className={'floating-delete-btn'}
                    onClick={() => deleteItem(index)}
                    sx={deleteBtnStyle}
                  >
                    <DeleteIcon fontSize="small" color="error" />
                  </Box>
                </Box>
              );
            })}

            <Button sx={tileStyle} onClick={handleClick}>
              <AddIcon color="disabled" fontSize="small" />
            </Button>
          </>
        ) : (
          <Button
            onClick={handleClick}
            sx={{
              width: '100%',
              height: '80px',
              background: 'transparent !important',
              ...flexStyles,
            }}
          >
            <Typography
              variant="h3"
              color="custom.gray600"
              fontWeight={200}
              align="center"
            >
              + {t('menu.addItemsOrEntireMenuGroup')}
            </Typography>
          </Button>
        )}
      </Box>

      <Menu
        id="add-tile-menu"
        anchorEl={anchorEl}
        open={dropdownOpen}
        onClose={() => setAnchorEl(null)}
        MenuListProps={{
          'aria-labelledby': 'add-tile-button',
        }}
      >
        <MenuItem onClick={() => setModalOpenTypeH('product')}>
          {t('menu.addItems')}
        </MenuItem>
        <MenuItem onClick={() => setModalOpenTypeH('displayGroup')}>
          {t('menu.addMenuGroup2')}
        </MenuItem>
      </Menu>

      {modalOpenType && (
        <ComboAddTileModal
          type={modalOpenType}
          items={modalOpenType === 'product' ? extractedItems : extractedGroups}
          addItems={addItems}
          onClose={() => setModalOpenType(null)}
        />
      )}
    </>
  );
}

const tileStyle = {
  position: 'relative',
  height: '70px',
  border: '1px solid',
  borderColor: 'custom.gray400',
  borderRadius: '6px',
  p: 1,
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'column',
  cursor: 'pointer',
  bgcolor: 'background.tinted',
  ':hover': {
    bgcolor: 'background.tinted',
    borderColor: 'custom.gray600',
    '> .floating-delete-btn': {
      display: 'flex',
    },
  },
};

const flexStyles = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const deleteBtnStyle = {
  ...flexStyles,
  display: 'none',
  position: 'absolute',
  width: '30px',
  height: '30px',
  top: 'calc(50% - 15px)',
  right: '10px',
  bgcolor: '#f2f2f2cc',
  borderRadius: '50%',
  cursor: 'pointer',
  ':hover': {
    bgcolor: '#d9d9d9',
  },
};
