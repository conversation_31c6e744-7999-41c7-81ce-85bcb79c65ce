import { getDownloadURL, ref, uploadBytes } from 'firebase/storage';
import {
  CreateParams,
  CreateResult,
  DataProvider,
  DeleteManyParams,
  DeleteManyResult,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetListResult,
  GetManyParams,
  GetManyReferenceParams,
  GetManyReferenceResult,
  GetManyResult,
  GetOneParams,
  GetOneResult,
  Identifier,
  RaRecord,
  UpdateManyParams,
  UpdateManyResult,
  UpdateParams,
  UpdateResult,
} from 'react-admin';

import { storage } from '~/configs/firebaseConfig';
import { generateFirestoreId } from '~/utils/generateFirestoreId';
import { getResourceInfo } from './utils/getResourceInfo';
import { getResourcePath } from './utils/getResourcePath';

export interface StorageDataProvider extends DataProvider {
  generateFirestoreId: () => string;
  getAccountId: () => string;
}

/*
filters :
    _ninc_any - does not include any
    _inc - includes value
    _inc_any - include any
    _eq - equal
    _eq_any - equal any
    _neq - not equal
    _neq_any - not equal any
    _gt - greater than
    _gte - greater than or equal
    _lt - less than
    _lte - less than or equal
    _q - text search
*/

// this method get the data from the file on the storage
const getResourceFileData = async (resourcePath: string) => {
  const fileRef = ref(storage, resourcePath);
  try {
    const downloadURL = await getDownloadURL(fileRef);
    const response = await fetch(downloadURL);
    const data = await response.json();
    return data;
  } catch (error) {
    return [];
  }
};

// this method save the data to the file on the storage
const saveResourceFileData = async (resourcePath: string, data: any[]) => {
  const fileRef = ref(storage, resourcePath);
  try {
    // we need to get a blob from the data
    const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
    await uploadBytes(fileRef, blob);
  } catch (error) {
    console.log('Error saving resource data', error);
  }
};

// Apply filters to data array
const applyFilters = <T extends RaRecord>(data: T[], filters: any): T[] => {
  if (!filters || Object.keys(filters).length === 0) return data;

  return data.filter(record => {
    return Object.entries(filters).every(([key, filterValue]) => {
      if (filterValue == null) return true;

      // Handle text search (_q)
      if (key.endsWith('_q')) {
        const actualKey = key.slice(0, -2);
        const recordValue = record[actualKey];

        if (
          typeof filterValue !== 'string' ||
          typeof recordValue !== 'string'
        ) {
          return false;
        }

        return recordValue.toLowerCase().includes(filterValue.toLowerCase());
      }

      // Handle does not include any (_ninc_any)
      if (key.endsWith('_ninc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -9);
        const recordValue = record[actualKey];

        if (Array.isArray(recordValue)) {
          return !filterValue.some(v => recordValue.includes(v));
        }
        return !filterValue.includes(recordValue);
      }

      // Handle includes value (_inc)
      if (key.endsWith('_inc') && !key.endsWith('_inc_any')) {
        const actualKey = key.slice(0, -4);
        const recordValue = record[actualKey];

        if (Array.isArray(recordValue)) {
          return Array.isArray(filterValue)
            ? filterValue.every(v => recordValue.includes(v))
            : recordValue.includes(filterValue);
        }
        return false;
      }

      // Handle includes any (_inc_any)
      if (key.endsWith('_inc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        const recordValue = record[actualKey];

        if (Array.isArray(recordValue)) {
          return filterValue.some(v => recordValue.includes(v));
        }
        return false;
      }

      // Handle equal (_eq)
      if (
        key.endsWith('_eq') &&
        !key.endsWith('_eq_any') &&
        !key.endsWith('_neq')
      ) {
        const actualKey = key.slice(0, -3);
        return record[actualKey] === filterValue;
      }

      // Handle equal any (_eq_any)
      if (key.endsWith('_eq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -7);
        return filterValue.includes(record[actualKey]);
      }

      // Handle not equal (_neq)
      if (key.endsWith('_neq') && !key.endsWith('_neq_any')) {
        const actualKey = key.slice(0, -4);
        return record[actualKey] !== filterValue;
      }

      // Handle not equal any (_neq_any)
      if (key.endsWith('_neq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        return !filterValue.includes(record[actualKey]);
      }

      // Handle greater than (_gt)
      if (key.endsWith('_gt')) {
        const actualKey = key.slice(0, -3);
        const recordValue = record[actualKey];
        return (
          typeof recordValue === 'number' &&
          typeof filterValue === 'number' &&
          recordValue > filterValue
        );
      }

      // Handle greater than or equal (_gte)
      if (key.endsWith('_gte')) {
        const actualKey = key.slice(0, -4);
        const recordValue = record[actualKey];
        return (
          typeof recordValue === 'number' &&
          typeof filterValue === 'number' &&
          recordValue >= filterValue
        );
      }

      // Handle less than (_lt)
      if (key.endsWith('_lt')) {
        const actualKey = key.slice(0, -3);
        const recordValue = record[actualKey];
        return (
          typeof recordValue === 'number' &&
          typeof filterValue === 'number' &&
          recordValue < filterValue
        );
      }

      // Handle less than or equal (_lte)
      if (key.endsWith('_lte')) {
        const actualKey = key.slice(0, -4);
        const recordValue = record[actualKey];
        return (
          typeof recordValue === 'number' &&
          typeof filterValue === 'number' &&
          recordValue <= filterValue
        );
      }

      // Default exact match
      const recordValue = record[key];

      // Handle array filters (for backwards compatibility)
      if (Array.isArray(filterValue)) {
        return filterValue.includes(recordValue);
      }

      // Handle string search (case-insensitive partial match for backwards compatibility)
      if (typeof filterValue === 'string' && typeof recordValue === 'string') {
        return recordValue.toLowerCase().includes(filterValue.toLowerCase());
      }

      // Exact match for other types
      return recordValue === filterValue;
    });
  });
};

// Apply sorting to data array
const applySorting = <T extends RaRecord>(
  data: T[],
  sort?: { field: string; order: 'ASC' | 'DESC' }
): T[] => {
  if (!sort) return data;

  const { field, order } = sort;

  return [...data].sort((a, b) => {
    const aVal = a[field];
    const bVal = b[field];

    // Handle null/undefined values
    if (aVal == null && bVal == null) return 0;
    if (aVal == null) return order === 'ASC' ? -1 : 1;
    if (bVal == null) return order === 'ASC' ? 1 : -1;

    // Handle string comparison
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return order === 'ASC'
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal);
    }

    // Handle numeric comparison
    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return order === 'ASC' ? aVal - bVal : bVal - aVal;
    }

    // Handle date comparison
    if (aVal instanceof Date && bVal instanceof Date) {
      return order === 'ASC'
        ? aVal.getTime() - bVal.getTime()
        : bVal.getTime() - aVal.getTime();
    }

    // Fallback to string comparison
    const aStr = String(aVal);
    const bStr = String(bVal);
    return order === 'ASC'
      ? aStr.localeCompare(bStr)
      : bStr.localeCompare(aStr);
  });
};

// Apply pagination to data array
const applyPagination = <T extends RaRecord>(
  data: T[],
  pagination?: { page: number; perPage: number }
): { data: T[]; total: number } => {
  const total = data.length;

  if (!pagination) return { data, total };

  const { page, perPage } = pagination;

  if (perPage === -1) {
    return { data, total };
  }

  const start = (page - 1) * perPage;
  const end = start + perPage;

  return {
    data: data.slice(start, end),
    total,
  };
};

export const getStorageDataProvider = (
  accountId: string
): StorageDataProvider => {
  const getList = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetListParams
  ): Promise<GetListResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);
    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const rawData = await getResourceFileData(resourcePath);

    // Apply filters
    const filteredData = applyFilters(rawData, params.filter);

    // Apply sorting
    const sortedData = applySorting(
      filteredData,
      params.sort || resourceInfo.defaultSort
    );

    // Apply pagination
    const { data, total } = applyPagination(sortedData, params.pagination);

    return {
      data: data as RecordType[],
      total,
      meta: params.meta,
    };
  };

  const getOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetOneParams
  ): Promise<GetOneResult<RecordType>> => {
    let resourcePath;
    try {
      resourcePath = getResourcePath(resource, accountId, params.meta);
    } catch (error) {
      // TODO! edit dialog with meta fields calls this method without meta fields (floorPlans)
      // console.warn('Error in getOne:', error);
      return Promise.resolve<GetOneResult>({ data: { id: params.id } });
    }
    const data = await getResourceFileData(resourcePath);
    const record = data.find((record: any) => record.id === params.id);
    if (record) {
      return { data: record, meta: params.meta };
    } else {
      return Promise.resolve<GetOneResult>({
        data: { id: params.id },
        meta: params.meta,
      });
    }
  };

  const getMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyParams
  ): Promise<GetManyResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    const records = data.filter((record: any) =>
      params.ids.includes(record.id)
    );
    return { data: records, meta: params.meta };
  };

  const getManyReference = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyReferenceParams
  ): Promise<GetManyReferenceResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);
    const { page = 1, perPage = Number.MAX_SAFE_INTEGER } =
      params.pagination ?? {};
    const { field, order } = params.sort ?? resourceInfo.defaultSort;
    const filter = params.filter ?? {}; // Get base filters

    filter[params.target] = params.id;

    return getList<RecordType>(resource, {
      pagination: { page, perPage },
      sort: { field, order },
      filter: filter,
      meta: params.meta,
      signal: params.signal,
    });
  };

  const create = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: CreateParams
  ): Promise<CreateResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    // check if params data has id, if not generate one
    let result;
    if (!params.data.id) {
      params.data.id = generateFirestoreId();
      data.push(params.data);
      result = params.data;
    } else {
      // check if the id already exists in the data
      const existingRecord = data.find(
        (record: any) => record.id === params.data.id
      );
      if (existingRecord) {
        // update the record instead of creating a new one
        const index = data.findIndex(
          (record: any) => record.id === params.data.id
        );
        data[index] = { ...existingRecord, ...params.data };
        result = data[index];
      } else {
        // create a new record
        data.push(params.data);
        result = params.data;
      }
    }
    await saveResourceFileData(resourcePath, data);
    return { data: result as RecordType, meta: params.meta };
  };

  const update = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateParams
  ): Promise<UpdateResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    // check if the id already exists in the data
    const existingRecord = data.find((record: any) => record.id === params.id);
    if (existingRecord) {
      // update the record
      const index = data.findIndex((record: any) => record.id === params.id);
      data[index] = { ...existingRecord, ...params.data, id: params.id };
      await saveResourceFileData(resourcePath, data);
      return { data: data[index] as RecordType, meta: params.meta };
    }
    return { data: {} as RecordType };
  };

  const updateMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateManyParams
  ): Promise<UpdateManyResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    // check if the id already exists in the data
    const existingRecords = data.filter((record: any) =>
      params.ids.includes(record.id)
    );
    if (existingRecords.length) {
      // update the records
      const updatedRecordIds = existingRecords.map((record: any) => {
        const index = data.findIndex(
          (dataRecord: any) => dataRecord.id === record.id
        );
        data[index] = { ...record, ...params.data };
        return data[index].id;
      });
      await saveResourceFileData(resourcePath, data);
      return { data: updatedRecordIds, meta: params.meta };
    }
    return { data: [], meta: params.meta };
  };

  const deleteOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteParams
  ): Promise<DeleteResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    // check if the id already exists in the data
    const existingRecord = data.find((record: any) => record.id === params.id);
    if (existingRecord) {
      // delete the record
      const index = data.findIndex((record: any) => record.id === params.id);
      data.splice(index, 1);
      await saveResourceFileData(resourcePath, data);
      return { data: existingRecord as RecordType, meta: params.meta };
    }
    return { data: {} as RecordType, meta: params.meta };
  };

  const deleteMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteManyParams
  ): Promise<DeleteManyResult<RecordType>> => {
    const resourcePath = getResourcePath(resource, accountId, params.meta);
    const data = await getResourceFileData(resourcePath);
    // check if the id already exists in the data
    const existingRecords = data.filter((record: any) =>
      params.ids.includes(record.id)
    );
    if (existingRecords.length) {
      // delete the records
      const deletedRecordIds: string[] = [];
      existingRecords.forEach((record: any) => {
        const index = data.findIndex(
          (dataRecord: any) => dataRecord.id === record.id
        );
        if (index !== -1) {
          data.splice(index, 1);
          deletedRecordIds.push(record.id);
        }
      });
      await saveResourceFileData(resourcePath, data);
      return { data: deletedRecordIds, meta: params.meta };
    }
    return { data: [], meta: params.meta };
  };

  return {
    getList: getList,
    getOne: getOne,
    getMany: getMany,
    getManyReference: getManyReference,
    create: create,
    update: update,
    updateMany: updateMany,
    delete: deleteOne,
    deleteMany: deleteMany,
    subscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    unsubscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    publish: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    generateFirestoreId: generateFirestoreId,
    getAccountId: () => accountId,
  };
};
