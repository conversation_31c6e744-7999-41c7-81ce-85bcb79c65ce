import { useCallback, useEffect, useRef } from 'react';
import { UploadedFile, PrivateFileContext } from '~/types/fileUpload';
import { FileUploadConfig } from '../types';
import { fileUploadManager } from '~/utils/FileUploadManager';
import { cleanupTempFiles as cleanupTempFilesUtil } from '~/utils/bucketManager';

/**
 * Hook for managing file lifecycle (temp to permanent, cleanup, etc.)
 */
export const useFileLifecycle = (config: FileUploadConfig) => {
    const trackedFilesRef = useRef<Set<string>>(new Set());

    // Move file from temporary to permanent storage
    const moveToPermament = useCallback(async (
        tempFile: UploadedFile,
        context?: PrivateFileContext
    ): Promise<UploadedFile> => {
        try {
            const permanentFile = await fileUploadManager.moveToPermLocation(tempFile, context);

            // Call moved callback if provided
            config.callbacks?.onFileMoved?.(permanentFile);

            return permanentFile;
        } catch (error) {
            console.error('Failed to move file to permanent storage:', error);
            throw error;
        }
    }, [config.callbacks]);

    // Move multiple files from temporary to permanent storage
    const moveMultipleToPermament = useCallback(async (
        tempFiles: UploadedFile[],
        context?: PrivateFileContext
    ): Promise<UploadedFile[]> => {
        const movePromises = tempFiles.map(file => moveToPermament(file, context));
        return Promise.all(movePromises);
    }, [moveToPermament]);

    // Delete a file
    const deleteFile = useCallback(async (
        file: UploadedFile,
        context?: PrivateFileContext
    ): Promise<void> => {
        try {
            await fileUploadManager.deleteFile(file, context);

            // Remove from tracked files
            trackedFilesRef.current.delete(file.fn);

            // Call deleted callback if provided
            config.callbacks?.onFileDeleted?.(file);
        } catch (error) {
            console.error('Failed to delete file:', error);
            throw error;
        }
    }, [config.callbacks]);

    // Delete multiple files
    const deleteMultipleFiles = useCallback(async (
        files: UploadedFile[],
        context?: PrivateFileContext
    ): Promise<void> => {
        const deletePromises = files.map(file => deleteFile(file, context));
        await Promise.all(deletePromises);
    }, [deleteFile]);

    // Track a file for lifecycle management
    const trackFile = useCallback((file: UploadedFile) => {
        trackedFilesRef.current.add(file.fn);
    }, []);

    // Untrack a file
    const untrackFile = useCallback((file: UploadedFile) => {
        trackedFilesRef.current.delete(file.fn);
    }, []);

    // Get all tracked files
    const getTrackedFiles = useCallback((): string[] => {
        return Array.from(trackedFilesRef.current);
    }, []);

    // Clear all tracked files
    const clearTrackedFiles = useCallback(() => {
        trackedFilesRef.current.clear();
    }, []);

    // Cleanup temporary files
    const cleanupTempFiles = useCallback(() => {
        try {
            cleanupTempFilesUtil();
            // Clear tracked files as they're no longer relevant
            clearTrackedFiles();
        } catch (error) {
            console.error('Failed to cleanup temp files:', error);
        }
    }, [clearTrackedFiles]);

    // Process files before create operation (move temp files to permanent)
    const processBeforeCreate = useCallback(async (
        data: any,
        uploadedFileFields: string[],
        context?: PrivateFileContext
    ): Promise<any> => {
        try {
            return await fileUploadManager.processBeforeCreate(data, uploadedFileFields, context);
        } catch (error) {
            console.error('Failed to process files before create:', error);
            throw error;
        }
    }, []);

    // Process files before update operation
    const processBeforeUpdate = useCallback(async (
        data: any,
        previousData: any,
        uploadedFileFields: string[],
        context?: PrivateFileContext
    ): Promise<any> => {
        try {
            return await fileUploadManager.processBeforeUpdate(data, previousData, uploadedFileFields, context);
        } catch (error) {
            console.error('Failed to process files before update:', error);
            throw error;
        }
    }, []);

    // Process files before delete operation
    const processBeforeDelete = useCallback(async (
        entityData: any,
        uploadedFileFields: string[],
        context?: PrivateFileContext
    ): Promise<void> => {
        try {
            await fileUploadManager.processBeforeDelete(entityData, uploadedFileFields, context);
        } catch (error) {
            console.error('Failed to process files before delete:', error);
            throw error;
        }
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            cleanupTempFiles();
        };
    }, [cleanupTempFiles]);

    return {
        // File movement
        moveToPermament,
        moveMultipleToPermament,

        // File deletion
        deleteFile,
        deleteMultipleFiles,

        // File tracking
        trackFile,
        untrackFile,
        getTrackedFiles,
        clearTrackedFiles,

        // Cleanup
        cleanupTempFiles,

        // Lifecycle processing
        processBeforeCreate,
        processBeforeUpdate,
        processBeforeDelete,
    };
};
