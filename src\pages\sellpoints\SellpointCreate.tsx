import { useCallback, useState } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  InputAdornment,
  Typography,
} from '@mui/material';
import { CreateDialog } from '@react-admin/ra-form-layout';
import { t } from 'i18next';
import { isEqual } from 'lodash';
import {
  FormDataConsumer,
  ReferenceInput,
  required,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextInput,
  TimeInput,
  useRedirect,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { redirect, useNavigate } from 'react-router-dom';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import { RESOURCES } from '~/providers/resources';
import CustomImageInput from '../../components/atoms/inputs/CustomImageInput';
import ModalHeader from '../../components/molecules/ModalHeader';
import Subsection from '../../components/molecules/Subsection';
import getFullscreenModalProps from '../../utils/getFullscreenModalProps';
import InfoPopup from './components/InfoPopup';
import { weekKeys } from './SellpointEdit';

const defaultHours = {
  startAt: '09:00',
  endAt: '02:00',
};

const SellpointCreateInner = () => {
  const [openInfoDialog, setOpenInfoDialog] = useState(false);
  const [closedDays, setClosedDays] = useState<string[]>([]);
  const [showBusinessHoursExceptions, setShowBusinessHoursExceptions] =
    useState(false);
  const [prevBusinessHours, setPrevBusinessHours] = useState(defaultHours);

  const redirect = useRedirect();
  const { setValue, getValues } = useFormContext();

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.LOCATIONS);
  }, [redirect]);

  const toggleClosedBusinessDay = (key: string) => {
    const index = closedDays.indexOf(key);
    const newDays = [...closedDays];
    if (index === -1) {
      newDays.push(key);
      setValue(`businessHours.${weekKeys.indexOf(key)}`, {
        startAt: null,
        endAt: null,
      });
    } else {
      newDays.splice(index, 1);
      const daily = getValues('businessHoursSchedule');
      setValue(`businessHours.${weekKeys.indexOf(key)}`, daily);
    }

    setClosedDays(newDays);
  };

  const onDailyHoursChange = (e: any, field: 'startAt' | 'endAt') => {
    const newHours = { ...prevBusinessHours, ...{ [field]: e.target.value } };
    const businessHours = getValues('businessHours');
    weekKeys.forEach((_, idx) => {
      if (isEqual(prevBusinessHours, businessHours[idx])) {
        setValue(`businessHours.${idx}`, newHours);
      }
    });

    setPrevBusinessHours(newHours);
  };

  return (
    <>
      <ModalHeader
        handleClose={handleClose}
        title={t('sellPointsPage.addSellpoint')}
      >
        <SaveButton
          type="button"
          icon={<></>}
          alwaysEnable
          label={t('shared.save')}
        />
      </ModalHeader>

      {/* Body */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '600px',
          width: '90%',
          mx: 'auto',
          my: 8,
          gap: 6,
        }}
      >
        <Subsection
          title={t('sellPointsPage.basicInformation')}
          subtitle={t('sellPointsPage.basicInformationDescription')}
        >
          <TextInput
            source="name"
            label={t('sellPointsPage.locationBusinessName')}
            validate={required()}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  onClick={() => setOpenInfoDialog(true)}
                  position="end"
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  <Typography variant="body2" color="primary" fontWeight={500}>
                    {t('sellPointsPage.whatsThis')}
                  </Typography>
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="caption" color="custom.gray600">
            {t('sellPointsPage.locationBusinessNameDescription')}
          </Typography>
          <Box height="24px" />
          <TextInput
            source="description"
            label={t('sellPointsPage.businessDescription')}
            multiline
            inputProps={{ maxLength: 1024 }}
          />
          <FormDataConsumer>
            {({ formData }) => (
              <Typography variant="caption" color="custom.gray600">
                {formData.description?.length ?? 0} / 1024
              </Typography>
            )}
          </FormDataConsumer>
        </Subsection>
        <CustomDivider />
        <Subsection title={t('sellPointsPage.businessAddress')}>
          <ReferenceInput source="type" reference="locationTypes">
            <SelectInput
              defaultValue={'mobile'}
              type="select"
              label={t('sellPointsPage.locationType')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
          <TextInput
            source="address.line1"
            label={t('sellPointsPage.addressLine1')}
            validate={required()}
          />
          <Grid container columnSpacing={2}>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="address.line2"
                label={t('sellPointsPage.addressLine2')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="address.city"
                label={t('sellPointsPage.city')}
                validate={required()}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="address.county"
                label={t('sellPointsPage.county')}
                validate={required()}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="address.zipCode"
                label={t('sellPointsPage.zipCode')}
              />
            </Grid>
          </Grid>
        </Subsection>
        <CustomDivider />
        <Subsection title={t('sellPointsPage.contactInformation')}>
          <Grid container columnSpacing={2}>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="contactInfo.email"
                label={t('sellPointsPage.emailAddress')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="contactInfo.phoneNumber"
                label={t('sellPointsPage.phoneNumber')}
              />
            </Grid>
          </Grid>
        </Subsection>
        <Subsection title={t('sellPointsPage.socialContact')}>
          <TextInput source="contactInfo.website" label="Website" />
          <Grid container columnSpacing={2}>
            <Grid item xs={12} sm={6}>
              <TextInput source="contactInfo.facebook" label="Facebook" />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextInput source="contactInfo.instagram" label="Instagram" />
            </Grid>
          </Grid>
          <TextInput source="contactInfo.youtube" label="Youtube" />
        </Subsection>
        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.branding')}
          subtitle={t('sellPointsPage.brandingDescription')}
        >
          <Typography variant="h4" mb={2}>
            Logo
          </Typography>
          <CustomImageInput source="logoURL" />
        </Subsection>

        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.businessHours')}
          subtitle={t('sellPointsPage.businessHoursDescription')}
        >
          <FormDataConsumer>
            {({ formData }) => (
              <>
                <Grid container columnSpacing={2} mb={1}>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="body2" mt={2}>
                      {t('sellpoints.daily')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TimeInput
                      label={t('sellPointsPage.startAt')}
                      source="businessHoursSchedule.startAt"
                      // no idea why this is neccessary but it doesn't work without it
                      parse={(value: string) => value}
                      onChange={e => onDailyHoursChange(e, 'startAt')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TimeInput
                      label={t('sellPointsPage.endAt')}
                      source="businessHoursSchedule.endAt"
                      parse={(value: string) => value}
                      sx={{ marginBottom: 0 }}
                      onChange={e => onDailyHoursChange(e, 'endAt')}
                    />
                    <Typography
                      variant="caption"
                      color="custom.gray600"
                      sx={{
                        opacity:
                          formData.businessHoursSchedule?.endAt?.split(':')[0] <
                          formData.businessHoursSchedule?.startAt?.split(':')[0]
                            ? 1
                            : 0,
                      }}
                    >
                      {t('sellPointsPage.nextDay')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      // @ts-ignore
                      variant="label"
                      color="primary"
                      sx={{ cursor: 'pointer' }}
                      onClick={() =>
                        setShowBusinessHoursExceptions(prev => !prev)
                      }
                    >
                      {showBusinessHoursExceptions
                        ? t('sellPointsPage.hideExceptions')
                        : t('sellPointsPage.addExceptions')}
                    </Typography>
                  </Grid>
                </Grid>
                {showBusinessHoursExceptions &&
                  weekKeys.map((key, idx) => (
                    <Grid key={idx} container columnSpacing={2} mb={1}>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2">
                          {t(`sellpoints.${key}`)}
                        </Typography>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={closedDays.includes(key)}
                              onChange={() => toggleClosedBusinessDay(key)}
                              inputProps={{ 'aria-label': 'controlled' }}
                            />
                          }
                          label={
                            <Typography variant="body2">
                              {t('sellPointsPage.closed')}
                            </Typography>
                          }
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TimeInput
                          label={t('sellPointsPage.startAt')}
                          source={`businessHours.${idx}.startAt`}
                          parse={(value: string) => value}
                          disabled={closedDays.includes(key)}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TimeInput
                          label={t('sellPointsPage.endAt')}
                          source={`businessHours.${idx}.endAt`}
                          parse={(value: string) => value}
                          sx={{ marginBottom: 0 }}
                          disabled={closedDays.includes(key)}
                        />
                        <Typography
                          variant="caption"
                          color="custom.gray600"
                          sx={{
                            opacity:
                              formData.businessHours?.[idx]?.endAt?.split(
                                ':'
                              )[0] <
                              formData.businessHours?.[idx]?.startAt?.split(
                                ':'
                              )[0]
                                ? 1
                                : 0,
                          }}
                        >
                          {t('sellPointsPage.nextDay')}
                        </Typography>
                      </Grid>
                    </Grid>
                  ))}
              </>
            )}
          </FormDataConsumer>
        </Subsection>

        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.bankInformation')}
          subtitle={t('sellPointsPage.bankInformationDescription')}
        >
          <ReferenceInput source="bankAccountId" reference="bankAccounts">
            <SelectInput
              type="select"
              label={t('sellPointsPage.transferAccount')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
        </Subsection>

        <CustomDivider />
        <Subsection
          title={t('sellPointsPage.preferredLanguage')}
          subtitle={t('sellPointsPage.preferredLanguageDescription')}
        >
          <ReferenceInput source="localization" reference="languages">
            <SelectInput
              defaultValue={'en-EN'}
              type="select"
              label={t('sellPointsPage.selectLanguage')}
              optionText="name"
              optionValue="id"
              validate={required()}
            />
          </ReferenceInput>
        </Subsection>
      </Box>
      <InfoPopup
        open={openInfoDialog}
        onClose={() => setOpenInfoDialog(false)}
      />
    </>
  );
};

export const SellpointCreate = () => {
  const transform = (data: any) => {
    const exceptions: any = {};
    data.businessHours.forEach((hours: any, idx: number) => {
      if (!isEqual(hours, data.businessHoursSchedule)) {
        exceptions[idx] = hours;
      }
    });
    return {
      ...data,
      businessHoursExceptions: exceptions,
      fullName: `${data.firstName} ${data.lastName}`,
    };
  };

  return (
    <CreateDialog {...getFullscreenModalProps()} transform={transform}>
      <SimpleForm
        toolbar={false}
        sx={{ p: 0 }}
        defaultValues={() => ({
          businessHours: Array(7).fill(defaultHours),
          businessHoursSchedule: defaultHours,
        })}
      >
        <SellpointCreateInner />
      </SimpleForm>
    </CreateDialog>
  );
};
