import { useState } from 'react';
import {
  Box,
  Divider,
  Menu,
  MenuItem,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import CustomDeleteWithConfirmButton from './CustomDeleteWithConfirmButton';
interface ModalTitleWithMenuProps {
  name: string;
  id: number;
  editButtonText?: string;
  deleteButtonText?: string;
  editModal?: React.ReactNode;
  openEditModal?: boolean;
  setOpenEditModal?: (open: boolean) => void;
  hasEdit?: boolean;
  hasDelete?: boolean;
  deleteOptions?: any;
}

export default function ModalTitleWithMenu({
  name,
  editButtonText,
  deleteButtonText,
  editModal,
  setOpenEditModal,
  hasEdit=true,
  hasDelete=true,
  deleteOptions
}: ModalTitleWithMenuProps) {
  const { t } = useTranslation('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const [uniqueId] = useState(() =>
    Math.random().toString(36).substring(2, 15)
  );

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleMenuClose();
    const deleteButton = document.querySelector(
      `#dwcb-${uniqueId} .ra-delete-button`
    ) as HTMLElement;
    if (deleteButton) {
      deleteButton.click();
    }
  };

  return (
    <Box
      sx={{
        '& div:last-of-type': {
          color: 'primary.main',
        },
        ...flexStyles,
      }}
    >
      <div>
        <Typography
          variant="h3"
          display="inline-block"
          onClick={handleMenuOpen}
          sx={{ cursor: 'pointer', color: 'primary.main' }}
        >
          {name}
        </Typography>
      </div>

      {editModal}

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        MenuListProps={{
          autoFocusItem: false,
        }}
      >
        {hasEdit && (
          <MenuItem
            onClick={() => {
              setAnchorEl(null);
              setOpenEditModal?.(true);
            }}
        >
            {editButtonText || t('menu.editMenu')}
          </MenuItem>
        )}

        <Divider sx={{ borderColor: 'rgba(0, 0, 0, 0.05)', my: 1, mx: 1 }} />

        {hasDelete && (
          <MenuItem
            onClick={handleDeleteClick}
            sx={{ color: '#DC4437' }}
          >
            {deleteButtonText || t('menu.deleteMenu')}
          </MenuItem>
        )}
      </Menu>

      <div id={`dwcb-${uniqueId}`} style={{ display: 'none' }}>
        <CustomDeleteWithConfirmButton
          field="name"
          mutationMode={deleteOptions?.mutationMode}
          mutationOptions={deleteOptions?.mutationOptions}
          {...deleteOptions}
        />
      </div>
    </Box>
  );
}

const flexStyles = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
  flexWrap: 'wrap',
};
