/* eslint-disable @typescript-eslint/no-explicit-any */
import { isNumberPrimitive } from './isNumber';
import { isObject } from './isObject';

/**
 * Merges the properties of the source object into the target object, summing numeric values.
 *
 * @param {any} target - The target object to merge properties into.
 * @param {any} source - The source object whose properties will be merged into the target. The source object is cloned before merging.
 *
 *
 * @remarks
 * - If the source is not an object or is null, the function returns without making any changes.
 * - Undefined values in the source object are ignored.
 * - For nested objects, the function ensures that the target has an object at the corresponding key and recursively merges the nested objects.
 * - Numeric values in the source object are summed with numeric values in the target object. If the target field is not a number, it is overwritten by the source value.
 * - If the source field is an array, the function concatenates the arrays if the target field is also an array. Otherwise, the target field is overwritten by the source value.
 * - For all other cases, the target field is overwritten by the source value.
 *
 * @example
 * ```typescript
 * const target = { a: 1, b: { c: 2 } };
 * const source = { a: 2, b: { c: 3, d: 4 }, e: 5 };
 * mergeObjectsSummingNumbers(target, source);
 * console.log(target); // { a: 3, b: { c: 5, d: 4 }, e: 5 }
 * ```
 * @return {boolean} - Returns true if any values were merged, otherwise false.
 */
export function mergeObjectsSummingNumbers(target: any, source: any): boolean {
  // Early return for non-objects
  if (!isObject(source)) {
    return false;
  }

  let hasValues = false;

  // Direct iteration without cloning
  for (const key in source) {
    if (!Object.prototype.hasOwnProperty.call(source, key)) continue;

    const sourceValue = source[key];

    // Skip undefined immediately
    if (sourceValue === undefined) {
      continue;
    }

    // Fast path for numbers (most common case in reported usage)
    if (isNumberPrimitive(sourceValue)) {
      if (sourceValue !== 0) {
        const targetValue = target[key];
        if (isNumberPrimitive(targetValue)) {
          target[key] = targetValue + sourceValue;
        } else {
          target[key] = sourceValue;
        }
        hasValues = true;
      }
      continue;
    }

    // Handle objects
    if (isObject(sourceValue)) {
      const targetValue = isObject(target[key]) ? target[key] : {};

      if (mergeObjectsSummingNumbers(targetValue, sourceValue)) {
        target[key] = targetValue;
        hasValues = true;
      }
      continue;
    }

    // Handle arrays
    if (Array.isArray(sourceValue)) {
      if (sourceValue.length > 0) {
        if (Array.isArray(target[key])) {
          // Faster than concat for small arrays
          const targetArray = target[key];
          for (let i = 0; i < sourceValue.length; i++) {
            targetArray.push(sourceValue[i]);
          }
        } else {
          target[key] = [...sourceValue]; // Create a new array
        }
        hasValues = true;
      }
      continue;
    }

    // Default case: just assign the value
    target[key] = sourceValue;
    hasValues = true;
  }

  return hasValues;
}

/**
 * Merges two objects by summing the values of properties with the same keys into a new object.
 *
 * This function clones both the target and source objects to ensure that the original objects
 * are not mutated. It then merges the cloned objects by summing the values of properties with
 * the same keys and returns the resulting new object.
 *
 * @param {any} target - The target object to merge into.
 * @param {any} source - The source object to merge from.
 * @return {any} A new object with merged properties, where values of properties with the same keys are summed.
 */
export function mergeObjectsSummingNumbersIntoNewObject(
  target: any,
  source: any
): any {
  // Clone target object to prevent mutation, the source object is cloned inside mergeObjectsSummingNumbers
  const newTarget = structuredClone(target);
  // Mutate the cloned target using the cloned source
  mergeObjectsSummingNumbers(newTarget, source);
  // Return the mutated clone
  return newTarget;
}
