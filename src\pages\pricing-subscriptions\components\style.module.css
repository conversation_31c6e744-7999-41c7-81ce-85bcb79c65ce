.table {
  border-collapse: collapse;
  width: 100%;
  margin: 2rem 0;
  overflow-x: auto;
}

.th,
.td {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  padding: 10px;
  text-align: center;
  padding-top: 20px;
  padding-bottom: 20px;
}

.th {
  background-color: white;
  font-weight: bold;
}

.th-dark {
  background-color: transparent;
  font-weight: bold;
}

.category {
  background-color: #f2f2f2;
}

.category-dark {
  background-color: #515151;
}

.td:first-child {
  border-left: 1px solid transparent;
}

.td:last-child {
  border-right: 1px solid transparent;
}

.th:first-child .th-dark:first-child {
  border-left: 1px solid transparent;
}

.th:last-child .th-dark:last-child {
  border-right: 1px solid transparent;
}

.th-dark:first-child {
  border-right: 1px solid white;
}

.th-dark:last-child {
  border-left: 1px solid white;
}
