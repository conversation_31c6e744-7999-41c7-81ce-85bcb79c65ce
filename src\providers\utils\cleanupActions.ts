type CleanupFunction = () => void;

export default {
  cleanupFunctions: [] as CleanupFunction[],
  register(cleanup: CleanupFunction) {
    console.log('Registering cleanup function...');
    this.cleanupFunctions.push(cleanup);
    return () => this.unregister(cleanup);
  },
  unregister(cleanup: CleanupFunction) {
    this.cleanupFunctions = this.cleanupFunctions.filter(fn => fn !== cleanup);
  },
  runAll() {
    // we need to run the cleanup functions in reverse order
    this.cleanupFunctions.reverse();
    console.log('Running cleanup functions...');
    for (const cleanup of this.cleanupFunctions) {
      try {
        cleanup();
      } catch (error) {
        console.error('Error in cleanup function:', error);
      }
    }
    this.cleanupFunctions = [];
  },
};
