import { useCallback, useEffect, useMemo, useState } from 'react';
import { Box } from '@mui/material';
import { useDataProvider } from 'react-admin';
import { useTranslation } from 'react-i18next';

import GroupingTable from '~/components/organisms/CustomTable/otherTables/GroupingTable';
import capitalize from '~/utils/capitalize';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';
import mergeAndSumObjects from '~/utils/mergeAndSumObjects';
import { ColumnConfig, FieldOption } from '../../../../../types/globals';
import ExtraDataModal from './ExtraDataModifierModal';

interface TableRow {
  id: string;
  variant: string;
  groupId: string;
  discountsValue: number;
  couponsValue: number;
  promotionsValue: number;
  netValue: number;
  measureUnit: string;
  vat: number | undefined;
  prepStation: string;
  name: string;
  groupName: string;
  quantity: number;
  value: number;
  items?: TableRow[];
  subItems?: TableRow[];
  extraData?: { [key: string]: any };
}

const baseModifierSalesTableConfig: ColumnConfig<TableRow>[] = [
  {
    id: 'id',
    textAlign: 'start',
    label: 'modifierSales.modifier',
    render: (row: TableRow) => {
      if (row.subItems && row.subItems.length > 0) {
        return (
          <span
            style={{
              fontSize: '14px',
              whiteSpace: 'nowrap',
            }}
          >
            {row.id.includes('@none')
              ? 'Regular'
              : row?.id.includes('Vat')
                ? capitalize(row?.id?.toLowerCase())
                : row.id}
          </span>
        );
      } else {
        return (
          <Box
            sx={{
              width: { xs: '100%', sm: '' },
              fontSize: '14px',
              minWidth: { xs: '', sm: '230px' },
              maxWidth: { xs: '100px', sm: '' },
              whiteSpace: 'normal',
            }}
          >
            {row.name === '@none'
              ? 'Regular'
              : capitalize(row?.name?.toLowerCase()) || row.id}{' '}
          </Box>
        );
      }
    },
  },
  {
    id: 'vat',
    render: (row: TableRow) => {
      return (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {typeof row.vat === 'number' ? row.vat + '%' : row.vat}
        </div>
      );
    },
    label: 'shared.tva',
    textAlign: 'end',
  },
  {
    id: 'prepStation',
    textAlign: 'end',
    label: 'itemSales.prepStation',
  },
  {
    id: 'groupId',
    textAlign: 'end',
    label: 'shared.category',
    render: (row: TableRow) => {
      return <>{row.groupName}</>;
    },
  },
  {
    id: 'measureUnit',
    label: 'modifierSales.unit',
    textAlign: 'end',
    render: (row: TableRow) => {
      return <>{row.measureUnit === 'undefined' ? '' : row.measureUnit} </>;
    },
  },
  {
    id: 'quantity',
    textAlign: 'end',
    label: 'itemSales.itemsSold',
    render: (row: TableRow) => {
      return (
        <>{Intl.NumberFormat('ro-RO').format(Number(row.quantity) / 1000)} </>
      );
    },
  },
  {
    id: 'value',
    textAlign: 'end',
    label: 'itemSales.grossSales',
    render: (row: TableRow) => {
      return <>{formatAndDivideNumber(row.value)}</>;
    },
  },
  {
    id: 'discountsValue',
    label: 'itemSales.discounts',
    textAlign: 'end',
    render: (row: TableRow) => {
      return (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.discountsValue
            ? '- ' + formatAndDivideNumber(row?.discountsValue)
            : ''}
        </div>
      );
    },
  },
  {
    id: 'couponsValue',
    label: 'itemSales.coupons',
    textAlign: 'end',
    render: (row: TableRow) => {
      return (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.couponsValue
            ? '- ' + formatAndDivideNumber(row?.couponsValue)
            : ''}
        </div>
      );
    },
  },
  {
    id: 'promotionsValue',
    label: 'itemSales.promotions',
    textAlign: 'end',
    render: (row: TableRow) => {
      return (
        <div
          style={{
            whiteSpace: 'nowrap',
            fontSize: '14px',
            //@ts-ignore
            '@media print': {
              backgroundColor: '#FFFFFF !important',
              color: 'black !important',
            },
          }}
        >
          {row?.promotionsValue
            ? '- ' + formatAndDivideNumber(row?.promotionsValue)
            : ''}
        </div>
      );
    },
  },
  {
    id: 'netValue',
    label: 'shared.netSales',
    textAlign: 'end',
    render: (row: TableRow) => {
      return <>{formatAndDivideNumber(row.netValue)}</>;
    },
  },
];

export default function ModifierSalesTable({
  tableData,
  fields,
  groupingItems,
  setFields,
  onChangeGrouping,
  formattedFilters,
  reportType,
  rawData,
  composedFilters,
  filters,
}: {
  tableData: any;
  groupingItems: string[];
  formattedFilters: any;
  fields: FieldOption[];
  onChangeGrouping?: (items: any[]) => void;
  reportType: string;
  rawData: any;
  composedFilters: any;
  filters: any;
  setFields: React.Dispatch<React.SetStateAction<FieldOption[]>>;
}) {
  const { t } = useTranslation();

  const modifierSalesTableConfig = useMemo(() => {
    return baseModifierSalesTableConfig.map(column => ({
      ...column,
      label: t(column.label),
    }));
  }, [t]);

  const modifierSalesTableData: TableRow[] = useMemo(() => {
    if (tableData && tableData.length > 0) {
      let mappedTableData = tableData as unknown as TableRow[];
      mappedTableData.sort((a, b) => Number(b.value) - Number(a.value));
      let totalItemsData = mergeAndSumObjects(
        mappedTableData || {}
      ) as TableRow;
      totalItemsData.id = 'Total';
      totalItemsData.name = 'Total';
      totalItemsData.subItems = [];
      totalItemsData.groupName = '';
      totalItemsData.vat = undefined;
      totalItemsData.groupId = '';
      totalItemsData.prepStation = '';
      totalItemsData.measureUnit = '';

      mappedTableData = [...mappedTableData, totalItemsData];
      return mappedTableData;
    }

    return [];
  }, [tableData]);

  const columnsToFilter = useMemo(() => {
    const columns = [
      'groupId',
      'measureUnit',
      'variant',
      'vat',
      'quantity',
      'couponsValue',
      'discountsValue',
      'promotionsValue',
      'prepStation',
      'value',
    ];
    return columns.filter(item => {
      return !groupingItems.includes(item);
    });
  }, [groupingItems, fields]);

  const groupingOptions = [
    { value: 'vat', label: t('shared.tva') },
    { value: 'groupId', label: t('shared.category_capitalize') },
    { value: 'prepStation', label: t('reportsPage.prepStation') },
    { value: 'measureUnit', label: t('reportsPage.unit') },
  ];

  return (
    <>
      <Box sx={{ py: 7, width: '100%' }}>
        <GroupingTable
          config={modifierSalesTableConfig}
          data={modifierSalesTableData}
          fields={fields}
          groupingOptions={groupingOptions}
          setFields={setFields}
          groupingItems={groupingItems}
          onChangeGrouping={onChangeGrouping}
          columnsToFilter={columnsToFilter}
          scrollable={true}
          separateFirstColumn={true}
          enableInfoModal={true}
          fixedFirstColumn={true}
          renderModalContent={rowData => (
            <ExtraDataModal
              extraData={{
                composedFilters,
                rawData,
                reportType,
                filters,
                rowData,
                formattedFilters,
              }}
            />
          )}
        />
      </Box>
    </>
  );
}
