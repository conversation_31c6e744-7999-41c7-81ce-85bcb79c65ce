/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Firestore Data Provider for React Admin
 *
 * This data provider connects React Admin to Firebase Firestore, supporting both:
 * 1. Collection resources - stored as documents in Firestore collections
 * 2. Embedded map resources - stored as fields within Firestore documents
 *
 * Key features:
 * - Collection resources use soft delete (setting _d flag)
 * - Embedded map resources use real delete (removing the field)
 * - Optimized batch operations for embedded map resources
 * - Timestamp tracking for all write operations
 */

import {
  collection,
  deleteDoc,
  deleteField,
  doc,
  documentId,
  getCountFromServer,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  Query,
  serverTimestamp,
  setDoc,
  startAfter,
  where,
  WhereFilterOp,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { get, set } from 'lodash';
import {
  CreateParams,
  CreateResult,
  DataProvider,
  DeleteManyParams,
  DeleteManyResult,
  DeleteParams,
  DeleteResult,
  GetListParams,
  GetListResult,
  GetManyParams,
  GetManyReferenceParams,
  GetManyReferenceResult,
  GetManyResult,
  GetOneParams,
  GetOneResult,
  RaRecord,
  UpdateManyParams,
  UpdateManyResult,
  UpdateParams,
  UpdateResult,
  withLifecycleCallbacks,
} from 'react-admin';

import { isObject } from '~/fake-provider/reports/utils/isObject';
import { db } from '../configs/firebaseConfig';
import { generateDeviceCodeBeforeDeviceCreate } from './helpers/generateDeviceCodeBeforeDeviceCreate';
import { getOrCreateMemberIdBeforeMemberCreate } from './helpers/getOrCreateMemberIdBeforeMemberCreate';
import {
  RecordWithCollectionMetadata,
  ResourceMetadataCommon,
  ResourceMetadataUpdatedAt,
} from './types';
import { clearUndefinedFields } from './utils/clearUndefinedFields';
import { getFirestoreConverter } from './utils/firestoreConverters';
import { generateFirestoreId } from './utils/generateFirestoreId';
import { getResourceInfo } from './utils/getResourceInfo';
import { getResourcePath } from './utils/getResourcePath';

export interface FirestoreDataProvider extends DataProvider {
  generateFirestoreId: () => string;
  getAccountId: () => string;
}

interface CursorMap {
  [resource: string]: {
    lastDoc: { [page: number]: unknown };
    total?: number;
  };
}

/*
filters :
    _ninc_any - does not include any
    _inc - includes value
    _inc_any - include any
    _eq - equal
    _eq_any - equal any
    _neq - not equal
    _neq_any - not equal any
    _gt - greater than
    _gte - greater than or equal
    _lt - less than
    _lte - less than or equal
    _q - text search
*/

interface FilterState {
  hasInequality: boolean;
  inequalityField?: string;
  hasArrayContains: boolean;
  hasArrayContainsAny: boolean;
  hasIn: boolean;
  hasNotIn: boolean;
  hasNotEqual: boolean;
}

const applyFilters = (baseQuery: Query, filters: any) => {
  let q = baseQuery;
  let countQuery = baseQuery;

  const state: FilterState = {
    hasInequality: false,
    inequalityField: undefined,
    hasArrayContains: false,
    hasArrayContainsAny: false,
    hasIn: false,
    hasNotIn: false,
    hasNotEqual: false,
  };

  const addFilter = (
    fieldPath: string,
    opStr: WhereFilterOp,
    val: any
  ): boolean => {
    if (['<', '<=', '>', '>='].includes(opStr)) {
      if (state.hasInequality && state.inequalityField !== fieldPath) {
        console.warn(
          `Ignoring inequality filter on ${fieldPath} as inequality already exists on ${state.inequalityField}`
        );
        return false;
      }
      state.hasInequality = true;
      state.inequalityField = fieldPath;
    } else if (opStr === 'array-contains' && state.hasArrayContains) {
      console.warn(
        `Ignoring array-contains filter on ${fieldPath} as another array-contains already exists`
      );
      return false;
    } else if (opStr === 'array-contains-any' && state.hasArrayContainsAny) {
      console.warn(
        `Ignoring array-contains-any filter on ${fieldPath} as another array-contains-any already exists`
      );
      return false;
    } else if (opStr === 'in' && state.hasIn) {
      console.warn(
        `Ignoring 'in' filter on ${fieldPath} as another 'in' already exists`
      );
      return false;
    } else if (opStr === 'not-in') {
      if (state.hasNotIn || state.hasNotEqual) {
        console.warn(
          `Ignoring 'not-in' filter on ${fieldPath} as another 'not-in' or '!=' already exists`
        );
        return false;
      }
      state.hasNotIn = true;
    } else if (opStr === '!=') {
      if (state.hasNotIn || state.hasNotEqual) {
        console.warn(
          `Ignoring '!=' filter on ${fieldPath} as another 'not-in' or '!=' already exists`
        );
        return false;
      }
      state.hasNotEqual = true;
    }

    q = query(q, where(fieldPath, opStr, val));
    countQuery = query(countQuery, where(fieldPath, opStr, val));
    return true;
  };

  Object.entries(filters).forEach(([key, value]) => {
    if (key.endsWith('_ninc_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -9);
      if (value.length > 0) {
        addFilter(actualKey, 'not-in', value.slice(0, 10));
      }
    } else if (key.endsWith('_inc')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, 'array-contains', value);
    } else if (key.endsWith('_inc_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -8);
      if (value.length > 0) {
        addFilter(actualKey, 'array-contains-any', value.slice(0, 10));
      }
    } else if (key.endsWith('_eq')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '==', value);
    } else if (key.endsWith('_eq_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -7);
      if (value.length > 0) {
        addFilter(actualKey, 'in', value.slice(0, 30));
      }
    } else if (key.endsWith('_neq')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '!=', value);
    } else if (key.endsWith('_neq_any') && Array.isArray(value)) {
      const actualKey = key.slice(0, -8);
      if (value.length > 0) {
        addFilter(actualKey, 'not-in', value.slice(0, 10));
      }
    } else if (key.endsWith('_gt')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '>', value);
    } else if (key.endsWith('_gte')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '>=', value);
    } else if (key.endsWith('_lt')) {
      const actualKey = key.slice(0, -3);
      addFilter(actualKey, '<', value);
    } else if (key.endsWith('_lte')) {
      const actualKey = key.slice(0, -4);
      addFilter(actualKey, '<=', value);
    } else {
      // Default to exact match
      addFilter(key, '==', value);
    }
  });

  return { q, countQuery };
};

const applyFilterToArray = (dataArray: RaRecord[], filter: any) => {
  return dataArray.filter(item => {
    return Object.entries(filter).every(([key, filterValue]) => {
      if (key.endsWith('_q')) {
        const actualKey = key.slice(0, -2);
        const itemValue = item[actualKey];

        if (typeof filterValue !== 'string' || typeof itemValue !== 'string') {
          return false;
        }

        const searchValue = filterValue.toLowerCase();
        const fieldValue = itemValue.toLowerCase();

        return fieldValue.includes(searchValue);
      }
      if (key.endsWith('_ninc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -9);
        return !filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_inc') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -4);
        const itemValue = item[actualKey];
        return (
          Array.isArray(itemValue) &&
          filterValue.every(v => itemValue.includes(v))
        );
      }
      if (key.endsWith('_inc_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        const itemValue = item[actualKey];
        return (
          Array.isArray(itemValue) &&
          filterValue.some(v => itemValue.includes(v))
        );
      }
      if (key.endsWith('_eq')) {
        const actualKey = key.slice(0, -3);
        return item[actualKey] === filterValue;
      }
      if (key.endsWith('_eq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -7);
        return filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_neq')) {
        const actualKey = key.slice(0, -4);
        return item[actualKey] !== filterValue;
      }
      if (key.endsWith('_neq_any') && Array.isArray(filterValue)) {
        const actualKey = key.slice(0, -8);
        return !filterValue.includes(item[actualKey]);
      }
      if (key.endsWith('_gt')) {
        const actualKey = key.slice(0, -3);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] > filterValue
        );
      }
      if (key.endsWith('_gte')) {
        const actualKey = key.slice(0, -4);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] >= filterValue
        );
      }
      if (key.endsWith('_lt')) {
        const actualKey = key.slice(0, -3);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] < filterValue
        );
      }
      if (key.endsWith('_lte')) {
        const actualKey = key.slice(0, -4);
        return (
          typeof item[actualKey] === 'number' &&
          typeof filterValue === 'number' &&
          item[actualKey] <= filterValue
        );
      }
      // Default exact match
      return item[key] === filterValue;
    });
  });
};

const getBasicFirestoreDataProvider = (
  accountId: string
): FirestoreDataProvider => {
  const cursorMap: CursorMap = {};

  const getList = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetListParams
  ): Promise<GetListResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const { page = 1, perPage = 10 } = params.pagination ?? {};
    const { field = 'id', order = 'ASC' } = params.sort ?? {};
    const filter = params.filter ?? {};

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    let allData: RaRecord[] = [];
    let total = 0;

    try {
      if (resourceInfo.type === 'collection') {
        if (!cursorMap[resource]) {
          cursorMap[resource] = { lastDoc: {} };
        }

        total = cursorMap[resource].total ?? 0;

        const collRef = collection(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const baseQuery = query(collRef);
        const { q: filteredQuery } = applyFilters(baseQuery, filter);

        // const countSnapshot = await getCountFromServer(countQuery);
        // total = countSnapshot.data().count;

        // Always order by _c desc for consistent pagination
        let finalQuery = query(filteredQuery, orderBy('_c', 'desc'));

        // Apply cursor if not first page
        if (page > 1) {
          const previousPageCursor = cursorMap[resource].lastDoc[page - 1];
          if (previousPageCursor) {
            finalQuery = query(finalQuery, startAfter(previousPageCursor));
          }
        }

        // Apply limit
        finalQuery = query(finalQuery, limit(perPage));

        const querySnapshot = await getDocs(finalQuery);
        const docs = querySnapshot.docs;

        allData = docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as RaRecord[];

        // Store cursor for next page
        if (docs.length > 0) {
          const lastDoc = docs[docs.length - 1];
          cursorMap[resource].lastDoc[page] = lastDoc;
        }

        // Only count total on first page
        if (page === 1) {
          const countSnapshot = await getCountFromServer(filteredQuery);
          total = countSnapshot.data().count;
          cursorMap[resource].total = total;
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          return { data: [], total: 0 };
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Apply filters
        const filteredData = applyFilterToArray(arrayData, filter);

        total = filteredData.length;

        // Apply sorting
        if (field && order) {
          filteredData.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            if (aValue < bValue) return order === 'ASC' ? -1 : 1;
            if (aValue > bValue) return order === 'ASC' ? 1 : -1;
            return 0;
          });
        }

        // Apply pagination
        if (perPage === -1) {
          allData = filteredData;
        } else {
          const startIndex = (page - 1) * perPage;
          allData = filteredData.slice(startIndex, startIndex + perPage);
        }
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          return { data: [], total: 0 };
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Apply filters
        const filteredData = applyFilterToArray(arrayData, filter);

        total = filteredData.length;

        // Apply sorting
        if (field && order) {
          filteredData.sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            if (aValue < bValue) return order === 'ASC' ? -1 : 1;
            if (aValue > bValue) return order === 'ASC' ? 1 : -1;
            return 0;
          });
        }

        // Apply pagination
        if (perPage === -1) {
          allData = filteredData;
        } else {
          const startIndex = (page - 1) * perPage;
          allData = filteredData.slice(startIndex, startIndex + perPage);
        }
      }

      return {
        data: allData as RecordType[],
        total: total,
      };
    } catch (error) {
      console.error(`FirestoreDP: getList ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const getOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetOneParams
  ): Promise<GetOneResult<RecordType>> => {
    console.log('FirestoreDP: getOne', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const recordId = params.id;

    try {
      if (resourceInfo.type === 'collection') {
        const docRef = doc(db, resourcePath, recordId).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const data = {
            id: docSnapshot.id,
            ...docSnapshot.data(),
          } as RaRecord;
          if (data._d === true) {
            throw new Error('Document not found'); // Treat soft-deleted as not found
          }
          return { data: data as RecordType };
        } else {
          throw new Error('Document not found');
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found'); // Or 'Parent document not found'
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];
        const item = arrayData.find((item: any) => {
          if (isObject(item)) {
            return item.id === recordId;
          } else {
            return false;
          }
        });

        if (!item) {
          throw new Error('Item not found');
        }

        return { data: item as RecordType };
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];
        const item = arrayData.find((item: any) => {
          if (isObject(item)) {
            return item.id === recordId;
          } else {
            return false;
          }
        });

        if (!item) {
          throw new Error('Item not found');
        }

        return { data: item as RecordType };
      }
      return Promise.resolve<GetOneResult>({ data: null });
    } catch (error) {
      if (error instanceof Error && error.message === 'Document not found') {
        throw error;
      }
      // Throw a generic error for other issues
      throw new Error(
        `Failed to get record ${resource}/${recordId}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  };

  const getMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyParams
  ): Promise<GetManyResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const ids = params.ids;
    if (!ids || ids.length === 0) {
      return { data: [] };
    }

    let allData: RecordType[] = [];

    try {
      if (resourceInfo.type === 'collection') {
        const collRef = collection(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const batchSize = 30; // Firestore 'in' query limit

        for (let i = 0; i < ids.length; i += batchSize) {
          const batchIds = ids.slice(i, i + batchSize);
          if (batchIds.length > 0) {
            const q = query(collRef, where(documentId(), 'in', batchIds));
            const querySnapshot = await getDocs(q);
            const batchData = querySnapshot.docs.map(
              doc => ({ id: doc.id, ...doc.data() }) as RecordType
            );
            allData.push(...batchData);
          }
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          // Filter array to get only requested items
          allData = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return ids.includes(item.id);
            } else {
              return false;
            }
          });
        }
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();
          const arrayData = get(docData, resourceInfo.field) || [];

          // Filter array to get only requested items
          allData = arrayData.filter((item: any) => {
            if (isObject(item)) {
              return ids.includes(item.id);
            } else {
              return false;
            }
          });
        }
      }

      return { data: allData };
    } catch (error) {
      console.error(`FirestoreDP: getMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const getManyReference = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: GetManyReferenceParams
  ): Promise<GetManyReferenceResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const { page = 1, perPage = 10 } = params.pagination ?? {};
    const { field = 'id', order = 'ASC' } = params.sort ?? {};
    const filter = params.filter ?? {}; // Get base filters

    filter[params.target] = params.id;

    return getList<RecordType>(resource, {
      pagination: { page, perPage },
      sort: { field, order },
      filter: filter,
      meta: params.meta,
      signal: params.signal,
    });
  };

  const create = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: CreateParams
  ): Promise<CreateResult<RecordType>> => {
    console.log('firestore create', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const { id: inputId, ...coreData } = params.data;
    const metaCommon: ResourceMetadataCommon = {
      _c: serverTimestamp(),
      _u: serverTimestamp(),
    };

    try {
      if (resourceInfo.type === 'collection') {
        let docId: string;
        if (inputId) {
          // Use the provided ID
          docId = inputId.toString();
        } else {
          // Generate firebase auto-generated ID
          docId = generateFirestoreId();
        }
        const docRef = doc(db, resourcePath, docId).withConverter(
          getFirestoreConverter(resource)
        );
        const dataWithCollectionMetaAndId: RecordWithCollectionMetadata = {
          ...coreData,
          ...metaCommon,
          _a: accountId,
          _b: resourceInfo.businessType,
          _d: resourceInfo.hasSoftDelete ? false : undefined,
          _e: eventId,
          id: docId,
        };
        await setDoc(docRef, dataWithCollectionMetaAndId);

        const resultData = {
          ...clearUndefinedFields(dataWithCollectionMetaAndId),
          _c: eventTimestamp,
          _u: eventTimestamp,
        };

        return { data: resultData as unknown as RecordType, meta: params.meta };
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        let recordId: string | number;

        if (inputId) {
          recordId = inputId;
        } else {
          recordId = generateFirestoreId();
        }
        // create the update payload that will be used to update the document
        const updatePayload = {
          _a: accountId,
        };
        let payload = {};
        let resultData = {};
        if (coreData._isJustAValue) {
          payload = {
            ...coreData,
            id: String(recordId),
          };
          resultData = {
            ...clearUndefinedFields(payload),
          };
        } else {
          payload = {
            ...coreData,
            ...metaCommon,
            _d: resourceInfo.hasSoftDelete ? false : undefined,
            _e: eventId,
            id: String(recordId),
          };
          resultData = {
            ...clearUndefinedFields(payload),
            _c: eventTimestamp,
            _u: eventTimestamp,
          };
        }
        set(updatePayload, `${resourceInfo.field}.${recordId}`, payload);

        // Use setDoc with merge option to update the embedded map because it is possible that the parent document does not exist yet.
        await setDoc(docRef, updatePayload, { merge: true });

        return { data: resultData as unknown as RecordType, meta: params.meta };
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);
        const existingData = docSnapshot.exists() ? docSnapshot.data() : {};
        const existingArray = get(existingData, resourceInfo.field) || [];

        let dataWithId: any;
        let recordId: string | number;

        const updatedArray = [];
        updatedArray.push(...existingArray);

        const newIndex = updatedArray.length;

        if (coreData._isJustAValue !== undefined && coreData._isJustAValue) {
          recordId = `${newIndex + 1}`;
          dataWithId = {
            id: recordId,
            value: coreData.value,
            _index: newIndex,
            _isJustAValue: true,
          };
        } else {
          if (inputId) {
            recordId = inputId;
          } else {
            recordId = `${newIndex + 1}`;
          }
          dataWithId = {
            ...coreData,
            id: recordId,
            _index: newIndex,
            _e: eventId,
          };
        }
        updatedArray.push(dataWithId);

        const updatePayload = {
          _a: accountId,
        };
        set(updatePayload, resourceInfo.field, updatedArray);

        // Update document
        await setDoc(docRef, updatePayload, { merge: true });

        return { data: dataWithId as unknown as RecordType, meta: params.meta };
      }
      return Promise.resolve<GetOneResult>({ data: null });
    } catch (error) {
      console.error(`FirestoreDP: create ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const update = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateParams
  ): Promise<UpdateResult<RecordType>> => {
    console.log('FirestoreDP: update', resource, params);
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const recordId = params.id;
    // Exclude 'id' from the data payload, as it shouldn't be changed.
    const { id: _dataId, ...coreData } = params.data; // Use _dataId to indicate unused variable
    const resultData = {
      ...(params.previousData ?? {}),
      ...coreData,
      id: recordId,
    };
    const metaUpdatedAt: ResourceMetadataUpdatedAt = {
      _u: serverTimestamp(), // Update timestamp on modification
    };

    try {
      if (resourceInfo.type === 'collection') {
        const docRef = doc(db, resourcePath, recordId).withConverter(
          getFirestoreConverter(resource)
        );
        const dataToUpdate = {
          ...coreData,
          ...metaUpdatedAt,
          _e: eventId,
        };
        await setDoc(docRef, dataToUpdate, { merge: true });
        // we add the updated timestamp for the result
        resultData._u = eventTimestamp;
        resultData._e = eventId;
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const updatePayload = {};
        let payload = {};
        if (coreData._isJustAValue) {
          payload = {
            ...coreData,
            id: String(recordId),
          };
        } else {
          payload = {
            ...coreData,
            ...metaUpdatedAt,
            _e: eventId,
          };
          // we add the updated timestamp for the result
          resultData._u = eventTimestamp;
          resultData._e = eventId;
        }
        set(updatePayload, `${resourceInfo.field}.${recordId}`, payload);
        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        const itemIndex = arrayData.findIndex((item: any) => {
          if (isObject(item)) {
            return item.id === recordId;
          } else {
            return false;
          }
        });

        if (itemIndex === -1) {
          throw new Error('Item not found');
        }

        const updatedItem = {
          ...arrayData[itemIndex],
          ...coreData,
          _e: eventId,
        };
        if (
          updatedItem._isJustAValue !== undefined &&
          updatedItem._isJustAValue
        ) {
          delete updatedItem._e;
        } else {
          resultData._e = updatedItem._e;
        }
        // serverTimestamp() is not supported in embedded arrays we use coreData
        // Update array
        const newArray = [...arrayData];
        newArray[itemIndex] = updatedItem;

        // Update document
        const updatePayload = {};
        set(updatePayload, resourceInfo.field, newArray);
        await setDoc(docRef, updatePayload, { merge: true });
      }
      return { data: resultData as unknown as RecordType };
    } catch (error) {
      console.error(
        `FirestoreDP: update ERROR [${resource}/${recordId}]:`,
        error
      );
      throw error;
    }
  };

  const updateMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: UpdateManyParams
  ): Promise<UpdateManyResult> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const ids = params.ids;

    const { id: _dataId, ...coreData } = params.data; // Use _dataId to indicate unused variable
    const dataToUpdate = {
      ...coreData,
      _u: serverTimestamp(), // Update timestamp on modification
      _e: eventId,
    };

    try {
      if (resourceInfo.type === 'collection') {
        await Promise.all(
          ids.map(id =>
            update<RecordType>(resource, {
              id,
              data: params.data,
              previousData: { id } as Partial<RecordType>, // Provide at least the ID
              meta: params.meta,
            })
          )
        );
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const updatePayload = {};
        if (coreData._isJustAValue) {
          delete coreData._u;
          delete coreData._e;
        }

        ids.forEach(id => {
          set(updatePayload, `${resourceInfo.field}.${id}`, dataToUpdate);
        });

        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Update matching items
        const updatedArray = arrayData.map((item: any) => {
          if (isObject(item)) {
            if (ids.includes(item.id)) {
              return {
                ...item,
                ...coreData,
              };
            }
          } else {
            return item;
          }
        });

        const updatePayload = {};
        set(updatePayload, resourceInfo.field, updatedArray);

        // Update document
        await setDoc(docRef, updatePayload, { merge: true });
      }

      return { data: ids }; // Return affected ids
    } catch (error) {
      console.error(`FirestoreDP: updateMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  const deleteOne = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteParams<RecordType>
  ): Promise<DeleteResult<RecordType>> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const recordId = params.id;

    try {
      if (resourceInfo.type === 'collection') {
        const docRef = doc(db, resourcePath, recordId as string).withConverter(
          getFirestoreConverter(resource)
        );
        if (resourceInfo.hasSoftDelete) {
          await setDoc(
            docRef,
            {
              _d: true,
              _u: serverTimestamp(),
              _e: eventId,
            },
            { merge: true }
          );
        } else {
          await deleteDoc(docRef);
        }
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const updatePayload = {};
        if (resourceInfo.hasSoftDelete) {
          set(updatePayload, `${resourceInfo.field}.${recordId}`, {
            _d: true,
            _u: serverTimestamp(),
            _e: eventId,
          });
        } else {
          // delete the field
          set(
            updatePayload,
            `${resourceInfo.field}.${recordId}`,
            deleteField()
          );
        }
        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );

        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];
        console.log('FirestoreDP: delete arrayData', arrayData);
        // Filter out the item to delete
        const newArray = arrayData.filter((item: any) => {
          if (isObject(item)) {
            return item.id !== recordId;
          } else {
            return true;
          }
        });

        const updatePayload = {};
        set(updatePayload, resourceInfo.field, newArray);

        await setDoc(docRef, updatePayload, { merge: true });
      }
      const previousDataWithId = {
        ...(params.previousData ?? {}),
        id: recordId,
      };
      return { data: previousDataWithId as RecordType };
    } catch (error) {
      console.error(
        `FirestoreDP: delete ERROR [${resource}/${recordId}]:`,
        error
      );
      throw error;
    }
  };

  const deleteMany = async <RecordType extends RaRecord = RaRecord>(
    resource: string,
    params: DeleteManyParams<RecordType>
  ): Promise<DeleteManyResult> => {
    const resourceInfo = getResourceInfo(resource, params.meta);

    const eventId: number | undefined = params.meta?._eventId;

    const eventTimestamp = params.meta?._eventTimestamp ?? new Date().getTime();

    const resourcePath = getResourcePath(resource, accountId, params.meta);

    const ids = params.ids;
    try {
      if (resourceInfo.type === 'collection') {
        await Promise.all(
          ids.map(id =>
            deleteOne<RecordType>(resource, {
              id,
              previousData: undefined, // deleteOne handles undefined previousData
              meta: params.meta,
            })
          )
        );
      } else if (resourceInfo.type === 'embeddedMap') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const updatePayload = {};
        if (resourceInfo.hasSoftDelete) {
          ids.forEach(id => {
            set(updatePayload, `${resourceInfo.field}.${id}`, {
              _d: true,
              _u: serverTimestamp(),
              _e: eventId,
            });
          });
        } else {
          ids.forEach(id => {
            set(updatePayload, `${resourceInfo.field}.${id}`, deleteField());
          });
        }

        await setDoc(docRef, updatePayload, { merge: true });
      } else if (resourceInfo.type === 'embeddedArray') {
        const docRef = doc(db, resourcePath).withConverter(
          getFirestoreConverter(resource)
        );
        const docSnapshot = await getDoc(docRef);

        if (!docSnapshot.exists()) {
          throw new Error('Document not found');
        }

        const docData = docSnapshot.data();
        const arrayData = get(docData, resourceInfo.field) || [];

        // Filter out items to be deleted
        const filteredArray = arrayData.filter((item: any) => {
          if (isObject(item)) {
            return !ids.includes(item.id);
          } else {
            return true;
          }
        });

        const updatePayload = {};
        set(updatePayload, resourceInfo.field, filteredArray);

        await setDoc(docRef, updatePayload, { merge: true });
      }
      // Return the IDs of the deleted records
      return { data: ids };
    } catch (error) {
      console.error(`FirestoreDP: deleteMany ERROR [${resource}]:`, error);
      throw error;
    }
  };

  // Return the provider object
  return {
    getList: getList,
    getOne: getOne,
    getMany: getMany,
    getManyReference: getManyReference,
    create: create,
    update: update,
    updateMany: updateMany,
    delete: deleteOne,
    deleteMany: deleteMany,
    subscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    unsubscribe: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    publish: (_: string, __: unknown) => {
      return Promise.resolve({ data: null });
    },
    generateFirestoreId: generateFirestoreId,
    getAccountId: () => accountId,
  };
};

export const getFirestoreDataProvider = (
  accountId: string
): FirestoreDataProvider => {
  const basicDataProvider = getBasicFirestoreDataProvider(accountId);
  return withLifecycleCallbacks(basicDataProvider, [
    getOrCreateMemberIdBeforeMemberCreate,
    generateDeviceCodeBeforeDeviceCreate,
  ]) as FirestoreDataProvider;
};
