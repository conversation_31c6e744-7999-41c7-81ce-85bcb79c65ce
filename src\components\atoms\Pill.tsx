import { PropsWithChildren } from 'react';
import { Box, BoxProps } from '@mui/material';

type PillProps = { sx?: BoxProps, bgColor?: string, color?: string } & PropsWithChildren;
export default function Pill({ children, sx, bgColor = 'background.tinted', color = 'text.primary' }: PillProps) {
  return (
    <Box
      gap={0.5}
      sx={{
        display: 'flex',
        width: 'fit-content',
        color: color,
        bgcolor: bgColor,
        borderRadius: 4,
        paddingY: 0.5,
        paddingX: 1.5,
        ...sx,
      }}
    >
      {children}
    </Box>
  );
}
