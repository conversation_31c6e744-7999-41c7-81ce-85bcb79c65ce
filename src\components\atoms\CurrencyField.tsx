import React from 'react';
import { <PERSON>Field, NumberFieldProps } from 'react-admin';

export interface CurrencyFieldProps
  extends Omit<NumberFieldProps, 'options' | 'source'> {
  /**
   * The source field name in the record (optional if defaultValue is provided)
   */
  source?: string;

  /**
   * The currency code to use (e.g., 'RON', 'USD', 'EUR')
   */
  currency?: string;

  /**
   * How to display the currency symbol
   * 'symbol' | 'narrowSymbol' | 'code' | 'name'
   */
  currencyDisplay?: 'symbol' | 'narrowSymbol' | 'code' | 'name';

  /**
   * Whether to divide the value by 10000 (for storing amounts as integer cents)
   */
  isStoredInCents?: boolean;

  /**
   * Additional Intl.NumberFormatOptions options
   */
  additionalOptions?: Partial<Intl.NumberFormatOptions>;
}

/**
 * Creates a transform function that converts values appropriately based on settings
 */
const createTransform = (isStoredInCents: boolean) => (value: any) => {
  // First convert string numbers to actual numbers
  const numericValue =
    value && typeof value === 'string' && !isNaN(value as any) ? +value : value;

  // Then apply cents conversion if needed
  return isStoredInCents && numericValue != null
    ? Number(numericValue) / 10000
    : numericValue;
};

/**
 * Field component to display currency values with consistent formatting
 *
 * @example
 * <CurrencyField source="price" currency="RON" locales="ro-RO" />
 * <CurrencyField defaultValue={record.price} currency="USD" />
 */
export const CurrencyField: React.FC<CurrencyFieldProps> = ({
  source,
  defaultValue,
  locales = 'en-IE',
  currency = 'EUR',
  currencyDisplay = 'narrowSymbol',
  isStoredInCents = true,
  additionalOptions = {},
  ...rest
}) => {
  // Create transform function with current isStoredInCents setting
  const transform = createTransform(isStoredInCents);

  // Only transform defaultValue if it exists
  // (otherwise the transform function will handle source values)
  const transformedDefaultValue =
    defaultValue !== undefined ? transform(defaultValue) : undefined;

  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    currencyDisplay,
    useGrouping: true,
    ...additionalOptions,
  };

  return (
    <NumberField
      // Always pass source (empty string if not provided)
      source={source || ''}
      defaultValue={transformedDefaultValue}
      locales={locales}
      options={options}
      // Only use transform for source values
      {...(source ? { transform } : {})}
      {...rest}
    />
  );
};
