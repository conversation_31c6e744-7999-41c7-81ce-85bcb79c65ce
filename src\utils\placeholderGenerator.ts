/**
 * Generates placeholder images for temporary files
 * Creates SVG-based placeholders that indicate the file is being processed
 */

import { UploadedFile } from '~/types/fileUpload';

export interface PlaceholderOptions {
    width?: number;
    height?: number;
    text?: string;
    subText?: string;
    backgroundColor?: string;
    textColor?: string;
    fontSize?: number;
    icon?: string;
}

/**
 * Generate a data URL for a placeholder image
 */
export const generatePlaceholderDataUrl = (options: PlaceholderOptions = {}): string => {
    const {
        width = 400,
        height = 300,
        text = 'Processing...',
        subText = '',
        backgroundColor = '#f5f5f5',
        textColor = '#666666',
        fontSize = 16,
        icon = ''
    } = options;

    const svg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="${backgroundColor}"/>
            <g>
                <rect x="20%" y="35%" width="60%" height="15%" rx="4" fill="#e0e0e0"/>
                <circle cx="30%" cy="25%" r="8" fill="#e0e0e0"/>
                ${icon ? `<text x="50%" y="60%" font-family="Arial, sans-serif" font-size="${fontSize + 8}"
                          text-anchor="middle" fill="${textColor}">${icon}</text>` : ''}
                <text x="50%" y="${icon ? '75%' : '65%'}" font-family="Arial, sans-serif" font-size="${fontSize}"
                      text-anchor="middle" fill="${textColor}" font-weight="500">${text}</text>
                ${subText ? `<text x="50%" y="${icon ? '85%' : '75%'}" font-family="Arial, sans-serif" font-size="${fontSize - 2}"
                            text-anchor="middle" fill="${textColor}" opacity="0.7">${subText}</text>` : ''}
            </g>
        </svg>
    `;

    // Convert SVG to data URL
    const encodedSvg = encodeURIComponent(svg);
    return `data:image/svg+xml,${encodedSvg}`;
};

/**
 * Generate a placeholder specifically for a temporary file
 */
export const generateTempFilePlaceholder = (file: UploadedFile, context?: 'original' | 'preview'): string => {
    const fileName = `${file.rn}.${file.e}`;
    const fileType = file.e.toUpperCase();

    // More meaningful messages based on context and file type
    let message = '';
    let subMessage = '';

    if (context === 'original') {
        message = 'Original file is being processed';
        subMessage = 'Preview will be available once processing is complete';
    } else {
        message = `${fileType} file uploaded successfully`;
        subMessage = 'Processing in progress...';
    }

    // Different placeholders based on file type
    if (['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp', 'tiff'].includes(file.e.toLowerCase())) {
        return generatePlaceholderDataUrl({
            width: 400,
            height: 300,
            text: message,
            subText: subMessage,
            backgroundColor: '#f8f9fa',
            textColor: '#6c757d',
            fontSize: 14,
            icon: '🖼️'
        });
    }

    if (['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv'].includes(file.e.toLowerCase())) {
        return generatePlaceholderDataUrl({
            width: 400,
            height: 300,
            text: message,
            subText: subMessage,
            backgroundColor: '#1a1a1a',
            textColor: '#ffffff',
            fontSize: 14,
            icon: '🎥'
        });
    }

    // Generic file placeholder
    return generatePlaceholderDataUrl({
        width: 400,
        height: 300,
        text: message,
        subText: subMessage,
        backgroundColor: '#e9ecef',
        textColor: '#495057',
        fontSize: 14,
        icon: '📄'
    });
};

/**
 * Generate a placeholder for a specific image variant
 */
export const generateVariantPlaceholder = (file: UploadedFile, variant: string): string => {
    const variantText = variant.charAt(0).toUpperCase() + variant.slice(1);

    // Get dimensions based on variant
    let width = 400;
    let height = 300;

    switch (variant) {
        case 'thumbnail':
            width = 150;
            height = 150;
            break;
        case 'card':
            width = 300;
            height = 200;
            break;
        case 'profile':
            width = 200;
            height = 200;
            break;
        case 'banner_small':
            width = 320;
            height = 180;
            break;
        case 'banner_medium':
            width = 640;
            height = 360;
            break;
        case 'banner_large':
            width = 1280;
            height = 720;
            break;
    }

    return generatePlaceholderDataUrl({
        width,
        height,
        text: `${variantText} • Processing...`,
        backgroundColor: '#f8f9fa',
        textColor: '#6c757d',
        fontSize: Math.max(12, Math.min(16, width / 25))
    });
};

/**
 * Check if a file should show a placeholder
 */
export const shouldShowPlaceholder = (file: UploadedFile, variant?: string): boolean => {
    // Show placeholder for temporary files when requesting original
    return file.x === true && (!variant || variant === 'original');
};

/**
 * Get the appropriate URL - either placeholder or real URL
 */
export const getFileUrlOrPlaceholder = async (
    file: UploadedFile,
    variant?: string,
    realUrlGenerator?: () => Promise<string>
): Promise<string> => {
    if (shouldShowPlaceholder(file, variant)) {
        if (variant && variant !== 'original') {
            return generateVariantPlaceholder(file, variant);
        }
        return generateTempFilePlaceholder(file);
    }

    // For non-temp files or variants, use real URL generator
    if (realUrlGenerator) {
        return await realUrlGenerator();
    }

    throw new Error('No URL generator provided for non-placeholder file');
};
