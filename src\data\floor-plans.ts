export const floorPlans = {
  floors: [
    {
      id: 0,
      name: 'Salon',
      active: true,
      label: 'S',
      sellPointId: 'W1HXNP7cHSCDfhskGxUd',
      items: [
        {
          number: '1',
          shape: 'square',
          position: {
            endY: '112',
            endX: '126',
            startY: '12',
            startX: '26',
          },
        },
        {
          number: '2',
          shape: 'square',
          position: {
            endY: '112',
            endX: '250',
            startY: '12',
            startX: '150',
          },
        },
        {
          number: '3',
          shape: 'square',
          position: {
            endY: '112',
            endX: '374',
            startY: '12',
            startX: '274',
          },
        },
        {
          number: '4',
          shape: 'square',
          position: {
            endY: '112',
            endX: '498',
            startY: '12',
            startX: '398',
          },
        },
        {
          number: '5',
          shape: 'square',
          position: {
            endY: '112',
            endX: '622',
            startY: '12',
            startX: '522',
          },
        },
        {
          number: '6',
          shape: 'square',
          position: {
            endY: '112',
            endX: '746',
            startY: '12',
            startX: '646',
          },
        },
        {
          number: '7',
          shape: 'square',
          position: {
            endY: '112',
            endX: '870',
            startY: '12',
            startX: '770',
          },
        },
        {
          number: '8',
          shape: 'square',
          position: {
            endY: '112',
            endX: '994',
            startY: '12',
            startX: '894',
          },
        },
        {
          number: '9',
          shape: 'square',
          position: {
            endY: '236',
            endX: '126',
            startY: '136',
            startX: '26',
          },
        },
        {
          number: '10',
          shape: 'square',
          position: {
            endY: '236',
            endX: '250',
            startY: '136',
            startX: '150',
          },
        },
        {
          number: '11',
          shape: 'square',
          position: {
            endY: '236',
            endX: '374',
            startY: '136',
            startX: '274',
          },
        },
        {
          number: '12',
          shape: 'square',
          position: {
            endY: '236',
            endX: '498',
            startY: '136',
            startX: '398',
          },
        },
        {
          number: '13',
          shape: 'square',
          position: {
            endY: '236',
            endX: '622',
            startY: '136',
            startX: '522',
          },
        },
        {
          number: '14',
          shape: 'square',
          position: {
            endY: '236',
            endX: '746',
            startY: '136',
            startX: '646',
          },
        },
        {
          number: '15',
          shape: 'square',
          position: {
            endY: '236',
            endX: '870',
            startY: '136',
            startX: '770',
          },
        },
        {
          number: '16',
          shape: 'square',
          position: {
            endY: '236',
            endX: '994',
            startY: '136',
            startX: '894',
          },
        },
        {
          number: '17',
          shape: 'square',
          position: {
            endY: '360',
            endX: '126',
            startY: '260',
            startX: '26',
          },
        },
        {
          number: '18',
          shape: 'square',
          position: {
            endY: '360',
            endX: '250',
            startY: '260',
            startX: '150',
          },
        },
        {
          number: '19',
          shape: 'square',
          position: {
            endY: '360',
            endX: '374',
            startY: '260',
            startX: '274',
          },
        },
        {
          number: '20',
          shape: 'square',
          position: {
            endY: '360',
            endX: '498',
            startY: '260',
            startX: '398',
          },
        },
        {
          number: '21',
          shape: 'square',
          position: {
            endY: '360',
            endX: '622',
            startY: '260',
            startX: '522',
          },
        },
        {
          number: '22',
          shape: 'square',
          position: {
            endY: '360',
            endX: '746',
            startY: '260',
            startX: '646',
          },
        },
        {
          number: '23',
          shape: 'square',
          position: {
            endY: '360',
            endX: '870',
            startY: '260',
            startX: '770',
          },
        },
        {
          number: '24',
          shape: 'square',
          position: {
            endY: '360',
            endX: '994',
            startY: '260',
            startX: '894',
          },
        },
        {
          number: '25',
          shape: 'square',
          position: {
            endY: '484',
            endX: '126',
            startY: '384',
            startX: '26',
          },
        },
        {
          number: '26',
          shape: 'square',
          position: {
            endY: '484',
            endX: '250',
            startY: '384',
            startX: '150',
          },
        },
        {
          number: '27',
          shape: 'square',
          position: {
            endY: '484',
            endX: '374',
            startY: '384',
            startX: '274',
          },
        },
        {
          number: '28',
          shape: 'square',
          position: {
            endY: '484',
            endX: '498',
            startY: '384',
            startX: '398',
          },
        },
        {
          number: '29',
          shape: 'square',
          position: {
            endY: '484',
            endX: '622',
            startY: '384',
            startX: '522',
          },
        },
        {
          number: '30',
          shape: 'square',
          position: {
            endY: '484',
            endX: '746',
            startY: '384',
            startX: '646',
          },
        },
        {
          number: '31',
          shape: 'square',
          position: {
            endY: '484',
            endX: '870',
            startY: '384',
            startX: '770',
          },
        },
        {
          number: '32',
          shape: 'square',
          position: {
            endY: '484',
            endX: '994',
            startY: '384',
            startX: '894',
          },
        },
        {
          number: '33',
          shape: 'square',
          position: {
            endY: '608',
            endX: '126',
            startY: '508',
            startX: '26',
          },
        },
        {
          number: '34',
          shape: 'square',
          position: {
            endY: '608',
            endX: '250',
            startY: '508',
            startX: '150',
          },
        },
        {
          number: '35',
          shape: 'square',
          position: {
            endY: '608',
            endX: '374',
            startY: '508',
            startX: '274',
          },
        },
        {
          number: '36',
          shape: 'square',
          position: {
            endY: '608',
            endX: '498',
            startY: '508',
            startX: '398',
          },
        },
        {
          number: '37',
          shape: 'square',
          position: {
            endY: '608',
            endX: '622',
            startY: '508',
            startX: '522',
          },
        },
        {
          number: '38',
          shape: 'square',
          position: {
            endY: '608',
            endX: '746',
            startY: '508',
            startX: '646',
          },
        },
        {
          number: '39',
          shape: 'square',
          position: {
            endY: '608',
            endX: '870',
            startY: '508',
            startX: '770',
          },
        },
        {
          number: '40',
          shape: 'square',
          position: {
            endY: '608',
            endX: '994',
            startY: '508',
            startX: '894',
          },
        },
      ],
    },
    {
      id: 1,
      name: 'Terasa',
      active: false,
      label: 'T',
      sellPointId: 'W1HXNP7cHSCDfhskGxUd2',
      items: [
        {
          number: '1',
          shape: 'square',
          position: {
            endY: '140',
            endX: '120',
            startY: '20',
            startX: '20',
          },
        },
        {
          number: '2',
          shape: 'square',
          position: {
            endY: '300',
            endX: '300',
            startY: '160',
            startX: '160',
          },
        },
      ],
    },
  ],
};
