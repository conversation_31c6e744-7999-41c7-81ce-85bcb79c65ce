import { SearchDataProvider } from '@react-admin/ra-search';
import { DataProvider, defaultDataProvider } from 'react-admin';

// Extend DataProvider type to include search method
export type DataProviderMatcher = (resource: string) => DataProvider;

/**
 * Combine multiple data providers into one.
 *
 * @param dataProviderMatcher A function that returns a data provider for a given resource.
 *
 * @example
 * const dataProvider = combineDataProviders(resource => {
 *    switch(resource) {
 *       case 'posts':
 *       case 'comments':
 *          return dataProvider1;
 *       case 'users':
 *          return dataProvider2;
 *       default:
 *         throw new Error('Unknown resource');
 *    }
 * });
 */
export const combineDataProviders = (
  dataProviderMatcher: DataProviderMatcher,
  searchableDataProvider: SearchDataProvider | undefined = undefined
): DataProvider =>
  new Proxy(defaultDataProvider, {
    get: (target, name) => {
      if (name === 'then') {
        return null;
      }
      // Handle the special case of search method
      if (name === 'search' && searchableDataProvider) {
        return (query: string) => {
          if (typeof name === 'symbol') {
            return;
          }
          return searchableDataProvider.search(query);
        };
      }
      // Handle regular DataProvider methods
      return (resource: string, ...params: any[]) => {
        if (typeof name === 'symbol') {
          return;
        }
        return dataProviderMatcher(resource)[name](resource, ...params);
      };
    },
  });
