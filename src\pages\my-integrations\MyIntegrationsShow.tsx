import { useMediaQuery } from '@mui/material';
import { Theme } from '@mui/material/styles';
import { ShowDialog } from '@react-admin/ra-form-layout';
import {
  SimpleShowLayout,
  useRecordContext,
  useRecordFromLocation,
} from 'react-admin';

import { MyIntegrationsOrderNowShow } from './MyIntegrationsOrderNowShow';
import { MyIntegrationsSagaShow } from './MyIntegrationsSagaShow';
import { MyIntegrationsWinMentorShow } from './MyIntegrationsWinMentorShow';

const MyIntegrationsShowInner = () => {
  const record = useRecordContext();

  return (
    <>
      {record?.id === 'winMentor' ? (
        <MyIntegrationsWinMentorShow />
      ) : record?.id === 'orderNow' ? (
        <MyIntegrationsOrderNowShow />
      ) : record?.id === 'saga' ? (
        <MyIntegrationsSagaShow />
      ) : (
        <></>
      )}
    </>
  );
};

export const MyIntegrationsShow = () => {
  const isMobile = useMediaQuery<Theme>(theme => theme.breakpoints.down('sm')); // sm breakpoint
  // Use useRecordFromLocation to get the
  const record = useRecordFromLocation();

  console.log('MyIntegrationsShow record:', record);

  // Determine dialog properties based on integration type and screen size
  // - OrderNow: Always fullScreen (for better QR code viewing)
  // - WinMentor: fullScreen on mobile, 'sm' size on larger devices
  // - Default: fullScreen on mobile, 'sm' size on larger devices
  let fullScreen = false;
  let maxWidth: 'sm' | false = 'sm';

  if (record?.id === 'orderNow') {
    // OrderNow: fullScreen on all devices for better QR code viewing
    fullScreen = true;
    maxWidth = false;
  } else if (record?.id === 'winMentor' || record?.id === 'saga') {
    // WinMentor and SAGA: fullScreen on mobile, sm on larger devices
    if (isMobile) {
      fullScreen = true;
      maxWidth = false;
    } else {
      fullScreen = false;
      maxWidth = 'sm';
    }
  } else {
    // Default behavior for other integrations
    fullScreen = isMobile;
    maxWidth = isMobile ? false : 'sm';
  }

  return (
    <ShowDialog fullScreen={true}>
      <SimpleShowLayout sx={{ p: 0 }}>
        <MyIntegrationsShowInner />
      </SimpleShowLayout>
    </ShowDialog>
  );
};
