import { useMemo } from 'react';
import { Box } from '@mui/material';

import ExtraDataCard from '~/components/molecules/ExtraDataCard';
import formatAndDivideNumber from '~/utils/formatAndDivideNumber';

export default function CompsCards({ compsData }: any) {
  const cardsConfig: { title: string; value: string | number }[] =
    useMemo(() => {
      return [
        {
          title: 'Total Comped',
          value:
            formatAndDivideNumber(
              (compsData?.totalItems?.netValue || 0) +
                (compsData?.totalModifier?.netValue || 0) +
                (compsData?.totalExtraCharges?.netValue || 0) +
                (compsData?.totalTips?.value || 0)
            ) || '-',
        },
        {
          title: 'Comped Items',
          value: formatAndDivideNumber(compsData?.totalItems?.netValue) || '-',
        },
        {
          title: 'Comped Modifiers',
          value:
            formatAndDivideNumber(compsData?.totalModifier?.netValue) || '-',
        },
        {
          title: 'Comped Extra Charges',
          value:
            formatAndDivideNumber(compsData?.totalExtraCharges?.netValue) ||
            '-',
        },
        {
          title: 'Comped Tips',
          value: formatAndDivideNumber(compsData?.totalTips?.value) || '-',
        },
      ];
    }, [compsData]);

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
          gap: 8,
          mt: 4,
          mb: 7,
          alignItems: 'center',
          justifyContent: 'space-around',
          pb: 2,
          borderBottom: '2px solid #F2F2F2',
          '@media print': {
            borderBottom: '2px solid black',
          },
        }}
      >
        {cardsConfig.map(
          (item: { title: string; value: string | number }, index: number) => {
            return (
              <Box
                key={index}
                sx={{
                  gridColumn: 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <ExtraDataCard item={item} />
              </Box>
            );
          }
        )}
      </Box>
    </>
  );
}
