import { useCallback } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Button, Grid, Typography } from '@mui/material';
import { ShowDialog } from '@react-admin/ra-form-layout';
import {
  SimpleShowLayout,
  TextField,
  useGetRecordId,
  useRedirect,
} from 'react-admin';

import { RESOURCES } from '~/providers/resources';
import GrayBgContainer from '../../components/atoms/GrayBgContainer';
import ListCard from '../../components/atoms/ListCard';
import getSideModalProps from '../../utils/getSideModalProps';

const Label = ({ value }: { value: string }) => (
  <Typography
    // @ts-ignore
    variant="label"
    fontWeight={500}
    sx={{ opacity: 0.5, display: 'block' }}
  >
    {value}
  </Typography>
);

const SellpointShowInner = () => {
  const redirect = useRedirect();
  const recordId = useGetRecordId();

  const handleClose = useCallback(() => {
    redirect('list', RESOURCES.LOCATIONS);
  }, [redirect]);

  return (
    <>
      {/* Header */}
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 1.5,
          position: 'sticky',
          top: 0,
          bgcolor: 'background.paper',
          zIndex: 3,
        }}
      >
        <Button
          // @ts-ignore
          variant="close-btn"
          aria-label="close"
          onClick={handleClose}
          sx={{ '& span': { mr: 0 } }}
        >
          <CloseIcon fontSize="small" />
        </Button>
        <Typography variant="h2">
          <TextField
            sx={{ fontSize: 24, fontWeight: '700' }}
            source="displayName"
          />
        </Typography>
        <Box
          onClick={() => {
            redirect('edit', RESOURCES.LOCATIONS, recordId);
          }}
          sx={{ p: 1, cursor: 'pointer' }}
        >
          {/* @ts-ignore */}
          <Typography variant="label" color="primary.main">
            Edit
          </Typography>
        </Box>
      </Box>

      {/* Body */}
      <GrayBgContainer sx={{ p: 1.5, minHeight: 'calc(100vh - 70px)' }}>
        {/* Sellpoint Information Section */}
        <ListCard
          sx={{
            width: '100%',
            alignItems: 'flex-start',
            py: 2.5,
            px: 3,
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h4">Sellpoint Information</Typography>
            </Grid>
            <Grid item xs={6}>
              <Label value={'Name'} />
              <TextField source="name" label="Name" />
            </Grid>
          </Grid>
        </ListCard>
      </GrayBgContainer>
    </>
  );
};

export default function SellpointShow() {
  return (
    <ShowDialog {...getSideModalProps({ hideBackdrop: true })}>
      <SimpleShowLayout
        sx={{
          p: 0,
          '& .RaSimpleShowLayout-row': {
            m: 0,
          },
        }}
      >
        <SellpointShowInner />
      </SimpleShowLayout>
    </ShowDialog>
  );
}
