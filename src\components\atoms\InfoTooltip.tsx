import InfoIcon from '@mui/icons-material/Info';
import { IconButton, styled, Tooltip, TooltipProps } from '@mui/material';

import { useTheme } from '../../contexts';

export default function InfoTooltip({ text }: { text: string }) {
  const { theme } = useTheme();

  const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} enterTouchDelay={0} />
  ))({
    [`& .MuiTooltip-tooltip`]: {
      maxWidth: 300,
      fontWeight: 200,
      fontSize: 13,
      padding: '10px 14px',
      color: theme.palette.mode === 'light' ? '#737373' : '#f2f2f2',
      backgroundColor: theme.palette.mode === 'light' ? 'white' : '#353434',
      boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px',
    },
  });

  return (
    <CustomWidthTooltip title={text} arrow>
      <IconButton>
        <InfoIcon color="disabled" />
      </IconButton>
    </CustomWidthTooltip>
  );
}
