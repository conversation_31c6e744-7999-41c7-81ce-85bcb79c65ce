import { defaultTheme } from 'react-admin';

const toolbarBg = '#F4F9FF'

export const lightTheme = {
  ...defaultTheme,
  palette: {
    mode: 'light',
    background: {
      default: '#fff',
      paper: '#fff',
      tinted: '#f2f2f2',
    },
    primary: {
      veryLight: '#0069ff1f',
      light: '#cce1ff',
      main: '#0064F0',
      dark: '#0049b2',
    },
    secondary: {
      main: '#fff',
    },
    error: {
      light: '#FFE5EA',
      main: '#DC4437',
      dark: '#BF0020',
    },
    success: {
      light: '#def5e6',
      main: '#007d2a',
      dark: '#1b5e20',
    },
    warning: {
      light: '#ffe6cc',
      main: '#ffe6cc',
      dark: '#ffe6cc',
    },
    custom: {
      gray200: '#f2f2f2',
      gray400: '#d9d9d9',
      gray600: '#989898',
      gray800: '#6D6D6D',
      fieldBg: '#fff',
      fadedText: '#0000004a',
      text: '#13131a',
      draggableTable: '#3d454b',
      modalHeader: 'white',
      warningText: '#F4B400',
    },
  },
  typography: {
    fontFamily: ['Geologica', 'Roboto'].join(','),
    h1: {
      fontSize: '34px',
      fontWeight: '500',
      '@media (max-width:600px)': {
        fontSize: '28px',
      },
    },
    h2: {
      fontSize: '24px',
      fontWeight: '700',
      '@media (max-width:600px)': {
        fontSize: '18px',
      },
    },
    h3: {
      fontSize: '20px',
      fontWeight: '700',
      '@media (max-width:600px)': {
        fontSize: '16px',
      },
    },
    h4: {
      fontSize: '18px',
      fontWeight: '700',
      '@media (max-width:600px)': {
        fontSize: '16px',
      },
    },
    h5: {
      fontSize: '16px',
      fontWeight: '600',
      '@media (max-width:600px)': {
        fontSize: '15px',
      },
    },
    h6: {
      fontSize: '16px',
      fontWeight: '600',
      '@media (max-width:600px)': {
        fontSize: '15px',
      },
    },
    label: {
      fontFamily: ['Geologica', 'Roboto'].join(','),
      fontSize: '14px',
      fontWeight: '600',
    },
    subtitle2: {
      color: '#737373',
      fontWeight: 200,
    },
  },
  components: {
    ...defaultTheme.components,
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: '40px',
          color: '#000000de',
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            padding: '8px',
          },
        },
      },
    },
    MuiButton: {
      defaultProps: {},
      styleOverrides: {
        root: {
          padding: '12px 20px',
          fontWeight: '500',
          boxShadow: 'none',
          transition: 'none',
          borderRadius: '6px',
          textTransform: 'none',
          fontSize: '15px',
          lineHeight: '1.5',
          '&:hover': {
            boxShadow: 'none',
          },
          '@media (max-width:600px)': {
            padding: '8px 15px',
            fontSize: '14px',
          },
        },
      },
      variants: [
        {
          props: { variant: 'outlined-2' },
          style: {
            backgroundColor: 'background.paper',
            border: '1px solid #DBDBDC',
            '&:hover': {
              opacity: 0.7,
            },
          },
        },
        {
          props: { variant: 'contained-light' },
          style: {
            background: 'rgba(0,0,0,.05)',
            color: '#0069FF',
            padding: '12px',
            minWidth: 'auto',
            '&:hover': {
              background: '#e6f0ff',
            },
            '&.MuiButton-contained-lightError': {
              color: '#DC4437',
              '&:hover': {
                background: '#ffe5ea',
              },

              '&.Mui-disabled': {
                color: 'rgba(0, 0, 0, 0.28)',
              },
            },
            '@media (max-width:600px)': {
              padding: '8px',
            },
          },
        },
        {
          props: { variant: 'contained' },
          style: {
            '@media (max-width:600px)': {
              minWidth: 'auto',
              padding: '8px',
            },
          },
        },
        {
          props: { variant: 'close-btn' },
          style: {
            background: 'rgba(0,0,0,.05)',
            color: 'black',
            padding: '12px',
            minWidth: 'auto',
            '&:hover': {
              background: '#e6f0ff',
            },
            '@media (max-width:600px)': {
              padding: '8px',
            },
          },
        },
        {
          props: { variant: 'transparent' },
          style: {
            background: 'rgba(0,0,0,.0)',
            color: 'black',
            padding: '8px 12px',
            height: '40px',
            minWidth: 'auto',
            border: 'solid 1px #d9d9d9',
            '&:hover': {
              background: 'rgba(0,0,0,.05)',
            },
            '@media (max-width:600px)': {
              padding: '8px',
            },
          },
        },
      ],
    },
    MuiButtonBase: {
      defaultProps: {
        disableRipple: true,
      },
    },
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          width: '100%',
          '& .MuiFormHelperText-root': {
            display: 'none',
          },
          '& .MuiInputBase-root': {
            minHeight: 45,
          },
          ' fieldset': {
            borderRadius: '6px',
          },
          '& .MuiInputBase-input, .MuiInputLabel-root': {
            fontSize: '14px',
            top: '4px',
          },
          '&.custom-outlined': {
            margin: 0,
            '& .MuiInputBase-input + input': {
              marginLeft: '200px',
            },
            '& .MuiInputBase-input + fieldset': {
              borderRadius: 0,
              border: 'none',
            },
          },
        },
      },
    },
    // MuiInputLabel: {
    //   styleOverrides: {
    //     root: {
    //       fontSize: '14px',
    //     },
    //   },
    // },
    MuiSelect: {
      defaultProps: {
        variant: 'outlined',
      },
      // styleOverrides: {
      //   root: {
      //     width: '100%',
      //     '& .MuiFormHelperText-root': {
      //       display: 'none',
      //     },
      //     '& .MuiSelect-select': {
      //       minHeight: '45px !important',
      //       boxSizing: 'border-box',
      //     },
      //     '& .MuiInputBase-root': {
      //       minHeight: '45px !important',
      //     },
      //     ' fieldset': {
      //       borderRadius: '6px',
      //     },
      //     '&.custom-outlined': {
      //       margin: 0,
      //       '& .MuiInputBase-input + input': {
      //         marginLeft: '200px',
      //       },
      //       '& .MuiInputBase-input + fieldset': {
      //         borderRadius: 0,
      //         border: 'none',
      //       },
      //     },
      //   },
      // },
    },
    RaBulkActionsToolbar: {
      styleOverrides: {
        root: {
          '& .RaBulkActionsToolbar-toolbar': {
            backgroundColor: toolbarBg,
          },
          '& .RaBulkActionsToolbar-icon:hover': {
            backgroundColor: toolbarBg,
          },
        },
      },
    },
    RaBulkDeleteWithConfirmButton: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: 'transparent',
          },
        },
      },
    },
    RaDatagrid: {
      styleOverrides: {
        root: {
          '& .RaDatagrid-tableWrapper': {
            marginTop: '20px',
          },
          '& .MuiTableBody-root .MuiTableRow-root.RaDatagrid-row:hover': {
            backgroundColor: '#cce1ff',
          },
          '& .RaDatagrid-headerCell': {
            borderBottom: '1px solid rgb(129, 129, 129)',
            borderTop: '1px solid rgba(129, 129, 129, 0.1)',
          },
          '& .RaDatagrid-rowCell, .MuiTableCell-body.MuiTableCell-paddingCheckbox':
            {
              borderBottom: '1px solid rgba(129, 129, 129, 0.1)',
              color: 'inherit',
            },
          '& .RaDatagrid-thead, .RaDatagrid-row': {
            height: '48px',
          },
        },
      },
    },
  },
  sidebar: {
    width: 300,
    closedWidth: 0,
  },
};
